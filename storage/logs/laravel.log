[2025-07-06 23:39:10] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections? (Connection: pgsql, SQL: select exists (select 1 from pg_class c, pg_namespace n where n.nspname = current_schema() and c.relname = 'migrations' and c.relkind in ('r', 'p') and n.oid = c.relnamespace)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: Connection refused
\tIs the server running on that host and accepting TCP/IP connections? (Connection: pgsql, SQL: select exists (select 1 from pg_class c, pg_namespace n where n.nspname = current_schema() and c.relname = 'migrations' and c.relkind in ('r', 'p') and n.oid = c.relnamespace)) at /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(55): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\StatusCommand->{closure:Illuminate\\Database\\Console\\Migrations\\StatusCommand::handle():54}()
#9 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(54): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#10 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#11 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#12 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#16 /Users/<USER>/Documents/augment-projects/auction/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 /Users/<USER>/Documents/augment-projects/auction/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Documents/augment-projects/auction/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Documents/augment-projects/auction/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 /Users/<USER>/Documents/augment-projects/auction/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: Connection refused
\tIs the server running on that host and accepting TCP/IP connections? at /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:67)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): PDO::connect('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#6 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select exists (...', Array)
#10 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#11 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#12 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#13 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#14 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#15 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#16 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(55): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\StatusCommand->{closure:Illuminate\\Database\\Console\\Migrations\\StatusCommand::handle():54}()
#19 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(54): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#21 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#22 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 /Users/<USER>/Documents/augment-projects/auction/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/Documents/augment-projects/auction/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/Documents/augment-projects/auction/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/Documents/augment-projects/auction/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Documents/augment-projects/auction/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}
"} 
[2025-07-08 14:57:22] local.ERROR: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections? (Connection: pgsql, SQL: select * from "sessions" where "id" = aWfsoky5YVEAQL5sGDZFcQRc4sbm1Tg2xWjDVxCT limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: Connection refused
\tIs the server running on that host and accepting TCP/IP connections? (Connection: pgsql, SQL: select * from \"sessions\" where \"id\" = aWfsoky5YVEAQL5sGDZFcQRc4sbm1Tg2xWjDVxCT limit 1) at /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select * from \"...', Array, false)
#3 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3115}()
#5 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3038): Illuminate\\Database\\Query\\Builder->first(Array)
#8 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('aWfsoky5YVEAQL5...')
#9 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('aWfsoky5YVEAQL5...')
#10 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#11 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Store.php(88): Illuminate\\Session\\Store->loadSession()
#12 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(146): Illuminate\\Session\\Store->start()
#13 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():143}(Object(Illuminate\\Session\\Store))
#14 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Documents/augment-projects/auction/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>')
#53 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] connection to server at \"127.0.0.1\", port 5432 failed: Connection refused
\tIs the server running on that host and accepting TCP/IP connections? at /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:67)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): PDO::connect('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=127....', 'postgres', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=127....', Array, Array)
#3 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#6 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(509): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select * from \"...', Array)
#9 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#10 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#11 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select * from \"...', Array, false)
#12 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#13 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3115}()
#14 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#15 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#16 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3038): Illuminate\\Database\\Query\\Builder->first(Array)
#17 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('aWfsoky5YVEAQL5...')
#18 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('aWfsoky5YVEAQL5...')
#19 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#20 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Store.php(88): Illuminate\\Session\\Store->loadSession()
#21 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(146): Illuminate\\Session\\Store->start()
#22 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():143}(Object(Illuminate\\Session\\Store))
#23 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#24 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#25 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Documents/augment-projects/auction/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Documents/augment-projects/auction/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>')
#62 {main}
"} 
