<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Auction Platform Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the auction platform
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Platform Settings
    |--------------------------------------------------------------------------
    */
    'platform_name' => env('AUCTION_PLATFORM_NAME', 'Auction Platform'),
    'platform_url' => env('APP_URL', 'http://localhost'),
    'support_email' => env('AUCTION_SUPPORT_EMAIL', '<EMAIL>'),

    /*
    |--------------------------------------------------------------------------
    | Auction Settings
    |--------------------------------------------------------------------------
    */
    'auction' => [
        'min_duration_minutes' => env('AUCTION_MIN_DURATION_MINUTES', 30),
        'max_duration_days' => env('AUCTION_MAX_DURATION_DAYS', 30),
        'default_duration_days' => env('AUCTION_DEFAULT_DURATION_DAYS', 7),
        'auto_extend_minutes' => env('AUCTION_AUTO_EXTEND_MINUTES', 10),
        'ending_soon_hours' => env('AUCTION_ENDING_SOON_HOURS', 24),
        'max_images_per_auction' => env('AUCTION_MAX_IMAGES', 10),
        'featured_duration_days' => env('AUCTION_FEATURED_DURATION_DAYS', 7),
    ],

    /*
    |--------------------------------------------------------------------------
    | Bidding Settings
    |--------------------------------------------------------------------------
    */
    'bidding' => [
        'min_bid_increment' => env('AUCTION_MIN_BID_INCREMENT', 1.00),
        'max_bid_amount' => env('AUCTION_MAX_BID_AMOUNT', 1000000.00),
        'proxy_bidding_enabled' => env('AUCTION_PROXY_BIDDING_ENABLED', true),
        'auto_bidding_enabled' => env('AUCTION_AUTO_BIDDING_ENABLED', true),
        'bid_history_public' => env('AUCTION_BID_HISTORY_PUBLIC', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    */
    'payment' => [
        'platform_fee_rate' => env('AUCTION_PLATFORM_FEE_RATE', 0.029), // 2.9%
        'fixed_fee' => env('AUCTION_FIXED_FEE', 0.30), // $0.30
        'payment_due_days' => env('AUCTION_PAYMENT_DUE_DAYS', 3),
        'auto_payment_enabled' => env('AUCTION_AUTO_PAYMENT_ENABLED', true),
        'refund_window_days' => env('AUCTION_REFUND_WINDOW_DAYS', 14),
        'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD'],
        'default_currency' => env('AUCTION_DEFAULT_CURRENCY', 'USD'),
    ],

    /*
    |--------------------------------------------------------------------------
    | User Settings
    |--------------------------------------------------------------------------
    */
    'user' => [
        'email_verification_required' => env('AUCTION_EMAIL_VERIFICATION_REQUIRED', true),
        'max_auctions_per_day' => [
            'user' => 0,
            'seller' => 5,
            'premium_seller' => 20,
            'admin' => 100,
        ],
        'commission_rates' => [
            'seller' => 0.10, // 10%
            'premium_seller' => 0.05, // 5%
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Upload Settings
    |--------------------------------------------------------------------------
    */
    'images' => [
        'max_file_size_kb' => env('AUCTION_MAX_IMAGE_SIZE_KB', 5120), // 5MB
        'allowed_mime_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
        ],
        'thumbnail_sizes' => [
            'thumb' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 400, 'height' => 400],
            'large' => ['width' => 800, 'height' => 800],
        ],
        'optimize_images' => env('AUCTION_OPTIMIZE_IMAGES', true),
        'convert_to_webp' => env('AUCTION_CONVERT_TO_WEBP', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'channels' => [
            'database' => true,
            'email' => env('AUCTION_EMAIL_NOTIFICATIONS', true),
            'sms' => env('AUCTION_SMS_NOTIFICATIONS', false),
            'push' => env('AUCTION_PUSH_NOTIFICATIONS', false),
        ],
        'types' => [
            'bid_placed' => ['database', 'email'],
            'outbid' => ['database', 'email'],
            'auction_ending' => ['database', 'email'],
            'auction_won' => ['database', 'email'],
            'auction_ended' => ['database', 'email'],
            'payment_confirmed' => ['database', 'email'],
            'payment_failed' => ['database', 'email'],
        ],
        'cleanup_after_days' => env('AUCTION_NOTIFICATION_CLEANUP_DAYS', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | WebSocket Settings
    |--------------------------------------------------------------------------
    */
    'websocket' => [
        'enabled' => env('AUCTION_WEBSOCKET_ENABLED', true),
        'driver' => env('BROADCAST_DRIVER', 'pusher'),
        'real_time_bidding' => env('AUCTION_REAL_TIME_BIDDING', true),
        'real_time_notifications' => env('AUCTION_REAL_TIME_NOTIFICATIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Settings
    |--------------------------------------------------------------------------
    */
    'search' => [
        'enabled' => env('AUCTION_SEARCH_ENABLED', true),
        'driver' => env('AUCTION_SEARCH_DRIVER', 'database'), // database, elasticsearch, algolia
        'results_per_page' => env('AUCTION_SEARCH_RESULTS_PER_PAGE', 20),
        'highlight_enabled' => env('AUCTION_SEARCH_HIGHLIGHT', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'auction_ttl' => env('AUCTION_CACHE_TTL', 300), // 5 minutes
        'category_ttl' => env('CATEGORY_CACHE_TTL', 3600), // 1 hour
        'user_stats_ttl' => env('USER_STATS_CACHE_TTL', 1800), // 30 minutes
        'search_ttl' => env('SEARCH_CACHE_TTL', 600), // 10 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'rate_limiting' => [
            'bid_attempts_per_minute' => env('AUCTION_BID_RATE_LIMIT', 10),
            'search_requests_per_minute' => env('AUCTION_SEARCH_RATE_LIMIT', 60),
            'image_uploads_per_hour' => env('AUCTION_IMAGE_UPLOAD_RATE_LIMIT', 50),
        ],
        'fraud_detection' => [
            'enabled' => env('AUCTION_FRAUD_DETECTION', true),
            'max_bid_increase_percentage' => env('AUCTION_MAX_BID_INCREASE', 500), // 500%
            'suspicious_activity_threshold' => env('AUCTION_SUSPICIOUS_THRESHOLD', 10),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Settings
    |--------------------------------------------------------------------------
    */
    'analytics' => [
        'enabled' => env('AUCTION_ANALYTICS_ENABLED', true),
        'track_views' => env('AUCTION_TRACK_VIEWS', true),
        'track_searches' => env('AUCTION_TRACK_SEARCHES', true),
        'track_bids' => env('AUCTION_TRACK_BIDS', true),
        'retention_days' => env('AUCTION_ANALYTICS_RETENTION_DAYS', 365),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Settings
    |--------------------------------------------------------------------------
    */
    'maintenance' => [
        'cleanup_expired_auctions' => env('AUCTION_CLEANUP_EXPIRED', true),
        'cleanup_old_notifications' => env('AUCTION_CLEANUP_NOTIFICATIONS', true),
        'cleanup_old_views' => env('AUCTION_CLEANUP_VIEWS', true),
        'cleanup_interval_hours' => env('AUCTION_CLEANUP_INTERVAL_HOURS', 24),
    ],
];
