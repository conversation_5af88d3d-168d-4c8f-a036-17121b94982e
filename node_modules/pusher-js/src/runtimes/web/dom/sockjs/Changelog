0.3.4
=====

 * Mentioned njoyce's fork of sockjs-gevent.
 * #90 - Don't catch onbeforeunload event - it breaks javascript://
   links in IE.
 * IE mangles 204 response code for 1223 on ajax, see:
   http://bugs.jquery.com/ticket/1450
 * Make `new` optional for SockJS constructor (via substack).
 * It is impossible to cancel JSONP polling request - compensate for that.
 * Refactored EventEmitter prototype (used only internally)


0.3.2
=====

 * #77 - Getting /info on modern browsers when html is served from
         file:// urls was broken.

0.3.1
=====

 * #61 - Meteor guys found that we unintentionally catch "onopen" errors.
 * #63 - Meteorjs guys found that xhr-streaming on Safari sometimes
   left busy cursor running.
 * Increased allowed time for websocket transport (from 1 rtt to 2),
   this should make ws transport more reliable over SSL, at the cost
   of slightly longer connection time for users with blocked ws.
 * #57 - previous fix didn't really work, sockjs-client still left
   a mess in browsers history when using iframe transports. This
   is fixed now.
 * #60 - <PERSON> 12 (next) claims to do AJAX2 / CORS, but can't
   do xhr-streaming.
 * #58 - onunload test sometimes failed on Safari on windows
 * Updated readme WRT websocket protocols
 * Updated readme WRT deployments on heroku
 * Add minimalistic license block to every source file.


0.3.0
=====

 * Temporarily disabled iframe tests - they are failing unpredictably.
 * #57 - pointing an iframe to "about:blank" during cleanup caused
   Opera to messup history.
 * #55 - Improved iframe abstraction (reduced a possible mem leak)
 * Refactored AJAX abstractions, for better CORS handing - again.
 * Add additional parent origin security check to an iframe.
 * Urls with hashes or query strings can't be passed to SockJS.
 * #18 - Mention workaround for Firefox ESC key issue
 * #53 - AMD compliance
 * sockjs/sockjs-protocol#28 - always use square brackets for
   websocket frames
 * #51 - initial support for IE10 - try XHR before XDR
 * #28 - handle onunload / onbeforeunload in a more robust fashion
 * #49 - support SockJS-client being used from files served from
   file:// urls.


0.2.1
=====

 * "smoke-latency.html" test was unnecesairly sending too much data.
 * Bumped core dependencies (coffee-script and uglify-js)
 * Minor updates to the README, few cosmetic changes in the code.


0.2.0
=====

 * The API had changed - use `protocols_whitelist` option instead of
   passing  an array of protocols as a second argument to SockJS constructor.
 * Dropped 'chunking-test' functionality and replace it with 'info'.
 * Rewritten protocol-choosing alogirthm, see "utils.detectProtocols" method.
 * Use dynamic protocol timeouts based on RTT, not hardcoded 5 seconds
 * #34 - Don't ever reuse `session_id`, especially when trying
   fallback protocols.
 * The test server got moved from SockJS-client to SockJS-node.
 * Don't test unicode surrogates - it can't work in some environments.
 * XHR/XDR helpers were rewritten, ajax transports were simplified.
 * Added a domain check in the iframe to improve security.
 * SockJS will now trigger 1002 error if there is a problem during handshake
   instead of 2000 error.
 * Smoke-throughput test is renamed to smoke-latency.

0.1.2
=====

 * #29 - Allow all unicode characters to be send over SockJS.
 * #15 - SockJS should now work fine even if the connection is started
   in HEAD, before BODY is loaded.
 * #28 - In rare circumstances WebSocket connection can be left intact
   after the page is unloaded in FireFox.
 * Updated scripts to work with Node 0.6.
 * Initial work to do better QUnit testing.
 * Updated the minifying script (always escape unicode chars, remove
   trailing comment).
 * Use string instead of array of chars (utils.js:random_number_string).


0.1.1
=====

 * #21 Get JsonP transport working on IE9 (Vladimir Dronnikov).
 * #26 Emit heartbeat event.
 * #27 Include license inline.


0.1.0
=====

 * SockJS-client can only send UTF-8 encodable strings.  Previously we
   took advantage of rich data structures and automatically
   json-encoded them, but this got removed.  Now, all data passed to
   `send` will be converted to string. This is also how native
 * `status` property on `EventClose` is renamed to `code`
   as per Websocket API
   WebSockets behave.
 * The test server was updated to new `sockjs-node` API
 * Fixed problem with Jsonp-polling transport on IE9
 * Repository was moved - updated links.


0.0.4
=====

 * All transports were refactored, some transports were introduced:
   htmlfile and separate xhr-streaming.
 * Added logic to detect support for http chunking, and thus a
   possibility to rule out streaming transports before running them.
 * Added 'cookie' option, useful for cookie-based load balancing
   (currently, it make a difference only for IE).
 * Added hack to prevent EventSource from crashing Safari and Chrome.
 * Loads and loads of other small and medium changes.


0.0.2
=====

 * Initial support for JSESSIONID based load balancing. Currently
   doesn't play nicely with IE XDomainRequest transport.


0.0.1
=====

 * Initial release.
