{"version": 3, "sources": ["webpack://Pusher/webpack/universalModuleDefinition", "webpack://Pusher/webpack/bootstrap", "webpack://Pusher/./node_modules/@stablelib/base64/base64.ts", "webpack://Pusher/./node_modules/@stablelib/utf8/utf8.ts", "webpack://Pusher/./node_modules/tweetnacl/nacl-fast.js", "webpack://Pusher/./src/core/pusher-with-encryption.js", "webpack://Pusher/./src/runtimes/web/dom/script_receiver_factory.ts", "webpack://Pusher/./src/core/defaults.ts", "webpack://Pusher/./src/runtimes/web/dom/dependencies.ts", "webpack://Pusher/./src/runtimes/web/dom/dependency_loader.ts", "webpack://Pusher/./src/core/utils/url_store.ts", "webpack://Pusher/./src/core/auth/options.ts", "webpack://Pusher/./src/core/errors.ts", "webpack://Pusher/./src/runtimes/isomorphic/auth/xhr_auth.ts", "webpack://Pusher/./src/core/base64.ts", "webpack://Pusher/./src/core/utils/timers/abstract_timer.ts", "webpack://Pusher/./src/core/utils/timers/index.ts", "webpack://Pusher/./src/core/util.ts", "webpack://Pusher/./src/core/utils/collections.ts", "webpack://Pusher/./src/core/logger.ts", "webpack://Pusher/./src/runtimes/web/auth/jsonp_auth.ts", "webpack://Pusher/./src/runtimes/web/dom/script_request.ts", "webpack://Pusher/./src/runtimes/web/dom/jsonp_request.ts", "webpack://Pusher/./src/runtimes/web/timeline/jsonp_timeline.ts", "webpack://Pusher/./src/core/transports/url_schemes.ts", "webpack://Pusher/./src/core/events/callback_registry.ts", "webpack://Pusher/./src/core/events/dispatcher.ts", "webpack://Pusher/./src/core/transports/transport_connection.ts", "webpack://Pusher/./src/core/transports/transport.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transports.ts", "webpack://Pusher/./src/runtimes/web/transports/transports.ts", "webpack://Pusher/./src/runtimes/web/net_info.ts", "webpack://Pusher/./src/core/transports/assistant_to_the_transport_manager.ts", "webpack://Pusher/./src/core/connection/protocol/protocol.ts", "webpack://Pusher/./src/core/connection/connection.ts", "webpack://Pusher/./src/core/connection/handshake/index.ts", "webpack://Pusher/./src/core/timeline/timeline_sender.ts", "webpack://Pusher/./src/core/channels/channel.ts", "webpack://Pusher/./src/core/channels/private_channel.ts", "webpack://Pusher/./src/core/channels/members.ts", "webpack://Pusher/./src/core/channels/presence_channel.ts", "webpack://Pusher/./src/core/channels/encrypted_channel.ts", "webpack://Pusher/./src/core/connection/connection_manager.ts", "webpack://Pusher/./src/core/channels/channels.ts", "webpack://Pusher/./src/core/utils/factory.ts", "webpack://Pusher/./src/core/transports/transport_manager.ts", "webpack://Pusher/./src/core/strategies/sequential_strategy.ts", "webpack://Pusher/./src/core/strategies/best_connected_ever_strategy.ts", "webpack://Pusher/./src/core/strategies/websocket_prioritized_cached_strategy.ts", "webpack://Pusher/./src/core/strategies/delayed_strategy.ts", "webpack://Pusher/./src/core/strategies/if_strategy.ts", "webpack://Pusher/./src/core/strategies/first_connected_strategy.ts", "webpack://Pusher/./src/runtimes/web/default_strategy.ts", "webpack://Pusher/./src/runtimes/web/http/http_xdomain_request.ts", "webpack://Pusher/./src/core/http/http_request.ts", "webpack://Pusher/./src/core/http/state.ts", "webpack://Pusher/./src/core/http/http_socket.ts", "webpack://Pusher/./src/core/timeline/level.ts", "webpack://Pusher/./src/core/http/http_streaming_socket.ts", "webpack://Pusher/./src/core/http/http_polling_socket.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http_xhr_request.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http.ts", "webpack://Pusher/./src/runtimes/web/http/http.ts", "webpack://Pusher/./src/runtimes/web/runtime.ts", "webpack://Pusher/./src/runtimes/web/transports/transport_connection_initializer.ts", "webpack://Pusher/./src/core/timeline/timeline.ts", "webpack://Pusher/./src/core/strategies/transport_strategy.ts", "webpack://Pusher/./src/core/strategies/strategy_builder.ts", "webpack://Pusher/./src/core/options.ts", "webpack://Pusher/./src/core/auth/user_authenticator.ts", "webpack://Pusher/./src/core/auth/channel_authorizer.ts", "webpack://Pusher/./src/core/config.ts", "webpack://Pusher/./src/core/auth/deprecated_channel_authorizer.ts", "webpack://Pusher/./src/core/watchlist.ts", "webpack://Pusher/./src/core/utils/flat_promise.ts", "webpack://Pusher/./src/core/user.ts", "webpack://Pusher/./src/core/pusher.ts", "webpack://Pusher/./src/core/pusher-with-encryption.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "_padding<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "this", "encode", "data", "out", "_encodeByte", "left", "maxDecoded<PERSON><PERSON>th", "decodedLength", "_getPaddingLength", "decode", "Uint8Array", "paddingLength", "op", "haveBad", "v0", "v1", "v2", "v3", "_decodeChar", "charCodeAt", "Error", "b", "result", "String", "fromCharCode", "Coder", "stdCoder", "URLSafeCoder", "urlSafeCoder", "INVALID_UTF8", "arr", "pos", "chars", "min", "n1", "n2", "n3", "push", "join", "nacl", "gf", "init", "Float64Array", "randombytes", "_0", "_9", "gf0", "gf1", "_121665", "D", "D2", "X", "Y", "I", "ts64", "x", "h", "vn", "xi", "y", "yi", "crypto_verify_16", "crypto_verify_32", "crypto_core_salsa20", "inp", "k", "u", "j0", "j1", "j2", "j3", "j4", "j5", "j6", "j7", "j8", "j9", "j10", "j11", "j12", "j13", "j14", "j15", "x0", "x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10", "x11", "x12", "x13", "x14", "x15", "core_salsa20", "crypto_core_hsalsa20", "core_hsalsa20", "sigma", "crypto_stream_salsa20_xor", "cpos", "mpos", "z", "crypto_stream_salsa20", "crypto_stream", "sn", "crypto_stream_xor", "poly1305", "t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7", "buffer", "Uint16Array", "pad", "leftover", "fin", "crypto_onetimeauth", "outpos", "update", "finish", "crypto_onetimeauth_verify", "hpos", "crypto_secretbox", "crypto_secretbox_open", "set25519", "a", "car25519", "v", "Math", "floor", "sel25519", "q", "pack25519", "j", "neq25519", "par25519", "unpack25519", "A", "Z", "M", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "t23", "t24", "t25", "t26", "t27", "t28", "t29", "t30", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "b10", "b11", "b12", "b13", "b14", "b15", "S", "inv25519", "pow2523", "crypto_scalarmult", "e", "f", "x32", "subarray", "x16", "crypto_scalarmult_base", "crypto_box_keypair", "crypto_box_beforenm", "blocks", "bytes", "d0", "d1", "d2", "d3", "d4", "d5", "d6", "d7", "d8", "d9", "hibit", "h0", "h1", "h2", "h3", "h4", "h5", "h6", "h7", "h8", "h9", "r0", "r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "mac", "macpos", "mask", "g", "want", "crypto_box_afternm", "crypto_box_open_afternm", "K", "crypto_hashblocks_hl", "hh", "hl", "bh0", "bh1", "bh2", "bh3", "bh4", "bh5", "bh6", "bh7", "bl0", "bl1", "bl2", "bl3", "bl4", "bl5", "bl6", "bl7", "th", "tl", "wh", "Int32Array", "wl", "ah0", "ah1", "ah2", "ah3", "ah4", "ah5", "ah6", "ah7", "al0", "al1", "al2", "al3", "al4", "al5", "al6", "al7", "crypto_hash", "add", "cswap", "pack", "tx", "ty", "zi", "scalarmult", "scalarbase", "crypto_sign_keypair", "pk", "sk", "seeded", "L", "modL", "carry", "reduce", "crypto_sign", "sm", "smlen", "crypto_sign_open", "chk", "num", "den", "den2", "den4", "den6", "unpackneg", "checkLengths", "checkArrayTypes", "arguments", "TypeError", "cleanup", "lowlevel", "crypto_box", "crypto_box_open", "crypto_secretbox_KEYBYTES", "crypto_secretbox_NONCEBYTES", "crypto_secretbox_ZEROBYTES", "crypto_secretbox_BOXZEROBYTES", "crypto_scalarmult_BYTES", "crypto_scalarmult_SCALARBYTES", "crypto_box_PUBLICKEYBYTES", "crypto_box_SECRETKEYBYTES", "crypto_box_BEFORENMBYTES", "crypto_box_NONCEBYTES", "crypto_box_ZEROBYTES", "crypto_box_BOXZEROBYTES", "crypto_sign_BYTES", "crypto_sign_PUBLICKEYBYTES", "crypto_sign_SECRETKEYBYTES", "crypto_sign_SEEDBYTES", "crypto_hash_BYTES", "randomBytes", "secretbox", "msg", "nonce", "open", "box", "<PERSON><PERSON><PERSON><PERSON>", "non<PERSON><PERSON><PERSON><PERSON>", "overheadLength", "scalarMult", "base", "scalar<PERSON>ength", "groupElementLength", "public<PERSON>ey", "secret<PERSON>ey", "before", "checkBoxLengths", "after", "keyPair", "fromSecretKey", "publicKeyLength", "secretKeyLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sign", "signed<PERSON>g", "tmp", "mlen", "detached", "sig", "verify", "fromSeed", "seed", "seedLength", "<PERSON><PERSON><PERSON><PERSON>", "hash", "hash<PERSON><PERSON><PERSON>", "setPRNG", "fn", "crypto", "self", "msCrypto", "getRandomValues", "default", "ScriptReceiverFactory", "prefix", "lastId", "callback", "number", "id", "called", "callbackWrapper", "apply", "receiver", "ScriptReceivers", "VERSION", "PROTOCOL", "wsPort", "wssPort", "wsPath", "httpHost", "httpPort", "httpsPort", "httpPath", "stats_host", "authEndpoint", "authTransport", "activityTimeout", "pongTimeout", "unavailableTimeout", "userAuthentication", "endpoint", "transport", "channelAuthorization", "cdn_http", "cdn_https", "dependency_suffix", "DependenciesReceivers", "Dependencies", "options", "receivers", "loading", "request", "createScriptRequest", "<PERSON><PERSON><PERSON>", "error", "remove", "callbacks", "success<PERSON>allback", "wasSuccessful", "send", "protocol", "getDocument", "location", "useTLS", "replace", "version", "getRoot", "suffix", "urlStore", "baseUrl", "urls", "authenticationEndpoint", "path", "authorizationEndpoint", "javascriptQuickStart", "triggeringClientEvents", "encryptedChannelSupport", "fullUrl", "AuthRequestType", "url<PERSON>bj", "url", "BadEventName", "super", "setPrototypeOf", "BadChannelName", "RequestTimedOut", "TransportPriorityTooLow", "TransportClosed", "UnsupportedFeature", "UnsupportedTransport", "UnsupportedStrategy", "HTTPAuthError", "status", "context", "query", "authOptions", "authRequestType", "xhr", "createXHR", "headerName", "setRequestHeader", "headers", "headers<PERSON>rovider", "dynamicHeaders", "onreadystatechange", "readyState", "parsed", "JSON", "parse", "responseText", "toString", "UserAuthentication", "ChannelAuthorization", "b64chars", "b64tab", "char<PERSON>t", "cb_utob", "cc", "utob", "cb_encode", "ccc", "padlen", "ord", "btoa", "set", "clear", "delay", "timer", "clearTimeout", "clearInterval", "setTimeout", "setInterval", "now", "Date", "valueOf", "defer", "args", "boundArguments", "Array", "slice", "concat", "extend", "target", "sources", "extensions", "constructor", "stringify", "safeJSONStringify", "arrayIndexOf", "array", "item", "nativeIndexOf", "indexOf", "objectApply", "keys", "_", "map", "filter", "test", "filterObject", "Boolean", "any", "encodeParamsObject", "encodeURIComponent", "buildQueryString", "params", "undefined", "method", "source", "objects", "paths", "derez", "nu", "$ref", "globalLog", "message", "console", "log", "globalLogWarn", "globalLogError", "warn", "defaultLoggingFunction", "logToConsole", "callback<PERSON><PERSON>", "nextAuthCallbackID", "document", "script", "createElement", "auth_callbacks", "callback_name", "src", "head", "getElementsByTagName", "documentElement", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "ScriptRequest", "errorString", "type", "charset", "addEventListener", "onerror", "onload", "async", "attachEvent", "navigator", "userAgent", "errorScript", "text", "nextS<PERSON>ling", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getAgent", "sender", "host", "createJSONPRequest", "getGenericURL", "baseScheme", "hostTLS", "hostNonTLS", "getGenericPath", "queryString", "ws", "getInitial", "http", "sockjs", "_callbacks", "prefixedEventName", "names", "removeCallback", "removeAllCallbacks", "binding", "failThrough", "global_callbacks", "eventName", "unbind", "unbind_global", "metadata", "hooks", "priority", "initialize", "transportConnectionInitializer", "state", "timeline", "generateUniqueID", "handlesActivityChecks", "supportsPing", "socket", "getSocket", "onError", "changeState", "bindListeners", "debug", "close", "ping", "beforeOpen", "onopen", "emit", "buildTimelineMessage", "closeEvent", "code", "reason", "<PERSON><PERSON><PERSON>", "unbindListeners", "onOpen", "onclose", "onClose", "onmessage", "onMessage", "onactivity", "onActivity", "info", "cid", "environment", "isSupported", "WSTransport", "isInitialized", "getWebSocketAPI", "createWebSocket", "httpConfiguration", "streamingConfiguration", "HTTPFactory", "createStreamingSocket", "pollingConfiguration", "createPollingSocket", "xhrConfiguration", "isXHRSupported", "xhr_streaming", "xhr_polling", "SockJSTransport", "file", "SockJS", "js_path", "ignore_null_origin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xdrConfiguration", "isXDRSupported", "XDRStreamingTransport", "XDRPollingTransport", "xdr_streaming", "xdr_polling", "onLine", "manager", "min<PERSON>ing<PERSON>elay", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "ping<PERSON><PERSON><PERSON>", "connection", "createConnection", "openTimestamp", "onClosed", "reportDeath", "lifespan", "max", "isAlive", "Protocol", "decodeMessage", "messageEvent", "messageData", "pusherEventData", "pusherEvent", "event", "channel", "user_id", "encodeMessage", "processHandshake", "activity_timeout", "action", "socket_id", "getCloseAction", "getCloseError", "send_event", "listeners", "activity", "closed", "handleCloseEvent", "listener", "isEmpty", "TimelineTransport", "pusher", "subscribed", "subscriptionPending", "subscriptionCancelled", "socketId", "auth", "handleSubscriptionSucceededEvent", "handleSubscriptionCountEvent", "unsubscribe", "subscription_count", "subscriptionCount", "authorize", "assign", "channel_data", "config", "channelAuthorizer", "channelName", "reset", "members", "member", "myID", "subscriptionData", "presence", "count", "me", "memberData", "user_info", "authData", "channelData", "setMyID", "user", "signinDonePromise", "user_data", "handleInternalEvent", "addedMember", "addMember", "removedMember", "removeMember", "onSubscription", "disconnect", "sharedSecret", "handleEncryptedEvent", "handleEvent", "ciphertext", "cipherText", "getDataToEmit", "raw", "usingTLS", "errorCallbacks", "buildErrorCallbacks", "connectionCallbacks", "buildConnectionCallbacks", "handshakeCallbacks", "buildHandshakeCallbacks", "Network", "getNetwork", "netinfo", "retryIn", "sendActivityCheck", "updateStrategy", "runner", "strategy", "updateState", "startConnecting", "setUnavailableTimer", "disconnectInternally", "handshake", "connect", "handshake<PERSON><PERSON><PERSON>", "abortConnecting", "abort", "clearRetryTimer", "clearUnavailableTimer", "abandonConnection", "getStrategy", "round", "retryTimer", "ensureAborted", "unavailableTimer", "stopActivityCheck", "activityTimer", "pong_timed_out", "resetActivity<PERSON>heck", "shouldRetry", "connected", "Infinity", "setConnection", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tls_only", "refused", "backoff", "retry", "newState", "previousState", "newStateDescription", "previous", "current", "channels", "createEncryptedChannel", "errMsg", "createPrivateChannel", "createPresenceChannel", "createChannel", "values", "createChannels", "createConnectionManager", "createTimelineSender", "createHandshake", "createAssistantToTheTransportManager", "livesLeft", "lives", "strategies", "loop", "failFast", "timeout", "timeoutLimit", "minPriority", "tryNextStrategy", "tryStrategy", "forceMinPriority", "isRunning", "callbackBuilder", "runners", "rs", "abort<PERSON><PERSON><PERSON>", "allRunnersFailed", "aborted", "transports", "ttl", "storage", "getLocalStorage", "serializedCache", "getTransportCacheKey", "flushTransportCache", "fetchTransportCache", "cacheSkipCount", "timestamp", "includes", "cached", "latency", "startTimestamp", "pop", "cb", "storeTransportCache", "IfStrategy", "trueBranch", "falseBranch", "FirstConnectedStrategy", "testSupportsStrategy", "baseOptions", "defineTransport", "definedTransports", "defineTransportStrategy", "wsStrategy", "ws_options", "wsHost", "wss_options", "sockjs_options", "timeouts", "ws_manager", "streaming_manager", "ws_transport", "wss_transport", "sockjs_transport", "xhr_streaming_transport", "xdr_streaming_transport", "xhr_polling_transport", "xdr_polling_transport", "ws_loop", "wss_loop", "sockjs_loop", "streaming_loop", "polling_loop", "http_loop", "http_fallback_loop", "getRequest", "xdr", "XDomainRequest", "ontimeout", "onprogress", "onChunk", "abortRequest", "payload", "position", "unloader", "addUnloadListener", "removeUnloadListener", "chunk", "advanceBuffer", "isBufferTooLong", "unreadData", "endOfLinePosition", "State", "autoIncrement", "getUniqueURL", "separator", "randomNumber", "randomInt", "TimelineLevel", "session", "randomString", "parts", "exec", "getLocation", "CONNECTING", "openStream", "sendRaw", "sendHeartbeat", "OPEN", "createSocketRequest", "start", "closeStream", "CLOSED", "onEvent", "onHeartbeat", "hostname", "urlParts", "stream", "getReceiveURL", "onFinished", "reconnect", "unbind_all", "getXHRAPI", "createSocket", "createRequest", "getDefaultStrategy", "Transports", "load", "XMLHttpRequest", "WebSocket", "MozWebSocket", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "initializeOnDocumentBody", "onDocumentBody", "ready", "getAuthorizers", "ajax", "jsonp", "body", "localStorage", "createXMLHttpRequest", "createMicrosoftXHR", "ActiveXObject", "createXDR", "<PERSON><PERSON><PERSON><PERSON>", "withCredentials", "documentProtocol", "getProtocol", "removeEventListener", "detachEvent", "Uint32Array", "events", "sent", "uniqueID", "level", "limit", "shift", "ERROR", "INFO", "DEBUG", "sendfn", "bundle", "lib", "cluster", "features", "failAttempt", "onInitialized", "serializedTransport", "transportClass", "enabledTransports", "disabledTransports", "getAssistant", "deferred", "validateOptions", "params<PERSON>rov<PERSON>", "dynamicParams", "composeChannel<PERSON><PERSON>y", "getHttpHost", "opts", "getWebsocketHost", "shouldUseTLS", "forceTLS", "getEnableStatsConfig", "enableStats", "disableStats", "buildUserAuthenticator", "buildChannelAuthorizer", "customHandler", "channelAuthorizerGenerator", "deprecatedAuthorizerOptions", "ChannelAuthorizerProxy", "authorizer", "buildChannelAuth", "bindWatchlistInternalEvent", "for<PERSON>ach", "watchlistEvent", "resolve", "reject", "promise", "Promise", "res", "rej", "signin_requested", "serverToUserChannel", "_signinDoneResolve", "_onAuthorize", "err", "_cleanup", "_signin", "_newSigninPromiseIfNeeded", "watchlist", "_onSigninSuccess", "userAuthenticator", "_subscribeChannels", "bind_global", "reinstateSubscription", "subscribe", "ensure_subscribed", "done", "setDone", "then", "catch", "isReady", "instances", "app_key", "check<PERSON><PERSON><PERSON><PERSON>", "statsHost", "timelineParams", "getConfig", "global_emitter", "sessionID", "getClientFeatures", "timelineSender", "subscribeAll", "isUsingTLS", "internal", "find", "all", "timelineSenderTimer", "event_name", "channel_name", "cancelSubscription", "signin", "Runtime", "setup"], "mappings": ";;;;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAgB,OAAID,IAEpBD,EAAa,OAAIC,IARnB,CASGK,QAAQ,WACX,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,G,gaCxErD,IAOA,aAGI,WAAoBC,QAAA,IAAAA,MAAA,UAAAA,oBAwLxB,OAtLI,YAAAC,cAAA,SAAcC,GACV,OAAKC,KAAKH,mBAGFE,EAAS,GAAK,EAAI,EAAI,GAFT,EAATA,EAAa,GAAK,EAAI,GAKtC,YAAAE,OAAA,SAAOC,GAIH,IAHA,IAAIC,EAAM,GAENtC,EAAI,EACDA,EAAIqC,EAAKH,OAAS,EAAGlC,GAAK,EAAG,CAChC,IAAIK,EAAKgC,EAAKrC,IAAM,GAAOqC,EAAKrC,EAAI,IAAM,EAAMqC,EAAKrC,EAAI,GACzDsC,GAAOH,KAAKI,YAAalC,IAAM,GAAS,IACxCiC,GAAOH,KAAKI,YAAalC,IAAM,GAAS,IACxCiC,GAAOH,KAAKI,YAAalC,IAAM,EAAS,IACxCiC,GAAOH,KAAKI,YAAalC,IAAM,EAAS,IAG5C,IAAMmC,EAAOH,EAAKH,OAASlC,EAC3B,GAAIwC,EAAO,EAAG,CACNnC,EAAKgC,EAAKrC,IAAM,IAAgB,IAATwC,EAAaH,EAAKrC,EAAI,IAAM,EAAI,GAC3DsC,GAAOH,KAAKI,YAAalC,IAAM,GAAS,IACxCiC,GAAOH,KAAKI,YAAalC,IAAM,GAAS,IAEpCiC,GADS,IAATE,EACOL,KAAKI,YAAalC,IAAM,EAAS,IAEjC8B,KAAKH,mBAAqB,GAErCM,GAAOH,KAAKH,mBAAqB,GAGrC,OAAOM,GAGX,YAAAG,iBAAA,SAAiBP,GACb,OAAKC,KAAKH,kBAGHE,EAAS,EAAI,EAAI,GAFH,EAATA,EAAa,GAAK,EAAI,GAKtC,YAAAQ,cAAA,SAAcX,GACV,OAAOI,KAAKM,iBAAiBV,EAAEG,OAASC,KAAKQ,kBAAkBZ,KAGnE,YAAAa,OAAA,SAAOb,GACH,GAAiB,IAAbA,EAAEG,OACF,OAAO,IAAIW,WAAW,GAS1B,IAPA,IAAMC,EAAgBX,KAAKQ,kBAAkBZ,GACvCG,EAASH,EAAEG,OAASY,EACpBR,EAAM,IAAIO,WAAWV,KAAKM,iBAAiBP,IAC7Ca,EAAK,EACL/C,EAAI,EACJgD,EAAU,EACVC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAC1BpD,EAAIkC,EAAS,EAAGlC,GAAK,EACxBiD,EAAKd,KAAKkB,YAAYtB,EAAEuB,WAAWtD,EAAI,IACvCkD,EAAKf,KAAKkB,YAAYtB,EAAEuB,WAAWtD,EAAI,IACvCmD,EAAKhB,KAAKkB,YAAYtB,EAAEuB,WAAWtD,EAAI,IACvCoD,EAAKjB,KAAKkB,YAAYtB,EAAEuB,WAAWtD,EAAI,IACvCsC,EAAIS,KAASE,GAAM,EAAMC,IAAO,EAChCZ,EAAIS,KAASG,GAAM,EAAMC,IAAO,EAChCb,EAAIS,KAASI,GAAM,EAAKC,EACxBJ,GA7ES,IA6EEC,EACXD,GA9ES,IA8EEE,EACXF,GA/ES,IA+EEG,EACXH,GAhFS,IAgFEI,EAmBf,GAjBIpD,EAAIkC,EAAS,IACbe,EAAKd,KAAKkB,YAAYtB,EAAEuB,WAAWtD,IACnCkD,EAAKf,KAAKkB,YAAYtB,EAAEuB,WAAWtD,EAAI,IACvCsC,EAAIS,KAASE,GAAM,EAAMC,IAAO,EAChCF,GAtFS,IAsFEC,EACXD,GAvFS,IAuFEE,GAEXlD,EAAIkC,EAAS,IACbiB,EAAKhB,KAAKkB,YAAYtB,EAAEuB,WAAWtD,EAAI,IACvCsC,EAAIS,KAASG,GAAM,EAAMC,IAAO,EAChCH,GA5FS,IA4FEG,GAEXnD,EAAIkC,EAAS,IACbkB,EAAKjB,KAAKkB,YAAYtB,EAAEuB,WAAWtD,EAAI,IACvCsC,EAAIS,KAASI,GAAM,EAAKC,EACxBJ,GAjGS,IAiGEI,GAEC,IAAZJ,EACA,MAAM,IAAIO,MAAM,kDAEpB,OAAOjB,GAYD,YAAAC,YAAV,SAAsBiB,GAqBlB,IAAIC,EAASD,EAYb,OAVAC,GAAU,GAEVA,GAAY,GAAKD,IAAO,EAAK,EAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,EAAK,EAEtBE,OAAOC,aAAaF,IAKrB,YAAAJ,YAAV,SAAsBhD,GAUlB,IAAIoD,EAlKS,IA+Kb,OAVAA,IAAa,GAAKpD,EAAMA,EAAI,MAAS,GArKxB,IAqK8CA,EAAI,GAAK,GAEpEoD,IAAa,GAAKpD,EAAMA,EAAI,MAAS,GAvKxB,IAuK8CA,EAAI,GAAK,GAEpEoD,IAAa,GAAKpD,EAAMA,EAAI,MAAS,GAzKxB,IAyK8CA,EAAI,GAAK,GAEpEoD,IAAa,GAAKpD,EAAMA,EAAI,MAAS,GA3KxB,IA2K8CA,EAAI,GAAK,EAEpEoD,IAAa,GAAKpD,EAAMA,EAAI,OAAU,GA7KzB,IA6K+CA,EAAI,GAAK,IAKjE,YAAAsC,kBAAR,SAA0BZ,GACtB,IAAIe,EAAgB,EACpB,GAAIX,KAAKH,kBAAmB,CACxB,IAAK,IAAIhC,EAAI+B,EAAEG,OAAS,EAAGlC,GAAK,GACxB+B,EAAE/B,KAAOmC,KAAKH,kBADahC,IAI/B8C,IAEJ,GAAIf,EAAEG,OAAS,GAAKY,EAAgB,EAChC,MAAM,IAAIS,MAAM,kCAGxB,OAAOT,GAGf,EA3LA,GAAa,EAAAc,QA6Lb,IAAMC,EAAW,IAAID,EAErB,kBAAuBvB,GACnB,OAAOwB,EAASzB,OAAOC,IAG3B,kBAAuBN,GACnB,OAAO8B,EAASjB,OAAOb,IAS3B,+B,+CAwCA,OAxCkC,OAQpB,YAAAQ,YAAV,SAAsBiB,GAClB,IAAIC,EAASD,EAYb,OAVAC,GAAU,GAEVA,GAAY,GAAKD,IAAO,EAAK,EAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,EAAK,GAEtBE,OAAOC,aAAaF,IAGrB,YAAAJ,YAAV,SAAsBhD,GAClB,IAAIoD,EA7OS,IA0Pb,OAVAA,IAAa,GAAKpD,EAAMA,EAAI,MAAS,GAhPxB,IAgP8CA,EAAI,GAAK,GAEpEoD,IAAa,GAAKpD,EAAMA,EAAI,MAAS,GAlPxB,IAkP8CA,EAAI,GAAK,GAEpEoD,IAAa,GAAKpD,EAAMA,EAAI,MAAS,GApPxB,IAoP8CA,EAAI,GAAK,GAEpEoD,IAAa,GAAKpD,EAAMA,EAAI,MAAS,GAtPxB,IAsP8CA,EAAI,GAAK,EAEpEoD,IAAa,GAAKpD,EAAMA,EAAI,OAAU,GAxPzB,IAwP+CA,EAAI,GAAK,IAI7E,EAxCA,CAAkCuD,GAArB,EAAAE,eA0Cb,IAAMC,EAAe,IAAID,EAEzB,yBAA8BzB,GAC1B,OAAO0B,EAAa3B,OAAOC,IAG/B,yBAA8BN,GAC1B,OAAOgC,EAAanB,OAAOb,IAIlB,EAAAE,cAAgB,SAACC,GAC1B,OAAA2B,EAAS5B,cAAcC,IAEd,EAAAO,iBAAmB,SAACP,GAC7B,OAAA2B,EAASpB,iBAAiBP,IAEjB,EAAAQ,cAAgB,SAACX,GAC1B,OAAA8B,EAASnB,cAAcX,K,8ECnR3B,IACMiC,EAAe,gCA2CrB,SAAgB/B,EAAcF,GAE1B,IADA,IAAI0B,EAAS,EACJzD,EAAI,EAAGA,EAAI+B,EAAEG,OAAQlC,IAAK,CAC/B,IAAMK,EAAI0B,EAAEuB,WAAWtD,GACvB,GAAIK,EAAI,IACJoD,GAAU,OACP,GAAIpD,EAAI,KACXoD,GAAU,OACP,GAAIpD,EAAI,MACXoD,GAAU,MACP,MAAIpD,GAAK,OAOZ,MAAM,IAAIkD,MA7DA,wBAuDV,GAAIvD,GAAK+B,EAAEG,OAAS,EAChB,MAAM,IAAIqB,MAxDJ,wBA0DVvD,IACAyD,GAAU,GAKlB,OAAOA,EAzDX,kBAAuB1B,GAOnB,IAHA,IAAMkC,EAAM,IAAIpB,WAAWZ,EAAcF,IAErCmC,EAAM,EACDlE,EAAI,EAAGA,EAAI+B,EAAEG,OAAQlC,IAAK,CAC/B,IAAIK,EAAI0B,EAAEuB,WAAWtD,GACjBK,EAAI,IACJ4D,EAAIC,KAAS7D,EACNA,EAAI,MACX4D,EAAIC,KAAS,IAAO7D,GAAK,EACzB4D,EAAIC,KAAS,IAAW,GAAJ7D,GACbA,EAAI,OACX4D,EAAIC,KAAS,IAAO7D,GAAK,GACzB4D,EAAIC,KAAS,IAAQ7D,GAAK,EAAK,GAC/B4D,EAAIC,KAAS,IAAW,GAAJ7D,IAEpBL,IACAK,GAAS,KAAJA,IAAc,GACnBA,GAAuB,KAAlB0B,EAAEuB,WAAWtD,GAClBK,GAAK,MAEL4D,EAAIC,KAAS,IAAO7D,GAAK,GACzB4D,EAAIC,KAAS,IAAQ7D,GAAK,GAAM,GAChC4D,EAAIC,KAAS,IAAQ7D,GAAK,EAAK,GAC/B4D,EAAIC,KAAS,IAAW,GAAJ7D,GAG5B,OAAO4D,GAOX,kBA2BA,kBAAuBA,GAEnB,IADA,IAAME,EAAkB,GACfnE,EAAI,EAAGA,EAAIiE,EAAI/B,OAAQlC,IAAK,CACjC,IAAIwD,EAAIS,EAAIjE,GAEZ,GAAQ,IAAJwD,EAAU,CACV,IAAIY,OAAG,EACP,GAAIZ,EAAI,IAAM,CAEV,GAAIxD,GAAKiE,EAAI/B,OACT,MAAM,IAAIqB,MAAMS,GAGpB,GAAoB,MAAV,KADJK,EAAKJ,IAAMjE,KAEb,MAAM,IAAIuD,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,EAAU,GAALa,EACvBD,EAAM,SACH,GAAIZ,EAAI,IAAM,CAEjB,GAAIxD,GAAKiE,EAAI/B,OAAS,EAClB,MAAM,IAAIqB,MAAMS,GAEpB,IAAMK,EAAKJ,IAAMjE,GACXsE,EAAKL,IAAMjE,GACjB,GAAoB,MAAV,IAALqE,IAAuC,MAAV,IAALC,GACzB,MAAM,IAAIf,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,IAAW,GAALa,IAAc,EAAU,GAALC,EAC3CF,EAAM,SACH,MAAIZ,EAAI,KAcX,MAAM,IAAID,MAAMS,GAZhB,GAAIhE,GAAKiE,EAAI/B,OAAS,EAClB,MAAM,IAAIqB,MAAMS,GAEdK,EAAKJ,IAAMjE,GACXsE,EAAKL,IAAMjE,GADjB,IAEMuE,EAAKN,IAAMjE,GACjB,GAAoB,MAAV,IAALqE,IAAuC,MAAV,IAALC,IAAuC,MAAV,IAALC,GACjD,MAAM,IAAIhB,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,IAAW,GAALa,IAAc,IAAW,GAALC,IAAc,EAAU,GAALC,EAC/DH,EAAM,MAKV,GAAIZ,EAAIY,GAAQZ,GAAK,OAAUA,GAAK,MAChC,MAAM,IAAID,MAAMS,GAGpB,GAAIR,GAAK,MAAS,CAEd,GAAIA,EAAI,QACJ,MAAM,IAAID,MAAMS,GAEpBR,GAAK,MACLW,EAAMK,KAAKd,OAAOC,aAAa,MAAUH,GAAK,KAC9CA,EAAI,MAAc,KAAJA,GAItBW,EAAMK,KAAKd,OAAOC,aAAaH,IAEnC,OAAOW,EAAMM,KAAK,M,iBC9ItB,SAAUC,GACV,aAQA,IAAIC,EAAK,SAASC,GAChB,IAAI5E,EAAGc,EAAI,IAAI+D,aAAa,IAC5B,GAAID,EAAM,IAAK5E,EAAI,EAAGA,EAAI4E,EAAK1C,OAAQlC,IAAKc,EAAEd,GAAK4E,EAAK5E,GACxD,OAAOc,GAILgE,EAAc,WAAuB,MAAM,IAAIvB,MAAM,YAErDwB,EAAK,IAAIlC,WAAW,IACpBmC,EAAK,IAAInC,WAAW,IAAKmC,EAAG,GAAK,EAErC,IAAIC,EAAMN,IACNO,EAAMP,EAAG,CAAC,IACVQ,EAAUR,EAAG,CAAC,MAAQ,IACtBS,EAAIT,EAAG,CAAC,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,MAAQ,KAAQ,IAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,QAChIU,EAAKV,EAAG,CAAC,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,MAAQ,KAAQ,IAAQ,MAAQ,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,OACjIW,EAAIX,EAAG,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,OAChIY,EAAIZ,EAAG,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,QAChIa,EAAIb,EAAG,CAAC,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,IAAQ,MAAQ,MAAQ,MAAQ,KAAQ,QAEpI,SAASc,EAAKC,EAAG1F,EAAG2F,EAAG1F,GACrByF,EAAE1F,GAAQ2F,GAAK,GAAM,IACrBD,EAAE1F,EAAE,GAAM2F,GAAK,GAAM,IACrBD,EAAE1F,EAAE,GAAM2F,GAAM,EAAK,IACrBD,EAAE1F,EAAE,GAAS,IAAJ2F,EACTD,EAAE1F,EAAE,GAAMC,GAAK,GAAO,IACtByF,EAAE1F,EAAE,GAAMC,GAAK,GAAO,IACtByF,EAAE1F,EAAE,GAAMC,GAAM,EAAM,IACtByF,EAAE1F,EAAE,GAAS,IAAJC,EAGX,SAAS2F,EAAGF,EAAGG,EAAIC,EAAGC,EAAItE,GACxB,IAAIzB,EAAEM,EAAI,EACV,IAAKN,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKM,GAAKoF,EAAEG,EAAG7F,GAAG8F,EAAEC,EAAG/F,GAC1C,OAAQ,EAAMM,EAAI,IAAO,GAAM,EAGjC,SAAS0F,EAAiBN,EAAGG,EAAIC,EAAGC,GAClC,OAAOH,EAAGF,EAAEG,EAAGC,EAAEC,EAAG,IAGtB,SAASE,EAAiBP,EAAGG,EAAIC,EAAGC,GAClC,OAAOH,EAAGF,EAAEG,EAAGC,EAAEC,EAAG,IA6UtB,SAASG,EAAoB5D,EAAI6D,EAAIC,EAAE/F,IA1UvC,SAAsBI,EAAGqB,EAAGsE,EAAG/F,GAsB7B,IArBA,IAmBegG,EAnBXC,EAAc,IAARjG,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EkG,EAAc,IAARH,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EI,EAAc,IAARJ,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EK,EAAc,IAARL,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EM,EAAc,IAARN,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EO,EAAc,IAARtG,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EuG,EAAc,IAAR9E,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9E+E,EAAc,IAAR/E,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EgF,EAAc,IAARhF,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EiF,EAAc,IAARjF,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EkF,EAAc,IAAR3G,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9E4G,EAAc,IAARb,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9Ec,EAAc,IAARd,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9Ee,EAAc,IAARf,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EgB,EAAc,IAARhB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EiB,EAAc,IAARhH,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAE9EiH,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EACpEiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EACpEiB,EAAMhB,EAEDrH,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAQ3BsH,IADAjB,GADA6B,IADA7B,GADAyB,IADAzB,GADAqB,IADArB,EAAIiB,EAAKY,EAAM,IACN,EAAI7B,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRqB,EAAK,IACJ,GAAKrB,IAAI,IACTyB,EAAK,IACN,GAAKzB,IAAI,GASlBsB,IADAtB,GADAkB,IADAlB,GADA8B,IADA9B,GADA0B,IADA1B,EAAIsB,EAAKJ,EAAK,IACL,EAAIlB,IAAI,IACRsB,EAAK,IACJ,EAAItB,IAAI,IACR0B,EAAK,IACN,GAAK1B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GASlB2B,IADA3B,GADAuB,IADAvB,GADAmB,IADAnB,GADA+B,IADA/B,EAAI2B,EAAMJ,EAAK,IACL,EAAIvB,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR+B,EAAM,IACN,GAAK/B,IAAI,IACTmB,EAAK,IACJ,GAAKnB,IAAI,GASnBgC,IADAhC,GADA4B,IADA5B,GADAwB,IADAxB,GADAoB,IADApB,EAAIgC,EAAMJ,EAAM,IACP,EAAI5B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACRoB,EAAK,IACJ,GAAKpB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASnBiB,IADAjB,GADAoB,IADApB,GADAmB,IADAnB,GADAkB,IADAlB,EAAIiB,EAAKG,EAAK,IACL,EAAIpB,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRkB,EAAK,IACL,GAAKlB,IAAI,IACTmB,EAAK,IACL,GAAKnB,IAAI,GASlBsB,IADAtB,GADAqB,IADArB,GADAwB,IADAxB,GADAuB,IADAvB,EAAIsB,EAAKD,EAAK,IACL,EAAIrB,IAAI,IACRsB,EAAK,IACL,EAAItB,IAAI,IACRuB,EAAK,IACL,GAAKvB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASlB2B,IADA3B,GADA0B,IADA1B,GADAyB,IADAzB,GADA4B,IADA5B,EAAI2B,EAAMD,EAAK,IACL,EAAI1B,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR4B,EAAM,IACN,GAAK5B,IAAI,IACTyB,EAAK,IACJ,GAAKzB,IAAI,GASnBgC,IADAhC,GADA+B,IADA/B,GADA8B,IADA9B,GADA6B,IADA7B,EAAIgC,EAAMD,EAAM,IACN,EAAI/B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACR6B,EAAM,IACN,GAAK7B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GAEpBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAElB5G,EAAG,GAAK6G,IAAQ,EAAI,IACpB7G,EAAG,GAAK6G,IAAQ,EAAI,IACpB7G,EAAG,GAAK6G,IAAO,GAAK,IACpB7G,EAAG,GAAK6G,IAAO,GAAK,IAEpB7G,EAAG,GAAK8G,IAAQ,EAAI,IACpB9G,EAAG,GAAK8G,IAAQ,EAAI,IACpB9G,EAAG,GAAK8G,IAAO,GAAK,IACpB9G,EAAG,GAAK8G,IAAO,GAAK,IAEpB9G,EAAG,GAAK+G,IAAQ,EAAI,IACpB/G,EAAG,GAAK+G,IAAQ,EAAI,IACpB/G,EAAE,IAAM+G,IAAO,GAAK,IACpB/G,EAAE,IAAM+G,IAAO,GAAK,IAEpB/G,EAAE,IAAMgH,IAAQ,EAAI,IACpBhH,EAAE,IAAMgH,IAAQ,EAAI,IACpBhH,EAAE,IAAMgH,IAAO,GAAK,IACpBhH,EAAE,IAAMgH,IAAO,GAAK,IAEpBhH,EAAE,IAAMiH,IAAQ,EAAI,IACpBjH,EAAE,IAAMiH,IAAQ,EAAI,IACpBjH,EAAE,IAAMiH,IAAO,GAAK,IACpBjH,EAAE,IAAMiH,IAAO,GAAK,IAEpBjH,EAAE,IAAMkH,IAAQ,EAAI,IACpBlH,EAAE,IAAMkH,IAAQ,EAAI,IACpBlH,EAAE,IAAMkH,IAAO,GAAK,IACpBlH,EAAE,IAAMkH,IAAO,GAAK,IAEpBlH,EAAE,IAAMmH,IAAQ,EAAI,IACpBnH,EAAE,IAAMmH,IAAQ,EAAI,IACpBnH,EAAE,IAAMmH,IAAO,GAAK,IACpBnH,EAAE,IAAMmH,IAAO,GAAK,IAEpBnH,EAAE,IAAMoH,IAAQ,EAAI,IACpBpH,EAAE,IAAMoH,IAAQ,EAAI,IACpBpH,EAAE,IAAMoH,IAAO,GAAK,IACpBpH,EAAE,IAAMoH,IAAO,GAAK,IAEpBpH,EAAE,IAAMqH,IAAQ,EAAI,IACpBrH,EAAE,IAAMqH,IAAQ,EAAI,IACpBrH,EAAE,IAAMqH,IAAO,GAAK,IACpBrH,EAAE,IAAMqH,IAAO,GAAK,IAEpBrH,EAAE,IAAMsH,IAAQ,EAAI,IACpBtH,EAAE,IAAMsH,IAAQ,EAAI,IACpBtH,EAAE,IAAMsH,IAAO,GAAK,IACpBtH,EAAE,IAAMsH,IAAO,GAAK,IAEpBtH,EAAE,IAAMuH,IAAS,EAAI,IACrBvH,EAAE,IAAMuH,IAAS,EAAI,IACrBvH,EAAE,IAAMuH,IAAQ,GAAK,IACrBvH,EAAE,IAAMuH,IAAQ,GAAK,IAErBvH,EAAE,IAAMwH,IAAS,EAAI,IACrBxH,EAAE,IAAMwH,IAAS,EAAI,IACrBxH,EAAE,IAAMwH,IAAQ,GAAK,IACrBxH,EAAE,IAAMwH,IAAQ,GAAK,IAErBxH,EAAE,IAAMyH,IAAS,EAAI,IACrBzH,EAAE,IAAMyH,IAAS,EAAI,IACrBzH,EAAE,IAAMyH,IAAQ,GAAK,IACrBzH,EAAE,IAAMyH,IAAQ,GAAK,IAErBzH,EAAE,IAAM0H,IAAS,EAAI,IACrB1H,EAAE,IAAM0H,IAAS,EAAI,IACrB1H,EAAE,IAAM0H,IAAQ,GAAK,IACrB1H,EAAE,IAAM0H,IAAQ,GAAK,IAErB1H,EAAE,IAAM2H,IAAS,EAAI,IACrB3H,EAAE,IAAM2H,IAAS,EAAI,IACrB3H,EAAE,IAAM2H,IAAQ,GAAK,IACrB3H,EAAE,IAAM2H,IAAQ,GAAK,IAErB3H,EAAE,IAAM4H,IAAS,EAAI,IACrB5H,EAAE,IAAM4H,IAAS,EAAI,IACrB5H,EAAE,IAAM4H,IAAQ,GAAK,IACrB5H,EAAE,IAAM4H,IAAQ,GAAK,IA6IrBC,CAAahG,EAAI6D,EAAIC,EAAE/F,GAGzB,SAASkI,EAAqBjG,EAAI6D,EAAIC,EAAE/F,IA7IxC,SAAuBI,EAAEqB,EAAEsE,EAAE/F,GAsB3B,IArBA,IAmBegG,EAFXiB,EAjBc,IAARjH,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAiBrEkH,EAhBK,IAARnB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAgB5DoB,EAfJ,IAARpB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAenDqB,EAdb,IAARrB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAc1CsB,EAbtB,IAARtB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAajCuB,EAZ/B,IAARtH,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAYxBuH,EAXxC,IAAR9F,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAWf+F,EAVjD,IAAR/F,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAW9EgG,EAVc,IAARhG,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAUrEiG,EATK,IAARjG,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAS5DkG,EARJ,IAAR3H,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAQjD4H,EAPf,IAAR7B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAOtC8B,EAN1B,IAAR9B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAM3B+B,EALrC,IAAR/B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAKhBgC,EAJhD,IAARhC,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAK9EiC,EAJc,IAARhI,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAMzEL,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAQ3BsH,IADAjB,GADA6B,IADA7B,GADAyB,IADAzB,GADAqB,IADArB,EAAIiB,EAAKY,EAAM,IACN,EAAI7B,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRqB,EAAK,IACJ,GAAKrB,IAAI,IACTyB,EAAK,IACN,GAAKzB,IAAI,GASlBsB,IADAtB,GADAkB,IADAlB,GADA8B,IADA9B,GADA0B,IADA1B,EAAIsB,EAAKJ,EAAK,IACL,EAAIlB,IAAI,IACRsB,EAAK,IACJ,EAAItB,IAAI,IACR0B,EAAK,IACN,GAAK1B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GASlB2B,IADA3B,GADAuB,IADAvB,GADAmB,IADAnB,GADA+B,IADA/B,EAAI2B,EAAMJ,EAAK,IACL,EAAIvB,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR+B,EAAM,IACN,GAAK/B,IAAI,IACTmB,EAAK,IACJ,GAAKnB,IAAI,GASnBgC,IADAhC,GADA4B,IADA5B,GADAwB,IADAxB,GADAoB,IADApB,EAAIgC,EAAMJ,EAAM,IACP,EAAI5B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACRoB,EAAK,IACJ,GAAKpB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASnBiB,IADAjB,GADAoB,IADApB,GADAmB,IADAnB,GADAkB,IADAlB,EAAIiB,EAAKG,EAAK,IACL,EAAIpB,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRkB,EAAK,IACL,GAAKlB,IAAI,IACTmB,EAAK,IACL,GAAKnB,IAAI,GASlBsB,IADAtB,GADAqB,IADArB,GADAwB,IADAxB,GADAuB,IADAvB,EAAIsB,EAAKD,EAAK,IACL,EAAIrB,IAAI,IACRsB,EAAK,IACL,EAAItB,IAAI,IACRuB,EAAK,IACL,GAAKvB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASlB2B,IADA3B,GADA0B,IADA1B,GADAyB,IADAzB,GADA4B,IADA5B,EAAI2B,EAAMD,EAAK,IACL,EAAI1B,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR4B,EAAM,IACN,GAAK5B,IAAI,IACTyB,EAAK,IACJ,GAAKzB,IAAI,GASnBgC,IADAhC,GADA+B,IADA/B,GADA8B,IADA9B,GADA6B,IADA7B,EAAIgC,EAAMD,EAAM,IACN,EAAI/B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACR6B,EAAM,IACN,GAAK7B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GAGrB5F,EAAG,GAAK6G,IAAQ,EAAI,IACpB7G,EAAG,GAAK6G,IAAQ,EAAI,IACpB7G,EAAG,GAAK6G,IAAO,GAAK,IACpB7G,EAAG,GAAK6G,IAAO,GAAK,IAEpB7G,EAAG,GAAKkH,IAAQ,EAAI,IACpBlH,EAAG,GAAKkH,IAAQ,EAAI,IACpBlH,EAAG,GAAKkH,IAAO,GAAK,IACpBlH,EAAG,GAAKkH,IAAO,GAAK,IAEpBlH,EAAG,GAAKuH,IAAS,EAAI,IACrBvH,EAAG,GAAKuH,IAAS,EAAI,IACrBvH,EAAE,IAAMuH,IAAQ,GAAK,IACrBvH,EAAE,IAAMuH,IAAQ,GAAK,IAErBvH,EAAE,IAAM4H,IAAS,EAAI,IACrB5H,EAAE,IAAM4H,IAAS,EAAI,IACrB5H,EAAE,IAAM4H,IAAQ,GAAK,IACrB5H,EAAE,IAAM4H,IAAQ,GAAK,IAErB5H,EAAE,IAAMmH,IAAQ,EAAI,IACpBnH,EAAE,IAAMmH,IAAQ,EAAI,IACpBnH,EAAE,IAAMmH,IAAO,GAAK,IACpBnH,EAAE,IAAMmH,IAAO,GAAK,IAEpBnH,EAAE,IAAMoH,IAAQ,EAAI,IACpBpH,EAAE,IAAMoH,IAAQ,EAAI,IACpBpH,EAAE,IAAMoH,IAAO,GAAK,IACpBpH,EAAE,IAAMoH,IAAO,GAAK,IAEpBpH,EAAE,IAAMqH,IAAQ,EAAI,IACpBrH,EAAE,IAAMqH,IAAQ,EAAI,IACpBrH,EAAE,IAAMqH,IAAO,GAAK,IACpBrH,EAAE,IAAMqH,IAAO,GAAK,IAEpBrH,EAAE,IAAMsH,IAAQ,EAAI,IACpBtH,EAAE,IAAMsH,IAAQ,EAAI,IACpBtH,EAAE,IAAMsH,IAAO,GAAK,IACpBtH,EAAE,IAAMsH,IAAO,GAAK,IAQpBS,CAAclG,EAAI6D,EAAIC,EAAE/F,GAG1B,IAAIoI,EAAQ,IAAI5F,WAAW,CAAC,IAAK,IAAK,IAAK,GAAI,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAI,MAGhG,SAAS6F,EAA0BrI,EAAEsI,EAAKvI,EAAEwI,EAAKpF,EAAE/B,EAAE2E,GACnD,IACIC,EAAGrG,EADH6I,EAAI,IAAIhG,WAAW,IAAK6C,EAAI,IAAI7C,WAAW,IAE/C,IAAK7C,EAAI,EAAGA,EAAI,GAAIA,IAAK6I,EAAE7I,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAAK6I,EAAE7I,GAAKyB,EAAEzB,GACjC,KAAOwD,GAAK,IAAI,CAEd,IADA0C,EAAoBR,EAAEmD,EAAEzC,EAAEqC,GACrBzI,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEsI,EAAK3I,GAAKI,EAAEwI,EAAK5I,GAAK0F,EAAE1F,GAEnD,IADAqG,EAAI,EACCrG,EAAI,EAAGA,EAAI,GAAIA,IAClBqG,EAAIA,GAAY,IAAPwC,EAAE7I,IAAa,EACxB6I,EAAE7I,GAAS,IAAJqG,EACPA,KAAO,EAET7C,GAAK,GACLmF,GAAQ,GACRC,GAAQ,GAEV,GAAIpF,EAAI,EAEN,IADA0C,EAAoBR,EAAEmD,EAAEzC,EAAEqC,GACrBzI,EAAI,EAAGA,EAAIwD,EAAGxD,IAAKK,EAAEsI,EAAK3I,GAAKI,EAAEwI,EAAK5I,GAAK0F,EAAE1F,GAEpD,OAAO,EAGT,SAAS8I,EAAsBzI,EAAEsI,EAAKnF,EAAE/B,EAAE2E,GACxC,IACIC,EAAGrG,EADH6I,EAAI,IAAIhG,WAAW,IAAK6C,EAAI,IAAI7C,WAAW,IAE/C,IAAK7C,EAAI,EAAGA,EAAI,GAAIA,IAAK6I,EAAE7I,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAAK6I,EAAE7I,GAAKyB,EAAEzB,GACjC,KAAOwD,GAAK,IAAI,CAEd,IADA0C,EAAoBR,EAAEmD,EAAEzC,EAAEqC,GACrBzI,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEsI,EAAK3I,GAAK0F,EAAE1F,GAEvC,IADAqG,EAAI,EACCrG,EAAI,EAAGA,EAAI,GAAIA,IAClBqG,EAAIA,GAAY,IAAPwC,EAAE7I,IAAa,EACxB6I,EAAE7I,GAAS,IAAJqG,EACPA,KAAO,EAET7C,GAAK,GACLmF,GAAQ,GAEV,GAAInF,EAAI,EAEN,IADA0C,EAAoBR,EAAEmD,EAAEzC,EAAEqC,GACrBzI,EAAI,EAAGA,EAAIwD,EAAGxD,IAAKK,EAAEsI,EAAK3I,GAAK0F,EAAE1F,GAExC,OAAO,EAGT,SAAS+I,EAAc1I,EAAEsI,EAAKrI,EAAEmB,EAAE2E,GAChC,IAAIrE,EAAI,IAAIc,WAAW,IACvB0F,EAAqBxG,EAAEN,EAAE2E,EAAEqC,GAE3B,IADA,IAAIO,EAAK,IAAInG,WAAW,GACf7C,EAAI,EAAGA,EAAI,EAAGA,IAAKgJ,EAAGhJ,GAAKyB,EAAEzB,EAAE,IACxC,OAAO8I,EAAsBzI,EAAEsI,EAAKrI,EAAE0I,EAAGjH,GAG3C,SAASkH,EAAkB5I,EAAEsI,EAAKvI,EAAEwI,EAAKtI,EAAEmB,EAAE2E,GAC3C,IAAIrE,EAAI,IAAIc,WAAW,IACvB0F,EAAqBxG,EAAEN,EAAE2E,EAAEqC,GAE3B,IADA,IAAIO,EAAK,IAAInG,WAAW,GACf7C,EAAI,EAAGA,EAAI,EAAGA,IAAKgJ,EAAGhJ,GAAKyB,EAAEzB,EAAE,IACxC,OAAO0I,EAA0BrI,EAAEsI,EAAKvI,EAAEwI,EAAKtI,EAAE0I,EAAGjH,GAQtD,IAAImH,EAAW,SAAS3H,GAQtB,IAAI4H,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAPhCvH,KAAKwH,OAAS,IAAI9G,WAAW,IAC7BV,KAAKrB,EAAI,IAAI8I,YAAY,IACzBzH,KAAKwD,EAAI,IAAIiE,YAAY,IACzBzH,KAAK0H,IAAM,IAAID,YAAY,GAC3BzH,KAAK2H,SAAW,EAChB3H,KAAK4H,IAAM,EAIXZ,EAAe,IAAV5H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGY,KAAKrB,EAAE,GAAkC,KAA7B,EACzDsI,EAAe,IAAV7H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGY,KAAKrB,EAAE,GAAkC,MAA3BqI,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAV9H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGY,KAAKrB,EAAE,GAAkC,MAA3BsI,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAV/H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGY,KAAKrB,EAAE,GAAkC,MAA3BuI,IAAQ,EAAMC,GAAO,GAChFC,EAAe,IAAVhI,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGY,KAAKrB,EAAE,GAAkC,KAA3BwI,IAAQ,EAAMC,GAAM,IAC/EpH,KAAKrB,EAAE,GAAOyI,IAAQ,EAAM,KAC5BC,EAAe,IAAVjI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGY,KAAKrB,EAAE,GAAkC,MAA3ByI,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVlI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGY,KAAKrB,EAAE,GAAkC,MAA3B0I,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVnI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGY,KAAKrB,EAAE,GAAkC,MAA3B2I,IAAQ,EAAMC,GAAO,GAChFvH,KAAKrB,EAAE,GAAO4I,IAAQ,EAAM,IAE5BvH,KAAK0H,IAAI,GAAe,IAAVtI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDY,KAAK0H,IAAI,GAAe,IAAVtI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDY,KAAK0H,IAAI,GAAe,IAAVtI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDY,KAAK0H,IAAI,GAAe,IAAVtI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDY,KAAK0H,IAAI,GAAe,IAAVtI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDY,KAAK0H,IAAI,GAAe,IAAVtI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDY,KAAK0H,IAAI,GAAe,IAAVtI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDY,KAAK0H,IAAI,GAAe,IAAVtI,EAAI,KAAwB,IAAVA,EAAI,MAAe,GAoUrD,SAASyI,EAAmB1H,EAAK2H,EAAQ7J,EAAGwI,EAAMnH,EAAG2E,GACnD,IAAIrE,EAAI,IAAImH,EAAS9C,GAGrB,OAFArE,EAAEmI,OAAO9J,EAAGwI,EAAMnH,GAClBM,EAAEoI,OAAO7H,EAAK2H,GACP,EAGT,SAASG,EAA0BzE,EAAG0E,EAAMjK,EAAGwI,EAAMnH,EAAG2E,GACtD,IAAIV,EAAI,IAAI7C,WAAW,IAEvB,OADAmH,EAAmBtE,EAAE,EAAEtF,EAAEwI,EAAKnH,EAAE2E,GACzBJ,EAAiBL,EAAE0E,EAAK3E,EAAE,GAGnC,SAAS4E,EAAiBjK,EAAED,EAAEE,EAAEmB,EAAE2E,GAChC,IAAIpG,EACJ,GAAIM,EAAI,GAAI,OAAQ,EAGpB,IAFA2I,EAAkB5I,EAAE,EAAED,EAAE,EAAEE,EAAEmB,EAAE2E,GAC9B4D,EAAmB3J,EAAG,GAAIA,EAAG,GAAIC,EAAI,GAAID,GACpCL,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEL,GAAK,EAChC,OAAO,EAGT,SAASuK,EAAsBnK,EAAEC,EAAEC,EAAEmB,EAAE2E,GACrC,IAAIpG,EACA0F,EAAI,IAAI7C,WAAW,IACvB,GAAIvC,EAAI,GAAI,OAAQ,EAEpB,GADAyI,EAAcrD,EAAE,EAAE,GAAGjE,EAAE2E,GACiC,IAApDgE,EAA0B/J,EAAG,GAAGA,EAAG,GAAGC,EAAI,GAAGoF,GAAU,OAAQ,EAEnE,IADAuD,EAAkB7I,EAAE,EAAEC,EAAE,EAAEC,EAAEmB,EAAE2E,GACzBpG,EAAI,EAAGA,EAAI,GAAIA,IAAKI,EAAEJ,GAAK,EAChC,OAAO,EAGT,SAASwK,EAAS1J,EAAG2J,GACnB,IAAIzK,EACJ,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKc,EAAEd,GAAU,EAALyK,EAAEzK,GAGpC,SAAS0K,EAASjK,GAChB,IAAIT,EAAG2K,EAAGtK,EAAI,EACd,IAAKL,EAAI,EAAGA,EAAI,GAAIA,IAClB2K,EAAIlK,EAAET,GAAKK,EAAI,MACfA,EAAIuK,KAAKC,MAAMF,EAAI,OACnBlK,EAAET,GAAK2K,EAAQ,MAAJtK,EAEbI,EAAE,IAAMJ,EAAE,EAAI,IAAMA,EAAE,GAGxB,SAASyK,EAAShJ,EAAGiJ,EAAGvH,GAEtB,IADA,IAAItC,EAAGb,IAAMmD,EAAE,GACNxD,EAAI,EAAGA,EAAI,GAAIA,IACtBkB,EAAIb,GAAKyB,EAAE9B,GAAK+K,EAAE/K,IAClB8B,EAAE9B,IAAMkB,EACR6J,EAAE/K,IAAMkB,EAIZ,SAAS8J,EAAUvK,EAAGgB,GACpB,IAAIzB,EAAGiL,EAAGzH,EACNpD,EAAIuE,IAAMzD,EAAIyD,IAClB,IAAK3E,EAAI,EAAGA,EAAI,GAAIA,IAAKkB,EAAElB,GAAKyB,EAAEzB,GAIlC,IAHA0K,EAASxJ,GACTwJ,EAASxJ,GACTwJ,EAASxJ,GACJ+J,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAEtB,IADA7K,EAAE,GAAKc,EAAE,GAAK,MACTlB,EAAI,EAAGA,EAAI,GAAIA,IAClBI,EAAEJ,GAAKkB,EAAElB,GAAK,OAAWI,EAAEJ,EAAE,IAAI,GAAM,GACvCI,EAAEJ,EAAE,IAAM,MAEZI,EAAE,IAAMc,EAAE,IAAM,OAAWd,EAAE,KAAK,GAAM,GACxCoD,EAAKpD,EAAE,KAAK,GAAM,EAClBA,EAAE,KAAO,MACT0K,EAAS5J,EAAGd,EAAG,EAAEoD,GAEnB,IAAKxD,EAAI,EAAGA,EAAI,GAAIA,IAClBS,EAAE,EAAET,GAAY,IAAPkB,EAAElB,GACXS,EAAE,EAAET,EAAE,GAAKkB,EAAElB,IAAI,EAIrB,SAASkL,EAAST,EAAGjH,GACnB,IAAInD,EAAI,IAAIwC,WAAW,IAAKvC,EAAI,IAAIuC,WAAW,IAG/C,OAFAmI,EAAU3K,EAAGoK,GACbO,EAAU1K,EAAGkD,GACNyC,EAAiB5F,EAAG,EAAGC,EAAG,GAGnC,SAAS6K,EAASV,GAChB,IAAInK,EAAI,IAAIuC,WAAW,IAEvB,OADAmI,EAAU1K,EAAGmK,GACC,EAAPnK,EAAE,GAGX,SAAS8K,EAAY3K,EAAGgB,GACtB,IAAIzB,EACJ,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKyB,EAAE,EAAEzB,IAAMyB,EAAE,EAAEzB,EAAE,IAAM,GACtDS,EAAE,KAAO,MAGX,SAAS4K,EAAE5K,EAAGgK,EAAGjH,GACf,IAAK,IAAIxD,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKyK,EAAEzK,GAAKwD,EAAExD,GAG/C,SAASsL,EAAE7K,EAAGgK,EAAGjH,GACf,IAAK,IAAIxD,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKyK,EAAEzK,GAAKwD,EAAExD,GAG/C,SAASuL,EAAE9K,EAAGgK,EAAGjH,GACf,IAAImH,EAAGtK,EACJ8I,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EACpE8B,EAAK,EAAIC,EAAK,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EACrEC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EACrEC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAC5DC,EAAKvJ,EAAE,GACPwJ,EAAKxJ,EAAE,GACPyJ,EAAKzJ,EAAE,GACP0J,EAAK1J,EAAE,GACP2J,EAAK3J,EAAE,GACP4J,EAAK5J,EAAE,GACP6J,EAAK7J,EAAE,GACP8J,EAAK9J,EAAE,GACP+J,EAAK/J,EAAE,GACPgK,EAAKhK,EAAE,GACPiK,EAAMjK,EAAE,IACRkK,EAAMlK,EAAE,IACRmK,EAAMnK,EAAE,IACRoK,EAAMpK,EAAE,IACRqK,EAAMrK,EAAE,IACRsK,EAAMtK,EAAE,IAGV2F,IADAwB,EAAIF,EAAE,IACIsC,EACV3D,GAAMuB,EAAIqC,EACV3D,GAAMsB,EAAIsC,EACV3D,GAAMqB,EAAIuC,EACV3D,GAAMoB,EAAIwC,EACV3D,GAAMmB,EAAIyC,EACV3D,GAAMkB,EAAI0C,EACV3D,GAAMiB,EAAI2C,EACV9B,GAAMb,EAAI4C,EACV9B,GAAMd,EAAI6C,EACV9B,GAAOf,EAAI8C,EACX9B,GAAOhB,EAAI+C,EACX9B,GAAOjB,EAAIgD,EACX9B,GAAOlB,EAAIiD,EACX9B,GAAOnB,EAAIkD,EACX9B,GAAOpB,EAAImD,EAEX1E,IADAuB,EAAIF,EAAE,IACIsC,EACV1D,GAAMsB,EAAIqC,EACV1D,GAAMqB,EAAIsC,EACV1D,GAAMoB,EAAIuC,EACV1D,GAAMmB,EAAIwC,EACV1D,GAAMkB,EAAIyC,EACV1D,GAAMiB,EAAI0C,EACV7B,GAAMb,EAAI2C,EACV7B,GAAMd,EAAI4C,EACV7B,GAAOf,EAAI6C,EACX7B,GAAOhB,EAAI8C,EACX7B,GAAOjB,EAAI+C,EACX7B,GAAOlB,EAAIgD,EACX7B,GAAOnB,EAAIiD,EACX7B,GAAOpB,EAAIkD,EACX7B,GAAOrB,EAAImD,EAEXzE,IADAsB,EAAIF,EAAE,IACIsC,EACVzD,GAAMqB,EAAIqC,EACVzD,GAAMoB,EAAIsC,EACVzD,GAAMmB,EAAIuC,EACVzD,GAAMkB,EAAIwC,EACVzD,GAAMiB,EAAIyC,EACV5B,GAAMb,EAAI0C,EACV5B,GAAMd,EAAI2C,EACV5B,GAAOf,EAAI4C,EACX5B,GAAOhB,EAAI6C,EACX5B,GAAOjB,EAAI8C,EACX5B,GAAOlB,EAAI+C,EACX5B,GAAOnB,EAAIgD,EACX5B,GAAOpB,EAAIiD,EACX5B,GAAOrB,EAAIkD,EACX5B,GAAOtB,EAAImD,EAEXxE,IADAqB,EAAIF,EAAE,IACIsC,EACVxD,GAAMoB,EAAIqC,EACVxD,GAAMmB,EAAIsC,EACVxD,GAAMkB,EAAIuC,EACVxD,GAAMiB,EAAIwC,EACV3B,GAAMb,EAAIyC,EACV3B,GAAMd,EAAI0C,EACV3B,GAAOf,EAAI2C,EACX3B,GAAOhB,EAAI4C,EACX3B,GAAOjB,EAAI6C,EACX3B,GAAOlB,EAAI8C,EACX3B,GAAOnB,EAAI+C,EACX3B,GAAOpB,EAAIgD,EACX3B,GAAOrB,EAAIiD,EACX3B,GAAOtB,EAAIkD,EACX3B,GAAOvB,EAAImD,EAEXvE,IADAoB,EAAIF,EAAE,IACIsC,EACVvD,GAAMmB,EAAIqC,EACVvD,GAAMkB,EAAIsC,EACVvD,GAAMiB,EAAIuC,EACV1B,GAAMb,EAAIwC,EACV1B,GAAMd,EAAIyC,EACV1B,GAAOf,EAAI0C,EACX1B,GAAOhB,EAAI2C,EACX1B,GAAOjB,EAAI4C,EACX1B,GAAOlB,EAAI6C,EACX1B,GAAOnB,EAAI8C,EACX1B,GAAOpB,EAAI+C,EACX1B,GAAOrB,EAAIgD,EACX1B,GAAOtB,EAAIiD,EACX1B,GAAOvB,EAAIkD,EACX1B,GAAOxB,EAAImD,EAEXtE,IADAmB,EAAIF,EAAE,IACIsC,EACVtD,GAAMkB,EAAIqC,EACVtD,GAAMiB,EAAIsC,EACVzB,GAAMb,EAAIuC,EACVzB,GAAMd,EAAIwC,EACVzB,GAAOf,EAAIyC,EACXzB,GAAOhB,EAAI0C,EACXzB,GAAOjB,EAAI2C,EACXzB,GAAOlB,EAAI4C,EACXzB,GAAOnB,EAAI6C,EACXzB,GAAOpB,EAAI8C,EACXzB,GAAOrB,EAAI+C,EACXzB,GAAOtB,EAAIgD,EACXzB,GAAOvB,EAAIiD,EACXzB,GAAOxB,EAAIkD,EACXzB,GAAOzB,EAAImD,EAEXrE,IADAkB,EAAIF,EAAE,IACIsC,EACVrD,GAAMiB,EAAIqC,EACVxB,GAAMb,EAAIsC,EACVxB,GAAMd,EAAIuC,EACVxB,GAAOf,EAAIwC,EACXxB,GAAOhB,EAAIyC,EACXxB,GAAOjB,EAAI0C,EACXxB,GAAOlB,EAAI2C,EACXxB,GAAOnB,EAAI4C,EACXxB,GAAOpB,EAAI6C,EACXxB,GAAOrB,EAAI8C,EACXxB,GAAOtB,EAAI+C,EACXxB,GAAOvB,EAAIgD,EACXxB,GAAOxB,EAAIiD,EACXxB,GAAOzB,EAAIkD,EACXxB,GAAO1B,EAAImD,EAEXpE,IADAiB,EAAIF,EAAE,IACIsC,EACVvB,GAAMb,EAAIqC,EACVvB,GAAMd,EAAIsC,EACVvB,GAAOf,EAAIuC,EACXvB,GAAOhB,EAAIwC,EACXvB,GAAOjB,EAAIyC,EACXvB,GAAOlB,EAAI0C,EACXvB,GAAOnB,EAAI2C,EACXvB,GAAOpB,EAAI4C,EACXvB,GAAOrB,EAAI6C,EACXvB,GAAOtB,EAAI8C,EACXvB,GAAOvB,EAAI+C,EACXvB,GAAOxB,EAAIgD,EACXvB,GAAOzB,EAAIiD,EACXvB,GAAO1B,EAAIkD,EACXvB,GAAO3B,EAAImD,EAEXtC,IADAb,EAAIF,EAAE,IACIsC,EACVtB,GAAMd,EAAIqC,EACVtB,GAAOf,EAAIsC,EACXtB,GAAOhB,EAAIuC,EACXtB,GAAOjB,EAAIwC,EACXtB,GAAOlB,EAAIyC,EACXtB,GAAOnB,EAAI0C,EACXtB,GAAOpB,EAAI2C,EACXtB,GAAOrB,EAAI4C,EACXtB,GAAOtB,EAAI6C,EACXtB,GAAOvB,EAAI8C,EACXtB,GAAOxB,EAAI+C,EACXtB,GAAOzB,EAAIgD,EACXtB,GAAO1B,EAAIiD,EACXtB,GAAO3B,EAAIkD,EACXtB,GAAO5B,EAAImD,EAEXrC,IADAd,EAAIF,EAAE,IACIsC,EACVrB,GAAOf,EAAIqC,EACXrB,GAAOhB,EAAIsC,EACXrB,GAAOjB,EAAIuC,EACXrB,GAAOlB,EAAIwC,EACXrB,GAAOnB,EAAIyC,EACXrB,GAAOpB,EAAI0C,EACXrB,GAAOrB,EAAI2C,EACXrB,GAAOtB,EAAI4C,EACXrB,GAAOvB,EAAI6C,EACXrB,GAAOxB,EAAI8C,EACXrB,GAAOzB,EAAI+C,EACXrB,GAAO1B,EAAIgD,EACXrB,GAAO3B,EAAIiD,EACXrB,GAAO5B,EAAIkD,EACXrB,GAAO7B,EAAImD,EAEXpC,IADAf,EAAIF,EAAE,KACKsC,EACXpB,GAAOhB,EAAIqC,EACXpB,GAAOjB,EAAIsC,EACXpB,GAAOlB,EAAIuC,EACXpB,GAAOnB,EAAIwC,EACXpB,GAAOpB,EAAIyC,EACXpB,GAAOrB,EAAI0C,EACXpB,GAAOtB,EAAI2C,EACXpB,GAAOvB,EAAI4C,EACXpB,GAAOxB,EAAI6C,EACXpB,GAAOzB,EAAI8C,EACXpB,GAAO1B,EAAI+C,EACXpB,GAAO3B,EAAIgD,EACXpB,GAAO5B,EAAIiD,EACXpB,GAAO7B,EAAIkD,EACXpB,GAAO9B,EAAImD,EAEXnC,IADAhB,EAAIF,EAAE,KACKsC,EACXnB,GAAOjB,EAAIqC,EACXnB,GAAOlB,EAAIsC,EACXnB,GAAOnB,EAAIuC,EACXnB,GAAOpB,EAAIwC,EACXnB,GAAOrB,EAAIyC,EACXnB,GAAOtB,EAAI0C,EACXnB,GAAOvB,EAAI2C,EACXnB,GAAOxB,EAAI4C,EACXnB,GAAOzB,EAAI6C,EACXnB,GAAO1B,EAAI8C,EACXnB,GAAO3B,EAAI+C,EACXnB,GAAO5B,EAAIgD,EACXnB,GAAO7B,EAAIiD,EACXnB,GAAO9B,EAAIkD,EACXnB,GAAO/B,EAAImD,EAEXlC,IADAjB,EAAIF,EAAE,KACKsC,EACXlB,GAAOlB,EAAIqC,EACXlB,GAAOnB,EAAIsC,EACXlB,GAAOpB,EAAIuC,EACXlB,GAAOrB,EAAIwC,EACXlB,GAAOtB,EAAIyC,EACXlB,GAAOvB,EAAI0C,EACXlB,GAAOxB,EAAI2C,EACXlB,GAAOzB,EAAI4C,EACXlB,GAAO1B,EAAI6C,EACXlB,GAAO3B,EAAI8C,EACXlB,GAAO5B,EAAI+C,EACXlB,GAAO7B,EAAIgD,EACXlB,GAAO9B,EAAIiD,EACXlB,GAAO/B,EAAIkD,EACXlB,GAAOhC,EAAImD,EAEXjC,IADAlB,EAAIF,EAAE,KACKsC,EACXjB,GAAOnB,EAAIqC,EACXjB,GAAOpB,EAAIsC,EACXjB,GAAOrB,EAAIuC,EACXjB,GAAOtB,EAAIwC,EACXjB,GAAOvB,EAAIyC,EACXjB,GAAOxB,EAAI0C,EACXjB,GAAOzB,EAAI2C,EACXjB,GAAO1B,EAAI4C,EACXjB,GAAO3B,EAAI6C,EACXjB,GAAO5B,EAAI8C,EACXjB,GAAO7B,EAAI+C,EACXjB,GAAO9B,EAAIgD,EACXjB,GAAO/B,EAAIiD,EACXjB,GAAOhC,EAAIkD,EACXjB,GAAOjC,EAAImD,EAEXhC,IADAnB,EAAIF,EAAE,KACKsC,EACXhB,GAAOpB,EAAIqC,EACXhB,GAAOrB,EAAIsC,EACXhB,GAAOtB,EAAIuC,EACXhB,GAAOvB,EAAIwC,EACXhB,GAAOxB,EAAIyC,EACXhB,GAAOzB,EAAI0C,EACXhB,GAAO1B,EAAI2C,EACXhB,GAAO3B,EAAI4C,EACXhB,GAAO5B,EAAI6C,EACXhB,GAAO7B,EAAI8C,EACXhB,GAAO9B,EAAI+C,EACXhB,GAAO/B,EAAIgD,EACXhB,GAAOhC,EAAIiD,EACXhB,GAAOjC,EAAIkD,EACXhB,GAAOlC,EAAImD,EAEX/B,IADApB,EAAIF,EAAE,KACKsC,EAkBX3D,GAAO,IAhBP6C,GAAOtB,EAAIsC,GAiBX5D,GAAO,IAhBP6C,GAAOvB,EAAIuC,GAiBX5D,GAAO,IAhBP6C,GAAOxB,EAAIwC,GAiBX5D,GAAO,IAhBP6C,GAAOzB,EAAIyC,GAiBX5D,GAAO,IAhBP6C,GAAO1B,EAAI0C,GAiBX5D,GAAO,IAhBP6C,GAAO3B,EAAI2C,GAiBX5D,GAAO,IAhBP6C,GAAO5B,EAAI4C,GAiBX/B,GAAO,IAhBPgB,GAAO7B,EAAI6C,GAiBX/B,GAAO,IAhBPgB,GAAO9B,EAAI8C,GAiBX/B,GAAO,IAhBPgB,GAAO/B,EAAI+C,GAiBX/B,GAAO,IAhBPgB,GAAOhC,EAAIgD,GAiBX/B,GAAO,IAhBPgB,GAAOjC,EAAIiD,GAiBX/B,GAAO,IAhBPgB,GAAOlC,EAAIkD,GAiBX/B,GAAO,IAhBPgB,GAAOnC,EAAImD,GAqBsC3E,GAAjDwB,GAnBAxB,GAAO,IAhBP6C,GAAOrB,EAAIqC,KAkCX3M,EAAI,GACU,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSvB,GAAjDuB,EAAKvB,EAAK/I,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACStB,GAAjDsB,EAAKtB,EAAKhJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSrB,GAAjDqB,EAAKrB,EAAKjJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSpB,GAAjDoB,EAAKpB,EAAKlJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSnB,GAAjDmB,EAAKnB,EAAKnJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSlB,GAAjDkB,EAAKlB,EAAKpJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSjB,GAAjDiB,EAAKjB,EAAKrJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSa,GAAjDb,EAAKa,EAAKnL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSc,GAAjDd,EAAKc,EAAKpL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQe,GAAhDf,EAAIe,EAAMrL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQgB,GAAhDhB,EAAIgB,EAAMtL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQiB,GAAhDjB,EAAIiB,EAAMvL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQkB,GAAhDlB,EAAIkB,EAAMxL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQmB,GAAhDnB,EAAImB,EAAMzL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQoB,GAAhDpB,EAAIoB,EAAM1L,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QAKSxB,GAAjDwB,GAJAxB,GAAM9I,EAAE,EAAI,IAAMA,EAAE,KAGpBA,EAAI,GACU,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSvB,GAAjDuB,EAAKvB,EAAK/I,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACStB,GAAjDsB,EAAKtB,EAAKhJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSrB,GAAjDqB,EAAKrB,EAAKjJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSpB,GAAjDoB,EAAKpB,EAAKlJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSnB,GAAjDmB,EAAKnB,EAAKnJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSlB,GAAjDkB,EAAKlB,EAAKpJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSjB,GAAjDiB,EAAKjB,EAAKrJ,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSa,GAAjDb,EAAKa,EAAKnL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACSc,GAAjDd,EAAKc,EAAKpL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQe,GAAhDf,EAAIe,EAAMrL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQgB,GAAhDhB,EAAIgB,EAAMtL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQiB,GAAhDjB,EAAIiB,EAAMvL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQkB,GAAhDlB,EAAIkB,EAAMxL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQmB,GAAhDnB,EAAImB,EAAMzL,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACQoB,GAAhDpB,EAAIoB,EAAM1L,EAAI,OAAgD,OAAzCA,EAAIuK,KAAKC,MAAMF,EAAI,QACxCxB,GAAM9I,EAAE,EAAI,IAAMA,EAAE,GAEpBI,EAAG,GAAK0I,EACR1I,EAAG,GAAK2I,EACR3I,EAAG,GAAK4I,EACR5I,EAAG,GAAK6I,EACR7I,EAAG,GAAK8I,EACR9I,EAAG,GAAK+I,EACR/I,EAAG,GAAKgJ,EACRhJ,EAAG,GAAKiJ,EACRjJ,EAAG,GAAK+K,EACR/K,EAAG,GAAKgL,EACRhL,EAAE,IAAMiL,EACRjL,EAAE,IAAMkL,EACRlL,EAAE,IAAMmL,EACRnL,EAAE,IAAMoL,EACRpL,EAAE,IAAMqL,EACRrL,EAAE,IAAMsL,EAGV,SAASgC,EAAEtN,EAAGgK,GACZc,EAAE9K,EAAGgK,EAAGA,GAGV,SAASuD,EAASvN,EAAGT,GACnB,IACIyK,EADApK,EAAIsE,IAER,IAAK8F,EAAI,EAAGA,EAAI,GAAIA,IAAKpK,EAAEoK,GAAKzK,EAAEyK,GAClC,IAAKA,EAAI,IAAKA,GAAK,EAAGA,IACpBsD,EAAE1N,EAAGA,GACI,IAANoK,GAAiB,IAANA,GAASc,EAAElL,EAAGA,EAAGL,GAEjC,IAAKyK,EAAI,EAAGA,EAAI,GAAIA,IAAKhK,EAAEgK,GAAKpK,EAAEoK,GAGpC,SAASwD,EAAQxN,EAAGT,GAClB,IACIyK,EADApK,EAAIsE,IAER,IAAK8F,EAAI,EAAGA,EAAI,GAAIA,IAAKpK,EAAEoK,GAAKzK,EAAEyK,GAClC,IAAKA,EAAI,IAAKA,GAAK,EAAGA,IAClBsD,EAAE1N,EAAGA,GACI,IAANoK,GAASc,EAAElL,EAAGA,EAAGL,GAExB,IAAKyK,EAAI,EAAGA,EAAI,GAAIA,IAAKhK,EAAEgK,GAAKpK,EAAEoK,GAGpC,SAASyD,EAAkBnD,EAAGtJ,EAAGK,GAC/B,IAC8BhB,EAAGd,EAD7B6I,EAAI,IAAIhG,WAAW,IACnB6C,EAAI,IAAIb,aAAa,IACrB4F,EAAI9F,IAAMnB,EAAImB,IAAMtE,EAAIsE,IACxBrE,EAAIqE,IAAMwJ,EAAIxJ,IAAMyJ,EAAIzJ,IAC5B,IAAK3E,EAAI,EAAGA,EAAI,GAAIA,IAAK6I,EAAE7I,GAAKyB,EAAEzB,GAIlC,IAHA6I,EAAE,IAAW,IAANpH,EAAE,IAAS,GAClBoH,EAAE,IAAI,IACNuC,EAAY1F,EAAE5D,GACT9B,EAAI,EAAGA,EAAI,GAAIA,IAClBwD,EAAExD,GAAG0F,EAAE1F,GACPM,EAAEN,GAAGyK,EAAEzK,GAAGK,EAAEL,GAAG,EAGjB,IADAyK,EAAE,GAAGnK,EAAE,GAAG,EACLN,EAAE,IAAKA,GAAG,IAAKA,EAElB8K,EAASL,EAAEjH,EADX1C,EAAG+H,EAAE7I,IAAI,MAAQ,EAAFA,GAAM,GAErB8K,EAASzK,EAAEC,EAAEQ,GACbuK,EAAE8C,EAAE1D,EAAEpK,GACNiL,EAAEb,EAAEA,EAAEpK,GACNgL,EAAEhL,EAAEmD,EAAElD,GACNgL,EAAE9H,EAAEA,EAAElD,GACNyN,EAAEzN,EAAE6N,GACJJ,EAAEK,EAAE3D,GACJc,EAAEd,EAAEpK,EAAEoK,GACNc,EAAElL,EAAEmD,EAAE2K,GACN9C,EAAE8C,EAAE1D,EAAEpK,GACNiL,EAAEb,EAAEA,EAAEpK,GACN0N,EAAEvK,EAAEiH,GACJa,EAAEjL,EAAEC,EAAE8N,GACN7C,EAAEd,EAAEpK,EAAE8E,GACNkG,EAAEZ,EAAEA,EAAEnK,GACNiL,EAAElL,EAAEA,EAAEoK,GACNc,EAAEd,EAAEnK,EAAE8N,GACN7C,EAAEjL,EAAEkD,EAAEkC,GACNqI,EAAEvK,EAAE2K,GACJrD,EAASL,EAAEjH,EAAE1C,GACbgK,EAASzK,EAAEC,EAAEQ,GAEf,IAAKd,EAAI,EAAGA,EAAI,GAAIA,IAClB0F,EAAE1F,EAAE,IAAIyK,EAAEzK,GACV0F,EAAE1F,EAAE,IAAIK,EAAEL,GACV0F,EAAE1F,EAAE,IAAIwD,EAAExD,GACV0F,EAAE1F,EAAE,IAAIM,EAAEN,GAEZ,IAAIqO,EAAM3I,EAAE4I,SAAS,IACjBC,EAAM7I,EAAE4I,SAAS,IAIrB,OAHAN,EAASK,EAAIA,GACb9C,EAAEgD,EAAIA,EAAIF,GACVrD,EAAUD,EAAEwD,GACL,EAGT,SAASC,EAAuBzD,EAAGtJ,GACjC,OAAOyM,EAAkBnD,EAAGtJ,EAAGuD,GAGjC,SAASyJ,EAAmB3I,EAAGJ,GAE7B,OADAZ,EAAYY,EAAG,IACR8I,EAAuB1I,EAAGJ,GAGnC,SAASgJ,EAAoBtI,EAAGN,EAAGJ,GACjC,IAAI3D,EAAI,IAAIc,WAAW,IAEvB,OADAqL,EAAkBnM,EAAG2D,EAAGI,GACjByC,EAAqBnC,EAAGrB,EAAIhD,EAAG0G,GA33BxCS,EAAStH,UAAU+M,OAAS,SAASvO,EAAGwI,EAAMgG,GA2B5C,IA1BA,IACIzF,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIrJ,EAChCwO,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAFpCC,EAAQpN,KAAK4H,IAAM,EAAI,KAIvByF,EAAKrN,KAAKwD,EAAE,GACZ8J,EAAKtN,KAAKwD,EAAE,GACZ+J,EAAKvN,KAAKwD,EAAE,GACZgK,EAAKxN,KAAKwD,EAAE,GACZiK,EAAKzN,KAAKwD,EAAE,GACZkK,EAAK1N,KAAKwD,EAAE,GACZmK,EAAK3N,KAAKwD,EAAE,GACZoK,EAAK5N,KAAKwD,EAAE,GACZqK,EAAK7N,KAAKwD,EAAE,GACZsK,EAAK9N,KAAKwD,EAAE,GAEZuK,EAAK/N,KAAKrB,EAAE,GACZqP,EAAKhO,KAAKrB,EAAE,GACZsP,EAAKjO,KAAKrB,EAAE,GACZuP,EAAKlO,KAAKrB,EAAE,GACZwP,EAAKnO,KAAKrB,EAAE,GACZyP,EAAKpO,KAAKrB,EAAE,GACZ0P,EAAKrO,KAAKrB,EAAE,GACZ2P,EAAKtO,KAAKrB,EAAE,GACZ4P,EAAKvO,KAAKrB,EAAE,GACZ6P,EAAKxO,KAAKrB,EAAE,GAET8N,GAAS,IAcdC,EAFAxO,EAAI,EAGJwO,IAdmDW,GAAmC,MAAtFrG,EAAkB,IAAb/I,EAAEwI,EAAM,IAA0B,IAAbxI,EAAEwI,EAAM,KAAc,IAcrCsH,EACXrB,IAdmDY,GAAmC,MAA3BtG,IAAO,IAAlEC,EAAkB,IAAbhJ,EAAEwI,EAAM,IAA0B,IAAbxI,EAAEwI,EAAM,KAAc,IAAgC,KAcpE,EAAI+H,GAChB9B,IAdmDa,GAAmC,MAA3BtG,IAAO,IAAlEC,EAAkB,IAAbjJ,EAAEwI,EAAM,IAA0B,IAAbxI,EAAEwI,EAAM,KAAc,IAAgC,KAcpE,EAAI8H,GAChB7B,IAdmDc,GAAmC,MAA3BtG,IAAQ,GAAnEC,EAAkB,IAAblJ,EAAEwI,EAAM,IAA0B,IAAbxI,EAAEwI,EAAM,KAAc,IAAgC,KAcpE,EAAI6H,GAEhBpQ,GADAwO,IAdmDe,GAAmC,MAA3BtG,IAAQ,GAAnEC,EAAkB,IAAbnJ,EAAEwI,EAAM,IAA0B,IAAbxI,EAAEwI,EAAM,KAAc,IAA+B,MAcnE,EAAI4H,MACJ,GAAK3B,GAAM,KACvBA,IAfAgB,GAAQtG,IAAQ,EAAM,OAeV,EAAIgH,GAChB1B,IAfmDiB,GAAmC,MAA3BvG,IAAO,IAAlEC,EAAkB,IAAbpJ,EAAEwI,EAAK,KAA2B,IAAbxI,EAAEwI,EAAK,MAAe,IAAgC,KAepE,EAAI0H,GAChBzB,IAfmDkB,GAAmC,MAA3BvG,IAAO,IAAlEC,EAAkB,IAAbrJ,EAAEwI,EAAK,KAA2B,IAAbxI,EAAEwI,EAAK,MAAe,IAAgC,KAepE,EAAIyH,GAChBxB,IAfmDmB,GAAmC,MAA3BvG,IAAQ,GAAnEC,EAAkB,IAAbtJ,EAAEwI,EAAK,KAA2B,IAAbxI,EAAEwI,EAAK,MAAe,IAAgC,KAepE,EAAIwH,GAIhBtB,EAFAzO,IADAwO,IAfAoB,GAAQvG,IAAO,EAAM6F,IAeT,EAAIY,MACH,GAGbrB,GAAMU,EAAKW,EACXrB,GAAMW,EAAKS,EACXpB,GAAMY,GAAM,EAAIiB,GAChB7B,GAAMa,GAAM,EAAIe,GAEhBrQ,GADAyO,GAAMc,GAAM,EAAIa,MACJ,GAAK3B,GAAM,KACvBA,GAAMe,GAAM,EAAIW,GAChB1B,GAAMgB,GAAM,EAAIS,GAChBzB,GAAMiB,GAAM,EAAIO,GAChBxB,GAAMkB,GAAM,EAAIK,GAEhBhQ,IADAyO,GAAMmB,GAAM,EAAIG,MACH,GAAKtB,GAAM,KAExBC,EAAK1O,EACL0O,GAAMS,EAAKY,EACXrB,GAAMU,EAAKU,EACXpB,GAAMW,EAAKQ,EACXnB,GAAMY,GAAM,EAAIgB,GAEhBtQ,GADA0O,GAAMa,GAAM,EAAIc,MACJ,GAAK3B,GAAM,KACvBA,GAAMc,GAAM,EAAIY,GAChB1B,GAAMe,GAAM,EAAIU,GAChBzB,GAAMgB,GAAM,EAAIQ,GAChBxB,GAAMiB,GAAM,EAAIM,GAIhBtB,EAFA3O,IADA0O,GAAMkB,GAAM,EAAII,MACH,GAGbrB,GAAMQ,EAAKa,EACXrB,GAAMS,EAAKW,EACXpB,GAAMU,EAAKS,EACXnB,GAAMW,EAAKO,EAEX7P,GADA2O,GAAMY,GAAM,EAAIe,MACJ,GAAK3B,GAAM,KACvBA,GAAMa,GAAM,EAAIa,GAChB1B,GAAMc,GAAM,EAAIW,GAChBzB,GAAMe,GAAM,EAAIS,GAChBxB,GAAMgB,GAAM,EAAIO,GAIhBtB,EAFA5O,IADA2O,GAAMiB,GAAM,EAAIK,MACH,GAGbrB,GAAMO,EAAKc,EACXrB,GAAMQ,EAAKY,EACXpB,GAAMS,EAAKU,EACXnB,GAAMU,EAAKQ,EAEX9P,GADA4O,GAAMW,EAAKM,KACC,GAAKjB,GAAM,KACvBA,GAAMY,GAAM,EAAIc,GAChB1B,GAAMa,GAAM,EAAIY,GAChBzB,GAAMc,GAAM,EAAIU,GAChBxB,GAAMe,GAAM,EAAIQ,GAIhBtB,EAFA7O,IADA4O,GAAMgB,GAAM,EAAIM,MACH,GAGbrB,GAAMM,EAAKe,EACXrB,GAAMO,EAAKa,EACXpB,GAAMQ,EAAKW,EACXnB,GAAMS,EAAKS,EAEX/P,GADA6O,GAAMU,EAAKO,KACC,GAAKjB,GAAM,KACvBA,GAAMW,EAAKK,EACXhB,GAAMY,GAAM,EAAIa,GAChBzB,GAAMa,GAAM,EAAIW,GAChBxB,GAAMc,GAAM,EAAIS,GAIhBtB,EAFA9O,IADA6O,GAAMe,GAAM,EAAIO,MACH,GAGbrB,GAAMK,EAAKgB,EACXrB,GAAMM,EAAKc,EACXpB,GAAMO,EAAKY,EACXnB,GAAMQ,EAAKU,EAEXhQ,GADA8O,GAAMS,EAAKQ,KACC,GAAKjB,GAAM,KACvBA,GAAMU,EAAKM,EACXhB,GAAMW,EAAKI,EACXf,GAAMY,GAAM,EAAIY,GAChBxB,GAAMa,GAAM,EAAIU,GAIhBtB,EAFA/O,IADA8O,GAAMc,GAAM,EAAIQ,MACH,GAGbrB,GAAMI,EAAKiB,EACXrB,GAAMK,EAAKe,EACXpB,GAAMM,EAAKa,EACXnB,GAAMO,EAAKW,EAEXjQ,GADA+O,GAAMQ,EAAKS,KACC,GAAKjB,GAAM,KACvBA,GAAMS,EAAKO,EACXhB,GAAMU,EAAKK,EACXf,GAAMW,EAAKG,EACXd,GAAMY,GAAM,EAAIW,GAIhBtB,EAFAhP,IADA+O,GAAMa,GAAM,EAAIS,MACH,GAGbrB,GAAMG,EAAKkB,EACXrB,GAAMI,EAAKgB,EACXpB,GAAMK,EAAKc,EACXnB,GAAMM,EAAKY,EAEXlQ,GADAgP,GAAMO,EAAKU,KACC,GAAKjB,GAAM,KACvBA,GAAMQ,EAAKQ,EACXhB,GAAMS,EAAKM,EACXf,GAAMU,EAAKI,EACXd,GAAMW,EAAKE,EAIXZ,EAFAjP,IADAgP,GAAMY,GAAM,EAAIU,MACH,GAGbrB,GAAME,EAAKmB,EACXrB,GAAMG,EAAKiB,EACXpB,GAAMI,EAAKe,EACXnB,GAAMK,EAAKa,EAEXnQ,GADAiP,GAAMM,EAAKW,KACC,GAAKjB,GAAM,KACvBA,GAAMO,EAAKS,EACXhB,GAAMQ,EAAKO,EACXf,GAAMS,EAAKK,EACXd,GAAMU,EAAKG,EAUXX,EAJAX,EAAS,MADTxO,GADAA,IAFAA,IADAiP,GAAMW,EAAKC,KACE,KAED,GAAK7P,EAAM,IAhILwO,GAAM,MAiIT,GAMfY,EAHAX,GADAzO,KAAW,GAKXqP,EA5GkBX,GAAM,KA6GxBY,EA/FkBX,GAAM,KAgGxBY,EAlFkBX,GAAM,KAmFxBY,EArEkBX,GAAM,KAsExBY,EAxDkBX,GAAM,KAyDxBY,EA3CkBX,GAAM,KA4CxBY,EA9BkBX,GAAM,KA+BxBY,EAjBkBX,GAAM,KAmBxB1G,GAAQ,GACRgG,GAAS,GAEXzM,KAAKwD,EAAE,GAAK6J,EACZrN,KAAKwD,EAAE,GAAK8J,EACZtN,KAAKwD,EAAE,GAAK+J,EACZvN,KAAKwD,EAAE,GAAKgK,EACZxN,KAAKwD,EAAE,GAAKiK,EACZzN,KAAKwD,EAAE,GAAKkK,EACZ1N,KAAKwD,EAAE,GAAKmK,EACZ3N,KAAKwD,EAAE,GAAKoK,EACZ5N,KAAKwD,EAAE,GAAKqK,EACZ7N,KAAKwD,EAAE,GAAKsK,GAGd/G,EAAStH,UAAUuI,OAAS,SAASyG,EAAKC,GACxC,IACIxQ,EAAGyQ,EAAM1C,EAAGpO,EADZ+Q,EAAI,IAAInH,YAAY,IAGxB,GAAIzH,KAAK2H,SAAU,CAGjB,IAFA9J,EAAImC,KAAK2H,SACT3H,KAAKwH,OAAO3J,KAAO,EACZA,EAAI,GAAIA,IAAKmC,KAAKwH,OAAO3J,GAAK,EACrCmC,KAAK4H,IAAM,EACX5H,KAAKwM,OAAOxM,KAAKwH,OAAQ,EAAG,IAK9B,IAFAtJ,EAAI8B,KAAKwD,EAAE,KAAO,GAClBxD,KAAKwD,EAAE,IAAM,KACR3F,EAAI,EAAGA,EAAI,GAAIA,IAClBmC,KAAKwD,EAAE3F,IAAMK,EACbA,EAAI8B,KAAKwD,EAAE3F,KAAO,GAClBmC,KAAKwD,EAAE3F,IAAM,KAaf,IAXAmC,KAAKwD,EAAE,IAAW,EAAJtF,EACdA,EAAI8B,KAAKwD,EAAE,KAAO,GAClBxD,KAAKwD,EAAE,IAAM,KACbxD,KAAKwD,EAAE,IAAMtF,EACbA,EAAI8B,KAAKwD,EAAE,KAAO,GAClBxD,KAAKwD,EAAE,IAAM,KACbxD,KAAKwD,EAAE,IAAMtF,EAEb0Q,EAAE,GAAK5O,KAAKwD,EAAE,GAAK,EACnBtF,EAAI0Q,EAAE,KAAO,GACbA,EAAE,IAAM,KACH/Q,EAAI,EAAGA,EAAI,GAAIA,IAClB+Q,EAAE/Q,GAAKmC,KAAKwD,EAAE3F,GAAKK,EACnBA,EAAI0Q,EAAE/Q,KAAO,GACb+Q,EAAE/Q,IAAM,KAKV,IAHA+Q,EAAE,IAAM,KAERD,GAAY,EAAJzQ,GAAS,EACZL,EAAI,EAAGA,EAAI,GAAIA,IAAK+Q,EAAE/Q,IAAM8Q,EAEjC,IADAA,GAAQA,EACH9Q,EAAI,EAAGA,EAAI,GAAIA,IAAKmC,KAAKwD,EAAE3F,GAAMmC,KAAKwD,EAAE3F,GAAK8Q,EAAQC,EAAE/Q,GAa5D,IAXAmC,KAAKwD,EAAE,GAAoE,OAA7DxD,KAAKwD,EAAE,GAAcxD,KAAKwD,EAAE,IAAM,IAChDxD,KAAKwD,EAAE,GAAoE,OAA7DxD,KAAKwD,EAAE,KAAQ,EAAMxD,KAAKwD,EAAE,IAAM,IAChDxD,KAAKwD,EAAE,GAAoE,OAA7DxD,KAAKwD,EAAE,KAAQ,EAAMxD,KAAKwD,EAAE,IAAO,GACjDxD,KAAKwD,EAAE,GAAoE,OAA7DxD,KAAKwD,EAAE,KAAQ,EAAMxD,KAAKwD,EAAE,IAAO,GACjDxD,KAAKwD,EAAE,GAAoE,OAA7DxD,KAAKwD,EAAE,KAAO,GAAOxD,KAAKwD,EAAE,IAAO,EAAMxD,KAAKwD,EAAE,IAAM,IACpExD,KAAKwD,EAAE,GAAoE,OAA7DxD,KAAKwD,EAAE,KAAQ,EAAMxD,KAAKwD,EAAE,IAAM,IAChDxD,KAAKwD,EAAE,GAAoE,OAA7DxD,KAAKwD,EAAE,KAAQ,EAAMxD,KAAKwD,EAAE,IAAO,GACjDxD,KAAKwD,EAAE,GAAoE,OAA7DxD,KAAKwD,EAAE,KAAQ,EAAMxD,KAAKwD,EAAE,IAAO,GAEjDyI,EAAIjM,KAAKwD,EAAE,GAAKxD,KAAK0H,IAAI,GACzB1H,KAAKwD,EAAE,GAAS,MAAJyI,EACPpO,EAAI,EAAGA,EAAI,EAAGA,IACjBoO,GAAOjM,KAAKwD,EAAE3F,GAAKmC,KAAK0H,IAAI7J,GAAM,IAAMoO,IAAM,IAAO,EACrDjM,KAAKwD,EAAE3F,GAAS,MAAJoO,EAGdwC,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAM1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAO1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAO1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAO1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAO1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAO1O,KAAKwD,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAO1O,KAAKwD,EAAE,KAAO,EAAK,KAGvCuD,EAAStH,UAAUsI,OAAS,SAAS9J,EAAGwI,EAAMgG,GAC5C,IAAI5O,EAAGgR,EAEP,GAAI7O,KAAK2H,SAAU,CAIjB,KAHAkH,EAAQ,GAAK7O,KAAK2H,UACP8E,IACToC,EAAOpC,GACJ5O,EAAI,EAAGA,EAAIgR,EAAMhR,IACpBmC,KAAKwH,OAAOxH,KAAK2H,SAAW9J,GAAKI,EAAEwI,EAAK5I,GAI1C,GAHA4O,GAASoC,EACTpI,GAAQoI,EACR7O,KAAK2H,UAAYkH,EACb7O,KAAK2H,SAAW,GAClB,OACF3H,KAAKwM,OAAOxM,KAAKwH,OAAQ,EAAG,IAC5BxH,KAAK2H,SAAW,EAUlB,GAPI8E,GAAS,KACXoC,EAAOpC,EAASA,EAAQ,GACxBzM,KAAKwM,OAAOvO,EAAGwI,EAAMoI,GACrBpI,GAAQoI,EACRpC,GAASoC,GAGPpC,EAAO,CACT,IAAK5O,EAAI,EAAGA,EAAI4O,EAAO5O,IACrBmC,KAAKwH,OAAOxH,KAAK2H,SAAW9J,GAAKI,EAAEwI,EAAK5I,GAC1CmC,KAAK2H,UAAY8E,IAikBrB,IAAIqC,EAAqB3G,EACrB4G,EAA0B3G,EAc9B,IAAI4G,EAAI,CACN,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,UAAY,UACpC,UAAY,WAAY,UAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,UAAY,UACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,UAAY,UACpC,UAAY,UAAY,UAAY,WACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,UAAY,WAAY,YAGtC,SAASC,EAAqBC,EAAIC,EAAIlR,EAAGqB,GAyBvC,IAxBA,IACI8P,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACnCC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACnCC,EAAIC,EAAIxS,EAAGiL,EAAGtF,EAAG1F,EAAGwK,EAAGjH,EAAGnD,EAAGC,EAH7BmS,EAAK,IAAIC,WAAW,IAAKC,EAAK,IAAID,WAAW,IAK7CE,EAAMvB,EAAG,GACTwB,EAAMxB,EAAG,GACTyB,EAAMzB,EAAG,GACT0B,EAAM1B,EAAG,GACT2B,EAAM3B,EAAG,GACT4B,EAAM5B,EAAG,GACT6B,EAAM7B,EAAG,GACT8B,EAAM9B,EAAG,GAET+B,EAAM9B,EAAG,GACT+B,EAAM/B,EAAG,GACTgC,EAAMhC,EAAG,GACTiC,EAAMjC,EAAG,GACTkC,EAAMlC,EAAG,GACTmC,EAAMnC,EAAG,GACToC,EAAMpC,EAAG,GACTqC,EAAMrC,EAAG,GAETpN,EAAM,EACHzC,GAAK,KAAK,CACf,IAAKzB,EAAI,EAAGA,EAAI,GAAIA,IAClBiL,EAAI,EAAIjL,EAAIkE,EACZuO,EAAGzS,GAAMI,EAAE6K,EAAE,IAAM,GAAO7K,EAAE6K,EAAE,IAAM,GAAO7K,EAAE6K,EAAE,IAAM,EAAK7K,EAAE6K,EAAE,GAC9D0H,EAAG3S,GAAMI,EAAE6K,EAAE,IAAM,GAAO7K,EAAE6K,EAAE,IAAM,GAAO7K,EAAE6K,EAAE,IAAM,EAAK7K,EAAE6K,EAAE,GAEhE,IAAKjL,EAAI,EAAGA,EAAI,GAAIA,IA+HlB,GA9HAuR,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACAC,EAENpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACAC,EAMNlJ,EAAQ,OAFRxK,EAAI0T,GAEYnQ,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAIwN,GAIY7S,EAAIqF,IAAM,GAM1B8E,GAAS,OAFTxK,GAAMuT,IAAQ,GAAOR,GAAO,KAAcQ,IAAQ,GAAOR,GAAO,KAAcA,IAAQ,EAAYQ,GAAO,KAExFhQ,GAAKvD,IAAM,GAC5BI,GAAS,OAJTsF,GAAMqN,IAAQ,GAAOQ,GAAO,KAAcR,IAAQ,GAAOQ,GAAO,KAAcA,IAAQ,EAAYR,GAAO,KAIxF1S,GAAKqF,IAAM,GAM5B8E,GAAS,OAFTxK,EAAKuT,EAAMC,GAASD,EAAME,GAETlQ,GAAKvD,IAAM,GAC5BI,GAAS,OAJTsF,EAAKqN,EAAMC,GAASD,EAAME,GAIT5S,GAAKqF,IAAM,GAM5B8E,GAAS,OAFTxK,EAAIkR,EAAI,EAAFnR,EAAI,IAEOwD,GAAKvD,IAAM,GAC5BI,GAAS,OAJTsF,EAAIwL,EAAI,EAAFnR,IAIWM,GAAKqF,IAAM,GAG5BA,EAAI8M,EAAGzS,EAAE,IAGQwD,IAFjBvD,EAAI0S,EAAG3S,EAAE,OAEmB,GAC5BK,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAG5BtF,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,GAUXwK,EAAQ,OAFRxK,EAJAuS,EAAS,MAAJ/H,EAAajH,GAAK,IAMPA,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAJA4M,EAAS,MAAJlS,GAFLC,GAAKD,IAAM,KAEY,IAQPC,EAAIqF,IAAM,GAM1B8E,GAAS,OAFTxK,GAAMmT,IAAQ,GAAOR,GAAO,IAAcA,IAAQ,EAAYQ,GAAO,KAAmBR,IAAQ,EAAYQ,GAAO,KAElG5P,GAAKvD,IAAM,GAC5BI,GAAS,OAJTsF,GAAMiN,IAAQ,GAAOQ,GAAO,IAAcA,IAAQ,EAAYR,GAAO,KAAmBQ,IAAQ,EAAYR,GAAO,KAIlGtS,GAAKqF,IAAM,GAMXnC,IAFjBvD,EAAKmT,EAAMC,EAAQD,EAAME,EAAQD,EAAMC,KAEX,GAC5BjT,GAAS,OAJTsF,EAAKiN,EAAMC,EAAQD,EAAME,EAAQD,EAAMC,GAItBxS,GAAKqF,IAAM,GAM5BmM,EAAW,OAHXzR,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,KACXK,GAAKD,IAAM,KAEgB,GAC3BiS,EAAW,MAAJ7H,EAAejH,GAAK,GAM3BiH,EAAQ,OAFRxK,EAAIiS,GAEY1O,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAI+L,GAIYpR,EAAIqF,IAAM,GAKTnC,IAFjBvD,EAAIuS,KAEwB,GAC5BnS,GAAS,OAJTsF,EAAI4M,GAIajS,GAAKqF,IAAM,GAS5BkN,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EANAtB,EAAW,OAHXrR,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,KACXK,GAAKD,IAAM,KAEgB,GAO3B4S,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNe,EAAMd,EAENuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EAdAtB,EAAW,MAAJzH,EAAejH,GAAK,GAe3BiQ,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNe,EAAMd,EAEFtS,EAAE,IAAO,GACX,IAAKiL,EAAI,EAAGA,EAAI,GAAIA,IAElBtF,EAAI8M,EAAGxH,GAGPR,EAAQ,OAFRxK,EAAI0S,EAAG1H,IAESzH,EAAIvD,IAAM,GAC1BI,EAAQ,MAAJsF,EAAYrF,EAAIqF,IAAM,GAE1BA,EAAI8M,GAAIxH,EAAE,GAAG,IAGbR,GAAS,OAFTxK,EAAI0S,GAAI1H,EAAE,GAAG,KAEIzH,GAAKvD,IAAM,GAC5BI,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAG5B4M,EAAKE,GAAIxH,EAAE,GAAG,IAKdR,GAAS,OAFTxK,IAFAuS,EAAKG,GAAI1H,EAAE,GAAG,OAED,EAAMsH,GAAM,KAAaC,IAAO,EAAMD,GAAM,KAAaC,IAAO,EAAMD,GAAM,KAExE/O,GAAKvD,IAAM,GAC5BI,GAAS,OAJTsF,GAAM4M,IAAO,EAAMC,GAAM,KAAaD,IAAO,EAAMC,GAAM,IAAYD,IAAO,GAI3DjS,GAAKqF,IAAM,GAG5B4M,EAAKE,GAAIxH,EAAE,IAAI,IAKEzH,IAFjBvD,IAFAuS,EAAKG,GAAI1H,EAAE,IAAI,OAEF,GAAOsH,GAAM,KAAcA,IAAO,GAAYC,GAAM,IAAmBA,IAAO,EAAMD,GAAM,OAE3E,GAC5BlS,GAAS,OAJTsF,GAAM4M,IAAO,GAAOC,GAAM,KAAcA,IAAO,GAAYD,GAAM,GAAkBA,IAAO,GAIzEjS,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXwS,EAAGxH,GAAU,MAAJ5K,EAAeC,GAAK,GAC7BqS,EAAG1H,GAAU,MAAJR,EAAejH,GAAK,GASnCiH,EAAQ,OAFRxK,EAAImT,GAEY5P,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAIiN,GAIYtS,EAAIqF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBvD,EAAIqR,EAAG,MAEqB,GAC5BjR,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXoR,EAAG,GAAKuB,EAAW,MAAJvS,EAAeC,GAAK,GACnCgR,EAAG,GAAK8B,EAAW,MAAJ3I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRxK,EAAIoT,GAEY7P,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAIkN,GAIYvS,EAAIqF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBvD,EAAIqR,EAAG,MAEqB,GAC5BjR,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXoR,EAAG,GAAKwB,EAAW,MAAJxS,EAAeC,GAAK,GACnCgR,EAAG,GAAK+B,EAAW,MAAJ5I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRxK,EAAIqT,GAEY9P,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAImN,GAIYxS,EAAIqF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBvD,EAAIqR,EAAG,MAEqB,GAC5BjR,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXoR,EAAG,GAAKyB,EAAW,MAAJzS,EAAeC,GAAK,GACnCgR,EAAG,GAAKgC,EAAW,MAAJ7I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRxK,EAAIsT,GAEY/P,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAIoN,GAIYzS,EAAIqF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBvD,EAAIqR,EAAG,MAEqB,GAC5BjR,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXoR,EAAG,GAAK0B,EAAW,MAAJ1S,EAAeC,GAAK,GACnCgR,EAAG,GAAKiC,EAAW,MAAJ9I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRxK,EAAIuT,GAEYhQ,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAIqN,GAIY1S,EAAIqF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBvD,EAAIqR,EAAG,MAEqB,GAC5BjR,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXoR,EAAG,GAAK2B,EAAW,MAAJ3S,EAAeC,GAAK,GACnCgR,EAAG,GAAKkC,EAAW,MAAJ/I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRxK,EAAIwT,GAEYjQ,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAIsN,GAIY3S,EAAIqF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBvD,EAAIqR,EAAG,MAEqB,GAC5BjR,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXoR,EAAG,GAAK4B,EAAW,MAAJ5S,EAAeC,GAAK,GACnCgR,EAAG,GAAKmC,EAAW,MAAJhJ,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRxK,EAAIyT,GAEYlQ,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAIuN,GAIY5S,EAAIqF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBvD,EAAIqR,EAAG,MAEqB,GAC5BjR,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXoR,EAAG,GAAK6B,EAAW,MAAJ7S,EAAeC,GAAK,GACnCgR,EAAG,GAAKoC,EAAW,MAAJjJ,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRxK,EAAI0T,GAEYnQ,EAAIvD,IAAM,GAC1BI,EAAQ,OAJRsF,EAAIwN,GAIY7S,EAAIqF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBvD,EAAIqR,EAAG,MAEqB,GAC5BjR,GAAS,MAAJsF,EAAYrF,GAAKqF,IAAM,GAI5BrF,IADAD,IADAmD,IAHAiH,GAAS,MAAJxK,KAGM,MACA,MACA,GAEXoR,EAAG,GAAK8B,EAAW,MAAJ9S,EAAeC,GAAK,GACnCgR,EAAG,GAAKqC,EAAW,MAAJlJ,EAAejH,GAAK,GAEnCU,GAAO,IACPzC,GAAK,IAGP,OAAOA,EAGT,SAASmS,EAAYtR,EAAKlC,EAAGqB,GAC3B,IAGIzB,EAHAqR,EAAK,IAAIqB,WAAW,GACpBpB,EAAK,IAAIoB,WAAW,GACpBhN,EAAI,IAAI7C,WAAW,KAChBW,EAAI/B,EAuBX,IArBA4P,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UACRA,EAAG,GAAK,WAERC,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UAERF,EAAqBC,EAAIC,EAAIlR,EAAGqB,GAChCA,GAAK,IAEAzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAK0F,EAAE1F,GAAKI,EAAEoD,EAAE/B,EAAEzB,GAQrC,IAPA0F,EAAEjE,GAAK,IAGPiE,GADAjE,EAAI,IAAI,KAAKA,EAAE,IAAI,EAAE,IACjB,GAAK,EACTgE,EAAKC,EAAGjE,EAAE,EAAK+B,EAAI,UAAc,EAAGA,GAAK,GACzC4N,EAAqBC,EAAIC,EAAI5L,EAAGjE,GAE3BzB,EAAI,EAAGA,EAAI,EAAGA,IAAKyF,EAAKnD,EAAK,EAAEtC,EAAGqR,EAAGrR,GAAIsR,EAAGtR,IAEjD,OAAO,EAGT,SAAS6T,EAAI/R,EAAGiJ,GACd,IAAIN,EAAI9F,IAAMnB,EAAImB,IAAMtE,EAAIsE,IACxBrE,EAAIqE,IAAMwJ,EAAIxJ,IAAMyJ,EAAIzJ,IACxBoM,EAAIpM,IAAMgB,EAAIhB,IAAMzD,EAAIyD,IAE5B2G,EAAEb,EAAG3I,EAAE,GAAIA,EAAE,IACbwJ,EAAEpK,EAAG6J,EAAE,GAAIA,EAAE,IACbQ,EAAEd,EAAGA,EAAGvJ,GACRmK,EAAE7H,EAAG1B,EAAE,GAAIA,EAAE,IACbuJ,EAAEnK,EAAG6J,EAAE,GAAIA,EAAE,IACbQ,EAAE/H,EAAGA,EAAGtC,GACRqK,EAAElL,EAAGyB,EAAE,GAAIiJ,EAAE,IACbQ,EAAElL,EAAGA,EAAGgF,GACRkG,EAAEjL,EAAGwB,EAAE,GAAIiJ,EAAE,IACbM,EAAE/K,EAAGA,EAAGA,GACRgL,EAAE6C,EAAG3K,EAAGiH,GACRa,EAAE8C,EAAG9N,EAAGD,GACRgL,EAAE0F,EAAGzQ,EAAGD,GACRgL,EAAE1F,EAAGnC,EAAGiH,GAERc,EAAEzJ,EAAE,GAAIqM,EAAGC,GACX7C,EAAEzJ,EAAE,GAAI6D,EAAGoL,GACXxF,EAAEzJ,EAAE,GAAIiP,EAAG3C,GACX7C,EAAEzJ,EAAE,GAAIqM,EAAGxI,GAGb,SAASmO,EAAMhS,EAAGiJ,EAAGvH,GACnB,IAAIxD,EACJ,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IACjB8K,EAAShJ,EAAE9B,GAAI+K,EAAE/K,GAAIwD,GAIzB,SAASuQ,GAAKjT,EAAGgB,GACf,IAAIkS,EAAKrP,IAAMsP,EAAKtP,IAAMuP,EAAKvP,IAC/BqJ,EAASkG,EAAIpS,EAAE,IACfyJ,EAAEyI,EAAIlS,EAAE,GAAIoS,GACZ3I,EAAE0I,EAAInS,EAAE,GAAIoS,GACZlJ,EAAUlK,EAAGmT,GACbnT,EAAE,KAAOqK,EAAS6I,IAAO,EAG3B,SAASG,GAAWrS,EAAGiJ,EAAGhJ,GACxB,IAAIyB,EAAGxD,EAKP,IAJAwK,EAAS1I,EAAE,GAAImD,GACfuF,EAAS1I,EAAE,GAAIoD,GACfsF,EAAS1I,EAAE,GAAIoD,GACfsF,EAAS1I,EAAE,GAAImD,GACVjF,EAAI,IAAKA,GAAK,IAAKA,EAEtB8T,EAAMhS,EAAGiJ,EADTvH,EAAKzB,EAAG/B,EAAE,EAAG,KAAS,EAAFA,GAAQ,GAE5B6T,EAAI9I,EAAGjJ,GACP+R,EAAI/R,EAAGA,GACPgS,EAAMhS,EAAGiJ,EAAGvH,GAIhB,SAAS4Q,GAAWtS,EAAGC,GACrB,IAAIgJ,EAAI,CAACpG,IAAMA,IAAMA,IAAMA,KAC3B6F,EAASO,EAAE,GAAIzF,GACfkF,EAASO,EAAE,GAAIxF,GACfiF,EAASO,EAAE,GAAI7F,GACfqG,EAAER,EAAE,GAAIzF,EAAGC,GACX4O,GAAWrS,EAAGiJ,EAAGhJ,GAGnB,SAASsS,GAAoBC,EAAIC,EAAIC,GACnC,IAEIxU,EAFAM,EAAI,IAAIuC,WAAW,IACnBf,EAAI,CAAC6C,IAAMA,IAAMA,IAAMA,KAY3B,IATK6P,GAAQ1P,EAAYyP,EAAI,IAC7BX,EAAYtT,EAAGiU,EAAI,IACnBjU,EAAE,IAAM,IACRA,EAAE,KAAO,IACTA,EAAE,KAAO,GAET8T,GAAWtS,EAAGxB,GACdyT,GAAKO,EAAIxS,GAEJ9B,EAAI,EAAGA,EAAI,GAAIA,IAAKuU,EAAGvU,EAAE,IAAMsU,EAAGtU,GACvC,OAAO,EAGT,IAAIyU,GAAI,IAAI5P,aAAa,CAAC,IAAM,IAAM,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,KAEvK,SAAS6P,GAAK5T,EAAG4E,GACf,IAAIiP,EAAO3U,EAAGiL,EAAG7E,EACjB,IAAKpG,EAAI,GAAIA,GAAK,KAAMA,EAAG,CAEzB,IADA2U,EAAQ,EACH1J,EAAIjL,EAAI,GAAIoG,EAAIpG,EAAI,GAAIiL,EAAI7E,IAAK6E,EACpCvF,EAAEuF,IAAM0J,EAAQ,GAAKjP,EAAE1F,GAAKyU,GAAExJ,GAAKjL,EAAI,KACvC2U,EAAQ/J,KAAKC,OAAOnF,EAAEuF,GAAK,KAAO,KAClCvF,EAAEuF,IAAc,IAAR0J,EAEVjP,EAAEuF,IAAM0J,EACRjP,EAAE1F,GAAK,EAGT,IADA2U,EAAQ,EACH1J,EAAI,EAAGA,EAAI,GAAIA,IAClBvF,EAAEuF,IAAM0J,GAASjP,EAAE,KAAO,GAAK+O,GAAExJ,GACjC0J,EAAQjP,EAAEuF,IAAM,EAChBvF,EAAEuF,IAAM,IAEV,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKvF,EAAEuF,IAAM0J,EAAQF,GAAExJ,GAC3C,IAAKjL,EAAI,EAAGA,EAAI,GAAIA,IAClB0F,EAAE1F,EAAE,IAAM0F,EAAE1F,IAAM,EAClBc,EAAEd,GAAY,IAAP0F,EAAE1F,GAIb,SAAS4U,GAAO9T,GACd,IAA8Bd,EAA1B0F,EAAI,IAAIb,aAAa,IACzB,IAAK7E,EAAI,EAAGA,EAAI,GAAIA,IAAK0F,EAAE1F,GAAKc,EAAEd,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKc,EAAEd,GAAK,EAChC0U,GAAK5T,EAAG4E,GAIV,SAASmP,GAAYC,EAAI1U,EAAGqB,EAAG8S,GAC7B,IACIvU,EAAGiL,EADH3K,EAAI,IAAIuC,WAAW,IAAK8C,EAAI,IAAI9C,WAAW,IAAK/B,EAAI,IAAI+B,WAAW,IAC7D6C,EAAI,IAAIb,aAAa,IAC3B/C,EAAI,CAAC6C,IAAMA,IAAMA,IAAMA,KAE3BiP,EAAYtT,EAAGiU,EAAI,IACnBjU,EAAE,IAAM,IACRA,EAAE,KAAO,IACTA,EAAE,KAAO,GAET,IAAIyU,EAAQtT,EAAI,GAChB,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAK8U,EAAG,GAAK9U,GAAKI,EAAEJ,GACvC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAK8U,EAAG,GAAK9U,GAAKM,EAAE,GAAKN,GAO7C,IALA4T,EAAY9S,EAAGgU,EAAGxG,SAAS,IAAK7M,EAAE,IAClCmT,GAAO9T,GACPsT,GAAWtS,EAAGhB,GACdiT,GAAKe,EAAIhT,GAEJ9B,EAAI,GAAIA,EAAI,GAAIA,IAAK8U,EAAG9U,GAAKuU,EAAGvU,GAIrC,IAHA4T,EAAYjO,EAAGmP,EAAIrT,EAAI,IACvBmT,GAAOjP,GAEF3F,EAAI,EAAGA,EAAI,GAAIA,IAAK0F,EAAE1F,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAK0F,EAAE1F,GAAKc,EAAEd,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAClB,IAAKiL,EAAI,EAAGA,EAAI,GAAIA,IAClBvF,EAAE1F,EAAEiL,IAAMtF,EAAE3F,GAAKM,EAAE2K,GAKvB,OADAyJ,GAAKI,EAAGxG,SAAS,IAAK5I,GACfqP,EAyCT,SAASC,GAAiB5U,EAAG0U,EAAIrT,EAAG6S,GAClC,IAAItU,EACAkB,EAAI,IAAI2B,WAAW,IAAK8C,EAAI,IAAI9C,WAAW,IAC3Cf,EAAI,CAAC6C,IAAMA,IAAMA,IAAMA,KACvBoG,EAAI,CAACpG,IAAMA,IAAMA,IAAMA,KAE3B,GAAIlD,EAAI,GAAI,OAAQ,EAEpB,GA9CF,SAAmBX,EAAGgB,GACpB,IAAIZ,EAAIyD,IAAMsQ,EAAMtQ,IAAMuQ,EAAMvQ,IAC5BwQ,EAAMxQ,IAAMyQ,EAAOzQ,IAAM0Q,EAAO1Q,IAChC2Q,EAAO3Q,IA2BX,OAzBA6F,EAAS1J,EAAE,GAAIoE,GACfkG,EAAYtK,EAAE,GAAIgB,GAClBiM,EAAEmH,EAAKpU,EAAE,IACTyK,EAAE4J,EAAKD,EAAK9P,GACZkG,EAAE4J,EAAKA,EAAKpU,EAAE,IACduK,EAAE8J,EAAKrU,EAAE,GAAIqU,GAEbpH,EAAEqH,EAAMD,GACRpH,EAAEsH,EAAMD,GACR7J,EAAE+J,EAAMD,EAAMD,GACd7J,EAAErK,EAAGoU,EAAMJ,GACX3J,EAAErK,EAAGA,EAAGiU,GAERlH,EAAQ/M,EAAGA,GACXqK,EAAErK,EAAGA,EAAGgU,GACR3J,EAAErK,EAAGA,EAAGiU,GACR5J,EAAErK,EAAGA,EAAGiU,GACR5J,EAAEzK,EAAE,GAAII,EAAGiU,GAEXpH,EAAEkH,EAAKnU,EAAE,IACTyK,EAAE0J,EAAKA,EAAKE,GACRjK,EAAS+J,EAAKC,IAAM3J,EAAEzK,EAAE,GAAIA,EAAE,GAAI0E,GAEtCuI,EAAEkH,EAAKnU,EAAE,IACTyK,EAAE0J,EAAKA,EAAKE,GACRjK,EAAS+J,EAAKC,IAAc,GAE5B/J,EAASrK,EAAE,MAASgB,EAAE,KAAK,GAAIwJ,EAAExK,EAAE,GAAImE,EAAKnE,EAAE,IAElDyK,EAAEzK,EAAE,GAAIA,EAAE,GAAIA,EAAE,IACT,GAWHyU,CAAUxK,EAAGuJ,GAAK,OAAQ,EAE9B,IAAKtU,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAK8U,EAAG9U,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKI,EAAEJ,EAAE,IAAMsU,EAAGtU,GAUtC,GATA4T,EAAYjO,EAAGvF,EAAGqB,GAClBmT,GAAOjP,GACPwO,GAAWrS,EAAGiJ,EAAGpF,GAEjByO,GAAWrJ,EAAG+J,EAAGxG,SAAS,KAC1BuF,EAAI/R,EAAGiJ,GACPgJ,GAAK7S,EAAGY,GAERL,GAAK,GACDwE,EAAiB6O,EAAI,EAAG5T,EAAG,GAAI,CACjC,IAAKlB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAK,EAC/B,OAAQ,EAGV,IAAKA,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAK8U,EAAG9U,EAAI,IACtC,OAAOyB,EAkFT,SAAS+T,GAAapP,EAAG3E,GACvB,GAhF8B,KAgF1B2E,EAAElE,OAAsC,MAAM,IAAIqB,MAAM,gBAC5D,GAhFgC,KAgF5B9B,EAAES,OAAwC,MAAM,IAAIqB,MAAM,kBAQhE,SAASkS,KACP,IAAK,IAAIzV,EAAI,EAAGA,EAAI0V,UAAUxT,OAAQlC,IACpC,KAAM0V,UAAU1V,aAAc6C,YAC5B,MAAM,IAAI8S,UAAU,mCAI1B,SAASC,GAAQ3R,GACf,IAAK,IAAIjE,EAAI,EAAGA,EAAIiE,EAAI/B,OAAQlC,IAAKiE,EAAIjE,GAAK,EA/EhD0E,EAAKmR,SAAW,CACdtN,qBAAsBA,EACtBU,kBAAmBA,EACnBF,cAAeA,EACfL,0BAA2BA,EAC3BI,sBAAuBA,EACvBkB,mBAAoBA,EACpBI,0BAA2BA,EAC3BpE,iBAAkBA,EAClBC,iBAAkBA,EAClBqE,iBAAkBA,EAClBC,sBAAuBA,EACvB2D,kBAAmBA,EACnBM,uBAAwBA,EACxBE,oBAAqBA,EACrBuC,mBAAoBA,EACpB6E,WAxsBF,SAAoBzV,EAAGD,EAAGE,EAAGmB,EAAGqE,EAAGJ,GACjC,IAAIU,EAAI,IAAIvD,WAAW,IAEvB,OADA6L,EAAoBtI,EAAGN,EAAGJ,GACnBuL,EAAmB5Q,EAAGD,EAAGE,EAAGmB,EAAG2E,IAssBtC2P,gBAnsBF,SAAyB3V,EAAGC,EAAGC,EAAGmB,EAAGqE,EAAGJ,GACtC,IAAIU,EAAI,IAAIvD,WAAW,IAEvB,OADA6L,EAAoBtI,EAAGN,EAAGJ,GACnBwL,EAAwB9Q,EAAGC,EAAGC,EAAGmB,EAAG2E,IAisB3CqI,mBAAoBA,EACpBmF,YAAaA,EACbiB,YAAaA,GACbR,oBAAqBA,GACrBW,iBAAkBA,GAElBgB,0BA1C8B,GA2C9BC,4BA1CgC,GA2ChCC,2BA1C+B,GA2C/BC,8BA1CkC,GA2ClCC,wBA1C4B,GA2C5BC,8BA1CkC,GA2ClCC,0BA1C8B,GA2C9BC,0BA1C8B,GA2C9BC,yBA1C6B,GA2C7BC,sBAlDgC,GAmDhCC,qBAlD+B,GAmD/BC,wBAlDkC,GAmDlCC,kBA1CsB,GA2CtBC,2BA1C+B,GA2C/BC,2BA1C+B,GA2C/BC,sBA1C0B,GA2C1BC,kBA1CsB,GA4CtBrS,GAAIA,EACJS,EAAGA,EACHqP,EAAGA,GACHzJ,UAAWA,EACXI,YAAaA,EACbG,EAAGA,EACHF,EAAGA,EACH0C,EAAGA,EACHzC,EAAGA,EACH2C,QAASA,EACT4F,IAAKA,EACLrJ,SAAUA,EACVkK,KAAMA,GACNP,WAAYA,GACZC,WAAYA,IA0Bd1P,EAAKuS,YAAc,SAASxV,GAC1B,IAAI+B,EAAI,IAAIX,WAAWpB,GAEvB,OADAqD,EAAYtB,EAAG/B,GACR+B,GAGTkB,EAAKwS,UAAY,SAASC,EAAKC,EAAO7V,GACpCkU,GAAgB0B,EAAKC,EAAO7V,GAC5BiU,GAAajU,EAAK6V,GAGlB,IAFA,IAAIhX,EAAI,IAAIyC,WA3GmB,GA2GqBsU,EAAIjV,QACpD7B,EAAI,IAAIwC,WAAWzC,EAAE8B,QAChBlC,EAAI,EAAGA,EAAImX,EAAIjV,OAAQlC,IAAKI,EAAEJ,EA7GR,IA6GwCmX,EAAInX,GAE3E,OADAsK,EAAiBjK,EAAGD,EAAGA,EAAE8B,OAAQkV,EAAO7V,GACjClB,EAAEiO,SA9GyB,KAiHpC5J,EAAKwS,UAAUG,KAAO,SAASC,EAAKF,EAAO7V,GACzCkU,GAAgB6B,EAAKF,EAAO7V,GAC5BiU,GAAajU,EAAK6V,GAGlB,IAFA,IAAI/W,EAAI,IAAIwC,WApHsB,GAoHqByU,EAAIpV,QACvD9B,EAAI,IAAIyC,WAAWxC,EAAE6B,QAChBlC,EAAI,EAAGA,EAAIsX,EAAIpV,OAAQlC,IAAKK,EAAEL,EAtHL,IAsHwCsX,EAAItX,GAC9E,OAAIK,EAAE6B,OAAS,IAC2C,IAAtDqI,EAAsBnK,EAAGC,EAAGA,EAAE6B,OAAQkV,EAAO7V,GADvB,KAEnBnB,EAAEkO,SA1HsB,KA6HjC5J,EAAKwS,UAAUK,UA/HiB,GAgIhC7S,EAAKwS,UAAUM,YA/HmB,GAgIlC9S,EAAKwS,UAAUO,eA9HqB,GAgIpC/S,EAAKgT,WAAa,SAASjW,EAAGK,GAE5B,GADA2T,GAAgBhU,EAAGK,GA/He,KAgI9BL,EAAES,OAA0C,MAAM,IAAIqB,MAAM,cAChE,GAlI4B,KAkIxBzB,EAAEI,OAAoC,MAAM,IAAIqB,MAAM,cAC1D,IAAIwH,EAAI,IAAIlI,WAnIgB,IAqI5B,OADAqL,EAAkBnD,EAAGtJ,EAAGK,GACjBiJ,GAGTrG,EAAKgT,WAAWC,KAAO,SAASlW,GAE9B,GADAgU,GAAgBhU,GAxIkB,KAyI9BA,EAAES,OAA0C,MAAM,IAAIqB,MAAM,cAChE,IAAIwH,EAAI,IAAIlI,WA3IgB,IA6I5B,OADA2L,EAAuBzD,EAAGtJ,GACnBsJ,GAGTrG,EAAKgT,WAAWE,aA/IoB,GAgJpClT,EAAKgT,WAAWG,mBAjJc,GAmJ9BnT,EAAK4S,IAAM,SAASH,EAAKC,EAAOU,EAAWC,GACzC,IAAI3R,EAAI1B,EAAK4S,IAAIU,OAAOF,EAAWC,GACnC,OAAOrT,EAAKwS,UAAUC,EAAKC,EAAOhR,IAGpC1B,EAAK4S,IAAIU,OAAS,SAASF,EAAWC,GACpCtC,GAAgBqC,EAAWC,GAzE7B,SAAyBzD,EAAIC,GAC3B,GA/E8B,KA+E1BD,EAAGpS,OAAsC,MAAM,IAAIqB,MAAM,uBAC7D,GA/E8B,KA+E1BgR,EAAGrS,OAAsC,MAAM,IAAIqB,MAAM,uBAwE7D0U,CAAgBH,EAAWC,GAC3B,IAAI3R,EAAI,IAAIvD,WAvJiB,IAyJ7B,OADA6L,EAAoBtI,EAAG0R,EAAWC,GAC3B3R,GAGT1B,EAAK4S,IAAIY,MAAQxT,EAAKwS,UAEtBxS,EAAK4S,IAAID,KAAO,SAASF,EAAKC,EAAOU,EAAWC,GAC9C,IAAI3R,EAAI1B,EAAK4S,IAAIU,OAAOF,EAAWC,GACnC,OAAOrT,EAAKwS,UAAUG,KAAKF,EAAKC,EAAOhR,IAGzC1B,EAAK4S,IAAID,KAAKa,MAAQxT,EAAKwS,UAAUG,KAErC3S,EAAK4S,IAAIa,QAAU,WACjB,IAAI7D,EAAK,IAAIzR,WAxKiB,IAyK1B0R,EAAK,IAAI1R,WAxKiB,IA0K9B,OADA4L,EAAmB6F,EAAIC,GAChB,CAACuD,UAAWxD,EAAIyD,UAAWxD,IAGpC7P,EAAK4S,IAAIa,QAAQC,cAAgB,SAASL,GAExC,GADAtC,GAAgBsC,GA9Kc,KA+K1BA,EAAU7V,OACZ,MAAM,IAAIqB,MAAM,uBAClB,IAAI+Q,EAAK,IAAIzR,WAlLiB,IAoL9B,OADA2L,EAAuB8F,EAAIyD,GACpB,CAACD,UAAWxD,EAAIyD,UAAW,IAAIlV,WAAWkV,KAGnDrT,EAAK4S,IAAIe,gBAvLuB,GAwLhC3T,EAAK4S,IAAIgB,gBAvLuB,GAwLhC5T,EAAK4S,IAAIiB,gBAvLsB,GAwL/B7T,EAAK4S,IAAIE,YA/LyB,GAgMlC9S,EAAK4S,IAAIG,eAAiB/S,EAAKwS,UAAUO,eAEzC/S,EAAK8T,KAAO,SAASrB,EAAKY,GAExB,GADAtC,GAAgB0B,EAAKY,GAtLU,KAuL3BA,EAAU7V,OACZ,MAAM,IAAIqB,MAAM,uBAClB,IAAIkV,EAAY,IAAI5V,WA3LE,GA2L2BsU,EAAIjV,QAErD,OADA2S,GAAY4D,EAAWtB,EAAKA,EAAIjV,OAAQ6V,GACjCU,GAGT/T,EAAK8T,KAAKnB,KAAO,SAASoB,EAAWX,GAEnC,GADArC,GAAgBgD,EAAWX,GAhMI,KAiM3BA,EAAU5V,OACZ,MAAM,IAAIqB,MAAM,uBAClB,IAAImV,EAAM,IAAI7V,WAAW4V,EAAUvW,QAC/ByW,EAAO3D,GAAiB0D,EAAKD,EAAWA,EAAUvW,OAAQ4V,GAC9D,GAAIa,EAAO,EAAG,OAAO,KAErB,IADA,IAAIvY,EAAI,IAAIyC,WAAW8V,GACd3Y,EAAI,EAAGA,EAAII,EAAE8B,OAAQlC,IAAKI,EAAEJ,GAAK0Y,EAAI1Y,GAC9C,OAAOI,GAGTsE,EAAK8T,KAAKI,SAAW,SAASzB,EAAKY,GAGjC,IAFA,IAAIU,EAAY/T,EAAK8T,KAAKrB,EAAKY,GAC3Bc,EAAM,IAAIhW,WA9MQ,IA+Mb7C,EAAI,EAAGA,EAAI6Y,EAAI3W,OAAQlC,IAAK6Y,EAAI7Y,GAAKyY,EAAUzY,GACxD,OAAO6Y,GAGTnU,EAAK8T,KAAKI,SAASE,OAAS,SAAS3B,EAAK0B,EAAKf,GAE7C,GADArC,GAAgB0B,EAAK0B,EAAKf,GApNJ,KAqNlBe,EAAI3W,OACN,MAAM,IAAIqB,MAAM,sBAClB,GAtN+B,KAsN3BuU,EAAU5V,OACZ,MAAM,IAAIqB,MAAM,uBAClB,IAEIvD,EAFA8U,EAAK,IAAIjS,WAzNS,GAyNsBsU,EAAIjV,QAC5C9B,EAAI,IAAIyC,WA1NU,GA0NqBsU,EAAIjV,QAE/C,IAAKlC,EAAI,EAAGA,EA5NU,GA4NaA,IAAK8U,EAAG9U,GAAK6Y,EAAI7Y,GACpD,IAAKA,EAAI,EAAGA,EAAImX,EAAIjV,OAAQlC,IAAK8U,EAAG9U,EA7Nd,IA6NqCmX,EAAInX,GAC/D,OAAQgV,GAAiB5U,EAAG0U,EAAIA,EAAG5S,OAAQ4V,IAAc,GAG3DpT,EAAK8T,KAAKL,QAAU,WAClB,IAAI7D,EAAK,IAAIzR,WAjOkB,IAkO3B0R,EAAK,IAAI1R,WAjOkB,IAmO/B,OADAwR,GAAoBC,EAAIC,GACjB,CAACuD,UAAWxD,EAAIyD,UAAWxD,IAGpC7P,EAAK8T,KAAKL,QAAQC,cAAgB,SAASL,GAEzC,GADAtC,GAAgBsC,GAvOe,KAwO3BA,EAAU7V,OACZ,MAAM,IAAIqB,MAAM,uBAElB,IADA,IAAI+Q,EAAK,IAAIzR,WA3OkB,IA4OtB7C,EAAI,EAAGA,EAAIsU,EAAGpS,OAAQlC,IAAKsU,EAAGtU,GAAK+X,EAAU,GAAG/X,GACzD,MAAO,CAAC8X,UAAWxD,EAAIyD,UAAW,IAAIlV,WAAWkV,KAGnDrT,EAAK8T,KAAKL,QAAQY,SAAW,SAASC,GAEpC,GADAvD,GAAgBuD,GA/OU,KAgPtBA,EAAK9W,OACP,MAAM,IAAIqB,MAAM,iBAGlB,IAFA,IAAI+Q,EAAK,IAAIzR,WApPkB,IAqP3B0R,EAAK,IAAI1R,WApPkB,IAqPtB7C,EAAI,EAAGA,EAAI,GAAIA,IAAKuU,EAAGvU,GAAKgZ,EAAKhZ,GAE1C,OADAqU,GAAoBC,EAAIC,GAAI,GACrB,CAACuD,UAAWxD,EAAIyD,UAAWxD,IAGpC7P,EAAK8T,KAAKH,gBA3PuB,GA4PjC3T,EAAK8T,KAAKF,gBA3PuB,GA4PjC5T,EAAK8T,KAAKS,WA3PkB,GA4P5BvU,EAAK8T,KAAKU,gBA/Pc,GAiQxBxU,EAAKyU,KAAO,SAAShC,GACnB1B,GAAgB0B,GAChB,IAAIxR,EAAI,IAAI9C,WA/PU,IAiQtB,OADA+Q,EAAYjO,EAAGwR,EAAKA,EAAIjV,QACjByD,GAGTjB,EAAKyU,KAAKC,WApQc,GAsQxB1U,EAAKoU,OAAS,SAASpT,EAAGI,GAGxB,OAFA2P,GAAgB/P,EAAGI,GAEF,IAAbJ,EAAExD,QAA6B,IAAb4D,EAAE5D,SACpBwD,EAAExD,SAAW4D,EAAE5D,QACkB,IAA7B0D,EAAGF,EAAG,EAAGI,EAAG,EAAGJ,EAAExD,UAG3BwC,EAAK2U,QAAU,SAASC,GACtBxU,EAAcwU,GAGhB,WAGE,IAAIC,EAAyB,oBAATC,KAAwBA,KAAKD,QAAUC,KAAKC,SAAY,KAC5E,GAAIF,GAAUA,EAAOG,gBAAiB,CAGpChV,EAAK2U,SAAQ,SAAS3T,EAAGjE,GACvB,IAAIzB,EAAG2K,EAAI,IAAI9H,WAAWpB,GAC1B,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,GAHT,MAIRuZ,EAAOG,gBAAgB/O,EAAE2D,SAAStO,EAAGA,EAAI4K,KAAKxG,IAAI3C,EAAIzB,EAJ9C,SAMV,IAAKA,EAAI,EAAGA,EAAIyB,EAAGzB,IAAK0F,EAAE1F,GAAK2K,EAAE3K,GACjC4V,GAAQjL,WAIV4O,EAAS,EAAQ,KACHA,EAAOtC,aACnBvS,EAAK2U,SAAQ,SAAS3T,EAAGjE,GACvB,IAAIzB,EAAG2K,EAAI4O,EAAOtC,YAAYxV,GAC9B,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAK0F,EAAE1F,GAAK2K,EAAE3K,GACjC4V,GAAQjL,MAtBhB,GA1zEA,CAs1EoClL,EAAOD,QAAUC,EAAOD,QAAWga,KAAK9U,KAAO8U,KAAK9U,MAAQ,K,gBCt1EhGjF,EAAOD,QAAU,EAAQ,GAA4Bma,S,6FCkB9C,MAAMC,EAKX,YAAYC,EAAgBtZ,GAC1B4B,KAAK2X,OAAS,EACd3X,KAAK0X,OAASA,EACd1X,KAAK5B,KAAOA,EAGd,OAAOwZ,GACL5X,KAAK2X,SAEL,IAAIE,EAAS7X,KAAK2X,OACdG,EAAK9X,KAAK0X,OAASG,EACnBzZ,EAAO4B,KAAK5B,KAAO,IAAMyZ,EAAS,IAElCE,GAAS,EACTC,EAAkB,WACfD,IACHH,EAASK,MAAM,KAAM1E,WACrBwE,GAAS,IAKb,OADA/X,KAAK6X,GAAUG,EACR,CAAEH,OAAQA,EAAQC,GAAIA,EAAI1Z,KAAMA,EAAMwZ,SAAUI,GAGzD,OAAOE,UACElY,KAAKkY,EAASL,SAIlB,IAAIM,EAAkB,IAAIV,EAC/B,kBACA,0BCUa,EAnCe,CAC5BW,QAAS,QACTC,SAAU,EAEVC,OAAQ,GACRC,QAAS,IACTC,OAAQ,GAERC,SAAU,oBACVC,SAAU,GACVC,UAAW,IACXC,SAAU,UAEVC,WAAY,mBAEZC,aAAc,eACdC,cAAe,OACfC,gBAAiB,KACjBC,YAAa,IACbC,mBAAoB,IACpBC,mBAAoB,CAClBC,SAAU,oBACVC,UAAW,QAEbC,qBAAsB,CACpBF,SAAU,eACVC,UAAW,QAIbE,SAAU,uBACVC,UAAW,wBACXC,kBAAmB,IC1Dd,IAAIC,EAAwB,IAAIjC,EACrC,uBACA,gCAGSkC,EAAe,ICaX,MAKb,YAAYC,GACV5Z,KAAK4Z,QAAUA,EACf5Z,KAAK6Z,UAAYD,EAAQC,WAAa1B,EACtCnY,KAAK8Z,QAAU,GAQjB,KAAK1b,EAAcwb,EAAchC,GAC/B,IAAIP,EAAOrX,KAEX,GAAIqX,EAAKyC,QAAQ1b,IAASiZ,EAAKyC,QAAQ1b,GAAM2B,OAAS,EACpDsX,EAAKyC,QAAQ1b,GAAMiE,KAAKuV,OACnB,CACLP,EAAKyC,QAAQ1b,GAAQ,CAACwZ,GAEtB,IAAImC,EAAU,GAAQC,oBAAoB3C,EAAK4C,QAAQ7b,EAAMwb,IACzD1B,EAAWb,EAAKwC,UAAU1a,QAAO,SAAU+a,GAG7C,GAFA7C,EAAKwC,UAAUM,OAAOjC,GAElBb,EAAKyC,QAAQ1b,GAAO,CACtB,IAAIgc,EAAY/C,EAAKyC,QAAQ1b,UACtBiZ,EAAKyC,QAAQ1b,GAOpB,IALA,IAAIic,EAAkB,SAAUC,GACzBA,GACHP,EAAQtG,WAGH5V,EAAI,EAAGA,EAAIuc,EAAUra,OAAQlC,IACpCuc,EAAUvc,GAAGqc,EAAOG,OAI1BN,EAAQQ,KAAKrC,IAQjB,QAAQ0B,GACN,IACIY,EAAW,GAAQC,cAAcC,SAASF,SAO9C,OANKZ,GAAWA,EAAQe,QAAwB,WAAbH,EAC3Bxa,KAAK4Z,QAAQJ,UAEbxZ,KAAK4Z,QAAQL,UAGVqB,QAAQ,OAAQ,IAAM,IAAM5a,KAAK4Z,QAAQiB,QAQtD,QAAQzc,EAAcwb,GACpB,OAAO5Z,KAAK8a,QAAQlB,GAAW,IAAMxb,EAAO4B,KAAK4Z,QAAQmB,OAAS,QDjFvB,CAC7CxB,SAAU,EAASA,SACnBC,UAAW,EAASA,UACpBqB,QAAS,EAASzC,QAClB2C,OAAQ,EAAStB,kBACjBI,UAAWH,IEVb,MAAMsB,EAAW,CACfC,QAAS,qBACTC,KAAM,CACJC,uBAAwB,CACtBC,KAAM,kDAERC,sBAAuB,CACrBD,KAAM,gDAERE,qBAAsB,CACpBF,KAAM,gCAERG,uBAAwB,CACtBH,KAAM,uDAERI,wBAAyB,CACvBC,QACE,iHA0BO,IC/CHC,ED+CG,EAhBQ,SAAUtc,GAC/B,MACMuc,EAASX,EAASE,KAAK9b,GAC7B,IAAKuc,EAAQ,MAAO,GAEpB,IAAIC,EAOJ,OANID,EAAOF,QACTG,EAAMD,EAAOF,QACJE,EAAOP,OAChBQ,EAAMZ,EAASC,QAAUU,EAAOP,MAG7BQ,EACE,QAAgBA,EADN,KC3CnB,SAAYF,GACV,2CACA,+CAFF,CAAYA,MAAe,KCEpB,MAAMG,UAAqBza,MAChC,YAAY4T,GACV8G,MAAM9G,GAENzW,OAAOwd,eAAe/b,gBAAiBP,YAIpC,MAAMuc,UAAuB5a,MAClC,YAAY4T,GACV8G,MAAM9G,GAENzW,OAAOwd,eAAe/b,gBAAiBP,YAIpC,MAAMwc,UAAwB7a,MACnC,YAAY4T,GACV8G,MAAM9G,GAENzW,OAAOwd,eAAe/b,gBAAiBP,YAGpC,MAAMyc,UAAgC9a,MAC3C,YAAY4T,GACV8G,MAAM9G,GAENzW,OAAOwd,eAAe/b,gBAAiBP,YAGpC,MAAM0c,UAAwB/a,MACnC,YAAY4T,GACV8G,MAAM9G,GAENzW,OAAOwd,eAAe/b,gBAAiBP,YAGpC,MAAM2c,UAA2Bhb,MACtC,YAAY4T,GACV8G,MAAM9G,GAENzW,OAAOwd,eAAe/b,gBAAiBP,YAGpC,MAAM4c,UAA6Bjb,MACxC,YAAY4T,GACV8G,MAAM9G,GAENzW,OAAOwd,eAAe/b,gBAAiBP,YAGpC,MAAM6c,UAA4Blb,MACvC,YAAY4T,GACV8G,MAAM9G,GAENzW,OAAOwd,eAAe/b,gBAAiBP,YAGpC,MAAM8c,UAAsBnb,MAEjC,YAAYob,EAAgBxH,GAC1B8G,MAAM9G,GACNhV,KAAKwc,OAASA,EAEdje,OAAOwd,eAAe/b,gBAAiBP,YCuB5B,MA3Ea,SAC1Bgd,EACAC,EACAC,EACAC,EACAhF,GAEA,MAAMiF,EAAM,GAAQC,YAKpB,IAAK,IAAIC,KAJTF,EAAI3H,KAAK,OAAQyH,EAAYvD,UAAU,GAGvCyD,EAAIG,iBAAiB,eAAgB,qCACdL,EAAYM,QACjCJ,EAAIG,iBAAiBD,EAAYJ,EAAYM,QAAQF,IAEvD,GAAmC,MAA/BJ,EAAYO,gBAAyB,CACvC,IAAIC,EAAiBR,EAAYO,kBACjC,IAAK,IAAIH,KAAcI,EACrBN,EAAIG,iBAAiBD,EAAYI,EAAeJ,IAsDpD,OAlDAF,EAAIO,mBAAqB,WACvB,GAAuB,IAAnBP,EAAIQ,WACN,GAAmB,MAAfR,EAAIL,OAAgB,CACtB,IAAItc,EACAod,GAAS,EAEb,IACEpd,EAAOqd,KAAKC,MAAMX,EAAIY,cACtBH,GAAS,EACT,MAAOtR,GACP4L,EACE,IAAI2E,EACF,IACA,sBAAsBK,EAAgBc,uEACpCb,EAAIY,gBAGR,MAIAH,GAEF1F,EAAS,KAAM1X,OAEZ,CACL,IAAI6a,EAAS,GACb,OAAQ6B,GACN,KAAKlB,EAAgBiC,mBACnB5C,EAAS,EAAwB,0BACjC,MACF,KAAKW,EAAgBkC,qBACnB7C,EAAS,oEAAoE,EAC3E,yBAINnD,EACE,IAAI2E,EACFM,EAAIL,OACJ,uCAAuCI,EAAgBc,0CACjCb,EAAIL,eAAeG,EAAYvD,aAAa2B,KAEpE,QAMR8B,EAAItC,KAAKmC,GACFG,GC5ET,IANA,IAAIrb,EAAeD,OAAOC,aAEtBqc,EACF,mEACEC,EAAS,GAEJ,EAAI,EAAGhgB,EAAI+f,EAAS9d,OAAQ,EAAIjC,EAAG,IAC1CggB,EAAOD,EAASE,OAAO,IAAM,EAG/B,IAAIC,EAAU,SAAU9f,GACtB,IAAI+f,EAAK/f,EAAEiD,WAAW,GACtB,OAAO8c,EAAK,IACR/f,EACA+f,EAAK,KACHzc,EAAa,IAAQyc,IAAO,GAAMzc,EAAa,IAAa,GAALyc,GACvDzc,EAAa,IAASyc,IAAO,GAAM,IACnCzc,EAAa,IAASyc,IAAO,EAAK,IAClCzc,EAAa,IAAa,GAALyc,IAGzBC,EAAO,SAAUha,GACnB,OAAOA,EAAE0W,QAAQ,gBAAiBoD,IAGhCG,EAAY,SAAUC,GACxB,IAAIC,EAAS,CAAC,EAAG,EAAG,GAAGD,EAAIre,OAAS,GAChCue,EACDF,EAAIjd,WAAW,IAAM,IACpBid,EAAIre,OAAS,EAAIqe,EAAIjd,WAAW,GAAK,IAAM,GAC5Cid,EAAIre,OAAS,EAAIqe,EAAIjd,WAAW,GAAK,GAOxC,MANY,CACV0c,EAASE,OAAOO,IAAQ,IACxBT,EAASE,OAAQO,IAAQ,GAAM,IAC/BD,GAAU,EAAI,IAAMR,EAASE,OAAQO,IAAQ,EAAK,IAClDD,GAAU,EAAI,IAAMR,EAASE,OAAa,GAANO,IAEzBhc,KAAK,KAGhBic,EACF,OAAOA,MACP,SAAUld,GACR,OAAOA,EAAEuZ,QAAQ,eAAgBuD,ICTtB,MAnCf,MAIE,YACEK,EACAC,EACAC,EACA9G,GAEA5X,KAAKye,MAAQA,EACbze,KAAK2e,MAAQH,EAAI,KACXxe,KAAK2e,QACP3e,KAAK2e,MAAQ/G,EAAS5X,KAAK2e,SAE5BD,GAOL,YACE,OAAsB,OAAf1e,KAAK2e,MAId,gBACM3e,KAAK2e,QACP3e,KAAKye,MAAMze,KAAK2e,OAChB3e,KAAK2e,MAAQ,QC5BnB,SAAS,EAAaA,GACpB,OAAOC,aAAaD,GAEtB,SAAS,EAAcA,GACrB,OAAOE,cAAcF,GAQhB,MAAM,UAAoB,EAC/B,YAAYD,EAAc9G,GACxBkE,MAAMgD,WAAY,EAAcJ,GAAO,SAAUC,GAE/C,OADA/G,IACO,SAUN,MAAM,UAAsB,EACjC,YAAY8G,EAAc9G,GACxBkE,MAAMiD,YAAa,EAAeL,GAAO,SAAUC,GAEjD,OADA/G,IACO+G,MC/Bb,IA6Be,EA7BJ,CACTK,IAAG,IACGC,KAAKD,IACAC,KAAKD,OAEL,IAAIC,MAAOC,UAItBC,MAAMvH,GACG,IAAI,EAAY,EAAGA,GAW5B,OAAOxZ,KAAiBghB,GACtB,IAAIC,EAAiBC,MAAM7f,UAAU8f,MAAMvhB,KAAKuV,UAAW,GAC3D,OAAO,SAAUhU,GACf,OAAOA,EAAOnB,GAAM6Z,MAAM1Y,EAAQ8f,EAAeG,OAAOjM,eCXvD,SAASkM,EAAUC,KAAgBC,GACxC,IAAK,IAAI9hB,EAAI,EAAGA,EAAI8hB,EAAQ5f,OAAQlC,IAAK,CACvC,IAAI+hB,EAAaD,EAAQ9hB,GACzB,IAAK,IAAI2B,KAAYogB,EAEjBA,EAAWpgB,IACXogB,EAAWpgB,GAAUqgB,aACrBD,EAAWpgB,GAAUqgB,cAAgBthB,OAErCmhB,EAAOlgB,GAAYigB,EAAOC,EAAOlgB,IAAa,GAAIogB,EAAWpgB,IAE7DkgB,EAAOlgB,GAAYogB,EAAWpgB,GAIpC,OAAOkgB,EAGF,SAASI,IAEd,IADA,IAAI7hB,EAAI,CAAC,UACAJ,EAAI,EAAGA,EAAI0V,UAAUxT,OAAQlC,IACR,iBAAjB0V,UAAU1V,GACnBI,EAAEoE,KAAKkR,UAAU1V,IAEjBI,EAAEoE,KAAK0d,EAAkBxM,UAAU1V,KAGvC,OAAOI,EAAEqE,KAAK,OAGT,SAAS0d,EAAaC,EAAcC,GAEzC,IAAIC,EAAgBb,MAAM7f,UAAU2gB,QACpC,GAAc,OAAVH,EACF,OAAQ,EAEV,GAAIE,GAAiBF,EAAMG,UAAYD,EACrC,OAAOF,EAAMG,QAAQF,GAEvB,IAAK,IAAIriB,EAAI,EAAGC,EAAImiB,EAAMlgB,OAAQlC,EAAIC,EAAGD,IACvC,GAAIoiB,EAAMpiB,KAAOqiB,EACf,OAAOriB,EAGX,OAAQ,EAaH,SAASwiB,EAAY9gB,EAAa0M,GACvC,IAAK,IAAI7M,KAAOG,EACVhB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQH,IAC/C6M,EAAE1M,EAAOH,GAAMA,EAAKG,GAUnB,SAAS+gB,EAAK/gB,GACnB,IAAI+gB,EAAO,GAIX,OAHAD,EAAY9gB,GAAQ,SAAUghB,EAAGnhB,GAC/BkhB,EAAKje,KAAKjD,MAELkhB,EA0BF,SAASrI,EAAMgI,EAAchU,EAAawQ,GAC/C,IAAK,IAAI5e,EAAI,EAAGA,EAAIoiB,EAAMlgB,OAAQlC,IAChCoO,EAAEjO,KAAKye,GAAW,OAAQwD,EAAMpiB,GAAIA,EAAGoiB,GAepC,SAASO,EAAIP,EAAchU,GAEhC,IADA,IAAI3K,EAAS,GACJzD,EAAI,EAAGA,EAAIoiB,EAAMlgB,OAAQlC,IAChCyD,EAAOe,KAAK4J,EAAEgU,EAAMpiB,GAAIA,EAAGoiB,EAAO3e,IAEpC,OAAOA,EAiCF,SAASmf,EAAOR,EAAcS,GACnCA,EACEA,GACA,SAAU5hB,GACR,QAASA,GAIb,IADA,IAAIwC,EAAS,GACJzD,EAAI,EAAGA,EAAIoiB,EAAMlgB,OAAQlC,IAC5B6iB,EAAKT,EAAMpiB,GAAIA,EAAGoiB,EAAO3e,IAC3BA,EAAOe,KAAK4d,EAAMpiB,IAGtB,OAAOyD,EAcF,SAASqf,EAAaphB,EAAgBmhB,GAC3C,IAAIpf,EAAS,GAMb,OALA+e,EAAY9gB,GAAQ,SAAUT,EAAOM,IAC9BshB,GAAQA,EAAK5hB,EAAOM,EAAKG,EAAQ+B,IAAYsf,QAAQ9hB,MACxDwC,EAAOlC,GAAON,MAGXwC,EA0BF,SAASuf,EAAIZ,EAAcS,GAChC,IAAK,IAAI7iB,EAAI,EAAGA,EAAIoiB,EAAMlgB,OAAQlC,IAChC,GAAI6iB,EAAKT,EAAMpiB,GAAIA,EAAGoiB,GACpB,OAAO,EAGX,OAAO,EAsBF,SAASa,EAAmB5gB,GACjC,OA5GqC+L,EA4Gd,SAAUnN,GAI/B,MAHqB,iBAAVA,IACTA,EAAQihB,EAAkBjhB,IAErBiiB,oBJ1QoBnhB,EI0QYd,EAAM4e,WJzQxCa,EAAKL,EAAKte,MADJ,IAAgBA,GI2JzB0B,EAAS,GACb+e,EA0GiBngB,GA1GG,SAAUpB,EAAOM,GACnCkC,EAAOlC,GAAO6M,EAAEnN,MAEXwC,EALF,IAAgC2K,EACjC3K,EAmHC,SAAS0f,EAAiB9gB,GAC/B,IAxDsBX,EAClB+B,EAuDA2f,EAASN,EAAazgB,GAAM,SAAUpB,GACxC,YAAiBoiB,IAAVpiB,KAQT,OALY0hB,GA5DUjhB,EA6DZuhB,EAAmBG,GA5DzB3f,EAAS,GACb+e,EAAY9gB,GAAQ,SAAUT,EAAOM,GACnCkC,EAAOe,KAAK,CAACjD,EAAKN,OAEbwC,GAyDL,EAAK6f,OAAO,OAAQ,MACpB7e,KAAK,KAoEF,SAASyd,EAAkBqB,GAChC,IACE,OAAO7D,KAAKuC,UAAUsB,GACtB,MAAOpV,GACP,OAAOuR,KAAKuC,WAzDVuB,EAAU,GACZC,EAAQ,GAEH,SAAUC,EAAMziB,EAAOsc,GAC5B,IAAIvd,EAAGO,EAAMojB,EAEb,cAAe1iB,GACb,IAAK,SACH,IAAKA,EACH,OAAO,KAET,IAAKjB,EAAI,EAAGA,EAAIwjB,EAAQthB,OAAQlC,GAAK,EACnC,GAAIwjB,EAAQxjB,KAAOiB,EACjB,MAAO,CAAE2iB,KAAMH,EAAMzjB,IAOzB,GAHAwjB,EAAQhf,KAAKvD,GACbwiB,EAAMjf,KAAK+Y,GAEoC,mBAA3C7c,OAAOkB,UAAUie,SAASzF,MAAMnZ,GAElC,IADA0iB,EAAK,GACA3jB,EAAI,EAAGA,EAAIiB,EAAMiB,OAAQlC,GAAK,EACjC2jB,EAAG3jB,GAAK0jB,EAAMziB,EAAMjB,GAAIud,EAAO,IAAMvd,EAAI,UAI3C,IAAKO,KADLojB,EAAK,GACQ1iB,EACPP,OAAOkB,UAAUC,eAAe1B,KAAKc,EAAOV,KAC9CojB,EAAGpjB,GAAQmjB,EACTziB,EAAMV,GACNgd,EAAO,IAAMmC,KAAKuC,UAAU1hB,GAAQ,MAK5C,OAAOojB,EACT,IAAK,SACL,IAAK,SACL,IAAK,UACH,OAAO1iB,GArCN,CAsD+BsiB,EAf3B,OA3CN,IACDC,EACFC,EClPW,UAjDf,oBAaU,KAAAI,UAAaC,IACf,OAAOC,SAAW,OAAOA,QAAQC,KACnC,OAAOD,QAAQC,IAAIF,IAdvB,SAASvC,GACPpf,KAAK6hB,IAAI7hB,KAAK0hB,UAAWtC,GAG3B,QAAQA,GACNpf,KAAK6hB,IAAI7hB,KAAK8hB,cAAe1C,GAG/B,SAASA,GACPpf,KAAK6hB,IAAI7hB,KAAK+hB,eAAgB3C,GASxB,cAAcuC,GAChB,OAAOC,SAAW,OAAOA,QAAQI,KACnC,OAAOJ,QAAQI,KAAKL,GAEpB3hB,KAAK0hB,UAAUC,GAIX,eAAeA,GACjB,OAAOC,SAAW,OAAOA,QAAQ1H,MACnC,OAAO0H,QAAQ1H,MAAMyH,GAErB3hB,KAAK8hB,cAAcH,GAIf,IACNM,KACG7C,GAEH,IAAIuC,EAAU7B,EAAU7H,MAAMjY,KAAMuT,WACpC,GAAI,GAAOsO,IACT,GAAOA,IAAIF,QACN,GAAI,GAAOO,aAAc,CAClBD,EAAuB5iB,KAAKW,KACxC6hB,CAAIF,MCGK,EAvCY,SACzBlF,EACAC,EACAC,EACAC,EACAhF,QAG0BsJ,IAAxBvE,EAAYM,SACmB,MAA/BN,EAAYO,iBAEZ,EAAO8E,KACL,4BAA4BpF,EAAgBc,6DAIhD,IAAIyE,EAAe1F,EAAQ2F,mBAAmB1E,WAC9CjB,EAAQ2F,qBAER,IAAIC,EAAW5F,EAAQhC,cACnB6H,EAASD,EAASE,cAAc,UAEpC9F,EAAQ+F,eAAeL,GAAgB,SAAUjiB,GAC/C0X,EAAS,KAAM1X,IAGjB,IAAIuiB,EAAgB,0BAA4BN,EAAe,KAC/DG,EAAOI,IACL/F,EAAYvD,SACZ,aACA2H,mBAAmB0B,GACnB,IACA/F,EAEF,IAAIiG,EACFN,EAASO,qBAAqB,QAAQ,IAAMP,EAASQ,gBACvDF,EAAKG,aAAaR,EAAQK,EAAKI,aCpClB,MAAMC,EAKnB,YAAYN,GACV1iB,KAAK0iB,IAAMA,EAGb,KAAKxK,GACH,IAAIb,EAAOrX,KACPijB,EAAc,iBAAmB5L,EAAKqL,IAE1CrL,EAAKiL,OAASD,SAASE,cAAc,UACrClL,EAAKiL,OAAOxK,GAAKI,EAASJ,GAC1BT,EAAKiL,OAAOI,IAAMrL,EAAKqL,IACvBrL,EAAKiL,OAAOY,KAAO,kBACnB7L,EAAKiL,OAAOa,QAAU,QAElB9L,EAAKiL,OAAOc,kBACd/L,EAAKiL,OAAOe,QAAU,WACpBnL,EAASN,SAASqL,IAEpB5L,EAAKiL,OAAOgB,OAAS,WACnBpL,EAASN,SAAS,QAGpBP,EAAKiL,OAAOlF,mBAAqB,WAEF,WAA3B/F,EAAKiL,OAAOjF,YACe,aAA3BhG,EAAKiL,OAAOjF,YAEZnF,EAASN,SAAS,YAOAsJ,IAAtB7J,EAAKiL,OAAOiB,OACNlB,SAAUmB,aAChB,SAAS9C,KAAK+C,UAAUC,YAExBrM,EAAKsM,YAActB,SAASE,cAAc,UAC1ClL,EAAKsM,YAAY7L,GAAKI,EAASJ,GAAK,SACpCT,EAAKsM,YAAYC,KAAO1L,EAAS9Z,KAAO,KAAO6kB,EAAc,MAC7D5L,EAAKiL,OAAOiB,MAAQlM,EAAKsM,YAAYJ,OAAQ,GAE7ClM,EAAKiL,OAAOiB,OAAQ,EAGtB,IAAIZ,EAAON,SAASO,qBAAqB,QAAQ,GACjDD,EAAKG,aAAazL,EAAKiL,OAAQK,EAAKI,YAChC1L,EAAKsM,aACPhB,EAAKG,aAAazL,EAAKsM,YAAatM,EAAKiL,OAAOuB,aAKpD,UACM7jB,KAAKsiB,SACPtiB,KAAKsiB,OAAOgB,OAAStjB,KAAKsiB,OAAOe,QAAU,KAC3CrjB,KAAKsiB,OAAOlF,mBAAqB,MAE/Bpd,KAAKsiB,QAAUtiB,KAAKsiB,OAAOwB,YAC7B9jB,KAAKsiB,OAAOwB,WAAWC,YAAY/jB,KAAKsiB,QAEtCtiB,KAAK2jB,aAAe3jB,KAAK2jB,YAAYG,YACvC9jB,KAAK2jB,YAAYG,WAAWC,YAAY/jB,KAAK2jB,aAE/C3jB,KAAKsiB,OAAS,KACdtiB,KAAK2jB,YAAc,MC9DR,MAAM,EAKnB,YAAY/H,EAAa1b,GACvBF,KAAK4b,IAAMA,EACX5b,KAAKE,KAAOA,EAOd,KAAKgY,GACH,IAAIlY,KAAK+Z,QAAT,CAIA,IAAI2C,EAAQ,EAA6B1c,KAAKE,MAC1C0b,EAAM5b,KAAK4b,IAAM,IAAM1D,EAASL,OAAS,IAAM6E,EACnD1c,KAAK+Z,QAAU,GAAQC,oBAAoB4B,GAC3C5b,KAAK+Z,QAAQQ,KAAKrC,IAIpB,UACMlY,KAAK+Z,SACP/Z,KAAK+Z,QAAQtG,WC1CnB,IA2Be,EALH,CACVrV,KAAM,QACN4lB,SAxBa,SAAUC,EAAwBtJ,GAC/C,OAAO,SAAUza,EAAW0X,GAC1B,IACIgE,EADS,QAAUjB,EAAS,IAAM,IAAM,OAEhCsJ,EAAOC,MAAQD,EAAOrK,QAAQsK,MAAQD,EAAOrK,QAAQwB,KAC7DrB,EAAU,GAAQoK,mBAAmBvI,EAAK1b,GAE1CgY,EAAW,GAAQC,gBAAgBhZ,QAAO,SAAU+a,EAAO5Y,GAC7D6W,EAAgBgC,OAAOjC,GACvB6B,EAAQtG,UAEJnS,GAAUA,EAAO4iB,OACnBD,EAAOC,KAAO5iB,EAAO4iB,MAEnBtM,GACFA,EAASsC,EAAO5Y,MAGpByY,EAAQQ,KAAKrC,MCrBjB,SAASkM,GACPC,EACApD,EACA7F,GAIA,OAFaiJ,GAAcpD,EAAOtG,OAAS,IAAM,IAEjC,OADLsG,EAAOtG,OAASsG,EAAOqD,QAAUrD,EAAOsD,YACpBnJ,EAGjC,SAASoJ,GAAeplB,EAAaqlB,GASnC,MARW,QAAUrlB,GAEnB,aACA,EAASiZ,SADT,sBAIA,EAASD,SACRqM,EAAc,IAAMA,EAAc,KAIhC,IAAIC,GAAgB,CACzBC,WAAY,SAAUvlB,EAAa6hB,GAEjC,OAAOmD,GAAc,KAAMnD,GADfA,EAAOrI,UAAY,IAAM4L,GAAeplB,EAAK,kBAKlDwlB,GAAkB,CAC3BD,WAAY,SAAUvlB,EAAa6hB,GAEjC,OAAOmD,GAAc,OAAQnD,GADjBA,EAAOrI,UAAY,WAAa4L,GAAeplB,MAKpDylB,GAAoB,CAC7BF,WAAY,SAAUvlB,EAAa6hB,GACjC,OAAOmD,GAAc,OAAQnD,EAAQA,EAAOrI,UAAY,YAE1DqB,QAAS,SAAU7a,EAAa6hB,GAC9B,OAAOuD,GAAeplB,KCxCX,MAAM,GAGnB,cACEY,KAAK8kB,WAAa,GAGpB,IAAI1mB,GACF,OAAO4B,KAAK8kB,WAAWpN,GAAOtZ,IAGhC,IAAIA,EAAcwZ,EAAoB6E,GACpC,IAAIsI,EAAoBrN,GAAOtZ,GAC/B4B,KAAK8kB,WAAWC,GACd/kB,KAAK8kB,WAAWC,IAAsB,GACxC/kB,KAAK8kB,WAAWC,GAAmB1iB,KAAK,CACtC8U,GAAIS,EACJ6E,QAASA,IAIb,OAAOre,EAAewZ,EAAqB6E,GACzC,GAAKre,GAASwZ,GAAa6E,EAA3B,CAKA,IAAIuI,EAAQ5mB,EAAO,CAACsZ,GAAOtZ,IAAS,EAAiB4B,KAAK8kB,YAEtDlN,GAAY6E,EACdzc,KAAKilB,eAAeD,EAAOpN,EAAU6E,GAErCzc,KAAKklB,mBAAmBF,QATxBhlB,KAAK8kB,WAAa,GAad,eAAeE,EAAiBpN,EAAoB6E,GAC1D,EACEuI,GACA,SAAU5mB,GACR4B,KAAK8kB,WAAW1mB,GAAQ,EACtB4B,KAAK8kB,WAAW1mB,IAAS,IACzB,SAAU+mB,GACR,OACGvN,GAAYA,IAAauN,EAAQhO,IACjCsF,GAAWA,IAAY0I,EAAQ1I,WAID,IAAjCzc,KAAK8kB,WAAW1mB,GAAM2B,eACjBC,KAAK8kB,WAAW1mB,KAG3B4B,MAII,mBAAmBglB,GACzB,EACEA,GACA,SAAU5mB,UACD4B,KAAK8kB,WAAW1mB,KAEzB4B,OAKN,SAAS0X,GAAOtZ,GACd,MAAO,IAAMA,EChEA,MAAM,GAKnB,YAAYgnB,GACVplB,KAAKoa,UAAY,IAAI,GACrBpa,KAAKqlB,iBAAmB,GACxBrlB,KAAKolB,YAAcA,EAGrB,KAAKE,EAAmB1N,EAAoB6E,GAE1C,OADAzc,KAAKoa,UAAU1I,IAAI4T,EAAW1N,EAAU6E,GACjCzc,KAGT,YAAY4X,GAEV,OADA5X,KAAKqlB,iBAAiBhjB,KAAKuV,GACpB5X,KAGT,OAAOslB,EAAoB1N,EAAqB6E,GAE9C,OADAzc,KAAKoa,UAAUD,OAAOmL,EAAW1N,EAAU6E,GACpCzc,KAGT,cAAc4X,GACZ,OAAKA,GAKL5X,KAAKqlB,iBAAmB,EACtBrlB,KAAKqlB,kBAAoB,GACxBnnB,GAAMA,IAAM0Z,GAGR5X,OATLA,KAAKqlB,iBAAmB,GACjBrlB,MAWX,aAGE,OAFAA,KAAKulB,SACLvlB,KAAKwlB,gBACExlB,KAGT,KAAKslB,EAAmBplB,EAAYulB,GAClC,IAAK,IAAI5nB,EAAI,EAAGA,EAAImC,KAAKqlB,iBAAiBtlB,OAAQlC,IAChDmC,KAAKqlB,iBAAiBxnB,GAAGynB,EAAWplB,GAGtC,IAAIka,EAAYpa,KAAKoa,UAAU1b,IAAI4mB,GAC/BlG,EAAO,GAYX,GAVIqG,EAGFrG,EAAK/c,KAAKnC,EAAMulB,GACPvlB,GAGTkf,EAAK/c,KAAKnC,GAGRka,GAAaA,EAAUra,OAAS,EAClC,IAASlC,EAAI,EAAGA,EAAIuc,EAAUra,OAAQlC,IACpCuc,EAAUvc,GAAGsZ,GAAGc,MAAMmC,EAAUvc,GAAG4e,SAAW,OAAQ2C,QAE/Cpf,KAAKolB,aACdplB,KAAKolB,YAAYE,EAAWplB,GAG9B,OAAOF,MC3CI,MAAM,WAA4B,GAc/C,YACE0lB,EACAtnB,EACAunB,EACAvmB,EACAwa,GAEAkC,QACA9b,KAAK4lB,WAAa,GAAQC,+BAC1B7lB,KAAK0lB,MAAQA,EACb1lB,KAAK5B,KAAOA,EACZ4B,KAAK2lB,SAAWA,EAChB3lB,KAAKZ,IAAMA,EACXY,KAAK4Z,QAAUA,EAEf5Z,KAAK8lB,MAAQ,MACb9lB,KAAK+lB,SAAWnM,EAAQmM,SACxB/lB,KAAKgZ,gBAAkBY,EAAQZ,gBAC/BhZ,KAAK8X,GAAK9X,KAAK+lB,SAASC,mBAO1B,wBACE,OAAOpF,QAAQ5gB,KAAK0lB,MAAMO,uBAO5B,eACE,OAAOrF,QAAQ5gB,KAAK0lB,MAAMQ,cAO5B,UACE,GAAIlmB,KAAKmmB,QAAyB,gBAAfnmB,KAAK8lB,MACtB,OAAO,EAGT,IAAIlK,EAAM5b,KAAK0lB,MAAMxK,KAAKyJ,WAAW3kB,KAAKZ,IAAKY,KAAK4Z,SACpD,IACE5Z,KAAKmmB,OAASnmB,KAAK0lB,MAAMU,UAAUxK,EAAK5b,KAAK4Z,SAC7C,MAAO5N,GAKP,OAJA,EAAKmT,MAAM,KACTnf,KAAKqmB,QAAQra,GACbhM,KAAKsmB,YAAY,aAEZ,EAOT,OAJAtmB,KAAKumB,gBAEL,EAAOC,MAAM,aAAc,CAAEnN,UAAWrZ,KAAK5B,KAAMwd,QACnD5b,KAAKsmB,YAAY,eACV,EAOT,QACE,QAAItmB,KAAKmmB,SACPnmB,KAAKmmB,OAAOM,SACL,GAWX,KAAKvmB,GACH,MAAmB,SAAfF,KAAK8lB,QAEP,EAAK3G,MAAM,KACLnf,KAAKmmB,QACPnmB,KAAKmmB,OAAO5L,KAAKra,MAGd,GAOX,OACqB,SAAfF,KAAK8lB,OAAoB9lB,KAAKkmB,gBAChClmB,KAAKmmB,OAAOO,OAIR,SACF1mB,KAAK0lB,MAAMiB,YACb3mB,KAAK0lB,MAAMiB,WACT3mB,KAAKmmB,OACLnmB,KAAK0lB,MAAMxK,KAAKjB,QAAQja,KAAKZ,IAAKY,KAAK4Z,UAG3C5Z,KAAKsmB,YAAY,QACjBtmB,KAAKmmB,OAAOS,YAAS1F,EAGf,QAAQhH,GACdla,KAAK6mB,KAAK,QAAS,CAAE3D,KAAM,iBAAkBhJ,MAAOA,IACpDla,KAAK+lB,SAAS7L,MAAMla,KAAK8mB,qBAAqB,CAAE5M,MAAOA,EAAMwD,cAGvD,QAAQqJ,GACVA,EACF/mB,KAAKsmB,YAAY,SAAU,CACzBU,KAAMD,EAAWC,KACjBC,OAAQF,EAAWE,OACnBC,SAAUH,EAAWG,WAGvBlnB,KAAKsmB,YAAY,UAEnBtmB,KAAKmnB,kBACLnnB,KAAKmmB,YAASjF,EAGR,UAAUS,GAChB3hB,KAAK6mB,KAAK,UAAWlF,GAGf,aACN3hB,KAAK6mB,KAAK,YAGJ,gBACN7mB,KAAKmmB,OAAOS,OAAS,KACnB5mB,KAAKonB,UAEPpnB,KAAKmmB,OAAO9C,QAAWnJ,IACrBla,KAAKqmB,QAAQnM,IAEfla,KAAKmmB,OAAOkB,QAAWN,IACrB/mB,KAAKsnB,QAAQP,IAEf/mB,KAAKmmB,OAAOoB,UAAa5F,IACvB3hB,KAAKwnB,UAAU7F,IAGb3hB,KAAKkmB,iBACPlmB,KAAKmmB,OAAOsB,WAAa,KACvBznB,KAAK0nB,eAKH,kBACF1nB,KAAKmmB,SACPnmB,KAAKmmB,OAAOS,YAAS1F,EACrBlhB,KAAKmmB,OAAO9C,aAAUnC,EACtBlhB,KAAKmmB,OAAOkB,aAAUnG,EACtBlhB,KAAKmmB,OAAOoB,eAAYrG,EACpBlhB,KAAKkmB,iBACPlmB,KAAKmmB,OAAOsB,gBAAavG,IAKvB,YAAY4E,EAAe7E,GACjCjhB,KAAK8lB,MAAQA,EACb9lB,KAAK+lB,SAAS4B,KACZ3nB,KAAK8mB,qBAAqB,CACxBhB,MAAOA,EACP7E,OAAQA,KAGZjhB,KAAK6mB,KAAKf,EAAO7E,GAGnB,qBAAqBU,GACnB,OAAO,EAAmB,CAAEiG,IAAK5nB,KAAK8X,IAAM6J,ICzNjC,MAAM,GAGnB,YAAY+D,GACV1lB,KAAK0lB,MAAQA,EAQf,YAAYmC,GACV,OAAO7nB,KAAK0lB,MAAMoC,YAAYD,GAWhC,iBACEzpB,EACAunB,EACAvmB,EACAwa,GAEA,OAAO,IAAI,GAAoB5Z,KAAK0lB,MAAOtnB,EAAMunB,EAAUvmB,EAAKwa,ICrCpE,IAAImO,GAAc,IAAI,GAA0B,CAC9C7M,KAAM,GACN+K,uBAAuB,EACvBC,cAAc,EAEd8B,cAAe,WACb,OAAOpH,QAAQ,GAAQqH,oBAEzBH,YAAa,WACX,OAAOlH,QAAQ,GAAQqH,oBAEzB7B,UAAW,SAAUxK,GACnB,OAAO,GAAQsM,gBAAgBtM,MAI/BuM,GAAoB,CACtBjN,KAAM,GACN+K,uBAAuB,EACvBC,cAAc,EACd8B,cAAe,WACb,OAAO,IAIAI,GAAyB,EAClC,CACEhC,UAAW,SAAUxK,GACnB,OAAO,GAAQyM,YAAYC,sBAAsB1M,KAGrDuM,IAESI,GAAuB,EAChC,CACEnC,UAAW,SAAUxK,GACnB,OAAO,GAAQyM,YAAYG,oBAAoB5M,KAGnDuM,IAGEM,GAAmB,CACrBX,YAAa,WACX,OAAO,GAAQY,mBAwBJ,GANmB,CAChChE,GAAIqD,GACJY,cAf0B,IAAI,GAE5B,EAAmB,GAAIP,GAAwBK,KAcjDG,YATwB,IAAI,GAE1B,EAAmB,GAAIL,GAAsBE,MC5D7CI,GAAkB,IAAI,GAA0B,CAClDC,KAAM,SACN5N,KAAM,GACN+K,uBAAuB,EACvBC,cAAc,EAEd4B,YAAa,WACX,OAAO,GAETE,cAAe,WACb,YAAyB9G,IAAlBzjB,OAAOsrB,QAEhB3C,UAAW,SAAUxK,EAAKhC,GACxB,OAAO,IAAInc,OAAOsrB,OAAOnN,EAAK,KAAM,CAClCoN,QAASrP,EAAaM,QAAQ,SAAU,CACtCU,OAAQf,EAAQe,SAElBsO,mBAAoBrP,EAAQsP,oBAGhCvC,WAAY,SAAUR,EAAQ/K,GAC5B+K,EAAO5L,KACLgD,KAAKuC,UAAU,CACb1E,KAAMA,QAMV+N,GAAmB,CACrBrB,YAAa,SAAUD,GAErB,OADU,GAAQuB,eAAevB,EAAYlN,UAM7C0O,GAAwB,IAAI,GAE5B,EAAmB,GAAIjB,GAAwBe,KAK/CG,GAAsB,IAAI,GAE1B,EAAmB,GAAIf,GAAsBY,KAIjD,GAAWI,cAAgBF,GAC3B,GAAWG,YAAcF,GACzB,GAAWzE,OAASgE,GAEL,UCjBR,IAAI,GAAU,IAxCd,cAAsB,GAC3B,cACE/M,QACA,IAAIzE,EAAOrX,UAEqBkhB,IAA5BzjB,OAAO2lB,mBACT3lB,OAAO2lB,iBACL,UACA,WACE/L,EAAKwP,KAAK,aAEZ,GAEFppB,OAAO2lB,iBACL,WACA,WACE/L,EAAKwP,KAAK,cAEZ,IAaN,WACE,YAAgC3F,IAA5BzjB,OAAOgmB,UAAUgG,QAGZhsB,OAAOgmB,UAAUgG,SCxBf,MAAM,GAOnB,YACEC,EACArQ,EACAO,GAEA5Z,KAAK0pB,QAAUA,EACf1pB,KAAKqZ,UAAYA,EACjBrZ,KAAK2pB,aAAe/P,EAAQ+P,aAC5B3pB,KAAK4pB,aAAehQ,EAAQgQ,aAC5B5pB,KAAK6pB,eAAY3I,EAanB,iBACE9iB,EACAunB,EACAvmB,EACAwa,GAEAA,EAAU,EAAmB,GAAIA,EAAS,CACxCZ,gBAAiBhZ,KAAK6pB,YAExB,IAAIC,EAAa9pB,KAAKqZ,UAAU0Q,iBAC9B3rB,EACAunB,EACAvmB,EACAwa,GAGEoQ,EAAgB,KAEhB5C,EAAS,WACX0C,EAAWvE,OAAO,OAAQ6B,GAC1B0C,EAAWzqB,KAAK,SAAU4qB,GAC1BD,EAAgB,EAAKhL,OAEnBiL,EAAYlD,IAGd,GAFA+C,EAAWvE,OAAO,SAAU0E,GAEJ,OAApBlD,EAAWC,MAAqC,OAApBD,EAAWC,KAEzChnB,KAAK0pB,QAAQQ,mBACR,IAAKnD,EAAWG,UAAY8C,EAAe,CAEhD,IAAIG,EAAW,EAAKnL,MAAQgL,EACxBG,EAAW,EAAInqB,KAAK4pB,eACtB5pB,KAAK0pB,QAAQQ,cACblqB,KAAK6pB,UAAYphB,KAAK2hB,IAAID,EAAW,EAAGnqB,KAAK2pB,iBAMnD,OADAG,EAAWzqB,KAAK,OAAQ+nB,GACjB0C,EAWT,YAAYjC,GACV,OAAO7nB,KAAK0pB,QAAQW,WAAarqB,KAAKqZ,UAAUyO,YAAYD,IC/FhE,MAAMyC,GAAW,CAgBfC,cAAe,SAAUC,GACvB,IACE,IAAIC,EAAclN,KAAKC,MAAMgN,EAAatqB,MACtCwqB,EAAkBD,EAAYvqB,KAClC,GAA+B,iBAApBwqB,EACT,IACEA,EAAkBnN,KAAKC,MAAMiN,EAAYvqB,MACzC,MAAO8L,IAEX,IAAI2e,EAA2B,CAC7BC,MAAOH,EAAYG,MACnBC,QAASJ,EAAYI,QACrB3qB,KAAMwqB,GAKR,OAHID,EAAYK,UACdH,EAAYG,QAAUL,EAAYK,SAE7BH,EACP,MAAO3e,GACP,KAAM,CAAEkX,KAAM,oBAAqBhJ,MAAOlO,EAAG9L,KAAMsqB,EAAatqB,QAUpE6qB,cAAe,SAAUH,GACvB,OAAOrN,KAAKuC,UAAU8K,IAiBxBI,iBAAkB,SAAUR,GAC1B,IAAI7I,EAAU2I,GAASC,cAAcC,GAErC,GAAsB,kCAAlB7I,EAAQiJ,MAA2C,CACrD,IAAKjJ,EAAQzhB,KAAK+qB,iBAChB,KAAM,6CAER,MAAO,CACLC,OAAQ,YACRpT,GAAI6J,EAAQzhB,KAAKirB,UACjBnS,gBAAiD,IAAhC2I,EAAQzhB,KAAK+qB,kBAE3B,GAAsB,iBAAlBtJ,EAAQiJ,MAGjB,MAAO,CACLM,OAAQlrB,KAAKorB,eAAezJ,EAAQzhB,MACpCga,MAAOla,KAAKqrB,cAAc1J,EAAQzhB,OAGpC,KAAM,qBAcVkrB,eAAgB,SAAUrE,GACxB,OAAIA,EAAWC,KAAO,IAMhBD,EAAWC,MAAQ,MAAQD,EAAWC,MAAQ,KACzC,UAEA,KAEoB,MAApBD,EAAWC,KACb,WACED,EAAWC,KAAO,KACpB,UACED,EAAWC,KAAO,KACpB,UACED,EAAWC,KAAO,KACpB,QAGA,WAaXqE,cAAe,SAAUtE,GACvB,OAAwB,MAApBA,EAAWC,MAAqC,OAApBD,EAAWC,KAClC,CACL9D,KAAM,cACNhjB,KAAM,CACJ8mB,KAAMD,EAAWC,KACjBrF,QAASoF,EAAWE,QAAUF,EAAWpF,UAItC,OAKE,UClIA,MAAM,WAAmB,GAKtC,YAAY7J,EAAYuB,GACtByC,QACA9b,KAAK8X,GAAKA,EACV9X,KAAKqZ,UAAYA,EACjBrZ,KAAKgZ,gBAAkBK,EAAUL,gBACjChZ,KAAKumB,gBAOP,wBACE,OAAOvmB,KAAKqZ,UAAU4M,wBAOxB,KAAK/lB,GACH,OAAOF,KAAKqZ,UAAUkB,KAAKra,GAU7B,WAAW9B,EAAc8B,EAAW2qB,GAClC,IAAID,EAAqB,CAAEA,MAAOxsB,EAAM8B,KAAMA,GAK9C,OAJI2qB,IACFD,EAAMC,QAAUA,GAElB,EAAOrE,MAAM,aAAcoE,GACpB5qB,KAAKua,KAAK,GAASwQ,cAAcH,IAQ1C,OACM5qB,KAAKqZ,UAAU6M,eACjBlmB,KAAKqZ,UAAUqN,OAEf1mB,KAAKsrB,WAAW,cAAe,IAKnC,QACEtrB,KAAKqZ,UAAUoN,QAGT,gBACN,IAAI8E,EAAY,CACd5J,QAAU6I,IACR,IAAIG,EACJ,IACEA,EAAc,GAASJ,cAAcC,GACrC,MAAOxe,GACPhM,KAAK6mB,KAAK,QAAS,CACjB3D,KAAM,oBACNhJ,MAAOlO,EACP9L,KAAMsqB,EAAatqB,OAIvB,QAAoBghB,IAAhByJ,EAA2B,CAG7B,OAFA,EAAOnE,MAAM,aAAcmE,GAEnBA,EAAYC,OAClB,IAAK,eACH5qB,KAAK6mB,KAAK,QAAS,CACjB3D,KAAM,cACNhjB,KAAMyqB,EAAYzqB,OAEpB,MACF,IAAK,cACHF,KAAK6mB,KAAK,QACV,MACF,IAAK,cACH7mB,KAAK6mB,KAAK,QAGd7mB,KAAK6mB,KAAK,UAAW8D,KAGzBa,SAAU,KACRxrB,KAAK6mB,KAAK,aAEZ3M,MAAQA,IACNla,KAAK6mB,KAAK,QAAS3M,IAErBuR,OAAS1E,IACPI,IAEIJ,GAAcA,EAAWC,MAC3BhnB,KAAK0rB,iBAAiB3E,GAGxB/mB,KAAKqZ,UAAY,KACjBrZ,KAAK6mB,KAAK,YAIVM,EAAkB,KACpB,EAAwBoE,EAAW,CAACI,EAAUf,KAC5C5qB,KAAKqZ,UAAUkM,OAAOqF,EAAOe,MAIjC,EAAwBJ,EAAW,CAACI,EAAUf,KAC5C5qB,KAAKqZ,UAAUha,KAAKurB,EAAOe,KAIvB,iBAAiB5E,GACvB,IAAImE,EAAS,GAASE,eAAerE,GACjC7M,EAAQ,GAASmR,cAActE,GAC/B7M,GACFla,KAAK6mB,KAAK,QAAS3M,GAEjBgR,GACFlrB,KAAK6mB,KAAKqE,EAAQ,CAAEA,OAAQA,EAAQhR,MAAOA,KCrIlC,MAAM,GAMnB,YACEb,EACAzB,GAEA5X,KAAKqZ,UAAYA,EACjBrZ,KAAK4X,SAAWA,EAChB5X,KAAKumB,gBAGP,QACEvmB,KAAKmnB,kBACLnnB,KAAKqZ,UAAUoN,QAGT,gBACNzmB,KAAKwnB,UAAavpB,IAGhB,IAAIqD,EAFJtB,KAAKmnB,kBAGL,IACE7lB,EAAS,GAAS0pB,iBAAiB/sB,GACnC,MAAO+N,GAGP,OAFAhM,KAAKgI,OAAO,QAAS,CAAEkS,MAAOlO,SAC9BhM,KAAKqZ,UAAUoN,QAIK,cAAlBnlB,EAAO4pB,OACTlrB,KAAKgI,OAAO,YAAa,CACvB8hB,WAAY,IAAI,GAAWxoB,EAAOwW,GAAI9X,KAAKqZ,WAC3CL,gBAAiB1X,EAAO0X,mBAG1BhZ,KAAKgI,OAAO1G,EAAO4pB,OAAQ,CAAEhR,MAAO5Y,EAAO4Y,QAC3Cla,KAAKqZ,UAAUoN,UAInBzmB,KAAKiqB,SAAYlD,IACf/mB,KAAKmnB,kBAEL,IAAI+D,EAAS,GAASE,eAAerE,IAAe,UAChD7M,EAAQ,GAASmR,cAActE,GACnC/mB,KAAKgI,OAAOkjB,EAAQ,CAAEhR,MAAOA,KAG/Bla,KAAKqZ,UAAUha,KAAK,UAAWW,KAAKwnB,WACpCxnB,KAAKqZ,UAAUha,KAAK,SAAUW,KAAKiqB,UAG7B,kBACNjqB,KAAKqZ,UAAUkM,OAAO,UAAWvlB,KAAKwnB,WACtCxnB,KAAKqZ,UAAUkM,OAAO,SAAUvlB,KAAKiqB,UAG/B,OAAOiB,EAAgBjK,GAC7BjhB,KAAK4X,SACH,EAAmB,CAAEyB,UAAWrZ,KAAKqZ,UAAW6R,OAAQA,GAAUjK,KC1EzD,MAAM,GAKnB,YAAY8E,EAAoBnM,GAC9B5Z,KAAK+lB,SAAWA,EAChB/lB,KAAK4Z,QAAUA,GAAW,GAG5B,KAAKe,EAAiB/C,GAChB5X,KAAK+lB,SAAS6F,WAIlB5rB,KAAK+lB,SAASxL,KACZ,GAAQsR,kBAAkB7H,SAAShkB,KAAM2a,GACzC/C,ICPS,MAAM,WAAgB,GAQnC,YAAYxZ,EAAc0tB,GACxBhQ,OAAM,SAAU8O,EAAO1qB,GACrB,EAAOsmB,MAAM,mBAAqBpoB,EAAO,QAAUwsB,MAGrD5qB,KAAK5B,KAAOA,EACZ4B,KAAK8rB,OAASA,EACd9rB,KAAK+rB,YAAa,EAClB/rB,KAAKgsB,qBAAsB,EAC3BhsB,KAAKisB,uBAAwB,EAO/B,UAAUC,EAAkBtU,GAC1B,OAAOA,EAAS,KAAM,CAAEuU,KAAM,KAIhC,QAAQvB,EAAe1qB,GACrB,GAAiC,IAA7B0qB,EAAMxK,QAAQ,WAChB,MAAM,IAAI,EACR,UAAYwK,EAAQ,mCAGxB,IAAK5qB,KAAK+rB,WAAY,CACpB,IAAIhR,EAAS,EAAwB,0BACrC,EAAOiH,KACL,0EAA0EjH,GAG9E,OAAO/a,KAAK8rB,OAAOR,WAAWV,EAAO1qB,EAAMF,KAAK5B,MAIlD,aACE4B,KAAK+rB,YAAa,EAClB/rB,KAAKgsB,qBAAsB,EAO7B,YAAYpB,GACV,IAAItF,EAAYsF,EAAMA,MAClB1qB,EAAO0qB,EAAM1qB,KACjB,GAAkB,2CAAdolB,EACFtlB,KAAKosB,iCAAiCxB,QACjC,GAAkB,uCAAdtF,EACTtlB,KAAKqsB,6BAA6BzB,QAC7B,GAA8C,IAA1CtF,EAAUlF,QAAQ,oBAA2B,CAEtDpgB,KAAK6mB,KAAKvB,EAAWplB,EADI,KAK7B,iCAAiC0qB,GAC/B5qB,KAAKgsB,qBAAsB,EAC3BhsB,KAAK+rB,YAAa,EACd/rB,KAAKisB,sBACPjsB,KAAK8rB,OAAOQ,YAAYtsB,KAAK5B,MAE7B4B,KAAK6mB,KAAK,gCAAiC+D,EAAM1qB,MAIrD,6BAA6B0qB,GACvBA,EAAM1qB,KAAKqsB,qBACbvsB,KAAKwsB,kBAAoB5B,EAAM1qB,KAAKqsB,oBAGtCvsB,KAAK6mB,KAAK,4BAA6B+D,EAAM1qB,MAI/C,YACMF,KAAK+rB,aAGT/rB,KAAKgsB,qBAAsB,EAC3BhsB,KAAKisB,uBAAwB,EAC7BjsB,KAAKysB,UACHzsB,KAAK8rB,OAAOhC,WAAWqB,UACvB,CAACjR,EAAqBha,KAChBga,GACFla,KAAKgsB,qBAAsB,EAI3B,EAAO9R,MAAMA,EAAMwD,YACnB1d,KAAK6mB,KACH,4BACAtoB,OAAOmuB,OACL,GACA,CACExJ,KAAM,YACNhJ,MAAOA,EAAMyH,SAEfzH,aAAiBqC,EAAgB,CAAEC,OAAQtC,EAAMsC,QAAW,MAIhExc,KAAK8rB,OAAOR,WAAW,mBAAoB,CACzCa,KAAMjsB,EAAKisB,KACXQ,aAAczsB,EAAKysB,aACnB9B,QAAS7qB,KAAK5B,UAQxB,cACE4B,KAAK+rB,YAAa,EAClB/rB,KAAK8rB,OAAOR,WAAW,qBAAsB,CAC3CT,QAAS7qB,KAAK5B,OAKlB,qBACE4B,KAAKisB,uBAAwB,EAI/B,wBACEjsB,KAAKisB,uBAAwB,GCvJlB,MAAM,WAAuB,GAM1C,UAAUC,EAAkBtU,GAC1B,OAAO5X,KAAK8rB,OAAOc,OAAOC,kBACxB,CACEC,YAAa9sB,KAAK5B,KAClB8tB,SAAUA,GAEZtU,IClBS,MAAM,GAMnB,cACE5X,KAAK+sB,QAUP,IAAIjV,GACF,OAAIvZ,OAAOkB,UAAUC,eAAe1B,KAAKgC,KAAKgtB,QAASlV,GAC9C,CACLA,GAAIA,EACJ6P,KAAM3nB,KAAKgtB,QAAQlV,IAGd,KAQX,KAAKF,GACH,EAAwB5X,KAAKgtB,QAAS,CAACC,EAAQnV,KAC7CF,EAAS5X,KAAKtB,IAAIoZ,MAKtB,QAAQA,GACN9X,KAAKktB,KAAOpV,EAId,eAAeqV,GACbntB,KAAKgtB,QAAUG,EAAiBC,SAASpW,KACzChX,KAAKqtB,MAAQF,EAAiBC,SAASC,MACvCrtB,KAAKstB,GAAKttB,KAAKtB,IAAIsB,KAAKktB,MAI1B,UAAUK,GAKR,OAJqC,OAAjCvtB,KAAKtB,IAAI6uB,EAAWzC,UACtB9qB,KAAKqtB,QAEPrtB,KAAKgtB,QAAQO,EAAWzC,SAAWyC,EAAWC,UACvCxtB,KAAKtB,IAAI6uB,EAAWzC,SAI7B,aAAayC,GACX,IAAIN,EAASjtB,KAAKtB,IAAI6uB,EAAWzC,SAKjC,OAJImC,WACKjtB,KAAKgtB,QAAQO,EAAWzC,SAC/B9qB,KAAKqtB,SAEAJ,EAIT,QACEjtB,KAAKgtB,QAAU,GACfhtB,KAAKqtB,MAAQ,EACbrtB,KAAKktB,KAAO,KACZltB,KAAKstB,GAAK,M,2SCpEC,MAAM,WAAwB,GAQ3C,YAAYlvB,EAAc0tB,GACxBhQ,MAAM1d,EAAM0tB,GACZ9rB,KAAKgtB,QAAU,IAAI,GAQrB,UAAUd,EAAkBtU,GAC1BkE,MAAM2Q,UAAUP,EAAU,CAAOhS,EAAOuT,IAAa,GAAD,gCAClD,IAAKvT,EAEH,GAA6B,OAD7BuT,EAAWA,GACEd,aAAsB,CACjC,IAAIe,EAAcnQ,KAAKC,MAAMiQ,EAASd,cACtC3sB,KAAKgtB,QAAQW,QAAQD,EAAY5C,aAC5B,CAEL,SADM9qB,KAAK8rB,OAAO8B,KAAKC,kBACW,MAA9B7tB,KAAK8rB,OAAO8B,KAAKE,UAId,CACL,IAAI/S,EAAS,EAAwB,yBAOrC,OANA,EAAOb,MACL,sCAAsCla,KAAK5B,yCACP2c,4CAGtCnD,EAAS,yBART5X,KAAKgtB,QAAQW,QAAQ3tB,KAAK8rB,OAAO8B,KAAKE,UAAUhW,IAatDF,EAASsC,EAAOuT,OAQpB,YAAY7C,GACV,IAAItF,EAAYsF,EAAMA,MACtB,GAA8C,IAA1CtF,EAAUlF,QAAQ,oBACpBpgB,KAAK+tB,oBAAoBnD,OACpB,CACL,IAAI1qB,EAAO0qB,EAAM1qB,KACbulB,EAAqB,GACrBmF,EAAME,UACRrF,EAASqF,QAAUF,EAAME,SAE3B9qB,KAAK6mB,KAAKvB,EAAWplB,EAAMulB,IAG/B,oBAAoBmF,GAClB,IAAItF,EAAYsF,EAAMA,MAClB1qB,EAAO0qB,EAAM1qB,KACjB,OAAQolB,GACN,IAAK,yCACHtlB,KAAKosB,iCAAiCxB,GACtC,MACF,IAAK,qCACH5qB,KAAKqsB,6BAA6BzB,GAClC,MACF,IAAK,+BACH,IAAIoD,EAAchuB,KAAKgtB,QAAQiB,UAAU/tB,GACzCF,KAAK6mB,KAAK,sBAAuBmH,GACjC,MACF,IAAK,iCACH,IAAIE,EAAgBluB,KAAKgtB,QAAQmB,aAAajuB,GAC1CguB,GACFluB,KAAK6mB,KAAK,wBAAyBqH,IAM3C,iCAAiCtD,GAC/B5qB,KAAKgsB,qBAAsB,EAC3BhsB,KAAK+rB,YAAa,EACd/rB,KAAKisB,sBACPjsB,KAAK8rB,OAAOQ,YAAYtsB,KAAK5B,OAE7B4B,KAAKgtB,QAAQoB,eAAexD,EAAM1qB,MAClCF,KAAK6mB,KAAK,gCAAiC7mB,KAAKgtB,UAKpD,aACEhtB,KAAKgtB,QAAQD,QACbjR,MAAMuS,c,oBC3FK,MAAM,WAAyB,GAI5C,YAAYjwB,EAAc0tB,EAAgBvpB,GACxCuZ,MAAM1d,EAAM0tB,GAJd,KAAA1sB,IAAkB,KAKhBY,KAAKuC,KAAOA,EAQd,UAAU2pB,EAAkBtU,GAC1BkE,MAAM2Q,UACJP,EACA,CAAChS,EAAqBuT,KACpB,GAAIvT,EAEF,YADAtC,EAASsC,EAAOuT,GAGlB,IAAIa,EAAeb,EAAwB,cACtCa,GASLtuB,KAAKZ,IAAM,kBAAakvB,UACjBb,EAAwB,cAC/B7V,EAAS,KAAM6V,IAVb7V,EACE,IAAIxW,MACF,+DAA+DpB,KAAK5B,MAEtE,QAWV,QAAQwsB,EAAe1qB,GACrB,MAAM,IAAI,EACR,oEAQJ,YAAY0qB,GACV,IAAItF,EAAYsF,EAAMA,MAClB1qB,EAAO0qB,EAAM1qB,KAE2B,IAA1ColB,EAAUlF,QAAQ,qBACe,IAAjCkF,EAAUlF,QAAQ,WAKpBpgB,KAAKuuB,qBAAqBjJ,EAAWplB,GAHnC4b,MAAM0S,YAAY5D,GAMd,qBAAqBA,EAAe1qB,GAC1C,IAAKF,KAAKZ,IAIR,YAHA,EAAOonB,MACL,gFAIJ,IAAKtmB,EAAKuuB,aAAevuB,EAAK+U,MAK5B,YAJA,EAAOiF,MACL,qGACEha,GAIN,IAAIwuB,EAAa,kBAAaxuB,EAAKuuB,YACnC,GAAIC,EAAW3uB,OAASC,KAAKuC,KAAKwS,UAAUO,eAI1C,YAHA,EAAO4E,MACL,oDAAoDla,KAAKuC,KAAKwS,UAAUO,wBAAwBoZ,EAAW3uB,UAI/G,IAAIkV,EAAQ,kBAAa/U,EAAK+U,OAC9B,GAAIA,EAAMlV,OAASC,KAAKuC,KAAKwS,UAAUM,YAIrC,YAHA,EAAO6E,MACL,+CAA+Cla,KAAKuC,KAAKwS,UAAUM,qBAAqBJ,EAAMlV,UAKlG,IAAI0M,EAAQzM,KAAKuC,KAAKwS,UAAUG,KAAKwZ,EAAYzZ,EAAOjV,KAAKZ,KAC7D,GAAc,OAAVqN,EAuBF,OAtBA,EAAO+Z,MACL,wIAIFxmB,KAAKysB,UAAUzsB,KAAK8rB,OAAOhC,WAAWqB,UAAW,CAACjR,EAAOuT,KACnDvT,EACF,EAAOA,MACL,iDAAiDuT,4DAIrDhhB,EAAQzM,KAAKuC,KAAKwS,UAAUG,KAAKwZ,EAAYzZ,EAAOjV,KAAKZ,KAC3C,OAAVqN,EAMJzM,KAAK6mB,KAAK+D,EAAO5qB,KAAK2uB,cAAcliB,IALlC,EAAOyN,MACL,qEASRla,KAAK6mB,KAAK+D,EAAO5qB,KAAK2uB,cAAcliB,IAKtC,cAAcA,GACZ,IAAImiB,EAAM,kBAAWniB,GACrB,IACE,OAAO8Q,KAAKC,MAAMoR,GAClB,SACA,OAAOA,ICpGE,MAAM,WAA0B,GAkB7C,YAAYxvB,EAAawa,GACvBkC,QACA9b,KAAK8lB,MAAQ,cACb9lB,KAAK8pB,WAAa,KAElB9pB,KAAKZ,IAAMA,EACXY,KAAK4Z,QAAUA,EACf5Z,KAAK+lB,SAAW/lB,KAAK4Z,QAAQmM,SAC7B/lB,KAAK6uB,SAAW7uB,KAAK4Z,QAAQe,OAE7B3a,KAAK8uB,eAAiB9uB,KAAK+uB,sBAC3B/uB,KAAKgvB,oBAAsBhvB,KAAKivB,yBAC9BjvB,KAAK8uB,gBAEP9uB,KAAKkvB,mBAAqBlvB,KAAKmvB,wBAAwBnvB,KAAK8uB,gBAE5D,IAAIM,EAAU,GAAQC,aAEtBD,EAAQ/vB,KAAK,SAAU,KACrBW,KAAK+lB,SAAS4B,KAAK,CAAE2H,QAAS,WACX,eAAftvB,KAAK8lB,OAAyC,gBAAf9lB,KAAK8lB,OACtC9lB,KAAKuvB,QAAQ,KAGjBH,EAAQ/vB,KAAK,UAAW,KACtBW,KAAK+lB,SAAS4B,KAAK,CAAE2H,QAAS,YAC1BtvB,KAAK8pB,YACP9pB,KAAKwvB,sBAITxvB,KAAKyvB,iBAQP,UACMzvB,KAAK8pB,YAAc9pB,KAAK0vB,SAGvB1vB,KAAK2vB,SAAS7H,eAInB9nB,KAAK4vB,YAAY,cACjB5vB,KAAK6vB,kBACL7vB,KAAK8vB,uBALH9vB,KAAK4vB,YAAY,WAYrB,KAAK1vB,GACH,QAAIF,KAAK8pB,YACA9pB,KAAK8pB,WAAWvP,KAAKra,GAahC,WAAW9B,EAAc8B,EAAW2qB,GAClC,QAAI7qB,KAAK8pB,YACA9pB,KAAK8pB,WAAWwB,WAAWltB,EAAM8B,EAAM2qB,GAOlD,aACE7qB,KAAK+vB,uBACL/vB,KAAK4vB,YAAY,gBAGnB,aACE,OAAO5vB,KAAK6uB,SAGN,kBACN,IAAIjX,EAAW,CAACsC,EAAO8V,KACjB9V,EACFla,KAAK0vB,OAAS1vB,KAAK2vB,SAASM,QAAQ,EAAGrY,GAEd,UAArBoY,EAAU9E,QACZlrB,KAAK6mB,KAAK,QAAS,CACjB3D,KAAM,iBACNhJ,MAAO8V,EAAU9V,QAEnBla,KAAK+lB,SAAS7L,MAAM,CAAEgW,eAAgBF,EAAU9V,UAEhDla,KAAKmwB,kBACLnwB,KAAKkvB,mBAAmBc,EAAU9E,QAAQ8E,KAIhDhwB,KAAK0vB,OAAS1vB,KAAK2vB,SAASM,QAAQ,EAAGrY,GAGjC,kBACF5X,KAAK0vB,SACP1vB,KAAK0vB,OAAOU,QACZpwB,KAAK0vB,OAAS,MAIV,wBACN1vB,KAAKmwB,kBACLnwB,KAAKqwB,kBACLrwB,KAAKswB,wBACDtwB,KAAK8pB,aACU9pB,KAAKuwB,oBACX9J,QAIP,iBACNzmB,KAAK2vB,SAAW3vB,KAAK4Z,QAAQ4W,YAAY,CACvCpxB,IAAKY,KAAKZ,IACV2mB,SAAU/lB,KAAK+lB,SACfpL,OAAQ3a,KAAK6uB,WAIT,QAAQnQ,GACd1e,KAAK+lB,SAAS4B,KAAK,CAAEuD,OAAQ,QAASxM,MAAOA,IACzCA,EAAQ,GACV1e,KAAK6mB,KAAK,gBAAiBpe,KAAKgoB,MAAM/R,EAAQ,MAEhD1e,KAAK0wB,WAAa,IAAI,EAAMhS,GAAS,EAAG,KACtC1e,KAAK+vB,uBACL/vB,KAAKiwB,YAID,kBACFjwB,KAAK0wB,aACP1wB,KAAK0wB,WAAWC,gBAChB3wB,KAAK0wB,WAAa,MAId,sBACN1wB,KAAK4wB,iBAAmB,IAAI,EAAM5wB,KAAK4Z,QAAQV,mBAAoB,KACjElZ,KAAK4vB,YAAY,iBAIb,wBACF5vB,KAAK4wB,kBACP5wB,KAAK4wB,iBAAiBD,gBAIlB,oBACN3wB,KAAK6wB,oBACL7wB,KAAK8pB,WAAWpD,OAEhB1mB,KAAK8wB,cAAgB,IAAI,EAAM9wB,KAAK4Z,QAAQX,YAAa,KACvDjZ,KAAK+lB,SAAS7L,MAAM,CAAE6W,eAAgB/wB,KAAK4Z,QAAQX,cACnDjZ,KAAKuvB,QAAQ,KAIT,qBACNvvB,KAAK6wB,oBAED7wB,KAAK8pB,aAAe9pB,KAAK8pB,WAAW7D,0BACtCjmB,KAAK8wB,cAAgB,IAAI,EAAM9wB,KAAKgZ,gBAAiB,KACnDhZ,KAAKwvB,uBAKH,oBACFxvB,KAAK8wB,eACP9wB,KAAK8wB,cAAcH,gBAIf,yBACN7B,GAEA,OAAO,EAAwC,GAAIA,EAAgB,CACjEnN,QAAUA,IAER3hB,KAAKgxB,qBACLhxB,KAAK6mB,KAAK,UAAWlF,IAEvB+E,KAAM,KACJ1mB,KAAKsrB,WAAW,cAAe,KAEjCE,SAAU,KACRxrB,KAAKgxB,sBAEP9W,MAAQA,IAENla,KAAK6mB,KAAK,QAAS3M,IAErBuR,OAAQ,KACNzrB,KAAKuwB,oBACDvwB,KAAKixB,eACPjxB,KAAKuvB,QAAQ,QAMb,wBACNT,GAEA,OAAO,EAAuC,GAAIA,EAAgB,CAChEoC,UAAYlB,IACVhwB,KAAKgZ,gBAAkBvQ,KAAKxG,IAC1BjC,KAAK4Z,QAAQZ,gBACbgX,EAAUhX,gBACVgX,EAAUlG,WAAW9Q,iBAAmBmY,KAE1CnxB,KAAKswB,wBACLtwB,KAAKoxB,cAAcpB,EAAUlG,YAC7B9pB,KAAKmrB,UAAYnrB,KAAK8pB,WAAWhS,GACjC9X,KAAK4vB,YAAY,YAAa,CAAEzE,UAAWnrB,KAAKmrB,eAK9C,sBACN,IAAIkG,EAAoBzZ,GACdtW,IACFA,EAAO4Y,OACTla,KAAK6mB,KAAK,QAAS,CAAE3D,KAAM,iBAAkBhJ,MAAO5Y,EAAO4Y,QAE7DtC,EAAStW,IAIb,MAAO,CACLgwB,SAAUD,EAAiB,KACzBrxB,KAAK6uB,UAAW,EAChB7uB,KAAKyvB,iBACLzvB,KAAKuvB,QAAQ,KAEfgC,QAASF,EAAiB,KACxBrxB,KAAKquB,eAEPmD,QAASH,EAAiB,KACxBrxB,KAAKuvB,QAAQ,OAEfkC,MAAOJ,EAAiB,KACtBrxB,KAAKuvB,QAAQ,MAKX,cAAczF,GAEpB,IAAK,IAAIc,KADT5qB,KAAK8pB,WAAaA,EACA9pB,KAAKgvB,oBACrBhvB,KAAK8pB,WAAWzqB,KAAKurB,EAAO5qB,KAAKgvB,oBAAoBpE,IAEvD5qB,KAAKgxB,qBAGC,oBACN,GAAKhxB,KAAK8pB,WAAV,CAIA,IAAK,IAAIc,KADT5qB,KAAK6wB,oBACa7wB,KAAKgvB,oBACrBhvB,KAAK8pB,WAAWvE,OAAOqF,EAAO5qB,KAAKgvB,oBAAoBpE,IAEzD,IAAId,EAAa9pB,KAAK8pB,WAEtB,OADA9pB,KAAK8pB,WAAa,KACXA,GAGD,YAAY4H,EAAkBxxB,GACpC,IAAIyxB,EAAgB3xB,KAAK8lB,MAEzB,GADA9lB,KAAK8lB,MAAQ4L,EACTC,IAAkBD,EAAU,CAC9B,IAAIE,EAAsBF,EACE,cAAxBE,IACFA,GAAuB,uBAAyB1xB,EAAKirB,WAEvD,EAAO3E,MACL,gBACAmL,EAAgB,OAASC,GAE3B5xB,KAAK+lB,SAAS4B,KAAK,CAAE7B,MAAO4L,EAAUzQ,OAAQ/gB,IAC9CF,KAAK6mB,KAAK,eAAgB,CAAEgL,SAAUF,EAAeG,QAASJ,IAC9D1xB,KAAK6mB,KAAK6K,EAAUxxB,IAIhB,cACN,MAAsB,eAAfF,KAAK8lB,OAAyC,cAAf9lB,KAAK8lB,OCtWhC,MAAM,GAGnB,cACE9lB,KAAK+xB,SAAW,GASlB,IAAI3zB,EAAc0tB,GAIhB,OAHK9rB,KAAK+xB,SAAS3zB,KACjB4B,KAAK+xB,SAAS3zB,GAwCpB,SAAuBA,EAAc0tB,GACnC,GAA2C,IAAvC1tB,EAAKgiB,QAAQ,sBAA6B,CAC5C,GAAI0L,EAAOc,OAAOrqB,KAChB,OAAO,GAAQyvB,uBAAuB5zB,EAAM0tB,EAAQA,EAAOc,OAAOrqB,MAEpE,IAAI0vB,EACF,0FACElX,EAAS,EAAwB,2BACrC,MAAM,IAAI,EAA0B,GAAGkX,MAAWlX,KAC7C,GAAiC,IAA7B3c,EAAKgiB,QAAQ,YACtB,OAAO,GAAQ8R,qBAAqB9zB,EAAM0tB,GACrC,GAAkC,IAA9B1tB,EAAKgiB,QAAQ,aACtB,OAAO,GAAQ+R,sBAAsB/zB,EAAM0tB,GACtC,GAA0B,IAAtB1tB,EAAKgiB,QAAQ,KACtB,MAAM,IAAI,EACR,sCAAwChiB,EAAO,MAGjD,OAAO,GAAQg0B,cAAch0B,EAAM0tB,GA1DXsG,CAAch0B,EAAM0tB,IAErC9rB,KAAK+xB,SAAS3zB,GAOvB,MACE,OzBiEG,SAAgBmB,GACrB,IAAI8yB,EAAS,GAIb,OAHAhS,EAAY9gB,GAAQ,SAAUT,GAC5BuzB,EAAOhwB,KAAKvD,MAEPuzB,EyBtEE,CAAmBryB,KAAK+xB,UAQjC,KAAK3zB,GACH,OAAO4B,KAAK+xB,SAAS3zB,GAOvB,OAAOA,GACL,IAAIysB,EAAU7qB,KAAK+xB,SAAS3zB,GAE5B,cADO4B,KAAK+xB,SAAS3zB,GACdysB,EAIT,aACE,EAAwB7qB,KAAK+xB,UAAU,SAAUlH,GAC/CA,EAAQwD,iBClCd,IAoDe,GApDD,CACZiE,eAAc,IACL,IAAI,GAGbC,wBAAuB,CACrBnzB,EACAwa,IAEO,IAAI,GAAkBxa,EAAKwa,GAGpCwY,cAAa,CAACh0B,EAAc0tB,IACnB,IAAI,GAAQ1tB,EAAM0tB,GAG3BoG,qBAAoB,CAAC9zB,EAAc0tB,IAC1B,IAAI,GAAe1tB,EAAM0tB,GAGlCqG,sBAAqB,CAAC/zB,EAAc0tB,IAC3B,IAAI,GAAgB1tB,EAAM0tB,GAGnCkG,uBAAsB,CACpB5zB,EACA0tB,EACAvpB,IAEO,IAAI,GAAiBnE,EAAM0tB,EAAQvpB,GAG5CiwB,qBAAoB,CAACzM,EAAoBnM,IAChC,IAAI,GAAemM,EAAUnM,GAGtC6Y,gBAAe,CACbpZ,EACAzB,IAEO,IAAI,GAAUyB,EAAWzB,GAGlC8a,qCAAoC,CAClChJ,EACArQ,EACAO,IAEO,IAAI,GAA+B8P,EAASrQ,EAAWO,ICxDnD,MAAM,GAInB,YAAYA,GACV5Z,KAAK4Z,QAAUA,GAAW,GAC1B5Z,KAAK2yB,UAAY3yB,KAAK4Z,QAAQgZ,OAASzB,IAQzC,aAAa9X,GACX,OAAO,GAAQqZ,qCAAqC1yB,KAAMqZ,EAAW,CACnEsQ,aAAc3pB,KAAK4Z,QAAQ+P,aAC3BC,aAAc5pB,KAAK4Z,QAAQgQ,eAQ/B,UACE,OAAO5pB,KAAK2yB,UAAY,EAI1B,cACE3yB,KAAK2yB,WAAa,GCjCP,MAAM,GAOnB,YAAYE,EAAwBjZ,GAClC5Z,KAAK6yB,WAAaA,EAClB7yB,KAAK8yB,KAAOlS,QAAQhH,EAAQkZ,MAC5B9yB,KAAK+yB,SAAWnS,QAAQhH,EAAQmZ,UAChC/yB,KAAKgzB,QAAUpZ,EAAQoZ,QACvBhzB,KAAKizB,aAAerZ,EAAQqZ,aAG9B,cACE,OAAO,EAAgBjzB,KAAK6yB,WAAY,EAAK1R,OAAO,gBAGtD,QAAQ+R,EAAqBtb,GAC3B,IAAIib,EAAa7yB,KAAK6yB,WAClBf,EAAU,EACVkB,EAAUhzB,KAAKgzB,QACftD,EAAS,KAETyD,EAAkB,CAACjZ,EAAO8V,KACxBA,EACFpY,EAAS,KAAMoY,IAEf8B,GAAoB,EAChB9xB,KAAK8yB,OACPhB,GAAoBe,EAAW9yB,QAG7B+xB,EAAUe,EAAW9yB,QACnBizB,IACFA,GAAoB,EAChBhzB,KAAKizB,eACPD,EAAUvqB,KAAKxG,IAAI+wB,EAAShzB,KAAKizB,gBAGrCvD,EAAS1vB,KAAKozB,YACZP,EAAWf,GACXoB,EACA,CAAEF,UAASD,SAAU/yB,KAAK+yB,UAC1BI,IAGFvb,GAAS,KAYf,OAPA8X,EAAS1vB,KAAKozB,YACZP,EAAWf,GACXoB,EACA,CAAEF,QAASA,EAASD,SAAU/yB,KAAK+yB,UACnCI,GAGK,CACL/C,MAAO,WACLV,EAAOU,SAETiD,iBAAkB,SAAU1zB,GAC1BuzB,EAAcvzB,EACV+vB,GACFA,EAAO2D,iBAAiB1zB,KAMxB,YACNgwB,EACAuD,EACAtZ,EACAhC,GAEA,IAAI+G,EAAQ,KACR+Q,EAAS,KAoBb,OAlBI9V,EAAQoZ,QAAU,IACpBrU,EAAQ,IAAI,EAAM/E,EAAQoZ,SAAS,WACjCtD,EAAOU,QACPxY,GAAS,OAIb8X,EAASC,EAASM,QAAQiD,GAAa,SAAUhZ,EAAO8V,GAClD9V,GAASyE,GAASA,EAAM2U,cAAgB1Z,EAAQmZ,WAIhDpU,GACFA,EAAMgS,gBAER/Y,EAASsC,EAAO8V,OAGX,CACLI,MAAO,WACDzR,GACFA,EAAMgS,gBAERjB,EAAOU,SAETiD,iBAAkB,SAAU1zB,GAC1B+vB,EAAO2D,iBAAiB1zB,MCpHjB,MAAM,GAGnB,YAAYkzB,GACV7yB,KAAK6yB,WAAaA,EAGpB,cACE,OAAO,EAAgB7yB,KAAK6yB,WAAY,EAAK1R,OAAO,gBAGtD,QAAQ+R,EAAqBtb,GAC3B,OA6BJ,SACEib,EACAK,EACAK,GAEA,IAAIC,EAAU,EAAgBX,GAAY,SAAUlD,EAAU9xB,EAAG0iB,EAAGkT,GAClE,OAAO9D,EAASM,QAAQiD,EAAaK,EAAgB11B,EAAG41B,OAE1D,MAAO,CACLrD,MAAO,WACL,EAAkBoD,EAASE,KAE7BL,iBAAkB,SAAU1zB,GAC1B,EAAkB6zB,GAAS,SAAU9D,GACnCA,EAAO2D,iBAAiB1zB,QA3CrBswB,CAAQjwB,KAAK6yB,WAAYK,GAAa,SAAUr1B,EAAG21B,GACxD,OAAO,SAAUtZ,EAAO8V,GACtBwD,EAAQ31B,GAAGqc,MAAQA,EACfA,EA8CZ,SAA0BsZ,GACxB,O7BsLK,SAAavT,EAAcS,GAChC,IAAK,IAAI7iB,EAAI,EAAGA,EAAIoiB,EAAMlgB,OAAQlC,IAChC,IAAK6iB,EAAKT,EAAMpiB,GAAIA,EAAGoiB,GACrB,OAAO,EAGX,OAAO,E6B5LA,CAAgBuT,GAAS,SAAU9D,GACxC,OAAO9O,QAAQ8O,EAAOxV,UA/CZyZ,CAAiBH,IACnB5b,GAAS,IAIb,EAAkB4b,GAAS,SAAU9D,GACnCA,EAAO2D,iBAAiBrD,EAAU3W,UAAUsM,aAE9C/N,EAAS,KAAMoY,SA2CvB,SAAS0D,GAAYhE,GACdA,EAAOxV,OAAUwV,EAAOkE,UAC3BlE,EAAOU,QACPV,EAAOkE,SAAU,GC1DN,MAAM,GAOnB,YACEjE,EACAkE,EACAja,GAEA5Z,KAAK2vB,SAAWA,EAChB3vB,KAAK6zB,WAAaA,EAClB7zB,KAAK8zB,IAAMla,EAAQka,KAAO,KAC1B9zB,KAAK6uB,SAAWjV,EAAQe,OACxB3a,KAAK+lB,SAAWnM,EAAQmM,SAG1B,cACE,OAAO/lB,KAAK2vB,SAAS7H,cAGvB,QAAQoL,EAAqBtb,GAC3B,IAAIiX,EAAW7uB,KAAK6uB,SAChBlH,EAkER,SAA6BkH,GAC3B,IAAIkF,EAAU,GAAQC,kBACtB,GAAID,EACF,IACE,IAAIE,EAAkBF,EAAQG,GAAqBrF,IACnD,GAAIoF,EACF,OAAO1W,KAAKC,MAAMyW,GAEpB,MAAOjoB,GACPmoB,GAAoBtF,GAGxB,OAAO,KA9EMuF,CAAoBvF,GAC3BwF,EAAiB1M,GAAQA,EAAK0M,eAAiB1M,EAAK0M,eAAiB,EAErExB,EAAa,CAAC7yB,KAAK2vB,UACvB,GAAIhI,GAAQA,EAAK2M,UAAYt0B,KAAK8zB,KAAO,EAAK9U,MAAO,CACnD,IAAI3F,EAAYrZ,KAAK6zB,WAAWlM,EAAKtO,WACjCA,IACE,CAAC,KAAM,OAAOkb,SAAS5M,EAAKtO,YAAcgb,EAAiB,GAC7Dr0B,KAAK+lB,SAAS4B,KAAK,CACjB6M,QAAQ,EACRnb,UAAWsO,EAAKtO,UAChBob,QAAS9M,EAAK8M,UAEhB5B,EAAWxwB,KACT,IAAI,GAAmB,CAACgX,GAAY,CAClC2Z,QAAwB,EAAfrL,EAAK8M,QAAc,IAC5B1B,UAAU,MAIdsB,KAKN,IAAIK,EAAiB,EAAK1V,MACtB0Q,EAASmD,EACV8B,MACA1E,QAAQiD,GAAa,SAAS0B,EAAG1a,EAAO8V,GACnC9V,GACFia,GAAoBtF,GAChBgE,EAAW9yB,OAAS,GACtB20B,EAAiB,EAAK1V,MACtB0Q,EAASmD,EAAW8B,MAAM1E,QAAQiD,EAAa0B,IAE/Chd,EAASsC,MA8CrB,SACE2U,EACAxV,EACAob,EACAJ,GAEA,IAAIN,EAAU,GAAQC,kBACtB,GAAID,EACF,IACEA,EAAQG,GAAqBrF,IAAa,EAA8B,CACtEyF,UAAW,EAAKtV,MAChB3F,UAAWA,EACXob,QAASA,EACTJ,eAAgBA,IAElB,MAAOroB,KA1DH6oB,CACEhG,EACAmB,EAAU3W,UAAUjb,KACpB,EAAK4gB,MAAQ0V,EACbL,GAEFzc,EAAS,KAAMoY,OAIrB,MAAO,CACLI,MAAO,WACLV,EAAOU,SAETiD,iBAAkB,SAAU1zB,GAC1BuzB,EAAcvzB,EACV+vB,GACFA,EAAO2D,iBAAiB1zB,MAOlC,SAASu0B,GAAqBrF,GAC5B,MAAO,mBAAqBA,EAAW,MAAQ,UAuCjD,SAASsF,GAAoBtF,GAC3B,IAAIkF,EAAU,GAAQC,kBACtB,GAAID,EACF,WACSA,EAAQG,GAAqBrF,IACpC,MAAO7iB,KC5IE,MAAM,GAInB,YAAY2jB,GAAsBjR,MAAO7G,IACvC7X,KAAK2vB,SAAWA,EAChB3vB,KAAK4Z,QAAU,CAAE8E,MAAO7G,GAG1B,cACE,OAAO7X,KAAK2vB,SAAS7H,cAGvB,QAAQoL,EAAqBtb,GAC3B,IACI8X,EADAC,EAAW3vB,KAAK2vB,SAEhBhR,EAAQ,IAAI,EAAM3e,KAAK4Z,QAAQ8E,OAAO,WACxCgR,EAASC,EAASM,QAAQiD,EAAatb,MAGzC,MAAO,CACLwY,MAAO,WACLzR,EAAMgS,gBACFjB,GACFA,EAAOU,SAGXiD,iBAAkB,SAAU1zB,GAC1BuzB,EAAcvzB,EACV+vB,GACFA,EAAO2D,iBAAiB1zB,MCjCnB,MAAMm1B,GAKnB,YACEpU,EACAqU,EACAC,GAEAh1B,KAAK0gB,KAAOA,EACZ1gB,KAAK+0B,WAAaA,EAClB/0B,KAAKg1B,YAAcA,EAGrB,cAEE,OADah1B,KAAK0gB,OAAS1gB,KAAK+0B,WAAa/0B,KAAKg1B,aACpClN,cAGhB,QAAQoL,EAAqBtb,GAE3B,OADa5X,KAAK0gB,OAAS1gB,KAAK+0B,WAAa/0B,KAAKg1B,aACpC/E,QAAQiD,EAAatb,ICxBxB,MAAMqd,GAGnB,YAAYtF,GACV3vB,KAAK2vB,SAAWA,EAGlB,cACE,OAAO3vB,KAAK2vB,SAAS7H,cAGvB,QAAQoL,EAAqBtb,GAC3B,IAAI8X,EAAS1vB,KAAK2vB,SAASM,QACzBiD,GACA,SAAUhZ,EAAO8V,GACXA,GACFN,EAAOU,QAETxY,EAASsC,EAAO8V,MAGpB,OAAON,GCdX,SAASwF,GAAqBvF,GAC5B,OAAO,WACL,OAAOA,EAAS7H,eAIpB,IAoLe,GApLU,SACvB8E,EACAuI,EACAC,GAEA,IAAIC,EAAiD,GAErD,SAASC,EACPl3B,EACA8kB,EACAyC,EACA/L,EACA8P,GAEA,IAAIrQ,EAAY+b,EACdxI,EACAxuB,EACA8kB,EACAyC,EACA/L,EACA8P,GAKF,OAFA2L,EAAkBj3B,GAAQib,EAEnBA,EAGT,IAyHIkc,EAzHAC,EAA8Bj3B,OAAOmuB,OAAO,GAAIyI,EAAa,CAC/D5Q,WAAYqI,EAAO6I,OAAS,IAAM7I,EAAOtU,OACzCgM,QAASsI,EAAO6I,OAAS,IAAM7I,EAAOrU,QACtCK,SAAUgU,EAAOpU,SAEfkd,EAA+Bn3B,OAAOmuB,OAAO,GAAI8I,EAAY,CAC/D7a,QAAQ,IAENgb,EAAkCp3B,OAAOmuB,OAAO,GAAIyI,EAAa,CACnE5Q,WAAYqI,EAAOnU,SAAW,IAAMmU,EAAOlU,SAC3C4L,QAASsI,EAAOnU,SAAW,IAAMmU,EAAOjU,UACxCC,SAAUgU,EAAOhU,WAGfgd,EAAW,CACb9C,MAAM,EACNE,QAAS,KACTC,aAAc,KAGZ4C,EAAa,IAAI,GAAiB,CACpClM,aAAc,IACdC,aAAcgD,EAAO5T,kBAEnB8c,EAAoB,IAAI,GAAiB,CAC3ClD,MAAO,EACPjJ,aAAc,IACdC,aAAcgD,EAAO5T,kBAGnB+c,EAAeT,EACjB,KACA,KACA,EACAE,EACAK,GAEEG,EAAgBV,EAClB,MACA,KACA,EACAI,EACAG,GAEEI,EAAmBX,EACrB,SACA,SACA,EACAK,GAEEO,EAA0BZ,EAC5B,gBACA,gBACA,EACAK,EACAG,GAEEK,EAA0Bb,EAC5B,gBACA,gBACA,EACAK,EACAG,GAEEM,EAAwBd,EAC1B,cACA,cACA,EACAK,GAEEU,EAAwBf,EAC1B,cACA,cACA,EACAK,GAGEW,EAAU,IAAI,GAAmB,CAACP,GAAeH,GACjDW,EAAW,IAAI,GAAmB,CAACP,GAAgBJ,GACnDY,EAAc,IAAI,GAAmB,CAACP,GAAmBL,GACzDa,EAAiB,IAAI,GACvB,CACE,IAAI3B,GACFI,GAAqBgB,GACrBA,EACAC,IAGJP,GAEEc,EAAe,IAAI,GACrB,CACE,IAAI5B,GACFI,GAAqBkB,GACrBA,EACAC,IAGJT,GAGEe,EAAY,IAAI,GAClB,CACE,IAAI7B,GACFI,GAAqBuB,GACrB,IAAI,GAA0B,CAC5BA,EACA,IAAI,GAAgBC,EAAc,CAAEhY,MAAO,QAE7CgY,IAGJd,GAGEgB,EAAqB,IAAI9B,GAC3BI,GAAqByB,GACrBA,EACAH,GAiBF,OAZEjB,EADEJ,EAAYxa,OACD,IAAI,GAA0B,CACzC2b,EACA,IAAI,GAAgBM,EAAoB,CAAElY,MAAO,QAGtC,IAAI,GAA0B,CACzC4X,EACA,IAAI,GAAgBC,EAAU,CAAE7X,MAAO,MACvC,IAAI,GAAgBkY,EAAoB,CAAElY,MAAO,QAI9C,IAAI,GACT,IAAIuW,GACF,IAAIH,GACFI,GAAqBa,GACrBR,EACAqB,IAGJvB,EACA,CACEvB,IAAK,KACL/N,SAAUoP,EAAYpP,SACtBpL,OAAQwa,EAAYxa,UC/JX,GA/BW,CACxBkc,WAAY,SAAU1Q,GACpB,IAAI2Q,EAAM,IAAUr5B,OAAQs5B,eAqB5B,OApBAD,EAAIE,UAAY,WACd7Q,EAAOU,KAAK,QAAS,IAAI,GACzBV,EAAOM,SAETqQ,EAAIzT,QAAU,SAAUrX,GACtBma,EAAOU,KAAK,QAAS7a,GACrBma,EAAOM,SAETqQ,EAAIG,WAAa,WACXH,EAAIrZ,cAAgBqZ,EAAIrZ,aAAa1d,OAAS,GAChDomB,EAAO+Q,QAAQ,IAAKJ,EAAIrZ,eAG5BqZ,EAAIxT,OAAS,WACPwT,EAAIrZ,cAAgBqZ,EAAIrZ,aAAa1d,OAAS,GAChDomB,EAAO+Q,QAAQ,IAAKJ,EAAIrZ,cAE1B0I,EAAOU,KAAK,WAAY,KACxBV,EAAOM,SAEFqQ,GAETK,aAAc,SAAUL,GACtBA,EAAIE,UAAYF,EAAIzT,QAAUyT,EAAIG,WAAaH,EAAIxT,OAAS,KAC5DwT,EAAI1G,UCzBO,MAAM,WAAoB,GAQvC,YAAY1K,EAAqBvE,EAAgBvF,GAC/CE,QACA9b,KAAK0lB,MAAQA,EACb1lB,KAAKmhB,OAASA,EACdnhB,KAAK4b,IAAMA,EAGb,MAAMwb,GACJp3B,KAAKq3B,SAAW,EAChBr3B,KAAK6c,IAAM7c,KAAK0lB,MAAMmR,WAAW72B,MAEjCA,KAAKs3B,SAAW,KACdt3B,KAAKymB,SAEP,GAAQ8Q,kBAAkBv3B,KAAKs3B,UAE/Bt3B,KAAK6c,IAAI3H,KAAKlV,KAAKmhB,OAAQnhB,KAAK4b,KAAK,GAEjC5b,KAAK6c,IAAIG,kBACXhd,KAAK6c,IAAIG,iBAAiB,eAAgB,oBAE5Chd,KAAK6c,IAAItC,KAAK6c,GAGhB,QACMp3B,KAAKs3B,WACP,GAAQE,qBAAqBx3B,KAAKs3B,UAClCt3B,KAAKs3B,SAAW,MAEdt3B,KAAK6c,MACP7c,KAAK0lB,MAAMyR,aAAan3B,KAAK6c,KAC7B7c,KAAK6c,IAAM,MAIf,QAAQL,EAAgBtc,GACtB,OAAa,CACX,IAAIu3B,EAAQz3B,KAAK03B,cAAcx3B,GAC/B,IAAIu3B,EAGF,MAFAz3B,KAAK6mB,KAAK,QAAS,CAAErK,OAAQA,EAAQtc,KAAMu3B,IAK3Cz3B,KAAK23B,gBAAgBz3B,IACvBF,KAAK6mB,KAAK,mBAIN,cAAcrf,GACpB,IAAIowB,EAAapwB,EAAO+X,MAAMvf,KAAKq3B,UAC/BQ,EAAoBD,EAAWxX,QAAQ,MAE3C,OAA2B,IAAvByX,GACF73B,KAAKq3B,UAAYQ,EAAoB,EAC9BD,EAAWrY,MAAM,EAAGsY,IAGpB,KAIH,gBAAgBrwB,GACtB,OAAOxH,KAAKq3B,WAAa7vB,EAAOzH,QAAUyH,EAAOzH,OAzE3B,QCL1B,IAAK+3B,IAAL,SAAKA,GACH,+BACA,mBACA,uBAHF,CAAKA,QAAK,KAMK,UCGXC,GAAgB,EA0LpB,SAASC,GAAapc,GACpB,IAAIqc,GAAkC,IAAtBrc,EAAIwE,QAAQ,KAAc,IAAM,IAChD,OAAOxE,EAAMqc,EAAY,OAAQ,IAAIhZ,KAAS,MAAQ8Y,KAQxD,SAASG,GAAa9N,GACpB,OAAO,GAAQ+N,UAAU/N,GAaZ,IC3NVgO,GD2NU,GAhNf,MAaE,YAAY1S,EAAoB9J,GAC9B5b,KAAK0lB,MAAQA,EACb1lB,KAAKq4B,QAAUH,GAAa,KAAQ,IAuLxC,SAAsBn4B,GAGpB,IAFA,IAAIuB,EAAS,GAEJzD,EAAI,EAAGA,EAAIkC,EAAQlC,IAC1ByD,EAAOe,KAAK61B,GAAa,IAAIxa,SAAS,KAGxC,OAAOpc,EAAOgB,KAAK,IA9LyBg2B,CAAa,GACvDt4B,KAAK0a,SA4JT,SAAqBkB,GACnB,IAAI2c,EAAQ,qBAAqBC,KAAK5c,GACtC,MAAO,CACLpG,KAAM+iB,EAAM,GACZ9T,YAAa8T,EAAM,IAhKHE,CAAY7c,GAC5B5b,KAAKqd,WAAa,GAAMqb,WACxB14B,KAAK24B,aAGP,KAAKvB,GACH,OAAOp3B,KAAK44B,QAAQrb,KAAKuC,UAAU,CAACsX,KAGtC,OACEp3B,KAAK0lB,MAAMmT,cAAc74B,MAG3B,MAAMgnB,EAAWC,GACfjnB,KAAKsnB,QAAQN,EAAMC,GAAQ,GAI7B,QAAQmQ,GACN,GAAIp3B,KAAKqd,aAAe,GAAMyb,KAW5B,OAAO,EAVP,IAKE,OAJA,GAAQC,oBACN,OACAf,IA6IUpc,EA7Ic5b,KAAK0a,SA6ID2d,EA7IWr4B,KAAKq4B,QA8I7Czc,EAAIpG,KAAO,IAAM6iB,EAAU,eA7I1BW,MAAM5B,IACD,EACP,MAAOprB,GACP,OAAO,EAyIf,IAAoB4P,EAAkByc,EAjIpC,YACEr4B,KAAKi5B,cACLj5B,KAAK24B,aAIP,QAAQ3R,EAAMC,EAAQC,GACpBlnB,KAAKi5B,cACLj5B,KAAKqd,WAAa,GAAM6b,OACpBl5B,KAAKqnB,SACPrnB,KAAKqnB,QAAQ,CACXL,KAAMA,EACNC,OAAQA,EACRC,SAAUA,IAKR,QAAQuQ,GAQd,IAAIL,EAPJ,GAAqB,MAAjBK,EAAMjb,OASV,OANIxc,KAAKqd,aAAe,GAAMyb,MAC5B94B,KAAK0nB,aAII+P,EAAMv3B,KAAKqf,MAAM,EAAG,IAE7B,IAAK,IACH6X,EAAU7Z,KAAKC,MAAMia,EAAMv3B,KAAKqf,MAAM,IAAM,MAC5Cvf,KAAKonB,OAAOgQ,GACZ,MACF,IAAK,IACHA,EAAU7Z,KAAKC,MAAMia,EAAMv3B,KAAKqf,MAAM,IAAM,MAC5C,IAAK,IAAI1hB,EAAI,EAAGA,EAAIu5B,EAAQr3B,OAAQlC,IAClCmC,KAAKm5B,QAAQ/B,EAAQv5B,IAEvB,MACF,IAAK,IACHu5B,EAAU7Z,KAAKC,MAAMia,EAAMv3B,KAAKqf,MAAM,IAAM,QAC5Cvf,KAAKm5B,QAAQ/B,GACb,MACF,IAAK,IACHp3B,KAAK0lB,MAAM0T,YAAYp5B,MACvB,MACF,IAAK,IACHo3B,EAAU7Z,KAAKC,MAAMia,EAAMv3B,KAAKqf,MAAM,IAAM,MAC5Cvf,KAAKsnB,QAAQ8P,EAAQ,GAAIA,EAAQ,IAAI,IAKnC,OAAOxd,GAqFjB,IAAqBgC,EAAayd,EAC5BC,EArFEt5B,KAAKqd,aAAe,GAAMqb,YACxB9e,GAAWA,EAAQyf,WACrBr5B,KAAK0a,SAASlF,MAkFDoG,EAlFoB5b,KAAK0a,SAASlF,KAkFrB6jB,EAlF2Bzf,EAAQyf,UAmF/DC,EAAW,oCAAoCd,KAAK5c,IACxC,GAAKyd,EAAWC,EAAS,KAlFrCt5B,KAAKqd,WAAa,GAAMyb,KAEpB94B,KAAK4mB,QACP5mB,KAAK4mB,UAGP5mB,KAAKsnB,QAAQ,KAAM,uBAAuB,GAItC,QAAQsD,GACV5qB,KAAKqd,aAAe,GAAMyb,MAAQ94B,KAAKunB,WACzCvnB,KAAKunB,UAAU,CAAErnB,KAAM0qB,IAInB,aACF5qB,KAAKynB,YACPznB,KAAKynB,aAID,QAAQvN,GACVla,KAAKqjB,SACPrjB,KAAKqjB,QAAQnJ,GAIT,aACNla,KAAKu5B,OAAS,GAAQR,oBACpB,OACAf,GAAah4B,KAAK0lB,MAAM8T,cAAcx5B,KAAK0a,SAAU1a,KAAKq4B,WAG5Dr4B,KAAKu5B,OAAOl6B,KAAK,QAAUo4B,IACzBz3B,KAAKk3B,QAAQO,KAEfz3B,KAAKu5B,OAAOl6B,KAAK,WAAamd,IAC5Bxc,KAAK0lB,MAAM+T,WAAWz5B,KAAMwc,KAE9Bxc,KAAKu5B,OAAOl6B,KAAK,kBAAmB,KAClCW,KAAK05B,cAGP,IACE15B,KAAKu5B,OAAOP,QACZ,MAAO9e,GACP,EAAKiF,MAAM,KACTnf,KAAKqmB,QAAQnM,GACbla,KAAKsnB,QAAQ,KAAM,6BAA6B,MAK9C,cACFtnB,KAAKu5B,SACPv5B,KAAKu5B,OAAOI,aACZ35B,KAAKu5B,OAAO9S,QACZzmB,KAAKu5B,OAAS,QEhKL,GAfU,CACvBC,cAAe,SAAU5d,EAAKyc,GAC5B,OAAOzc,EAAIpG,KAAO,IAAM6iB,EAAU,iBAAmBzc,EAAI6I,aAE3D2U,YAAa,SAAUjT,GACrBA,EAAOyS,QAAQ,OAEjBC,cAAe,SAAU1S,GACvBA,EAAOyS,QAAQ,OAEjBa,WAAY,SAAUtT,EAAQ3J,GAC5B2J,EAAOmB,QAAQ,KAAM,2BAA6B9K,EAAS,KAAK,KCSrD,GAnBU,CACvBgd,cAAe,SAAU5d,EAAkByc,GACzC,OAAOzc,EAAIpG,KAAO,IAAM6iB,EAAU,OAASzc,EAAI6I,aAEjD2U,YAAa,aAGbP,cAAe,SAAU1S,GACvBA,EAAOyS,QAAQ,OAEjBa,WAAY,SAAUtT,EAAQ3J,GACb,MAAXA,EACF2J,EAAOuT,YAEPvT,EAAOmB,QAAQ,KAAM,2BAA6B9K,EAAS,KAAK,KCgBvD,GA7BW,CACxBqa,WAAY,SAAU1Q,GACpB,IACItJ,EAAM,IADQ,GAAQ+c,aAmB1B,OAjBA/c,EAAIO,mBAAqBP,EAAIoa,WAAa,WACxC,OAAQpa,EAAIQ,YACV,KAAK,EACCR,EAAIY,cAAgBZ,EAAIY,aAAa1d,OAAS,GAChDomB,EAAO+Q,QAAQra,EAAIL,OAAQK,EAAIY,cAEjC,MACF,KAAK,EAECZ,EAAIY,cAAgBZ,EAAIY,aAAa1d,OAAS,GAChDomB,EAAO+Q,QAAQra,EAAIL,OAAQK,EAAIY,cAEjC0I,EAAOU,KAAK,WAAYhK,EAAIL,QAC5B2J,EAAOM,UAIN5J,GAETsa,aAAc,SAAUta,GACtBA,EAAIO,mBAAqB,KACzBP,EAAIuT,UCCO,GAtBS,CACtB,sBAAsBxU,GACpB,OAAO5b,KAAK65B,aAAa,GAAgBje,IAG3C,oBAAoBA,GAClB,OAAO5b,KAAK65B,aAAa,GAAcje,IAGzCie,aAAY,CAACnU,EAAoB9J,IACxB,IAAI,GAAW8J,EAAO9J,GAG/B,UAAUuF,EAAgBvF,GACxB,OAAO5b,KAAK85B,cAAc,GAAU3Y,EAAQvF,IAG9Cke,cAAa,CAACpU,EAAqBvE,EAAgBvF,IAC1C,IAAI,GAAY8J,EAAOvE,EAAQvF,GCxB1C,UAAiB,SAAUuF,EAAQvF,GACjC,OAAO5b,KAAK85B,cAAc,GAAU3Y,EAAQvF,KCyK/B,GAzJQ,CAErBwG,mBAAoB,EACpBI,eAAgB,GAChBrK,kBACAuB,wBACAqgB,mBAAA,GACAC,WAAA,GACAnU,+BCtBa,WACb,IAAIxO,EAAOrX,KAEXqX,EAAK0O,SAAS4B,KACZtQ,EAAKyP,qBAAqB,CACxBzN,UAAWhC,EAAKjZ,MAAQiZ,EAAKuC,QAAQe,OAAS,IAAM,OAIpDtD,EAAKqO,MAAMsC,gBACb3Q,EAAKiP,YAAY,eACRjP,EAAKqO,MAAMoD,MACpBzR,EAAKiP,YAAY,gBACjB3M,EAAasgB,KACX5iB,EAAKqO,MAAMoD,KACX,CAAEnO,OAAQtD,EAAKuC,QAAQe,SACvB,SAAUT,EAAOtC,GACXP,EAAKqO,MAAMsC,iBACb3Q,EAAKiP,YAAY,eACjB1O,GAAS,KAELsC,GACF7C,EAAKgP,QAAQnM,GAEf7C,EAAKiQ,UACL1P,GAAS,QAKfP,EAAKiQ,WDPPe,YDtBa,GCwBbwD,kBAAmB,EAEnB+N,UAAS,IACAn8B,OAAOy8B,eAGhBjS,gBAAe,IACNxqB,OAAO08B,WAAa18B,OAAO28B,aAGpC,MAAMC,GACE58B,OAAQ68B,OAASD,EACvB,IAAIE,EAA2B,KAC7Bv6B,KAAKw6B,eAAeH,EAAYI,QAEvBh9B,OAAQ8f,KAGjBgd,IAFA5gB,EAAasgB,KAAK,QAAS,GAAIM,IAMnC9f,YAAW,IACF4H,SAGT,cACE,OAAOriB,KAAKya,cAAcC,SAASF,UAGrCkgB,eAAc,KACL,CAAEC,KAAM,EAASC,MAAO,IAGjC,eAAehjB,GACTyK,SAASwY,KACXjjB,IAEAkH,WAAW,KACT9e,KAAKw6B,eAAe5iB,IACnB,IAIPuM,mBAAkB,CAACvI,EAAa1b,IACvB,IAAI,EAAa0b,EAAK1b,GAG/B8Z,oBAAoB0I,GACX,IAAIM,EAAcN,GAG3B,kBACE,IACE,OAAOjlB,OAAOq9B,aACd,MAAO9uB,GACP,SAIJ,YACE,OAAIhM,KAAK45B,YACA55B,KAAK+6B,uBAEL/6B,KAAKg7B,sBAIhB,uBAEE,OAAO,IADWh7B,KAAK45B,cAIzBoB,mBAAkB,IACT,IAAIC,cAAc,qBAG3B5L,WAAU,IACD,GAGT,gBAAgBzT,GAEd,OAAO,IADW5b,KAAKioB,kBAChB,CAAgBrM,IAGzB,oBAAoBuF,EAAgBvF,GAClC,GAAI5b,KAAK0oB,iBACP,OAAO1oB,KAAKqoB,YAAYvL,UAAUqE,EAAQvF,GACrC,GAAI5b,KAAKopB,eAAyC,IAA1BxN,EAAIwE,QAAQ,WACzC,OAAOpgB,KAAKqoB,YAAY6S,UAAU/Z,EAAQvF,GAE1C,KAAM,gDAIV,iBACE,IAAIuf,EAAcn7B,KAAK45B,YACvB,OACEhZ,QAAQua,SAAsDja,KAAtC,IAAIia,GAAcC,iBAI9C,eAAezgB,GACb,IAAIH,EAAWG,EAAS,SAAW,QAC/B0gB,EAAmBr7B,KAAKs7B,cAC5B,OACE1a,QAAanjB,OAAuB,iBAAM49B,IAAqB7gB,GAInE,kBAAkBmR,QACgBzK,IAA5BzjB,OAAO2lB,iBACT3lB,OAAO2lB,iBAAiB,SAAUuI,GAAU,QACZzK,IAAvBzjB,OAAO+lB,aAChB/lB,OAAO+lB,YAAY,WAAYmI,IAInC,qBAAqBA,QACazK,IAA5BzjB,OAAO2lB,iBACT3lB,OAAO89B,oBAAoB,SAAU5P,GAAU,QACfzK,IAAvBzjB,OAAO+9B,aAChB/9B,OAAO+9B,YAAY,WAAY7P,IAInCwM,UAAU/N,GAWD3hB,KAAKC,OANKjL,OAAO2Z,QAAU3Z,OAAiB,UAC3B8Z,gBAAgB,IAAIkkB,YAAY,IAAI,GAE1C,WAAK,IAGMrR,KNzKjC,SAAKgO,GACH,qBACA,mBACA,qBAHF,CAAKA,QAAa,KAMH,UQOA,MAAM,GAQnB,YAAYh5B,EAAai5B,EAAiBze,GACxC5Z,KAAKZ,IAAMA,EACXY,KAAKq4B,QAAUA,EACfr4B,KAAK07B,OAAS,GACd17B,KAAK4Z,QAAUA,GAAW,GAC1B5Z,KAAK27B,KAAO,EACZ37B,KAAK47B,SAAW,EAGlB,IAAIC,EAAOjR,GACLiR,GAAS77B,KAAK4Z,QAAQiiB,QACxB77B,KAAK07B,OAAOr5B,KACV,EAAmB,GAAIuoB,EAAO,CAAE0J,UAAW,EAAKtV,SAE9Chf,KAAK4Z,QAAQkiB,OAAS97B,KAAK07B,OAAO37B,OAASC,KAAK4Z,QAAQkiB,OAC1D97B,KAAK07B,OAAOK,SAKlB,MAAMnR,GACJ5qB,KAAK6hB,IAAI,GAAMma,MAAOpR,GAGxB,KAAKA,GACH5qB,KAAK6hB,IAAI,GAAMoa,KAAMrR,GAGvB,MAAMA,GACJ5qB,KAAK6hB,IAAI,GAAMqa,MAAOtR,GAGxB,UACE,OAA8B,IAAvB5qB,KAAK07B,OAAO37B,OAGrB,KAAKo8B,EAAQvkB,GACX,IAAI1X,EAAO,EACT,CACEm4B,QAASr4B,KAAKq4B,QACd+D,OAAQp8B,KAAK27B,KAAO,EACpBv8B,IAAKY,KAAKZ,IACVi9B,IAAK,KACLxhB,QAAS7a,KAAK4Z,QAAQiB,QACtByhB,QAASt8B,KAAK4Z,QAAQ0iB,QACtBC,SAAUv8B,KAAK4Z,QAAQ2iB,SACvBxW,SAAU/lB,KAAK07B,QAEjB17B,KAAK4Z,QAAQqH,QAaf,OAVAjhB,KAAK07B,OAAS,GACdS,EAAOj8B,EAAM,CAACga,EAAO5Y,KACd4Y,GACHla,KAAK27B,OAEH/jB,GACFA,EAASsC,EAAO5Y,MAIb,EAGT,mBAEE,OADAtB,KAAK47B,WACE57B,KAAK47B,UCvED,MAAM,GAMnB,YACEx9B,EACAunB,EACAtM,EACAO,GAEA5Z,KAAK5B,KAAOA,EACZ4B,KAAK2lB,SAAWA,EAChB3lB,KAAKqZ,UAAYA,EACjBrZ,KAAK4Z,QAAUA,GAAW,GAO5B,cACE,OAAO5Z,KAAKqZ,UAAUyO,YAAY,CAChCnN,OAAQ3a,KAAK4Z,QAAQe,SASzB,QAAQuY,EAAqBtb,GAC3B,IAAK5X,KAAK8nB,cACR,OAAO0U,GAAY,IAAI,EAA8B5kB,GAChD,GAAI5X,KAAK2lB,SAAWuN,EACzB,OAAOsJ,GAAY,IAAI,EAAkC5kB,GAG3D,IAAIsZ,GAAY,EACZ7X,EAAYrZ,KAAKqZ,UAAU0Q,iBAC7B/pB,KAAK5B,KACL4B,KAAK2lB,SACL3lB,KAAK4Z,QAAQxa,IACbY,KAAK4Z,SAEHoW,EAAY,KAEZyM,EAAgB,WAClBpjB,EAAUkM,OAAO,cAAekX,GAChCpjB,EAAU4W,WAER7I,EAAS,WACX4I,EAAY,GAAQyC,gBAAgBpZ,GAAW,SAAU/X,GACvD4vB,GAAY,EACZ/J,IACAvP,EAAS,KAAMtW,OAGf+kB,EAAU,SAAUnM,GACtBiN,IACAvP,EAASsC,IAEP+P,EAAW,WAEb,IAAIyS,EADJvV,IAOAuV,EAAsB,EAA8BrjB,GACpDzB,EAAS,IAAI,EAAuB8kB,KAGlCvV,EAAkB,WACpB9N,EAAUkM,OAAO,cAAekX,GAChCpjB,EAAUkM,OAAO,OAAQ6B,GACzB/N,EAAUkM,OAAO,QAASc,GAC1BhN,EAAUkM,OAAO,SAAU0E,IAW7B,OARA5Q,EAAUha,KAAK,cAAeo9B,GAC9BpjB,EAAUha,KAAK,OAAQ+nB,GACvB/N,EAAUha,KAAK,QAASgnB,GACxBhN,EAAUha,KAAK,SAAU4qB,GAGzB5Q,EAAUuM,aAEH,CACLwK,MAAO,KACDc,IAGJ/J,IACI6I,EACFA,EAAUvJ,QAEVpN,EAAUoN,UAGd4M,iBAAmB1zB,IACbuxB,GAGAlxB,KAAK2lB,SAAWhmB,IACdqwB,EACFA,EAAUvJ,QAEVpN,EAAUoN,YAQtB,SAAS+V,GAAYtiB,EAActC,GAIjC,OAHA,EAAKuH,OAAM,WACTvH,EAASsC,MAEJ,CACLkW,MAAO,aACPiD,iBAAkB,cCnItB,MAAQ2G,WAAU,IAAK,GAEhB,IAAI,GAAkB,SAC3BpN,EACAxuB,EACA8kB,EACAyC,EACA/L,EACA8P,GAEA,IAWIrQ,EAXAsjB,EAAiB,GAAWzZ,GAChC,IAAKyZ,EACH,MAAM,IAAI,EAA4BzZ,GA0BxC,QAtBI0J,EAAOgQ,oBACuD,IAA9D,EAAyBhQ,EAAOgQ,kBAAmBx+B,IACnDwuB,EAAOiQ,qBACwD,IAA/D,EAAyBjQ,EAAOiQ,mBAAoBz+B,KAItDwb,EAAUrb,OAAOmuB,OACf,CAAExD,iBAAkB0D,EAAO1D,kBAC3BtP,GAGFP,EAAY,IAAI,GACdjb,EACAunB,EACA+D,EAAUA,EAAQoT,aAAaH,GAAkBA,EACjD/iB,IAGFP,EAAY,GAGPA,GAGL,GAAgC,CAClCyO,YAAa,WACX,OAAO,GAETmI,QAAS,SAAU1P,EAAG3I,GACpB,IAAImlB,EAAW,EAAK5d,OAAM,WACxBvH,EAAS,IAAI,MAEf,MAAO,CACLwY,MAAO,WACL2M,EAASpM,iBAEX0C,iBAAkB,gBCjBjB,SAAS2J,GAAgBpjB,GAC9B,GAAe,MAAXA,EACF,KAAM,kCAER,GAAuB,MAAnBA,EAAQ0iB,QACV,KAAM,wCAEJ,iBAAkB1iB,GACpB,EAAOoI,KACL,iECMS,OAtBbrF,IAEA,QAA+D,IAApD,GAAQ+d,iBAAiB/d,EAAYtD,WAC9C,KAAM,IAAIsD,EAAYtD,gDAGxB,MAAO,CACL4H,EACArJ,KAEA,MAAM8E,EAvCkB,EAC1BuE,EACAtE,KAEA,IAAID,EAAQ,aAAeqE,mBAAmBE,EAAOiL,UAErD,IAAK,IAAI9sB,KAAOud,EAAYsE,OAC1BvE,GACE,IACAqE,mBAAmB3hB,GACnB,IACA2hB,mBAAmBpE,EAAYsE,OAAO7hB,IAG1C,GAAkC,MAA9Bud,EAAYsgB,eAAwB,CACtC,IAAIC,EAAgBvgB,EAAYsgB,iBAChC,IAAK,IAAI79B,KAAO89B,EACdxgB,GACE,IACAqE,mBAAmB3hB,GACnB,IACA2hB,mBAAmBmc,EAAc99B,IAIvC,OAAOsd,GAcSygB,CAAoBlc,EAAQtE,GAE1C,GAAQ+d,iBAAiB/d,EAAYtD,WACnC,GACAqD,EACAC,EACAjB,EAAgBiC,mBAChB/F,KCOS,OAtBb+E,IAEA,QAA+D,IAApD,GAAQ+d,iBAAiB/d,EAAYtD,WAC9C,KAAM,IAAIsD,EAAYtD,gDAGxB,MAAO,CACL4H,EACArJ,KAEA,MAAM8E,EAzCkB,EAC1BuE,EACAtE,KAEA,IAAID,EAAQ,aAAeqE,mBAAmBE,EAAOiL,UAIrD,IAAK,IAAI9sB,KAFTsd,GAAS,iBAAmBqE,mBAAmBE,EAAO6L,aAEtCnQ,EAAYsE,OAC1BvE,GACE,IACAqE,mBAAmB3hB,GACnB,IACA2hB,mBAAmBpE,EAAYsE,OAAO7hB,IAG1C,GAAkC,MAA9Bud,EAAYsgB,eAAwB,CACtC,IAAIC,EAAgBvgB,EAAYsgB,iBAChC,IAAK,IAAI79B,KAAO89B,EACdxgB,GACE,IACAqE,mBAAmB3hB,GACnB,IACA2hB,mBAAmBmc,EAAc99B,IAIvC,OAAOsd,GAcS,CAAoBuE,EAAQtE,GAE1C,GAAQ+d,iBAAiB/d,EAAYtD,WACnC,GACAqD,EACAC,EACAjB,EAAgBkC,qBAChBhG,KCgCN,SAASwlB,GAAYC,GACnB,OAAIA,EAAK5kB,SACA4kB,EAAK5kB,SAEV4kB,EAAKf,QACA,UAAUe,EAAKf,qBAEjB,EAAS7jB,SAGlB,SAAS6kB,GAAiBD,GACxB,OAAIA,EAAK5H,OACA4H,EAAK5H,OAMP,MAJ4B4H,EAAKf,qBAO1C,SAASiB,GAAaF,GACpB,MAA8B,WAA1B,GAAQ/B,gBAEiB,IAAlB+B,EAAKG,SASlB,SAASC,GAAqBJ,GAC5B,MAAI,gBAAiBA,EACZA,EAAKK,YAEV,iBAAkBL,IACZA,EAAKM,aAKjB,SAASC,GAAuBP,GAC9B,MAAMlkB,EAAqB,OAAH,wBACnB,EAASA,oBACTkkB,EAAKlkB,oBAEV,MACE,kBAAmBA,GACoB,MAAvCA,EAAkC,cAE3BA,EAAkC,cAGpC,GAAkBA,GA8B3B,SAAS0kB,GACPR,EACAvR,GAEA,MAAMxS,EA/BR,SAA0B+jB,EAAevR,GACvC,IAAIxS,EAuBJ,MAtBI,yBAA0B+jB,EAC5B/jB,EAAuB,OAAH,wBACf,EAASA,sBACT+jB,EAAK/jB,uBAGVA,EAAuB,CACrBD,UAAWgkB,EAAKtkB,eAAiB,EAASA,cAC1CK,SAAUikB,EAAKvkB,cAAgB,EAASA,cAEtC,SAAUukB,IACR,WAAYA,EAAKlR,OAAM7S,EAAqB2H,OAASoc,EAAKlR,KAAKlL,QAC/D,YAAaoc,EAAKlR,OACpB7S,EAAqB2D,QAAUogB,EAAKlR,KAAKlP,UAEzC,eAAgBogB,IAClB/jB,EAAqBwkB,cCxIW,EACpChS,EACAnP,EACAohB,KAEA,MAAMC,EAA2D,CAC/DjlB,cAAe4D,EAAYtD,UAC3BP,aAAc6D,EAAYvD,SAC1B+S,KAAM,CACJlL,OAAQtE,EAAYsE,OACpBhE,QAASN,EAAYM,UAGzB,MAAO,CACLgE,EACArJ,KAEA,MAAMiT,EAAUiB,EAAOjB,QAAQ5J,EAAO6L,aAKpCiR,EAA2BlT,EAASmT,GACpBvR,UAAUxL,EAAOiL,SAAUtU,KDiHNqmB,CACnCnS,EACAxS,EACA+jB,EAAKa,cAGJ5kB,EAOsB6kB,CAAiBd,EAAMvR,GACpD,MACE,kBAAmBxS,GACsB,MAAzCA,EAAoC,cAE7BA,EAAoC,cAGtC,GAAkBA,GEvLZ,MAAM,WAAwB,GAG3C,YAAmBwS,GACjBhQ,OAAM,SAAUwJ,EAAWplB,GACzB,EAAOsmB,MAAM,wCAAwClB,MAGvDtlB,KAAK8rB,OAASA,EACd9rB,KAAKo+B,6BAGP,YAAYzT,GACVA,EAAYzqB,KAAKw7B,OAAO2C,QAASC,IAC/Bt+B,KAAK6mB,KAAKyX,EAAelgC,KAAMkgC,KAI3B,6BACNt+B,KAAK8rB,OAAOhC,WAAWzqB,KAAK,UAAYsrB,IAEpB,qCADFA,EAAYC,OAE1B5qB,KAAKwuB,YAAY7D,MCjBV,OATf,WACE,IAAI4T,EAASC,EAKb,MAAO,CAAEC,QAJO,IAAIC,QAAQ,CAACC,EAAKC,KAChCL,EAAUI,EACVH,EAASI,IAEOL,UAASC,WCKd,MAAM,WAAmB,GAStC,YAAmB1S,GACjBhQ,OAAM,SAAUwJ,EAAWplB,GACzB,EAAOsmB,MAAM,4BAA8BlB,MAT/C,KAAAuZ,kBAA4B,EAC5B,KAAA/Q,UAAiB,KACjB,KAAAgR,oBAA+B,KAC/B,KAAAjR,kBAAkC,KAE1B,KAAAkR,mBAA+B,KA8D/B,KAAAC,aAA2C,CACjDC,EACAxR,KAEA,GAAIwR,EAGF,OAFA,EAAOjd,KAAK,wBAAwBid,QACpCj/B,KAAKk/B,WAIPl/B,KAAK8rB,OAAOR,WAAW,gBAAiB,CACtCa,KAAMsB,EAAStB,KACf2B,UAAWL,EAASK,aApEtB9tB,KAAK8rB,OAASA,EACd9rB,KAAK8rB,OAAOhC,WAAWzqB,KAAK,eAAgB,EAAGwyB,WAAUC,cACtC,cAAbD,GAAwC,cAAZC,GAC9B9xB,KAAKm/B,UAEU,cAAbtN,GAAwC,cAAZC,IAC9B9xB,KAAKk/B,WACLl/B,KAAKo/B,+BAITp/B,KAAKq/B,UAAY,IAAI,GAAgBvT,GAErC9rB,KAAK8rB,OAAOhC,WAAWzqB,KAAK,UAAYurB,IAEpB,0BADFA,EAAMA,OAEpB5qB,KAAKs/B,iBAAiB1U,EAAM1qB,MAG5BF,KAAK8+B,qBACL9+B,KAAK8+B,oBAAoB1gC,OAASwsB,EAAMC,SAExC7qB,KAAK8+B,oBAAoBtQ,YAAY5D,KAKpC,SACD5qB,KAAK6+B,mBAIT7+B,KAAK6+B,kBAAmB,EACxB7+B,KAAKm/B,WAGC,UACDn/B,KAAK6+B,mBAIV7+B,KAAKo/B,4BAEgC,cAAjCp/B,KAAK8rB,OAAOhC,WAAWhE,OAK3B9lB,KAAK8rB,OAAOc,OAAO2S,kBACjB,CACErT,SAAUlsB,KAAK8rB,OAAOhC,WAAWqB,WAEnCnrB,KAAKg/B,eAsBD,iBAAiB9+B,GACvB,IACEF,KAAK8tB,UAAYvQ,KAAKC,MAAMtd,EAAK4tB,WACjC,MAAO9hB,GAGP,OAFA,EAAOkO,MAAM,0CAA0Cha,EAAK4tB,gBAC5D9tB,KAAKk/B,WAIP,GAAiC,iBAAtBl/B,KAAK8tB,UAAUhW,IAAyC,KAAtB9X,KAAK8tB,UAAUhW,GAK1D,OAJA,EAAOoC,MACL,+CAA+Cla,KAAK8tB,gBAEtD9tB,KAAKk/B,WAKPl/B,KAAK++B,qBACL/+B,KAAKw/B,qBAGC,qBAYNx/B,KAAK8+B,oBAAsB,IAAI,GAC7B,mBAAmB9+B,KAAK8tB,UAAUhW,GAClC9X,KAAK8rB,QAEP9rB,KAAK8+B,oBAAoBW,YAAY,CAACna,EAAWplB,KAEH,IAA1ColB,EAAUlF,QAAQ,qBACe,IAAjCkF,EAAUlF,QAAQ,YAKpBpgB,KAAK6mB,KAAKvB,EAAWplB,KAvBG,CAAC2qB,IACrBA,EAAQmB,qBAAuBnB,EAAQoB,sBACzCpB,EAAQ6U,wBAEP7U,EAAQmB,qBACwB,cAAjChsB,KAAK8rB,OAAOhC,WAAWhE,OAEvB+E,EAAQ8U,aAkBZC,CAAkB5/B,KAAK8+B,qBAGjB,WACN9+B,KAAK8tB,UAAY,KACb9tB,KAAK8+B,sBACP9+B,KAAK8+B,oBAAoBnF,aACzB35B,KAAK8+B,oBAAoBzQ,aACzBruB,KAAK8+B,oBAAsB,MAGzB9+B,KAAK6+B,kBAGP7+B,KAAK++B,qBAID,4BACN,IAAK/+B,KAAK6+B,iBACR,OAIF,GAAI7+B,KAAK6tB,oBAAuB7tB,KAAK6tB,kBAA0BgS,KAC7D,OAKF,MAAM,QAAEpB,EAAO,QAAEF,EAASC,OAAQje,GAAM,KACvCke,EAAgBoB,MAAO,EACxB,MAAMC,EAAU,KACbrB,EAAgBoB,MAAO,GAE1BpB,EAAQsB,KAAKD,GAASE,MAAMF,GAC5B9/B,KAAK6tB,kBAAoB4Q,EACzBz+B,KAAK++B,mBAAqBR,GC/J9B,MAAqB,GAYnB,eACE,GAAO0B,SAAU,EACjB,IAAK,IAAIpiC,EAAI,EAAGC,EAAI,GAAOoiC,UAAUngC,OAAQlC,EAAIC,EAAGD,IAClD,GAAOqiC,UAAUriC,GAAGoyB,UAMhB,2BACN,OAAO,EACL,EAAyB,CAAEvL,GAAI,GAAQsV,WAAWtV,KAAM,SAAU3lB,GAChE,OAAOA,EAAE+oB,YAAY,QAgB3B,YAAYqY,EAAiBvmB,IAsL/B,SAAqBxa,GACnB,GAAIA,QACF,KAAM,0DAvLNghC,CAAYD,GACZnD,GAAgBpjB,GAChB5Z,KAAKZ,IAAM+gC,EACXngC,KAAK4sB,OLfF,SAAmByQ,EAAevR,GACvC,IAAIc,EAAiB,CACnB5T,gBAAiBqkB,EAAKrkB,iBAAmB,EAASA,gBAClDsjB,QAASe,EAAKf,QACd1jB,SAAUykB,EAAKzkB,UAAY,EAASA,SACpCF,SAAU2kB,EAAK3kB,UAAY,EAASA,SACpCC,UAAW0kB,EAAK1kB,WAAa,EAASA,UACtCM,YAAaokB,EAAKpkB,aAAe,EAASA,YAC1ConB,UAAWhD,EAAKgD,WAAa,EAASxnB,WACtCK,mBAAoBmkB,EAAKnkB,oBAAsB,EAASA,mBACxDV,OAAQ6kB,EAAK7kB,QAAU,EAASA,OAChCF,OAAQ+kB,EAAK/kB,QAAU,EAASA,OAChCC,QAAS8kB,EAAK9kB,SAAW,EAASA,QAElCmlB,YAAaD,GAAqBJ,GAClC5kB,SAAU2kB,GAAYC,GACtB1iB,OAAQ4iB,GAAaF,GACrB5H,OAAQ6H,GAAiBD,GAEzBkC,kBAAmB3B,GAAuBP,GAC1CxQ,kBAAmBgR,GAAuBR,EAAMvR,IAclD,MAXI,uBAAwBuR,IAC1BzQ,EAAOiQ,mBAAqBQ,EAAKR,oBAC/B,sBAAuBQ,IACzBzQ,EAAOgQ,kBAAoBS,EAAKT,mBAC9B,qBAAsBS,IACxBzQ,EAAO1D,iBAAmBmU,EAAKnU,kBAC7B,mBAAoBmU,IAAMzQ,EAAO0T,eAAiBjD,EAAKiD,gBACvD,SAAUjD,IACZzQ,EAAOrqB,KAAO86B,EAAK96B,MAGdqqB,EKnBS2T,CAAU3mB,EAAS5Z,MAEjCA,KAAK+xB,SAAW,GAAQO,iBACxBtyB,KAAKwgC,eAAiB,IAAI,GAC1BxgC,KAAKygC,UAAY,GAAQtI,UAAU,KAEnCn4B,KAAK+lB,SAAW,IAAI,GAAS/lB,KAAKZ,IAAKY,KAAKygC,UAAW,CACrDnE,QAASt8B,KAAK4sB,OAAO0P,QACrBC,SAAU,GAAOmE,oBACjBzf,OAAQjhB,KAAK4sB,OAAO0T,gBAAkB,GACtCxE,MAAO,GACPD,MAAO,GAAcI,KACrBphB,QAAS,EAASzC,UAEhBpY,KAAK4sB,OAAO8Q,cACd19B,KAAK2gC,eAAiB,GAAQnO,qBAAqBxyB,KAAK+lB,SAAU,CAChE7B,KAAMlkB,KAAK4sB,OAAOyT,UAClBjlB,KAAM,gBAAkB,GAAQyQ,kBAAkBztB,QAQtD4B,KAAK8pB,WAAa,GAAQyI,wBAAwBvyB,KAAKZ,IAAK,CAC1DoxB,YALiB5W,GACV,GAAQmgB,mBAAmB/5B,KAAK4sB,OAAQhT,EAAS,IAKxDmM,SAAU/lB,KAAK+lB,SACf/M,gBAAiBhZ,KAAK4sB,OAAO5T,gBAC7BC,YAAajZ,KAAK4sB,OAAO3T,YACzBC,mBAAoBlZ,KAAK4sB,OAAO1T,mBAChCyB,OAAQiG,QAAQ5gB,KAAK4sB,OAAOjS,UAG9B3a,KAAK8pB,WAAWzqB,KAAK,YAAa,KAChCW,KAAK4gC,eACD5gC,KAAK2gC,gBACP3gC,KAAK2gC,eAAepmB,KAAKva,KAAK8pB,WAAW+W,gBAI7C7gC,KAAK8pB,WAAWzqB,KAAK,UAAYurB,IAC/B,IACIkW,EAAqD,IADzClW,EAAMA,MACGxK,QAAQ,oBACjC,GAAIwK,EAAMC,QAAS,CACjB,IAAIA,EAAU7qB,KAAK6qB,QAAQD,EAAMC,SAC7BA,GACFA,EAAQ2D,YAAY5D,GAInBkW,GACH9gC,KAAKwgC,eAAe3Z,KAAK+D,EAAMA,MAAOA,EAAM1qB,QAGhDF,KAAK8pB,WAAWzqB,KAAK,aAAc,KACjCW,KAAK+xB,SAAS1D,eAEhBruB,KAAK8pB,WAAWzqB,KAAK,eAAgB,KACnCW,KAAK+xB,SAAS1D,eAEhBruB,KAAK8pB,WAAWzqB,KAAK,QAAU4/B,IAC7B,EAAOjd,KAAKid,KAGd,GAAOiB,UAAU79B,KAAKrC,MACtBA,KAAK+lB,SAAS4B,KAAK,CAAEuY,UAAW,GAAOA,UAAUngC,SAEjDC,KAAK4tB,KAAO,IAAI,GAAW5tB,MAEvB,GAAOigC,SACTjgC,KAAKiwB,UAIT,QAAQ7xB,GACN,OAAO4B,KAAK+xB,SAASgP,KAAK3iC,GAG5B,cACE,OAAO4B,KAAK+xB,SAASiP,MAGvB,UAGE,GAFAhhC,KAAK8pB,WAAWmG,UAEZjwB,KAAK2gC,iBACF3gC,KAAKihC,oBAAqB,CAC7B,IAAIpS,EAAW7uB,KAAK8pB,WAAW+W,aAC3BF,EAAiB3gC,KAAK2gC,eAC1B3gC,KAAKihC,oBAAsB,IAAI,EAAc,KAAO,WAClDN,EAAepmB,KAAKsU,OAM5B,aACE7uB,KAAK8pB,WAAWuE,aAEZruB,KAAKihC,sBACPjhC,KAAKihC,oBAAoBtQ,gBACzB3wB,KAAKihC,oBAAsB,MAI/B,KAAKC,EAAoBtpB,EAAoB6E,GAE3C,OADAzc,KAAKwgC,eAAenhC,KAAK6hC,EAAYtpB,EAAU6E,GACxCzc,KAGT,OAAOkhC,EAAqBtpB,EAAqB6E,GAE/C,OADAzc,KAAKwgC,eAAejb,OAAO2b,EAAYtpB,EAAU6E,GAC1Czc,KAGT,YAAY4X,GAEV,OADA5X,KAAKwgC,eAAef,YAAY7nB,GACzB5X,KAGT,cAAc4X,GAEZ,OADA5X,KAAKwgC,eAAehb,cAAc5N,GAC3B5X,KAGT,WAAW4X,GAET,OADA5X,KAAKwgC,eAAe7G,aACb35B,KAGT,eACE,IAAI8sB,EACJ,IAAKA,KAAe9sB,KAAK+xB,SAASA,SAC5B/xB,KAAK+xB,SAASA,SAASryB,eAAeotB,IACxC9sB,KAAK2/B,UAAU7S,GAKrB,UAAUqU,GACR,IAAItW,EAAU7qB,KAAK+xB,SAASrgB,IAAIyvB,EAAcnhC,MAS9C,OARI6qB,EAAQmB,qBAAuBnB,EAAQoB,sBACzCpB,EAAQ6U,wBAEP7U,EAAQmB,qBACiB,cAA1BhsB,KAAK8pB,WAAWhE,OAEhB+E,EAAQ8U,YAEH9U,EAGT,YAAYsW,GACV,IAAItW,EAAU7qB,KAAK+xB,SAASgP,KAAKI,GAC7BtW,GAAWA,EAAQmB,oBACrBnB,EAAQuW,sBAERvW,EAAU7qB,KAAK+xB,SAAS5X,OAAOgnB,KAChBtW,EAAQkB,YACrBlB,EAAQyB,cAKd,WAAW4U,EAAoBhhC,EAAW2qB,GACxC,OAAO7qB,KAAK8pB,WAAWwB,WAAW4V,EAAYhhC,EAAM2qB,GAGtD,eACE,OAAO7qB,KAAK4sB,OAAOjS,OAGrB,SACE3a,KAAK4tB,KAAKyT,UAxNL,GAAAnB,UAAsB,GACtB,GAAAD,SAAmB,EACnB,GAAA/d,cAAwB,EAGxB,GAAAof,QAA2B,GAC3B,GAAAnpB,gBAA6B,GAASA,gBACtC,GAAAuB,sBAAmC,GAASA,sBAC5C,GAAA8I,eAA4B,GAASA,eAVzB,UAoOrB,GAAQ+e,MAAM,I,YCxPC,MAAM,WAA6B,GAChD,YAAYpB,EAAiBvmB,GAC3B,GAAOsI,aAAe,GAAqBA,aAC3C,GAAOL,IAAM,GAAqBA,IAElCmb,GAAgBpjB,GAChBA,EAAQrX,KAAO,GACfuZ,MAAMqkB,EAASvmB", "file": "pusher-with-encryption.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Pusher\"] = factory();\n\telse\n\t\troot[\"Pusher\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 3);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package base64 implements Base64 encoding and decoding.\n */\n\n// Invalid character used in decoding to indicate\n// that the character to decode is out of range of\n// alphabet and cannot be decoded.\nconst INVALID_BYTE = 256;\n\n/**\n * Implements standard Base64 encoding.\n *\n * Operates in constant time.\n */\nexport class Coder {\n    // TODO(dchest): methods to encode chunk-by-chunk.\n\n    constructor(private _padding<PERSON>haracter = \"=\") { }\n\n    encodedLength(length: number): number {\n        if (!this._padding<PERSON>haracter) {\n            return (length * 8 + 5) / 6 | 0;\n        }\n        return (length + 2) / 3 * 4 | 0;\n    }\n\n    encode(data: Uint8Array): string {\n        let out = \"\";\n\n        let i = 0;\n        for (; i < data.length - 2; i += 3) {\n            let c = (data[i] << 16) | (data[i + 1] << 8) | (data[i + 2]);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            out += this._encodeByte((c >>> 1 * 6) & 63);\n            out += this._encodeByte((c >>> 0 * 6) & 63);\n        }\n\n        const left = data.length - i;\n        if (left > 0) {\n            let c = (data[i] << 16) | (left === 2 ? data[i + 1] << 8 : 0);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            if (left === 2) {\n                out += this._encodeByte((c >>> 1 * 6) & 63);\n            } else {\n                out += this._paddingCharacter || \"\";\n            }\n            out += this._paddingCharacter || \"\";\n        }\n\n        return out;\n    }\n\n    maxDecodedLength(length: number): number {\n        if (!this._paddingCharacter) {\n            return (length * 6 + 7) / 8 | 0;\n        }\n        return length / 4 * 3 | 0;\n    }\n\n    decodedLength(s: string): number {\n        return this.maxDecodedLength(s.length - this._getPaddingLength(s));\n    }\n\n    decode(s: string): Uint8Array {\n        if (s.length === 0) {\n            return new Uint8Array(0);\n        }\n        const paddingLength = this._getPaddingLength(s);\n        const length = s.length - paddingLength;\n        const out = new Uint8Array(this.maxDecodedLength(length));\n        let op = 0;\n        let i = 0;\n        let haveBad = 0;\n        let v0 = 0, v1 = 0, v2 = 0, v3 = 0;\n        for (; i < length - 4; i += 4) {\n            v0 = this._decodeChar(s.charCodeAt(i + 0));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n            haveBad |= v2 & INVALID_BYTE;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (i < length - 1) {\n            v0 = this._decodeChar(s.charCodeAt(i));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n        }\n        if (i < length - 2) {\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            haveBad |= v2 & INVALID_BYTE;\n        }\n        if (i < length - 3) {\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (haveBad !== 0) {\n            throw new Error(\"Base64Coder: incorrect characters for decoding\");\n        }\n        return out;\n    }\n\n    // Standard encoding have the following encoded/decoded ranges,\n    // which we need to convert between.\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  +   /\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   43  47\n    //\n\n    // Encode 6 bits in b into a new character.\n    protected _encodeByte(b: number): string {\n        // Encoding uses constant time operations as follows:\n        //\n        // 1. Define comparison of A with B using (A - B) >>> 8:\n        //          if A > B, then result is positive integer\n        //          if A <= B, then result is 0\n        //\n        // 2. Define selection of C or 0 using bitwise AND: X & C:\n        //          if X == 0, then result is 0\n        //          if X != 0, then result is C\n        //\n        // 3. Start with the smallest comparison (b >= 0), which is always\n        //    true, so set the result to the starting ASCII value (65).\n        //\n        // 4. Continue comparing b to higher ASCII values, and selecting\n        //    zero if comparison isn't true, otherwise selecting a value\n        //    to add to result, which:\n        //\n        //          a) undoes the previous addition\n        //          b) provides new value to add\n        //\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 43);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 43) - 63 + 47);\n\n        return String.fromCharCode(result);\n    }\n\n    // Decode a character code into a byte.\n    // Must return 256 if character is out of alphabet range.\n    protected _decodeChar(c: number): number {\n        // Decoding works similar to encoding: using the same comparison\n        // function, but now it works on ranges: result is always incremented\n        // by value, but this value becomes zero if the range is not\n        // satisfied.\n        //\n        // Decoding starts with invalid value, 256, which is then\n        // subtracted when the range is satisfied. If none of the ranges\n        // apply, the function returns 256, which is then checked by\n        // the caller to throw error.\n        let result = INVALID_BYTE; // start with invalid character\n\n        // c == 43 (c > 42 and c < 44)\n        result += (((42 - c) & (c - 44)) >>> 8) & (-INVALID_BYTE + c - 43 + 62);\n        // c == 47 (c > 46 and c < 48)\n        result += (((46 - c) & (c - 48)) >>> 8) & (-INVALID_BYTE + c - 47 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n\n    private _getPaddingLength(s: string): number {\n        let paddingLength = 0;\n        if (this._paddingCharacter) {\n            for (let i = s.length - 1; i >= 0; i--) {\n                if (s[i] !== this._paddingCharacter) {\n                    break;\n                }\n                paddingLength++;\n            }\n            if (s.length < 4 || paddingLength > 2) {\n                throw new Error(\"Base64Coder: incorrect padding\");\n            }\n        }\n        return paddingLength;\n    }\n\n}\n\nconst stdCoder = new Coder();\n\nexport function encode(data: Uint8Array): string {\n    return stdCoder.encode(data);\n}\n\nexport function decode(s: string): Uint8Array {\n    return stdCoder.decode(s);\n}\n\n/**\n * Implements URL-safe Base64 encoding.\n * (Same as Base64, but '+' is replaced with '-', and '/' with '_').\n *\n * Operates in constant time.\n */\nexport class URLSafeCoder extends Coder {\n    // URL-safe encoding have the following encoded/decoded ranges:\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  -   _\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   45  95\n    //\n\n    protected _encodeByte(b: number): string {\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 45);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 45) - 63 + 95);\n\n        return String.fromCharCode(result);\n    }\n\n    protected _decodeChar(c: number): number {\n        let result = INVALID_BYTE;\n\n        // c == 45 (c > 44 and c < 46)\n        result += (((44 - c) & (c - 46)) >>> 8) & (-INVALID_BYTE + c - 45 + 62);\n        // c == 95 (c > 94 and c < 96)\n        result += (((94 - c) & (c - 96)) >>> 8) & (-INVALID_BYTE + c - 95 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n}\n\nconst urlSafeCoder = new URLSafeCoder();\n\nexport function encodeURLSafe(data: Uint8Array): string {\n    return urlSafeCoder.encode(data);\n}\n\nexport function decodeURLSafe(s: string): Uint8Array {\n    return urlSafeCoder.decode(s);\n}\n\n\nexport const encodedLength = (length: number) =>\n    stdCoder.encodedLength(length);\n\nexport const maxDecodedLength = (length: number) =>\n    stdCoder.maxDecodedLength(length);\n\nexport const decodedLength = (s: string) =>\n    stdCoder.decodedLength(s);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package utf8 implements UTF-8 encoding and decoding.\n */\n\nconst INVALID_UTF16 = \"utf8: invalid string\";\nconst INVALID_UTF8 = \"utf8: invalid source encoding\";\n\n/**\n * Encodes the given string into UTF-8 byte array.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encode(s: string): Uint8Array {\n    // Calculate result length and allocate output array.\n    // encodedLength() also validates string and throws errors,\n    // so we don't need repeat validation here.\n    const arr = new Uint8Array(encodedLength(s));\n\n    let pos = 0;\n    for (let i = 0; i < s.length; i++) {\n        let c = s.charCodeAt(i);\n        if (c < 0x80) {\n            arr[pos++] = c;\n        } else if (c < 0x800) {\n            arr[pos++] = 0xc0 | c >> 6;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else if (c < 0xd800) {\n            arr[pos++] = 0xe0 | c >> 12;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else {\n            i++; // get one more character\n            c = (c & 0x3ff) << 10;\n            c |= s.charCodeAt(i) & 0x3ff;\n            c += 0x10000;\n\n            arr[pos++] = 0xf0 | c >> 18;\n            arr[pos++] = 0x80 | (c >> 12) & 0x3f;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        }\n    }\n    return arr;\n}\n\n/**\n * Returns the number of bytes required to encode the given string into UTF-8.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encodedLength(s: string): number {\n    let result = 0;\n    for (let i = 0; i < s.length; i++) {\n        const c = s.charCodeAt(i);\n        if (c < 0x80) {\n            result += 1;\n        } else if (c < 0x800) {\n            result += 2;\n        } else if (c < 0xd800) {\n            result += 3;\n        } else if (c <= 0xdfff) {\n            if (i >= s.length - 1) {\n                throw new Error(INVALID_UTF16);\n            }\n            i++; // \"eat\" next character\n            result += 4;\n        } else {\n            throw new Error(INVALID_UTF16);\n        }\n    }\n    return result;\n}\n\n/**\n * Decodes the given byte array from UTF-8 into a string.\n * Throws if encoding is invalid.\n */\nexport function decode(arr: Uint8Array): string {\n    const chars: string[] = [];\n    for (let i = 0; i < arr.length; i++) {\n        let b = arr[i];\n\n        if (b & 0x80) {\n            let min;\n            if (b < 0xe0) {\n                // Need 1 more byte.\n                if (i >= arr.length) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x1f) << 6 | (n1 & 0x3f);\n                min = 0x80;\n            } else if (b < 0xf0) {\n                // Need 2 more bytes.\n                if (i >= arr.length - 1) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 12 | (n1 & 0x3f) << 6 | (n2 & 0x3f);\n                min = 0x800;\n            } else if (b < 0xf8) {\n                // Need 3 more bytes.\n                if (i >= arr.length - 2) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                const n3 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80 || (n3 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 18 | (n1 & 0x3f) << 12 | (n2 & 0x3f) << 6 | (n3 & 0x3f);\n                min = 0x10000;\n            } else {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b < min || (b >= 0xd800 && b <= 0xdfff)) {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b >= 0x10000) {\n                // Surrogate pair.\n                if (b > 0x10ffff) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b -= 0x10000;\n                chars.push(String.fromCharCode(0xd800 | (b >> 10)));\n                b = 0xdc00 | (b & 0x3ff);\n            }\n        }\n\n        chars.push(String.fromCharCode(b));\n    }\n    return chars.join(\"\");\n}\n", "(function(nacl) {\n'use strict';\n\n// Ported in 2014 by <PERSON> and <PERSON>.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (typeof require !== 'undefined') {\n    // Node.js.\n    crypto = require('crypto');\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})(typeof module !== 'undefined' && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n", "module.exports = require('./pusher-with-encryption').default;\n", "import ScriptReceiver from './script_receiver';\n\n/** Builds receivers for JSONP and Script requests.\n *\n * Each receiver is an object with following fields:\n * - number - unique (for the factory instance), numerical id of the receiver\n * - id - a string ID that can be used in DOM attributes\n * - name - name of the function triggering the receiver\n * - callback - callback function\n *\n * Receivers are triggered only once, on the first callback call.\n *\n * Receivers can be called by their name or by accessing factory object\n * by the number key.\n *\n * @param {String} prefix the prefix used in ids\n * @param {String} name the name of the object\n */\nexport class ScriptReceiverFactory {\n  lastId: number;\n  prefix: string;\n  name: string;\n\n  constructor(prefix: string, name: string) {\n    this.lastId = 0;\n    this.prefix = prefix;\n    this.name = name;\n  }\n\n  create(callback: Function): ScriptReceiver {\n    this.lastId++;\n\n    var number = this.lastId;\n    var id = this.prefix + number;\n    var name = this.name + '[' + number + ']';\n\n    var called = false;\n    var callbackWrapper = function () {\n      if (!called) {\n        callback.apply(null, arguments);\n        called = true;\n      }\n    };\n\n    this[number] = callbackWrapper;\n    return { number: number, id: id, name: name, callback: callbackWrapper };\n  }\n\n  remove(receiver: ScriptReceiver) {\n    delete this[receiver.number];\n  }\n}\n\nexport var ScriptReceivers = new ScriptReceiverFactory(\n  '_pusher_script_',\n  'Pusher.ScriptReceivers',\n);\n", "import {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions,\n} from './auth/options';\nimport { AuthTransport } from './config';\n\nexport interface DefaultConfig {\n  VERSION: string;\n  PROTOCOL: number;\n  wsPort: number;\n  wssPort: number;\n  wsPath: string;\n  httpHost: string;\n  httpPort: number;\n  httpsPort: number;\n  httpPath: string;\n  stats_host: string;\n  authEndpoint: string;\n  authTransport: AuthTransport;\n  activityTimeout: number;\n  pongTimeout: number;\n  unavailableTimeout: number;\n  userAuthentication: UserAuthenticationOptions;\n  channelAuthorization: ChannelAuthorizationOptions;\n\n  cdn_http?: string;\n  cdn_https?: string;\n  dependency_suffix?: string;\n}\n\nvar Defaults: DefaultConfig = {\n  VERSION: VERSION,\n  PROTOCOL: 7,\n\n  wsPort: 80,\n  wssPort: 443,\n  wsPath: '',\n  // DEPRECATED: SockJS fallback parameters\n  httpHost: 'sockjs.pusher.com',\n  httpPort: 80,\n  httpsPort: 443,\n  httpPath: '/pusher',\n  // DEPRECATED: Stats\n  stats_host: 'stats.pusher.com',\n  // DEPRECATED: Other settings\n  authEndpoint: '/pusher/auth',\n  authTransport: 'ajax',\n  activityTimeout: 120000,\n  pongTimeout: 30000,\n  unavailableTimeout: 10000,\n  userAuthentication: {\n    endpoint: '/pusher/user-auth',\n    transport: 'ajax',\n  },\n  channelAuthorization: {\n    endpoint: '/pusher/auth',\n    transport: 'ajax',\n  },\n\n  // CDN configuration\n  cdn_http: CDN_HTTP,\n  cdn_https: CDN_HTTPS,\n  dependency_suffix: DEPENDENCY_SUFFIX,\n};\n\nexport default Defaults;\n", "import { ScriptReceiverFactory } from './script_receiver_factory';\nimport Defaults from 'core/defaults';\nimport DependencyLoader from './dependency_loader';\n\nexport var DependenciesReceivers = new ScriptReceiverFactory(\n  '_pusher_dependencies',\n  'Pusher.DependenciesReceivers',\n);\n\nexport var Dependencies = new DependencyLoader({\n  cdn_http: Defaults.cdn_http,\n  cdn_https: Defaults.cdn_https,\n  version: Defaults.VERSION,\n  suffix: Defaults.dependency_suffix,\n  receivers: DependenciesReceivers,\n});\n", "import {\n  <PERSON>riptRecei<PERSON>,\n  ScriptReceiverFactory,\n} from './script_receiver_factory';\nimport Runtime from 'runtime';\nimport ScriptRequest from './script_request';\n\n/** Handles loading dependency files.\n *\n * Dependency loaders don't remember whether a resource has been loaded or\n * not. It is caller's responsibility to make sure the resource is not loaded\n * twice. This is because it's impossible to detect resource loading status\n * without knowing its content.\n *\n * Options:\n * - cdn_http - url to HTTP CND\n * - cdn_https - url to HTTPS CDN\n * - version - version of pusher-js\n * - suffix - suffix appended to all names of dependency files\n *\n * @param {Object} options\n */\nexport default class DependencyLoader {\n  options: any;\n  receivers: ScriptReceiverFactory;\n  loading: any;\n\n  constructor(options: any) {\n    this.options = options;\n    this.receivers = options.receivers || ScriptReceivers;\n    this.loading = {};\n  }\n\n  /** Loads the dependency from CDN.\n   *\n   * @param  {String} name\n   * @param  {Function} callback\n   */\n  load(name: string, options: any, callback: Function) {\n    var self = this;\n\n    if (self.loading[name] && self.loading[name].length > 0) {\n      self.loading[name].push(callback);\n    } else {\n      self.loading[name] = [callback];\n\n      var request = Runtime.createScriptRequest(self.getPath(name, options));\n      var receiver = self.receivers.create(function (error) {\n        self.receivers.remove(receiver);\n\n        if (self.loading[name]) {\n          var callbacks = self.loading[name];\n          delete self.loading[name];\n\n          var successCallback = function (wasSuccessful) {\n            if (!wasSuccessful) {\n              request.cleanup();\n            }\n          };\n          for (var i = 0; i < callbacks.length; i++) {\n            callbacks[i](error, successCallback);\n          }\n        }\n      });\n      request.send(receiver);\n    }\n  }\n\n  /** Returns a root URL for pusher-js CDN.\n   *\n   * @returns {String}\n   */\n  getRoot(options: any): string {\n    var cdn;\n    var protocol = Runtime.getDocument().location.protocol;\n    if ((options && options.useTLS) || protocol === 'https:') {\n      cdn = this.options.cdn_https;\n    } else {\n      cdn = this.options.cdn_http;\n    }\n    // make sure there are no double slashes\n    return cdn.replace(/\\/*$/, '') + '/' + this.options.version;\n  }\n\n  /** Returns a full path to a dependency file.\n   *\n   * @param {String} name\n   * @returns {String}\n   */\n  getPath(name: string, options: any): string {\n    return this.getRoot(options) + '/' + name + this.options.suffix + '.js';\n  }\n}\n", "/**\n * A place to store help URLs for error messages etc\n */\n\nconst urlStore = {\n  baseUrl: 'https://pusher.com',\n  urls: {\n    authenticationEndpoint: {\n      path: '/docs/channels/server_api/authenticating_users',\n    },\n    authorizationEndpoint: {\n      path: '/docs/channels/server_api/authorizing-users/',\n    },\n    javascriptQuickStart: {\n      path: '/docs/javascript_quick_start',\n    },\n    triggeringClientEvents: {\n      path: '/docs/client_api_guide/client_events#trigger-events',\n    },\n    encryptedChannelSupport: {\n      fullUrl:\n        'https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support',\n    },\n  },\n};\n\n/** Builds a consistent string with links to pusher documentation\n *\n * @param {string} key - relevant key in the url_store.urls object\n * @return {string} suffix string to append to log message\n */\nconst buildLogSuffix = function (key: string): string {\n  const urlPrefix = 'See:';\n  const urlObj = urlStore.urls[key];\n  if (!urlObj) return '';\n\n  let url;\n  if (urlObj.fullUrl) {\n    url = urlObj.fullUrl;\n  } else if (urlObj.path) {\n    url = urlStore.baseUrl + urlObj.path;\n  }\n\n  if (!url) return '';\n  return `${urlPrefix} ${url}`;\n};\n\nexport default { buildLogSuffix };\n", "export enum AuthRequestType {\n  UserAuthentication = 'user-authentication',\n  ChannelAuthorization = 'channel-authorization',\n}\n\nexport interface ChannelAuthorizationData {\n  auth: string;\n  channel_data?: string;\n  shared_secret?: string;\n}\n\nexport type ChannelAuthorizationCallback = (\n  error: Error | null,\n  authData: ChannelAuthorizationData | null,\n) => void;\n\nexport interface ChannelAuthorizationRequestParams {\n  socketId: string;\n  channelName: string;\n}\n\nexport interface ChannelAuthorizationHandler {\n  (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ): void;\n}\n\nexport interface UserAuthenticationData {\n  auth: string;\n  user_data: string;\n}\n\nexport type UserAuthenticationCallback = (\n  error: Error | null,\n  authData: UserAuthenticationData | null,\n) => void;\n\nexport interface UserAuthenticationRequestParams {\n  socketId: string;\n}\n\nexport interface UserAuthenticationHandler {\n  (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback,\n  ): void;\n}\n\nexport type AuthTransportCallback =\n  | ChannelAuthorizationCallback\n  | UserAuthenticationCallback;\n\nexport interface AuthOptionsT<AuthHandler> {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n  customHandler?: AuthHandler;\n}\n\nexport declare type UserAuthenticationOptions =\n  AuthOptionsT<UserAuthenticationHandler>;\nexport declare type ChannelAuthorizationOptions =\n  AuthOptionsT<ChannelAuthorizationHandler>;\n\nexport interface InternalAuthOptions {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n}\n", "/** Error classes used throughout the library. */\n// https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\nexport class BadEventName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class BadChannelName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class RequestTimedOut extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportPriorityTooLow extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportClosed extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedFeature extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedTransport extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedStrategy extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class HTTPAuthError extends Error {\n  status: number;\n  constructor(status: number, msg?: string) {\n    super(msg);\n    this.status = status;\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n", "import TimelineSender from 'core/timeline/timeline_sender';\nimport * as Collections from 'core/utils/collections';\nimport Util from 'core/util';\nimport Runtime from 'runtime';\nimport { AuthTransport } from 'core/auth/auth_transports';\nimport AbstractRuntime from 'runtimes/interface';\nimport UrlStore from 'core/utils/url_store';\nimport {\n  AuthRequestType,\n  AuthTransportCallback,\n  InternalAuthOptions,\n} from 'core/auth/options';\nimport { HTTPAuthError } from 'core/errors';\n\nconst ajax: AuthTransport = function (\n  context: AbstractRuntime,\n  query: string,\n  authOptions: InternalAuthOptions,\n  authRequestType: AuthRequestType,\n  callback: AuthTransportCallback,\n) {\n  const xhr = Runtime.createXHR();\n  xhr.open('POST', authOptions.endpoint, true);\n\n  // add request headers\n  xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n  for (var headerName in authOptions.headers) {\n    xhr.setRequestHeader(headerName, authOptions.headers[headerName]);\n  }\n  if (authOptions.headersProvider != null) {\n    let dynamicHeaders = authOptions.headersProvider();\n    for (var headerName in dynamicHeaders) {\n      xhr.setRequestHeader(headerName, dynamicHeaders[headerName]);\n    }\n  }\n\n  xhr.onreadystatechange = function () {\n    if (xhr.readyState === 4) {\n      if (xhr.status === 200) {\n        let data;\n        let parsed = false;\n\n        try {\n          data = JSON.parse(xhr.responseText);\n          parsed = true;\n        } catch (e) {\n          callback(\n            new HTTPAuthError(\n              200,\n              `JSON returned from ${authRequestType.toString()} endpoint was invalid, yet status code was 200. Data was: ${\n                xhr.responseText\n              }`,\n            ),\n            null,\n          );\n        }\n\n        if (parsed) {\n          // prevents double execution.\n          callback(null, data);\n        }\n      } else {\n        let suffix = '';\n        switch (authRequestType) {\n          case AuthRequestType.UserAuthentication:\n            suffix = UrlStore.buildLogSuffix('authenticationEndpoint');\n            break;\n          case AuthRequestType.ChannelAuthorization:\n            suffix = `Clients must be authorized to join private or presence channels. ${UrlStore.buildLogSuffix(\n              'authorizationEndpoint',\n            )}`;\n            break;\n        }\n        callback(\n          new HTTPAuthError(\n            xhr.status,\n            `Unable to retrieve auth string from ${authRequestType.toString()} endpoint - ` +\n              `received status: ${xhr.status} from ${authOptions.endpoint}. ${suffix}`,\n          ),\n          null,\n        );\n      }\n    }\n  };\n\n  xhr.send(query);\n  return xhr;\n};\n\nexport default ajax;\n", "export default function encode(s: any): string {\n  return btoa(utob(s));\n}\n\nvar fromCharCode = String.fromCharCode;\n\nvar b64chars =\n  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nvar b64tab = {};\n\nfor (var i = 0, l = b64chars.length; i < l; i++) {\n  b64tab[b64chars.charAt(i)] = i;\n}\n\nvar cb_utob = function (c) {\n  var cc = c.charCodeAt(0);\n  return cc < 0x80\n    ? c\n    : cc < 0x800\n      ? fromCharCode(0xc0 | (cc >>> 6)) + fromCharCode(0x80 | (cc & 0x3f))\n      : fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +\n        fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +\n        fromCharCode(0x80 | (cc & 0x3f));\n};\n\nvar utob = function (u) {\n  return u.replace(/[^\\x00-\\x7F]/g, cb_utob);\n};\n\nvar cb_encode = function (ccc) {\n  var padlen = [0, 2, 1][ccc.length % 3];\n  var ord =\n    (ccc.charCodeAt(0) << 16) |\n    ((ccc.length > 1 ? ccc.charCodeAt(1) : 0) << 8) |\n    (ccc.length > 2 ? ccc.charCodeAt(2) : 0);\n  var chars = [\n    b64chars.charAt(ord >>> 18),\n    b64chars.charAt((ord >>> 12) & 63),\n    padlen >= 2 ? '=' : b64chars.charAt((ord >>> 6) & 63),\n    padlen >= 1 ? '=' : b64chars.charAt(ord & 63),\n  ];\n  return chars.join('');\n};\n\nvar btoa =\n  global.btoa ||\n  function (b) {\n    return b.replace(/[\\s\\S]{1,3}/g, cb_encode);\n  };\n", "import TimedCallback from './timed_callback';\nimport { Delay, Scheduler, Canceller } from './scheduling';\n\nabstract class Timer {\n  protected clear: Canceller;\n  protected timer: number | void;\n\n  constructor(\n    set: Scheduler,\n    clear: Canceller,\n    delay: Delay,\n    callback: TimedCallback,\n  ) {\n    this.clear = clear;\n    this.timer = set(() => {\n      if (this.timer) {\n        this.timer = callback(this.timer);\n      }\n    }, delay);\n  }\n\n  /** Returns whether the timer is still running.\n   *\n   * @return {Boolean}\n   */\n  isRunning(): boolean {\n    return this.timer !== null;\n  }\n\n  /** Aborts a timer when it's running. */\n  ensureAborted() {\n    if (this.timer) {\n      this.clear(this.timer);\n      this.timer = null;\n    }\n  }\n}\n\nexport default Timer;\n", "import Timer from './abstract_timer';\nimport TimedCallback from './timed_callback';\nimport { Delay } from './scheduling';\n\n// We need to bind clear functions this way to avoid exceptions on IE8\nfunction clearTimeout(timer) {\n  global.clearTimeout(timer);\n}\nfunction clearInterval(timer) {\n  global.clearInterval(timer);\n}\n\n/** Cross-browser compatible one-off timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class OneOffTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setTimeout, clearTimeout, delay, function (timer) {\n      callback();\n      return null;\n    });\n  }\n}\n\n/** Cross-browser compatible periodic timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class PeriodicTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setInterval, clearInterval, delay, function (timer) {\n      callback();\n      return timer;\n    });\n  }\n}\n", "import * as Collections from './utils/collections';\nimport TimedCallback from './utils/timers/timed_callback';\nimport { OneOffTimer, PeriodicTimer } from './utils/timers';\n\nvar Util = {\n  now(): number {\n    if (Date.now) {\n      return Date.now();\n    } else {\n      return new Date().valueOf();\n    }\n  },\n\n  defer(callback: TimedCallback): OneOffTimer {\n    return new OneOffTimer(0, callback);\n  },\n\n  /** Builds a function that will proxy a method call to its first argument.\n   *\n   * Allows partial application of arguments, so additional arguments are\n   * prepended to the argument list.\n   *\n   * @param  {String} name method name\n   * @return {Function} proxy function\n   */\n  method(name: string, ...args: any[]): Function {\n    var boundArguments = Array.prototype.slice.call(arguments, 1);\n    return function (object) {\n      return object[name].apply(object, boundArguments.concat(arguments));\n    };\n  },\n};\n\nexport default Util;\n", "import base64encode from '../base64';\nimport Util from '../util';\n\n/** Merges multiple objects into the target argument.\n *\n * For properties that are plain Objects, performs a deep-merge. For the\n * rest it just copies the value of the property.\n *\n * To extend prototypes use it as following:\n *   Pusher.Util.extend(Target.prototype, Base.prototype)\n *\n * You can also use it to merge objects without altering them:\n *   Pusher.Util.extend({}, object1, object2)\n *\n * @param  {Object} target\n * @return {Object} the target argument\n */\nexport function extend<T>(target: any, ...sources: any[]): T {\n  for (var i = 0; i < sources.length; i++) {\n    var extensions = sources[i];\n    for (var property in extensions) {\n      if (\n        extensions[property] &&\n        extensions[property].constructor &&\n        extensions[property].constructor === Object\n      ) {\n        target[property] = extend(target[property] || {}, extensions[property]);\n      } else {\n        target[property] = extensions[property];\n      }\n    }\n  }\n  return target;\n}\n\nexport function stringify(): string {\n  var m = ['Pusher'];\n  for (var i = 0; i < arguments.length; i++) {\n    if (typeof arguments[i] === 'string') {\n      m.push(arguments[i]);\n    } else {\n      m.push(safeJSONStringify(arguments[i]));\n    }\n  }\n  return m.join(' : ');\n}\n\nexport function arrayIndexOf(array: any[], item: any): number {\n  // MSIE doesn't have array.indexOf\n  var nativeIndexOf = Array.prototype.indexOf;\n  if (array === null) {\n    return -1;\n  }\n  if (nativeIndexOf && array.indexOf === nativeIndexOf) {\n    return array.indexOf(item);\n  }\n  for (var i = 0, l = array.length; i < l; i++) {\n    if (array[i] === item) {\n      return i;\n    }\n  }\n  return -1;\n}\n\n/** Applies a function f to all properties of an object.\n *\n * Function f gets 3 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function objectApply(object: any, f: Function) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      f(object[key], key, object);\n    }\n  }\n}\n\n/** Return a list of objects own proerty keys\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function keys(object: any): string[] {\n  var keys = [];\n  objectApply(object, function (_, key) {\n    keys.push(key);\n  });\n  return keys;\n}\n\n/** Return a list of object's own property values\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function values(object: any): any[] {\n  var values = [];\n  objectApply(object, function (value) {\n    values.push(value);\n  });\n  return values;\n}\n\n/** Applies a function f to all elements of an array.\n *\n * Function f gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function apply(array: any[], f: Function, context?: any) {\n  for (var i = 0; i < array.length; i++) {\n    f.call(context || global, array[i], i, array);\n  }\n}\n\n/** Maps all elements of the array and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function map(array: any[], f: Function): any[] {\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    result.push(f(array[i], i, array, result));\n  }\n  return result;\n}\n\n/** Maps all elements of the object and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function mapObject(object: any, f: Function): any {\n  var result = {};\n  objectApply(object, function (value, key) {\n    result[key] = f(value);\n  });\n  return result;\n}\n\n/** Filters elements of the array using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function filter(array: any[], test: Function): any[] {\n  test =\n    test ||\n    function (value) {\n      return !!value;\n    };\n\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array, result)) {\n      result.push(array[i]);\n    }\n  }\n  return result;\n}\n\n/** Filters properties of the object using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function filterObject(object: Object, test: Function) {\n  var result = {};\n  objectApply(object, function (value, key) {\n    if ((test && test(value, key, object, result)) || Boolean(value)) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\n\n/** Flattens an object into a two-dimensional array.\n *\n * @param  {Object} object\n * @return {Array} resulting array of [key, value] pairs\n */\nexport function flatten(object: Object): any[] {\n  var result = [];\n  objectApply(object, function (value, key) {\n    result.push([key, value]);\n  });\n  return result;\n}\n\n/** Checks whether any element of the array passes the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function any(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/** Checks whether all elements of the array pass the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function all(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (!test(array[i], i, array)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function encodeParamsObject(data): string {\n  return mapObject(data, function (value) {\n    if (typeof value === 'object') {\n      value = safeJSONStringify(value);\n    }\n    return encodeURIComponent(base64encode(value.toString()));\n  });\n}\n\nexport function buildQueryString(data: any): string {\n  var params = filterObject(data, function (value) {\n    return value !== undefined;\n  });\n\n  var query = map(\n    flatten(encodeParamsObject(params)),\n    Util.method('join', '='),\n  ).join('&');\n\n  return query;\n}\n\n/**\n * See https://github.com/douglascrockford/JSON-js/blob/master/cycle.js\n *\n * Remove circular references from an object. Required for JSON.stringify in\n * React Native, which tends to blow up a lot.\n *\n * @param  {any} object\n * @return {any}        Decycled object\n */\nexport function decycleObject(object: any): any {\n  var objects = [],\n    paths = [];\n\n  return (function derez(value, path) {\n    var i, name, nu;\n\n    switch (typeof value) {\n      case 'object':\n        if (!value) {\n          return null;\n        }\n        for (i = 0; i < objects.length; i += 1) {\n          if (objects[i] === value) {\n            return { $ref: paths[i] };\n          }\n        }\n\n        objects.push(value);\n        paths.push(path);\n\n        if (Object.prototype.toString.apply(value) === '[object Array]') {\n          nu = [];\n          for (i = 0; i < value.length; i += 1) {\n            nu[i] = derez(value[i], path + '[' + i + ']');\n          }\n        } else {\n          nu = {};\n          for (name in value) {\n            if (Object.prototype.hasOwnProperty.call(value, name)) {\n              nu[name] = derez(\n                value[name],\n                path + '[' + JSON.stringify(name) + ']',\n              );\n            }\n          }\n        }\n        return nu;\n      case 'number':\n      case 'string':\n      case 'boolean':\n        return value;\n    }\n  })(object, '$');\n}\n\n/**\n * Provides a cross-browser and cross-platform way to safely stringify objects\n * into JSON. This is particularly necessary for ReactNative, where circular JSON\n * structures throw an exception.\n *\n * @param  {any}    source The object to stringify\n * @return {string}        The serialized output.\n */\nexport function safeJSONStringify(source: any): string {\n  try {\n    return JSON.stringify(source);\n  } catch (e) {\n    return JSON.stringify(decycleObject(source));\n  }\n}\n", "import { stringify } from './utils/collections';\nimport Pusher from './pusher';\n\nclass Logger {\n  debug(...args: any[]) {\n    this.log(this.globalLog, args);\n  }\n\n  warn(...args: any[]) {\n    this.log(this.globalLogWarn, args);\n  }\n\n  error(...args: any[]) {\n    this.log(this.globalLogError, args);\n  }\n\n  private globalLog = (message: string) => {\n    if (global.console && global.console.log) {\n      global.console.log(message);\n    }\n  };\n\n  private globalLogWarn(message: string) {\n    if (global.console && global.console.warn) {\n      global.console.warn(message);\n    } else {\n      this.globalLog(message);\n    }\n  }\n\n  private globalLogError(message: string) {\n    if (global.console && global.console.error) {\n      global.console.error(message);\n    } else {\n      this.globalLogWarn(message);\n    }\n  }\n\n  private log(\n    defaultLoggingFunction: (message: string) => void,\n    ...args: any[]\n  ) {\n    var message = stringify.apply(this, arguments);\n    if (Pusher.log) {\n      Pusher.log(message);\n    } else if (Pusher.logToConsole) {\n      const log = defaultLoggingFunction.bind(this);\n      log(message);\n    }\n  }\n}\n\nexport default new Logger();\n", "import Browser from '../browser';\nimport Logger from 'core/logger';\nimport <PERSON><PERSON><PERSON><PERSON>e<PERSON> from '../dom/jsonp_request';\nimport { <PERSON>riptReceivers } from '../dom/script_receiver_factory';\nimport { AuthTransport } from 'core/auth/auth_transports';\nimport {\n  AuthRequestType,\n  AuthTransportCallback,\n  InternalAuthOptions,\n} from 'core/auth/options';\n\nvar jsonp: AuthTransport = function (\n  context: Browser,\n  query: string,\n  authOptions: InternalAuthOptions,\n  authRequestType: AuthRequestType,\n  callback: AuthTransportCallback,\n) {\n  if (\n    authOptions.headers !== undefined ||\n    authOptions.headersProvider != null\n  ) {\n    Logger.warn(\n      `To send headers with the ${authRequestType.toString()} request, you must use AJAX, rather than JSONP.`,\n    );\n  }\n\n  var callbackName = context.nextAuthCallbackID.toString();\n  context.nextAuthCallbackID++;\n\n  var document = context.getDocument();\n  var script = document.createElement('script');\n  // Hacked wrapper.\n  context.auth_callbacks[callbackName] = function (data) {\n    callback(null, data);\n  };\n\n  var callback_name = \"Pusher.auth_callbacks['\" + callbackName + \"']\";\n  script.src =\n    authOptions.endpoint +\n    '?callback=' +\n    encodeURIComponent(callback_name) +\n    '&' +\n    query;\n\n  var head =\n    document.getElementsByTagName('head')[0] || document.documentElement;\n  head.insertBefore(script, head.firstChild);\n};\n\nexport default jsonp;\n", "import <PERSON>riptReceiver from './script_receiver';\n\n/** Sends a generic HTTP GET request using a script tag.\n *\n * By constructing URL in a specific way, it can be used for loading\n * JavaScript resources or JSONP requests. It can notify about errors, but\n * only in certain environments. Please take care of monitoring the state of\n * the request yourself.\n *\n * @param {String} src\n */\nexport default class ScriptRequest {\n  src: string;\n  script: any;\n  errorScript: any;\n\n  constructor(src: string) {\n    this.src = src;\n  }\n\n  send(receiver: ScriptReceiver) {\n    var self = this;\n    var errorString = 'Error loading ' + self.src;\n\n    self.script = document.createElement('script');\n    self.script.id = receiver.id;\n    self.script.src = self.src;\n    self.script.type = 'text/javascript';\n    self.script.charset = 'UTF-8';\n\n    if (self.script.addEventListener) {\n      self.script.onerror = function () {\n        receiver.callback(errorString);\n      };\n      self.script.onload = function () {\n        receiver.callback(null);\n      };\n    } else {\n      self.script.onreadystatechange = function () {\n        if (\n          self.script.readyState === 'loaded' ||\n          self.script.readyState === 'complete'\n        ) {\n          receiver.callback(null);\n        }\n      };\n    }\n\n    // Opera<11.6 hack for missing onerror callback\n    if (\n      self.script.async === undefined &&\n      (<any>document).attachEvent &&\n      /opera/i.test(navigator.userAgent)\n    ) {\n      self.errorScript = document.createElement('script');\n      self.errorScript.id = receiver.id + '_error';\n      self.errorScript.text = receiver.name + \"('\" + errorString + \"');\";\n      self.script.async = self.errorScript.async = false;\n    } else {\n      self.script.async = true;\n    }\n\n    var head = document.getElementsByTagName('head')[0];\n    head.insertBefore(self.script, head.firstChild);\n    if (self.errorScript) {\n      head.insertBefore(self.errorScript, self.script.nextSibling);\n    }\n  }\n\n  /** Cleans up the DOM remains of the script request. */\n  cleanup() {\n    if (this.script) {\n      this.script.onload = this.script.onerror = null;\n      this.script.onreadystatechange = null;\n    }\n    if (this.script && this.script.parentNode) {\n      this.script.parentNode.removeChild(this.script);\n    }\n    if (this.errorScript && this.errorScript.parentNode) {\n      this.errorScript.parentNode.removeChild(this.errorScript);\n    }\n    this.script = null;\n    this.errorScript = null;\n  }\n}\n", "import ScriptReceiver from './script_receiver';\nimport ScriptRequest from './script_request';\nimport * as Collections from 'core/utils/collections';\nimport Util from 'core/util';\nimport Runtime from '../runtime';\n\n/** Sends data via JSONP.\n *\n * Data is a key-value map. Its values are JSON-encoded and then passed\n * through base64. Finally, keys and encoded values are appended to the query\n * string.\n *\n * The class itself does not guarantee raising errors on failures, as it's not\n * possible to support such feature on all browsers. Instead, JSONP endpoint\n * should call back in a way that's easy to distinguish from browser calls,\n * for example by passing a second argument to the receiver.\n *\n * @param {String} url\n * @param {Object} data key-value map of data to be submitted\n */\nexport default class JSONPRequest {\n  url: string;\n  data: any;\n  request: ScriptRequest;\n\n  constructor(url: string, data: any) {\n    this.url = url;\n    this.data = data;\n  }\n\n  /** Sends the actual JSONP request.\n   *\n   * @param {ScriptReceiver} receiver\n   */\n  send(receiver: ScriptReceiver) {\n    if (this.request) {\n      return;\n    }\n\n    var query = Collections.buildQueryString(this.data);\n    var url = this.url + '/' + receiver.number + '?' + query;\n    this.request = Runtime.createScriptRequest(url);\n    this.request.send(receiver);\n  }\n\n  /** Cleans up the DOM remains of the JSONP request. */\n  cleanup() {\n    if (this.request) {\n      this.request.cleanup();\n    }\n  }\n}\n", "import TimelineSender from 'core/timeline/timeline_sender';\nimport TimelineTransport from 'core/timeline/timeline_transport';\nimport Browser from 'runtime';\nimport { AuthTransport } from 'core/auth/auth_transports';\nimport { ScriptReceivers } from '../dom/script_receiver_factory';\n\nvar getAgent = function (sender: TimelineSender, useTLS: boolean) {\n  return function (data: any, callback: Function) {\n    var scheme = 'http' + (useTLS ? 's' : '') + '://';\n    var url =\n      scheme + (sender.host || sender.options.host) + sender.options.path;\n    var request = Browser.createJSONPRequest(url, data);\n\n    var receiver = Browser.ScriptReceivers.create(function (error, result) {\n      ScriptReceivers.remove(receiver);\n      request.cleanup();\n\n      if (result && result.host) {\n        sender.host = result.host;\n      }\n      if (callback) {\n        callback(error, result);\n      }\n    });\n    request.send(receiver);\n  };\n};\n\nvar jsonp = {\n  name: 'jsonp',\n  getAgent,\n};\n\nexport default jsonp;\n", "import Defaults from '../defaults';\nimport { default as URLScheme, URLSchemeParams } from './url_scheme';\n\nfunction getGenericURL(\n  baseScheme: string,\n  params: URLSchemeParams,\n  path: string,\n): string {\n  var scheme = baseScheme + (params.useTLS ? 's' : '');\n  var host = params.useTLS ? params.hostTLS : params.hostNonTLS;\n  return scheme + '://' + host + path;\n}\n\nfunction getGenericPath(key: string, queryString?: string): string {\n  var path = '/app/' + key;\n  var query =\n    '?protocol=' +\n    Defaults.PROTOCOL +\n    '&client=js' +\n    '&version=' +\n    Defaults.VERSION +\n    (queryString ? '&' + queryString : '');\n  return path + query;\n}\n\nexport var ws: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '') + getGenericPath(key, 'flash=false');\n    return getGenericURL('ws', params, path);\n  },\n};\n\nexport var http: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '/pusher') + getGenericPath(key);\n    return getGenericURL('http', params, path);\n  },\n};\n\nexport var sockjs: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    return getGenericURL('http', params, params.httpPath || '/pusher');\n  },\n  getPath: function (key: string, params: URLSchemeParams): string {\n    return getGenericPath(key);\n  },\n};\n", "import Callback from './callback';\nimport * as Collections from '../utils/collections';\nimport CallbackTable from './callback_table';\n\nexport default class CallbackRegistry {\n  _callbacks: CallbackTable;\n\n  constructor() {\n    this._callbacks = {};\n  }\n\n  get(name: string): Callback[] {\n    return this._callbacks[prefix(name)];\n  }\n\n  add(name: string, callback: Function, context: any) {\n    var prefixedEventName = prefix(name);\n    this._callbacks[prefixedEventName] =\n      this._callbacks[prefixedEventName] || [];\n    this._callbacks[prefixedEventName].push({\n      fn: callback,\n      context: context,\n    });\n  }\n\n  remove(name?: string, callback?: Function, context?: any) {\n    if (!name && !callback && !context) {\n      this._callbacks = {};\n      return;\n    }\n\n    var names = name ? [prefix(name)] : Collections.keys(this._callbacks);\n\n    if (callback || context) {\n      this.removeCallback(names, callback, context);\n    } else {\n      this.removeAllCallbacks(names);\n    }\n  }\n\n  private removeCallback(names: string[], callback: Function, context: any) {\n    Collections.apply(\n      names,\n      function (name) {\n        this._callbacks[name] = Collections.filter(\n          this._callbacks[name] || [],\n          function (binding) {\n            return (\n              (callback && callback !== binding.fn) ||\n              (context && context !== binding.context)\n            );\n          },\n        );\n        if (this._callbacks[name].length === 0) {\n          delete this._callbacks[name];\n        }\n      },\n      this,\n    );\n  }\n\n  private removeAllCallbacks(names: string[]) {\n    Collections.apply(\n      names,\n      function (name) {\n        delete this._callbacks[name];\n      },\n      this,\n    );\n  }\n}\n\nfunction prefix(name: string): string {\n  return '_' + name;\n}\n", "import * as Collections from '../utils/collections';\nimport Callback from './callback';\nimport Metadata from '../channels/metadata';\nimport CallbackRegistry from './callback_registry';\n\n/** Manages callback bindings and event emitting.\n *\n * @param Function failThrough called when no listeners are bound to an event\n */\nexport default class Dispatcher {\n  callbacks: CallbackRegistry;\n  global_callbacks: Function[];\n  failThrough: Function;\n\n  constructor(failThrough?: Function) {\n    this.callbacks = new CallbackRegistry();\n    this.global_callbacks = [];\n    this.failThrough = failThrough;\n  }\n\n  bind(eventName: string, callback: Function, context?: any) {\n    this.callbacks.add(eventName, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function) {\n    this.global_callbacks.push(callback);\n    return this;\n  }\n\n  unbind(eventName?: string, callback?: Function, context?: any) {\n    this.callbacks.remove(eventName, callback, context);\n    return this;\n  }\n\n  unbind_global(callback?: Function) {\n    if (!callback) {\n      this.global_callbacks = [];\n      return this;\n    }\n\n    this.global_callbacks = Collections.filter(\n      this.global_callbacks || [],\n      (c) => c !== callback,\n    );\n\n    return this;\n  }\n\n  unbind_all() {\n    this.unbind();\n    this.unbind_global();\n    return this;\n  }\n\n  emit(eventName: string, data?: any, metadata?: Metadata): Dispatcher {\n    for (var i = 0; i < this.global_callbacks.length; i++) {\n      this.global_callbacks[i](eventName, data);\n    }\n\n    var callbacks = this.callbacks.get(eventName);\n    var args = [];\n\n    if (metadata) {\n      // if there's a metadata argument, we need to call the callback with both\n      // data and metadata regardless of whether data is undefined\n      args.push(data, metadata);\n    } else if (data) {\n      // metadata is undefined, so we only need to call the callback with data\n      // if data exists\n      args.push(data);\n    }\n\n    if (callbacks && callbacks.length > 0) {\n      for (var i = 0; i < callbacks.length; i++) {\n        callbacks[i].fn.apply(callbacks[i].context || global, args);\n      }\n    } else if (this.failThrough) {\n      this.failThrough(eventName, data);\n    }\n\n    return this;\n  }\n}\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Logger from '../logger';\nimport TransportHooks from './transport_hooks';\nimport Socket from '../socket';\nimport Runtime from 'runtime';\nimport Timeline from '../timeline/timeline';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides universal API for transport connections.\n *\n * Transport connection is a low-level object that wraps a connection method\n * and exposes a simple evented interface for the connection state and\n * messaging. It does not implement Pusher-specific WebSocket protocol.\n *\n * Additionally, it fetches resources needed for transport to work and exposes\n * an interface for querying transport features.\n *\n * States:\n * - new - initial state after constructing the object\n * - initializing - during initialization phase, usually fetching resources\n * - intialized - ready to establish a connection\n * - connection - when connection is being established\n * - open - when connection ready to be used\n * - closed - after connection was closed be either side\n *\n * Emits:\n * - error - after the connection raised an error\n *\n * Options:\n * - useTLS - whether connection should be over TLS\n * - hostTLS - host to connect to when connection is over TLS\n * - hostNonTLS - host to connect to when connection is over TLS\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class TransportConnection extends EventsDispatcher {\n  hooks: TransportHooks;\n  name: string;\n  priority: number;\n  key: string;\n  options: TransportConnectionOptions;\n  state: string;\n  timeline: Timeline;\n  activityTimeout: number;\n  id: number;\n  socket: Socket;\n  beforeOpen: Function;\n  initialize: Function;\n\n  constructor(\n    hooks: TransportHooks,\n    name: string,\n    priority: number,\n    key: string,\n    options: TransportConnectionOptions,\n  ) {\n    super();\n    this.initialize = Runtime.transportConnectionInitializer;\n    this.hooks = hooks;\n    this.name = name;\n    this.priority = priority;\n    this.key = key;\n    this.options = options;\n\n    this.state = 'new';\n    this.timeline = options.timeline;\n    this.activityTimeout = options.activityTimeout;\n    this.id = this.timeline.generateUniqueID();\n  }\n\n  /** Checks whether the transport handles activity checks by itself.\n   *\n   * @return {Boolean}\n   */\n  handlesActivityChecks(): boolean {\n    return Boolean(this.hooks.handlesActivityChecks);\n  }\n\n  /** Checks whether the transport supports the ping/pong API.\n   *\n   * @return {Boolean}\n   */\n  supportsPing(): boolean {\n    return Boolean(this.hooks.supportsPing);\n  }\n\n  /** Tries to establish a connection.\n   *\n   * @returns {Boolean} false if transport is in invalid state\n   */\n  connect(): boolean {\n    if (this.socket || this.state !== 'initialized') {\n      return false;\n    }\n\n    var url = this.hooks.urls.getInitial(this.key, this.options);\n    try {\n      this.socket = this.hooks.getSocket(url, this.options);\n    } catch (e) {\n      Util.defer(() => {\n        this.onError(e);\n        this.changeState('closed');\n      });\n      return false;\n    }\n\n    this.bindListeners();\n\n    Logger.debug('Connecting', { transport: this.name, url });\n    this.changeState('connecting');\n    return true;\n  }\n\n  /** Closes the connection.\n   *\n   * @return {Boolean} true if there was a connection to close\n   */\n  close(): boolean {\n    if (this.socket) {\n      this.socket.close();\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends data over the open connection.\n   *\n   * @param {String} data\n   * @return {Boolean} true only when in the \"open\" state\n   */\n  send(data: any): boolean {\n    if (this.state === 'open') {\n      // Workaround for MobileSafari bug (see https://gist.github.com/2052006)\n      Util.defer(() => {\n        if (this.socket) {\n          this.socket.send(data);\n        }\n      });\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends a ping if the connection is open and transport supports it. */\n  ping() {\n    if (this.state === 'open' && this.supportsPing()) {\n      this.socket.ping();\n    }\n  }\n\n  private onOpen() {\n    if (this.hooks.beforeOpen) {\n      this.hooks.beforeOpen(\n        this.socket,\n        this.hooks.urls.getPath(this.key, this.options),\n      );\n    }\n    this.changeState('open');\n    this.socket.onopen = undefined;\n  }\n\n  private onError(error) {\n    this.emit('error', { type: 'WebSocketError', error: error });\n    this.timeline.error(this.buildTimelineMessage({ error: error.toString() }));\n  }\n\n  private onClose(closeEvent?: any) {\n    if (closeEvent) {\n      this.changeState('closed', {\n        code: closeEvent.code,\n        reason: closeEvent.reason,\n        wasClean: closeEvent.wasClean,\n      });\n    } else {\n      this.changeState('closed');\n    }\n    this.unbindListeners();\n    this.socket = undefined;\n  }\n\n  private onMessage(message) {\n    this.emit('message', message);\n  }\n\n  private onActivity() {\n    this.emit('activity');\n  }\n\n  private bindListeners() {\n    this.socket.onopen = () => {\n      this.onOpen();\n    };\n    this.socket.onerror = (error) => {\n      this.onError(error);\n    };\n    this.socket.onclose = (closeEvent) => {\n      this.onClose(closeEvent);\n    };\n    this.socket.onmessage = (message) => {\n      this.onMessage(message);\n    };\n\n    if (this.supportsPing()) {\n      this.socket.onactivity = () => {\n        this.onActivity();\n      };\n    }\n  }\n\n  private unbindListeners() {\n    if (this.socket) {\n      this.socket.onopen = undefined;\n      this.socket.onerror = undefined;\n      this.socket.onclose = undefined;\n      this.socket.onmessage = undefined;\n      if (this.supportsPing()) {\n        this.socket.onactivity = undefined;\n      }\n    }\n  }\n\n  private changeState(state: string, params?: any) {\n    this.state = state;\n    this.timeline.info(\n      this.buildTimelineMessage({\n        state: state,\n        params: params,\n      }),\n    );\n    this.emit(state, params);\n  }\n\n  buildTimelineMessage(message): any {\n    return Collections.extend({ cid: this.id }, message);\n  }\n}\n", "import Factory from '../utils/factory';\nimport TransportHooks from './transport_hooks';\nimport TransportConnection from './transport_connection';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides interface for transport connection instantiation.\n *\n * Takes transport-specific hooks as the only argument, which allow checking\n * for transport support and creating its connections.\n *\n * Supported hooks: * - file - the name of the file to be fetched during initialization\n * - urls - URL scheme to be used by transport\n * - handlesActivityCheck - true when the transport handles activity checks\n * - supportsPing - true when the transport has a ping/activity API\n * - isSupported - tells whether the transport is supported in the environment\n * - getSocket - creates a WebSocket-compatible transport socket\n *\n * See transports.js for specific implementations.\n *\n * @param {Object} hooks object containing all needed transport hooks\n */\nexport default class Transport {\n  hooks: TransportHooks;\n\n  constructor(hooks: TransportHooks) {\n    this.hooks = hooks;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * @param {Object} envronment te environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: any): boolean {\n    return this.hooks.isSupported(environment);\n  }\n\n  /** Creates a transport connection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: any,\n  ): TransportConnection {\n    return new TransportConnection(this.hooks, name, priority, key, options);\n  }\n}\n", "import * as URLSchemes from 'core/transports/url_schemes';\nimport URLScheme from 'core/transports/url_scheme';\nimport Transport from 'core/transports/transport';\nimport Util from 'core/util';\nimport * as Collections from 'core/utils/collections';\nimport TransportHooks from 'core/transports/transport_hooks';\nimport TransportsTable from 'core/transports/transports_table';\nimport Runtime from 'runtime';\n\n/** WebSocket transport.\n *\n * Uses native WebSocket implementation, including MozWebSocket supported by\n * earlier Firefox versions.\n */\nvar WSTransport = new Transport(<TransportHooks>{\n  urls: URLSchemes.ws,\n  handlesActivityChecks: false,\n  supportsPing: false,\n\n  isInitialized: function () {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  isSupported: function (): boolean {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  getSocket: function (url) {\n    return Runtime.createWebSocket(url);\n  },\n});\n\nvar httpConfiguration = {\n  urls: URLSchemes.http,\n  handlesActivityChecks: false,\n  supportsPing: true,\n  isInitialized: function () {\n    return true;\n  },\n};\n\nexport var streamingConfiguration = Collections.extend(\n  {\n    getSocket: function (url) {\n      return Runtime.HTTPFactory.createStreamingSocket(url);\n    },\n  },\n  httpConfiguration,\n);\nexport var pollingConfiguration = Collections.extend(\n  {\n    getSocket: function (url) {\n      return Runtime.HTTPFactory.createPollingSocket(url);\n    },\n  },\n  httpConfiguration,\n);\n\nvar xhrConfiguration = {\n  isSupported: function (): boolean {\n    return Runtime.isXHRSupported();\n  },\n};\n\n/** HTTP streaming transport using CORS-enabled XMLHttpRequest. */\nvar XHRStreamingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, streamingConfiguration, xhrConfiguration)\n  ),\n);\n\n/** HTTP long-polling transport using CORS-enabled XMLHttpRequest. */\nvar XHRPollingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, pollingConfiguration, xhrConfiguration)\n  ),\n);\n\nvar Transports: TransportsTable = {\n  ws: WSTransport,\n  xhr_streaming: XHRStreamingTransport,\n  xhr_polling: XHRPollingTransport,\n};\n\nexport default Transports;\n", "import {\n  default as Transports,\n  streamingConfiguration,\n  pollingConfiguration,\n} from 'isomorphic/transports/transports';\nimport Transport from 'core/transports/transport';\nimport TransportHooks from 'core/transports/transport_hooks';\nimport * as URLSchemes from 'core/transports/url_schemes';\nimport Runtime from 'runtime';\nimport { Dependencies } from '../dom/dependencies';\nimport * as Collections from 'core/utils/collections';\n\nvar SockJSTransport = new Transport(<TransportHooks>{\n  file: 'sockjs',\n  urls: URLSchemes.sockjs,\n  handlesActivityChecks: true,\n  supportsPing: false,\n\n  isSupported: function () {\n    return true;\n  },\n  isInitialized: function () {\n    return window.SockJS !== undefined;\n  },\n  getSocket: function (url, options) {\n    return new window.SockJS(url, null, {\n      js_path: Dependencies.getPath('sockjs', {\n        useTLS: options.useTLS,\n      }),\n      ignore_null_origin: options.ignoreNullOrigin,\n    });\n  },\n  beforeOpen: function (socket, path) {\n    socket.send(\n      JSON.stringify({\n        path: path,\n      }),\n    );\n  },\n});\n\nvar xdrConfiguration = {\n  isSupported: function (environment): boolean {\n    var yes = Runtime.isXDRSupported(environment.useTLS);\n    return yes;\n  },\n};\n\n/** HTTP streaming transport using XDomainRequest (IE 8,9). */\nvar XDRStreamingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, streamingConfiguration, xdrConfiguration)\n  ),\n);\n\n/** HTTP long-polling transport using XDomainRequest (IE 8,9). */\nvar XDRPollingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, pollingConfiguration, xdrConfiguration)\n  ),\n);\n\nTransports.xdr_streaming = XDRStreamingTransport;\nTransports.xdr_polling = XDRPollingTransport;\nTransports.sockjs = SockJSTransport;\n\nexport default Transports;\n", "import Reachability from 'core/reachability';\nimport { default as EventsDispatcher } from 'core/events/dispatcher';\n\n/** Really basic interface providing network availability info.\n *\n * Emits:\n * - online - when browser goes online\n * - offline - when browser goes offline\n */\nexport class NetInfo extends EventsDispatcher implements Reachability {\n  constructor() {\n    super();\n    var self = this;\n    // This is okay, as IE doesn't support this stuff anyway.\n    if (window.addEventListener !== undefined) {\n      window.addEventListener(\n        'online',\n        function () {\n          self.emit('online');\n        },\n        false,\n      );\n      window.addEventListener(\n        'offline',\n        function () {\n          self.emit('offline');\n        },\n        false,\n      );\n    }\n  }\n\n  /** Returns whether browser is online or not\n   *\n   * Offline means definitely offline (no connection to router).\n   * Inverse does NOT mean definitely online (only currently supported in Safari\n   * and even there only means the device has a connection to the router).\n   *\n   * @return {Boolean}\n   */\n  isOnline(): boolean {\n    if (window.navigator.onLine === undefined) {\n      return true;\n    } else {\n      return window.navigator.onLine;\n    }\n  }\n}\n\nexport var Network = new NetInfo();\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport TransportManager from './transport_manager';\nimport TransportConnection from './transport_connection';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\n\n/** Creates transport connections monitored by a transport manager.\n *\n * When a transport is closed, it might mean the environment does not support\n * it. It's possible that messages get stuck in an intermediate buffer or\n * proxies terminate inactive connections. To combat these problems,\n * assistants monitor the connection lifetime, report unclean exits and\n * adjust ping timeouts to keep the connection active. The decision to disable\n * a transport is the manager's responsibility.\n *\n * @param {TransportManager} manager\n * @param {TransportConnection} transport\n * @param {Object} options\n */\nexport default class AssistantToTheTransportManager {\n  manager: TransportManager;\n  transport: Transport;\n  minPingDelay: number;\n  maxPingDelay: number;\n  pingDelay: number;\n\n  constructor(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions,\n  ) {\n    this.manager = manager;\n    this.transport = transport;\n    this.minPingDelay = options.minPingDelay;\n    this.maxPingDelay = options.maxPingDelay;\n    this.pingDelay = undefined;\n  }\n\n  /** Creates a transport connection.\n   *\n   * This function has the same API as Transport#createConnection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: Object,\n  ): TransportConnection {\n    options = Collections.extend({}, options, {\n      activityTimeout: this.pingDelay,\n    });\n    var connection = this.transport.createConnection(\n      name,\n      priority,\n      key,\n      options,\n    );\n\n    var openTimestamp = null;\n\n    var onOpen = function () {\n      connection.unbind('open', onOpen);\n      connection.bind('closed', onClosed);\n      openTimestamp = Util.now();\n    };\n    var onClosed = (closeEvent) => {\n      connection.unbind('closed', onClosed);\n\n      if (closeEvent.code === 1002 || closeEvent.code === 1003) {\n        // we don't want to use transports not obeying the protocol\n        this.manager.reportDeath();\n      } else if (!closeEvent.wasClean && openTimestamp) {\n        // report deaths only for short-living transport\n        var lifespan = Util.now() - openTimestamp;\n        if (lifespan < 2 * this.maxPingDelay) {\n          this.manager.reportDeath();\n          this.pingDelay = Math.max(lifespan / 2, this.minPingDelay);\n        }\n      }\n    };\n\n    connection.bind('open', onOpen);\n    return connection;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * This function has the same API as Transport#isSupported. Might return false\n   * when the manager decides to kill the transport.\n   *\n   * @param {Object} environment the environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: string): boolean {\n    return this.manager.isAlive() && this.transport.isSupported(environment);\n  }\n}\n", "import Action from './action';\nimport { PusherEvent } from './message-types';\n/**\n * Provides functions for handling Pusher protocol-specific messages.\n */\n\nconst Protocol = {\n  /**\n   * Decodes a message in a Pusher format.\n   *\n   * The MessageEvent we receive from the transport should contain a pusher event\n   * (https://pusher.com/docs/pusher_protocol#events) serialized as JSO<PERSON> in the\n   * data field\n   *\n   * The pusher event may contain a data field too, and it may also be\n   * serialised as JSON\n   *\n   * Throws errors when messages are not parse'able.\n   *\n   * @param  {MessageEvent} messageEvent\n   * @return {PusherEvent}\n   */\n  decodeMessage: function (messageEvent: MessageEvent): PusherEvent {\n    try {\n      var messageData = JSON.parse(messageEvent.data);\n      var pusherEventData = messageData.data;\n      if (typeof pusherEventData === 'string') {\n        try {\n          pusherEventData = JSON.parse(messageData.data);\n        } catch (e) {}\n      }\n      var pusherEvent: PusherEvent = {\n        event: messageData.event,\n        channel: messageData.channel,\n        data: pusherEventData,\n      };\n      if (messageData.user_id) {\n        pusherEvent.user_id = messageData.user_id;\n      }\n      return pusherEvent;\n    } catch (e) {\n      throw { type: 'MessageParseError', error: e, data: messageEvent.data };\n    }\n  },\n\n  /**\n   * Encodes a message to be sent.\n   *\n   * @param  {PusherEvent} event\n   * @return {String}\n   */\n  encodeMessage: function (event: PusherEvent): string {\n    return JSON.stringify(event);\n  },\n\n  /**\n   * Processes a handshake message and returns appropriate actions.\n   *\n   * Returns an object with an 'action' and other action-specific properties.\n   *\n   * There are three outcomes when calling this function. First is a successful\n   * connection attempt, when pusher:connection_established is received, which\n   * results in a 'connected' action with an 'id' property. When passed a\n   * pusher:error event, it returns a result with action appropriate to the\n   * close code and an error. Otherwise, it raises an exception.\n   *\n   * @param {String} message\n   * @result Object\n   */\n  processHandshake: function (messageEvent: MessageEvent): Action {\n    var message = Protocol.decodeMessage(messageEvent);\n\n    if (message.event === 'pusher:connection_established') {\n      if (!message.data.activity_timeout) {\n        throw 'No activity timeout specified in handshake';\n      }\n      return {\n        action: 'connected',\n        id: message.data.socket_id,\n        activityTimeout: message.data.activity_timeout * 1000,\n      };\n    } else if (message.event === 'pusher:error') {\n      // From protocol 6 close codes are sent only once, so this only\n      // happens when connection does not support close codes\n      return {\n        action: this.getCloseAction(message.data),\n        error: this.getCloseError(message.data),\n      };\n    } else {\n      throw 'Invalid handshake';\n    }\n  },\n\n  /**\n   * Dispatches the close event and returns an appropriate action name.\n   *\n   * See:\n   * 1. https://developer.mozilla.org/en-US/docs/WebSockets/WebSockets_reference/CloseEvent\n   * 2. http://pusher.com/docs/pusher_protocol\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {String} close action name\n   */\n  getCloseAction: function (closeEvent): string {\n    if (closeEvent.code < 4000) {\n      // ignore 1000 CLOSE_NORMAL, 1001 CLOSE_GOING_AWAY,\n      //        1005 CLOSE_NO_STATUS, 1006 CLOSE_ABNORMAL\n      // ignore 1007...3999\n      // handle 1002 CLOSE_PROTOCOL_ERROR, 1003 CLOSE_UNSUPPORTED,\n      //        1004 CLOSE_TOO_LARGE\n      if (closeEvent.code >= 1002 && closeEvent.code <= 1004) {\n        return 'backoff';\n      } else {\n        return null;\n      }\n    } else if (closeEvent.code === 4000) {\n      return 'tls_only';\n    } else if (closeEvent.code < 4100) {\n      return 'refused';\n    } else if (closeEvent.code < 4200) {\n      return 'backoff';\n    } else if (closeEvent.code < 4300) {\n      return 'retry';\n    } else {\n      // unknown error\n      return 'refused';\n    }\n  },\n\n  /**\n   * Returns an error or null basing on the close event.\n   *\n   * Null is returned when connection was closed cleanly. Otherwise, an object\n   * with error details is returned.\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {Object} error object\n   */\n  getCloseError: function (closeEvent): any {\n    if (closeEvent.code !== 1000 && closeEvent.code !== 1001) {\n      return {\n        type: 'PusherError',\n        data: {\n          code: closeEvent.code,\n          message: closeEvent.reason || closeEvent.message,\n        },\n      };\n    } else {\n      return null;\n    }\n  },\n};\n\nexport default Protocol;\n", "import * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Protocol from './protocol/protocol';\nimport { PusherEvent } from './protocol/message-types';\nimport Logger from '../logger';\nimport TransportConnection from '../transports/transport_connection';\nimport Socket from '../socket';\n/**\n * Provides Pusher protocol interface for transports.\n *\n * Emits following events:\n * - message - on received messages\n * - ping - on ping requests\n * - pong - on pong responses\n * - error - when the transport emits an error\n * - closed - after closing the transport\n *\n * It also emits more events when connection closes with a code.\n * See Protocol.getCloseAction to get more details.\n *\n * @param {Number} id\n * @param {AbstractTransport} transport\n */\nexport default class Connection extends EventsDispatcher implements Socket {\n  id: string;\n  transport: TransportConnection;\n  activityTimeout: number;\n\n  constructor(id: string, transport: TransportConnection) {\n    super();\n    this.id = id;\n    this.transport = transport;\n    this.activityTimeout = transport.activityTimeout;\n    this.bindListeners();\n  }\n\n  /** Returns whether used transport handles activity checks by itself\n   *\n   * @returns {Boolean} true if activity checks are handled by the transport\n   */\n  handlesActivityChecks() {\n    return this.transport.handlesActivityChecks();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data: any): boolean {\n    return this.transport.send(data);\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string): boolean {\n    var event: PusherEvent = { event: name, data: data };\n    if (channel) {\n      event.channel = channel;\n    }\n    Logger.debug('Event sent', event);\n    return this.send(Protocol.encodeMessage(event));\n  }\n\n  /** Sends a ping message to the server.\n   *\n   * Basing on the underlying transport, it might send either transport's\n   * protocol-specific ping or pusher:ping event.\n   */\n  ping() {\n    if (this.transport.supportsPing()) {\n      this.transport.ping();\n    } else {\n      this.send_event('pusher:ping', {});\n    }\n  }\n\n  /** Closes the connection. */\n  close() {\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    var listeners = {\n      message: (messageEvent: MessageEvent) => {\n        var pusherEvent;\n        try {\n          pusherEvent = Protocol.decodeMessage(messageEvent);\n        } catch (e) {\n          this.emit('error', {\n            type: 'MessageParseError',\n            error: e,\n            data: messageEvent.data,\n          });\n        }\n\n        if (pusherEvent !== undefined) {\n          Logger.debug('Event recd', pusherEvent);\n\n          switch (pusherEvent.event) {\n            case 'pusher:error':\n              this.emit('error', {\n                type: 'PusherError',\n                data: pusherEvent.data,\n              });\n              break;\n            case 'pusher:ping':\n              this.emit('ping');\n              break;\n            case 'pusher:pong':\n              this.emit('pong');\n              break;\n          }\n          this.emit('message', pusherEvent);\n        }\n      },\n      activity: () => {\n        this.emit('activity');\n      },\n      error: (error) => {\n        this.emit('error', error);\n      },\n      closed: (closeEvent) => {\n        unbindListeners();\n\n        if (closeEvent && closeEvent.code) {\n          this.handleCloseEvent(closeEvent);\n        }\n\n        this.transport = null;\n        this.emit('closed');\n      },\n    };\n\n    var unbindListeners = () => {\n      Collections.objectApply(listeners, (listener, event) => {\n        this.transport.unbind(event, listener);\n      });\n    };\n\n    Collections.objectApply(listeners, (listener, event) => {\n      this.transport.bind(event, listener);\n    });\n  }\n\n  private handleCloseEvent(closeEvent: any) {\n    var action = Protocol.getCloseAction(closeEvent);\n    var error = Protocol.getCloseError(closeEvent);\n    if (error) {\n      this.emit('error', error);\n    }\n    if (action) {\n      this.emit(action, { action: action, error: error });\n    }\n  }\n}\n", "import Util from '../../util';\nimport * as Collections from '../../utils/collections';\nimport Protocol from '../protocol/protocol';\nimport Connection from '../connection';\nimport TransportConnection from '../../transports/transport_connection';\nimport HandshakePayload from './handshake_payload';\n\n/**\n * Handles Pusher protocol handshakes for transports.\n *\n * Calls back with a result object after handshake is completed. Results\n * always have two fields:\n * - action - string describing action to be taken after the handshake\n * - transport - the transport object passed to the constructor\n *\n * Different actions can set different additional properties on the result.\n * In the case of 'connected' action, there will be a 'connection' property\n * containing a Connection object for the transport. Other actions should\n * carry an 'error' property.\n *\n * @param {AbstractTransport} transport\n * @param {Function} callback\n */\nexport default class Handshake {\n  transport: TransportConnection;\n  callback: (HandshakePayload) => void;\n  onMessage: Function;\n  onClosed: Function;\n\n  constructor(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void,\n  ) {\n    this.transport = transport;\n    this.callback = callback;\n    this.bindListeners();\n  }\n\n  close() {\n    this.unbindListeners();\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    this.onMessage = (m) => {\n      this.unbindListeners();\n\n      var result;\n      try {\n        result = Protocol.processHandshake(m);\n      } catch (e) {\n        this.finish('error', { error: e });\n        this.transport.close();\n        return;\n      }\n\n      if (result.action === 'connected') {\n        this.finish('connected', {\n          connection: new Connection(result.id, this.transport),\n          activityTimeout: result.activityTimeout,\n        });\n      } else {\n        this.finish(result.action, { error: result.error });\n        this.transport.close();\n      }\n    };\n\n    this.onClosed = (closeEvent) => {\n      this.unbindListeners();\n\n      var action = Protocol.getCloseAction(closeEvent) || 'backoff';\n      var error = Protocol.getCloseError(closeEvent);\n      this.finish(action, { error: error });\n    };\n\n    this.transport.bind('message', this.onMessage);\n    this.transport.bind('closed', this.onClosed);\n  }\n\n  private unbindListeners() {\n    this.transport.unbind('message', this.onMessage);\n    this.transport.unbind('closed', this.onClosed);\n  }\n\n  private finish(action: string, params: any) {\n    this.callback(\n      Collections.extend({ transport: this.transport, action: action }, params),\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport base64encode from '../base64';\nimport Timeline from './timeline';\nimport Runtime from 'runtime';\n\nexport interface TimelineSenderOptions {\n  host?: string;\n  port?: number;\n  path?: string;\n}\n\nexport default class TimelineSender {\n  timeline: Timeline;\n  options: TimelineSenderOptions;\n  host: string;\n\n  constructor(timeline: Timeline, options: TimelineSenderOptions) {\n    this.timeline = timeline;\n    this.options = options || {};\n  }\n\n  send(useTLS: boolean, callback?: Function) {\n    if (this.timeline.isEmpty()) {\n      return;\n    }\n\n    this.timeline.send(\n      Runtime.TimelineTransport.getAgent(this, useTLS),\n      callback,\n    );\n  }\n}\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metada<PERSON> from './metadata';\nimport UrlStore from '../utils/url_store';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback,\n} from '../auth/options';\nimport { HTTPAuthError } from '../errors';\n\n/** Provides base public channel interface with an event emitter.\n *\n * Emits:\n * - pusher:subscription_succeeded - after subscribing successfully\n * - other non-internal events\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class Channel extends EventsDispatcher {\n  name: string;\n  pusher: Pusher;\n  subscribed: boolean;\n  subscriptionPending: boolean;\n  subscriptionCancelled: boolean;\n  subscriptionCount: null;\n\n  constructor(name: string, pusher: Pusher) {\n    super(function (event, data) {\n      Logger.debug('No callbacks on ' + name + ' for ' + event);\n    });\n\n    this.name = name;\n    this.pusher = pusher;\n    this.subscribed = false;\n    this.subscriptionPending = false;\n    this.subscriptionCancelled = false;\n  }\n\n  /** Skips authorization, since public channels don't require it.\n   *\n   * @param {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return callback(null, { auth: '' });\n  }\n\n  /** Triggers an event */\n  trigger(event: string, data: any) {\n    if (event.indexOf('client-') !== 0) {\n      throw new Errors.BadEventName(\n        \"Event '\" + event + \"' does not start with 'client-'\",\n      );\n    }\n    if (!this.subscribed) {\n      var suffix = UrlStore.buildLogSuffix('triggeringClientEvents');\n      Logger.warn(\n        `Client event triggered before channel 'subscription_succeeded' event . ${suffix}`,\n      );\n    }\n    return this.pusher.send_event(event, data, this.name);\n  }\n\n  /** Signals disconnection to the channel. For internal use only. */\n  disconnect() {\n    this.subscribed = false;\n    this.subscriptionPending = false;\n  }\n\n  /** Handles a PusherEvent. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (eventName === 'pusher_internal:subscription_succeeded') {\n      this.handleSubscriptionSucceededEvent(event);\n    } else if (eventName === 'pusher_internal:subscription_count') {\n      this.handleSubscriptionCountEvent(event);\n    } else if (eventName.indexOf('pusher_internal:') !== 0) {\n      var metadata: Metadata = {};\n      this.emit(eventName, data, metadata);\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.emit('pusher:subscription_succeeded', event.data);\n    }\n  }\n\n  handleSubscriptionCountEvent(event: PusherEvent) {\n    if (event.data.subscription_count) {\n      this.subscriptionCount = event.data.subscription_count;\n    }\n\n    this.emit('pusher:subscription_count', event.data);\n  }\n\n  /** Sends a subscription request. For internal use only. */\n  subscribe() {\n    if (this.subscribed) {\n      return;\n    }\n    this.subscriptionPending = true;\n    this.subscriptionCancelled = false;\n    this.authorize(\n      this.pusher.connection.socket_id,\n      (error: Error | null, data: ChannelAuthorizationData) => {\n        if (error) {\n          this.subscriptionPending = false;\n          // Why not bind to 'pusher:subscription_error' a level up, and log there?\n          // Binding to this event would cause the warning about no callbacks being\n          // bound (see constructor) to be suppressed, that's not what we want.\n          Logger.error(error.toString());\n          this.emit(\n            'pusher:subscription_error',\n            Object.assign(\n              {},\n              {\n                type: 'AuthError',\n                error: error.message,\n              },\n              error instanceof HTTPAuthError ? { status: error.status } : {},\n            ),\n          );\n        } else {\n          this.pusher.send_event('pusher:subscribe', {\n            auth: data.auth,\n            channel_data: data.channel_data,\n            channel: this.name,\n          });\n        }\n      },\n    );\n  }\n\n  /** Sends an unsubscription request. For internal use only. */\n  unsubscribe() {\n    this.subscribed = false;\n    this.pusher.send_event('pusher:unsubscribe', {\n      channel: this.name,\n    });\n  }\n\n  /** Cancels an in progress subscription. For internal use only. */\n  cancelSubscription() {\n    this.subscriptionCancelled = true;\n  }\n\n  /** Reinstates an in progress subscripiton. For internal use only. */\n  reinstateSubscription() {\n    this.subscriptionCancelled = false;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Channel from './channel';\nimport { ChannelAuthorizationCallback } from '../auth/options';\n\n/** Extends public channels to provide private channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class PrivateChannel extends Channel {\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return this.pusher.config.channelAuthorizer(\n      {\n        channelName: this.name,\n        socketId: socketId,\n      },\n      callback,\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\n\n/** Represents a collection of members of a presence channel. */\nexport default class Members {\n  members: any;\n  count: number;\n  myID: any;\n  me: any;\n\n  constructor() {\n    this.reset();\n  }\n\n  /** Returns member's info for given id.\n   *\n   * Resulting object containts two fields - id and info.\n   *\n   * @param {Number} id\n   * @return {Object} member's info or null\n   */\n  get(id: string): any {\n    if (Object.prototype.hasOwnProperty.call(this.members, id)) {\n      return {\n        id: id,\n        info: this.members[id],\n      };\n    } else {\n      return null;\n    }\n  }\n\n  /** Calls back for each member in unspecified order.\n   *\n   * @param  {Function} callback\n   */\n  each(callback: Function) {\n    Collections.objectApply(this.members, (member, id) => {\n      callback(this.get(id));\n    });\n  }\n\n  /** Updates the id for connected member. For internal use only. */\n  setMyID(id: string) {\n    this.myID = id;\n  }\n\n  /** Handles subscription data. For internal use only. */\n  onSubscription(subscriptionData: any) {\n    this.members = subscriptionData.presence.hash;\n    this.count = subscriptionData.presence.count;\n    this.me = this.get(this.myID);\n  }\n\n  /** Adds a new member to the collection. For internal use only. */\n  addMember(memberData: any) {\n    if (this.get(memberData.user_id) === null) {\n      this.count++;\n    }\n    this.members[memberData.user_id] = memberData.user_info;\n    return this.get(memberData.user_id);\n  }\n\n  /** Adds a member from the collection. For internal use only. */\n  removeMember(memberData: any) {\n    var member = this.get(memberData.user_id);\n    if (member) {\n      delete this.members[memberData.user_id];\n      this.count--;\n    }\n    return member;\n  }\n\n  /** Resets the collection to the initial state. For internal use only. */\n  reset() {\n    this.members = {};\n    this.count = 0;\n    this.myID = null;\n    this.me = null;\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport Logger from '../logger';\nimport Members from './members';\nimport Pusher from '../pusher';\nimport UrlStore from 'core/utils/url_store';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metadata from './metadata';\nimport { ChannelAuthorizationData } from '../auth/options';\n\nexport default class PresenceChannel extends PrivateChannel {\n  members: Members;\n\n  /** Adds presence channel functionality to private channels.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   */\n  constructor(name: string, pusher: Pusher) {\n    super(name, pusher);\n    this.members = new Members();\n  }\n\n  /** Authorizes the connection as a member of the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: Function) {\n    super.authorize(socketId, async (error, authData) => {\n      if (!error) {\n        authData = authData as ChannelAuthorizationData;\n        if (authData.channel_data != null) {\n          var channelData = JSON.parse(authData.channel_data);\n          this.members.setMyID(channelData.user_id);\n        } else {\n          await this.pusher.user.signinDonePromise;\n          if (this.pusher.user.user_data != null) {\n            // If the user is signed in, get the id of the authenticated user\n            // and allow the presence authorization to continue.\n            this.members.setMyID(this.pusher.user.user_data.id);\n          } else {\n            let suffix = UrlStore.buildLogSuffix('authorizationEndpoint');\n            Logger.error(\n              `Invalid auth response for channel '${this.name}', ` +\n                `expected 'channel_data' field. ${suffix}, ` +\n                `or the user should be signed in.`,\n            );\n            callback('Invalid auth response');\n            return;\n          }\n        }\n      }\n      callback(error, authData);\n    });\n  }\n\n  /** Handles presence and subscription events. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    if (eventName.indexOf('pusher_internal:') === 0) {\n      this.handleInternalEvent(event);\n    } else {\n      var data = event.data;\n      var metadata: Metadata = {};\n      if (event.user_id) {\n        metadata.user_id = event.user_id;\n      }\n      this.emit(eventName, data, metadata);\n    }\n  }\n  handleInternalEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    switch (eventName) {\n      case 'pusher_internal:subscription_succeeded':\n        this.handleSubscriptionSucceededEvent(event);\n        break;\n      case 'pusher_internal:subscription_count':\n        this.handleSubscriptionCountEvent(event);\n        break;\n      case 'pusher_internal:member_added':\n        var addedMember = this.members.addMember(data);\n        this.emit('pusher:member_added', addedMember);\n        break;\n      case 'pusher_internal:member_removed':\n        var removedMember = this.members.removeMember(data);\n        if (removedMember) {\n          this.emit('pusher:member_removed', removedMember);\n        }\n        break;\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.members.onSubscription(event.data);\n      this.emit('pusher:subscription_succeeded', this.members);\n    }\n  }\n\n  /** Resets the channel state, including members map. For internal use only. */\n  disconnect() {\n    this.members.reset();\n    super.disconnect();\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { decode as encodeUTF8 } from '@stablelib/utf8';\nimport { decode as decodeBase64 } from '@stablelib/base64';\nimport Dispatcher from '../events/dispatcher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback,\n} from '../auth/options';\nimport * as nacl from 'tweetnacl';\n\n/** Extends private channels to provide encrypted channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class EncryptedChannel extends PrivateChannel {\n  key: Uint8Array = null;\n  nacl: nacl;\n\n  constructor(name: string, pusher: Pusher, nacl: nacl) {\n    super(name, pusher);\n    this.nacl = nacl;\n  }\n\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    super.authorize(\n      socketId,\n      (error: Error | null, authData: ChannelAuthorizationData) => {\n        if (error) {\n          callback(error, authData);\n          return;\n        }\n        let sharedSecret = authData['shared_secret'];\n        if (!sharedSecret) {\n          callback(\n            new Error(\n              `No shared_secret key in auth payload for encrypted channel: ${this.name}`,\n            ),\n            null,\n          );\n          return;\n        }\n        this.key = decodeBase64(sharedSecret);\n        delete authData['shared_secret'];\n        callback(null, authData);\n      },\n    );\n  }\n\n  trigger(event: string, data: any): boolean {\n    throw new Errors.UnsupportedFeature(\n      'Client events are not currently supported for encrypted channels',\n    );\n  }\n\n  /** Handles an event. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (\n      eventName.indexOf('pusher_internal:') === 0 ||\n      eventName.indexOf('pusher:') === 0\n    ) {\n      super.handleEvent(event);\n      return;\n    }\n    this.handleEncryptedEvent(eventName, data);\n  }\n\n  private handleEncryptedEvent(event: string, data: any): void {\n    if (!this.key) {\n      Logger.debug(\n        'Received encrypted event before key has been retrieved from the authEndpoint',\n      );\n      return;\n    }\n    if (!data.ciphertext || !data.nonce) {\n      Logger.error(\n        'Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: ' +\n          data,\n      );\n      return;\n    }\n    let cipherText = decodeBase64(data.ciphertext);\n    if (cipherText.length < this.nacl.secretbox.overheadLength) {\n      Logger.error(\n        `Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${cipherText.length}`,\n      );\n      return;\n    }\n    let nonce = decodeBase64(data.nonce);\n    if (nonce.length < this.nacl.secretbox.nonceLength) {\n      Logger.error(\n        `Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${nonce.length}`,\n      );\n      return;\n    }\n\n    let bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n    if (bytes === null) {\n      Logger.debug(\n        'Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint...',\n      );\n      // Try a single time to retrieve a new auth key and decrypt the event with it\n      // If this fails, a new key will be requested when a new message is received\n      this.authorize(this.pusher.connection.socket_id, (error, authData) => {\n        if (error) {\n          Logger.error(\n            `Failed to make a request to the authEndpoint: ${authData}. Unable to fetch new key, so dropping encrypted event`,\n          );\n          return;\n        }\n        bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n        if (bytes === null) {\n          Logger.error(\n            `Failed to decrypt event with new key. Dropping encrypted event`,\n          );\n          return;\n        }\n        this.emit(event, this.getDataToEmit(bytes));\n        return;\n      });\n      return;\n    }\n    this.emit(event, this.getDataToEmit(bytes));\n  }\n\n  // Try and parse the decrypted bytes as JSON. If we can't parse it, just\n  // return the utf-8 string\n  getDataToEmit(bytes: Uint8Array): string {\n    let raw = encodeUTF8(bytes);\n    try {\n      return JSON.parse(raw);\n    } catch {\n      return raw;\n    }\n  }\n}\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport { Config } from '../config';\nimport Logger from '../logger';\nimport HandshakePayload from './handshake/handshake_payload';\nimport Connection from './connection';\nimport Strategy from '../strategies/strategy';\nimport StrategyRunner from '../strategies/strategy_runner';\nimport * as Collections from '../utils/collections';\nimport Timeline from '../timeline/timeline';\nimport ConnectionManagerOptions from './connection_manager_options';\nimport Runtime from 'runtime';\n\nimport {\n  ErrorCallbacks,\n  HandshakeCallbacks,\n  ConnectionCallbacks,\n} from './callbacks';\nimport Action from './protocol/action';\n\n/** Manages connection to Pusher.\n *\n * Uses a strategy (currently only default), timers and network availability\n * info to establish a connection and export its state. In case of failures,\n * manages reconnection attempts.\n *\n * Exports state changes as following events:\n * - \"state_change\", { previous: p, current: state }\n * - state\n *\n * States:\n * - initialized - initial state, never transitioned to\n * - connecting - connection is being established\n * - connected - connection has been fully established\n * - disconnected - on requested disconnection\n * - unavailable - after connection timeout or when there's no network\n * - failed - when the connection strategy is not supported\n *\n * Options:\n * - unavailableTimeout - time to transition to unavailable state\n * - activityTimeout - time after which ping message should be sent\n * - pongTimeout - time for Pusher to respond with pong before reconnecting\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class ConnectionManager extends EventsDispatcher {\n  key: string;\n  options: ConnectionManagerOptions;\n  state: string;\n  connection: Connection;\n  usingTLS: boolean;\n  timeline: Timeline;\n  socket_id: string;\n  unavailableTimer: Timer;\n  activityTimer: Timer;\n  retryTimer: Timer;\n  activityTimeout: number;\n  strategy: Strategy;\n  runner: StrategyRunner;\n  errorCallbacks: ErrorCallbacks;\n  handshakeCallbacks: HandshakeCallbacks;\n  connectionCallbacks: ConnectionCallbacks;\n\n  constructor(key: string, options: ConnectionManagerOptions) {\n    super();\n    this.state = 'initialized';\n    this.connection = null;\n\n    this.key = key;\n    this.options = options;\n    this.timeline = this.options.timeline;\n    this.usingTLS = this.options.useTLS;\n\n    this.errorCallbacks = this.buildErrorCallbacks();\n    this.connectionCallbacks = this.buildConnectionCallbacks(\n      this.errorCallbacks,\n    );\n    this.handshakeCallbacks = this.buildHandshakeCallbacks(this.errorCallbacks);\n\n    var Network = Runtime.getNetwork();\n\n    Network.bind('online', () => {\n      this.timeline.info({ netinfo: 'online' });\n      if (this.state === 'connecting' || this.state === 'unavailable') {\n        this.retryIn(0);\n      }\n    });\n    Network.bind('offline', () => {\n      this.timeline.info({ netinfo: 'offline' });\n      if (this.connection) {\n        this.sendActivityCheck();\n      }\n    });\n\n    this.updateStrategy();\n  }\n\n  /** Establishes a connection to Pusher.\n   *\n   * Does nothing when connection is already established. See top-level doc\n   * to find events emitted on connection attempts.\n   */\n  connect() {\n    if (this.connection || this.runner) {\n      return;\n    }\n    if (!this.strategy.isSupported()) {\n      this.updateState('failed');\n      return;\n    }\n    this.updateState('connecting');\n    this.startConnecting();\n    this.setUnavailableTimer();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data) {\n    if (this.connection) {\n      return this.connection.send(data);\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string) {\n    if (this.connection) {\n      return this.connection.send_event(name, data, channel);\n    } else {\n      return false;\n    }\n  }\n\n  /** Closes the connection. */\n  disconnect() {\n    this.disconnectInternally();\n    this.updateState('disconnected');\n  }\n\n  isUsingTLS() {\n    return this.usingTLS;\n  }\n\n  private startConnecting() {\n    var callback = (error, handshake) => {\n      if (error) {\n        this.runner = this.strategy.connect(0, callback);\n      } else {\n        if (handshake.action === 'error') {\n          this.emit('error', {\n            type: 'HandshakeError',\n            error: handshake.error,\n          });\n          this.timeline.error({ handshakeError: handshake.error });\n        } else {\n          this.abortConnecting(); // we don't support switching connections yet\n          this.handshakeCallbacks[handshake.action](handshake);\n        }\n      }\n    };\n    this.runner = this.strategy.connect(0, callback);\n  }\n\n  private abortConnecting() {\n    if (this.runner) {\n      this.runner.abort();\n      this.runner = null;\n    }\n  }\n\n  private disconnectInternally() {\n    this.abortConnecting();\n    this.clearRetryTimer();\n    this.clearUnavailableTimer();\n    if (this.connection) {\n      var connection = this.abandonConnection();\n      connection.close();\n    }\n  }\n\n  private updateStrategy() {\n    this.strategy = this.options.getStrategy({\n      key: this.key,\n      timeline: this.timeline,\n      useTLS: this.usingTLS,\n    });\n  }\n\n  private retryIn(delay) {\n    this.timeline.info({ action: 'retry', delay: delay });\n    if (delay > 0) {\n      this.emit('connecting_in', Math.round(delay / 1000));\n    }\n    this.retryTimer = new Timer(delay || 0, () => {\n      this.disconnectInternally();\n      this.connect();\n    });\n  }\n\n  private clearRetryTimer() {\n    if (this.retryTimer) {\n      this.retryTimer.ensureAborted();\n      this.retryTimer = null;\n    }\n  }\n\n  private setUnavailableTimer() {\n    this.unavailableTimer = new Timer(this.options.unavailableTimeout, () => {\n      this.updateState('unavailable');\n    });\n  }\n\n  private clearUnavailableTimer() {\n    if (this.unavailableTimer) {\n      this.unavailableTimer.ensureAborted();\n    }\n  }\n\n  private sendActivityCheck() {\n    this.stopActivityCheck();\n    this.connection.ping();\n    // wait for pong response\n    this.activityTimer = new Timer(this.options.pongTimeout, () => {\n      this.timeline.error({ pong_timed_out: this.options.pongTimeout });\n      this.retryIn(0);\n    });\n  }\n\n  private resetActivityCheck() {\n    this.stopActivityCheck();\n    // send ping after inactivity\n    if (this.connection && !this.connection.handlesActivityChecks()) {\n      this.activityTimer = new Timer(this.activityTimeout, () => {\n        this.sendActivityCheck();\n      });\n    }\n  }\n\n  private stopActivityCheck() {\n    if (this.activityTimer) {\n      this.activityTimer.ensureAborted();\n    }\n  }\n\n  private buildConnectionCallbacks(\n    errorCallbacks: ErrorCallbacks,\n  ): ConnectionCallbacks {\n    return Collections.extend<ConnectionCallbacks>({}, errorCallbacks, {\n      message: (message) => {\n        // includes pong messages from server\n        this.resetActivityCheck();\n        this.emit('message', message);\n      },\n      ping: () => {\n        this.send_event('pusher:pong', {});\n      },\n      activity: () => {\n        this.resetActivityCheck();\n      },\n      error: (error) => {\n        // just emit error to user - socket will already be closed by browser\n        this.emit('error', error);\n      },\n      closed: () => {\n        this.abandonConnection();\n        if (this.shouldRetry()) {\n          this.retryIn(1000);\n        }\n      },\n    });\n  }\n\n  private buildHandshakeCallbacks(\n    errorCallbacks: ErrorCallbacks,\n  ): HandshakeCallbacks {\n    return Collections.extend<HandshakeCallbacks>({}, errorCallbacks, {\n      connected: (handshake: HandshakePayload) => {\n        this.activityTimeout = Math.min(\n          this.options.activityTimeout,\n          handshake.activityTimeout,\n          handshake.connection.activityTimeout || Infinity,\n        );\n        this.clearUnavailableTimer();\n        this.setConnection(handshake.connection);\n        this.socket_id = this.connection.id;\n        this.updateState('connected', { socket_id: this.socket_id });\n      },\n    });\n  }\n\n  private buildErrorCallbacks(): ErrorCallbacks {\n    let withErrorEmitted = (callback) => {\n      return (result: Action | HandshakePayload) => {\n        if (result.error) {\n          this.emit('error', { type: 'WebSocketError', error: result.error });\n        }\n        callback(result);\n      };\n    };\n\n    return {\n      tls_only: withErrorEmitted(() => {\n        this.usingTLS = true;\n        this.updateStrategy();\n        this.retryIn(0);\n      }),\n      refused: withErrorEmitted(() => {\n        this.disconnect();\n      }),\n      backoff: withErrorEmitted(() => {\n        this.retryIn(1000);\n      }),\n      retry: withErrorEmitted(() => {\n        this.retryIn(0);\n      }),\n    };\n  }\n\n  private setConnection(connection) {\n    this.connection = connection;\n    for (var event in this.connectionCallbacks) {\n      this.connection.bind(event, this.connectionCallbacks[event]);\n    }\n    this.resetActivityCheck();\n  }\n\n  private abandonConnection() {\n    if (!this.connection) {\n      return;\n    }\n    this.stopActivityCheck();\n    for (var event in this.connectionCallbacks) {\n      this.connection.unbind(event, this.connectionCallbacks[event]);\n    }\n    var connection = this.connection;\n    this.connection = null;\n    return connection;\n  }\n\n  private updateState(newState: string, data?: any) {\n    var previousState = this.state;\n    this.state = newState;\n    if (previousState !== newState) {\n      var newStateDescription = newState;\n      if (newStateDescription === 'connected') {\n        newStateDescription += ' with new socket ID ' + data.socket_id;\n      }\n      Logger.debug(\n        'State changed',\n        previousState + ' -> ' + newStateDescription,\n      );\n      this.timeline.info({ state: newState, params: data });\n      this.emit('state_change', { previous: previousState, current: newState });\n      this.emit(newState, data);\n    }\n  }\n\n  private shouldRetry(): boolean {\n    return this.state === 'connecting' || this.state === 'connected';\n  }\n}\n", "import Channel from './channel';\nimport * as Collections from '../utils/collections';\nimport ChannelTable from './channel_table';\nimport Factory from '../utils/factory';\nimport Pusher from '../pusher';\nimport Logger from '../logger';\nimport * as Errors from '../errors';\nimport urlStore from '../utils/url_store';\n\n/** Handles a channel map. */\nexport default class Channels {\n  channels: ChannelTable;\n\n  constructor() {\n    this.channels = {};\n  }\n\n  /** Creates or retrieves an existing channel by its name.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   * @return {Channel}\n   */\n  add(name: string, pusher: Pusher) {\n    if (!this.channels[name]) {\n      this.channels[name] = createChannel(name, pusher);\n    }\n    return this.channels[name];\n  }\n\n  /** Returns a list of all channels\n   *\n   * @return {Array}\n   */\n  all(): Channel[] {\n    return Collections.values(this.channels);\n  }\n\n  /** Finds a channel by its name.\n   *\n   * @param {String} name\n   * @return {Channel} channel or null if it doesn't exist\n   */\n  find(name: string) {\n    return this.channels[name];\n  }\n\n  /** Removes a channel from the map.\n   *\n   * @param {String} name\n   */\n  remove(name: string) {\n    var channel = this.channels[name];\n    delete this.channels[name];\n    return channel;\n  }\n\n  /** Proxies disconnection signal to all channels. */\n  disconnect() {\n    Collections.objectApply(this.channels, function (channel) {\n      channel.disconnect();\n    });\n  }\n}\n\nfunction createChannel(name: string, pusher: Pusher): Channel {\n  if (name.indexOf('private-encrypted-') === 0) {\n    if (pusher.config.nacl) {\n      return Factory.createEncryptedChannel(name, pusher, pusher.config.nacl);\n    }\n    let errMsg =\n      'Tried to subscribe to a private-encrypted- channel but no nacl implementation available';\n    let suffix = urlStore.buildLogSuffix('encryptedChannelSupport');\n    throw new Errors.UnsupportedFeature(`${errMsg}. ${suffix}`);\n  } else if (name.indexOf('private-') === 0) {\n    return Factory.createPrivateChannel(name, pusher);\n  } else if (name.indexOf('presence-') === 0) {\n    return Factory.createPresenceChannel(name, pusher);\n  } else if (name.indexOf('#') === 0) {\n    throw new Errors.BadChannelName(\n      'Cannot create a channel with name \"' + name + '\".',\n    );\n  } else {\n    return Factory.createChannel(name, pusher);\n  }\n}\n", "import AssistantToTheTransportManager from '../transports/assistant_to_the_transport_manager';\nimport PingDelayOptions from '../transports/ping_delay_options';\nimport Transport from '../transports/transport';\nimport TransportManager from '../transports/transport_manager';\nimport Handshake from '../connection/handshake';\nimport TransportConnection from '../transports/transport_connection';\nimport SocketHooks from '../http/socket_hooks';\nimport HTTPSocket from '../http/http_socket';\n\nimport Timeline from '../timeline/timeline';\nimport {\n  default as TimelineSender,\n  TimelineSenderOptions,\n} from '../timeline/timeline_sender';\nimport PresenceChannel from '../channels/presence_channel';\nimport PrivateChannel from '../channels/private_channel';\nimport EncryptedChannel from '../channels/encrypted_channel';\nimport Channel from '../channels/channel';\nimport ConnectionManager from '../connection/connection_manager';\nimport ConnectionManagerOptions from '../connection/connection_manager_options';\nimport Ajax from '../http/ajax';\nimport Channels from '../channels/channels';\nimport Pusher from '../pusher';\nimport { Config } from '../config';\nimport * as nacl from 'tweetnacl';\n\nvar Factory = {\n  createChannels(): Channels {\n    return new Channels();\n  },\n\n  createConnectionManager(\n    key: string,\n    options: ConnectionManagerOptions,\n  ): ConnectionManager {\n    return new ConnectionManager(key, options);\n  },\n\n  createChannel(name: string, pusher: Pusher): Channel {\n    return new Channel(name, pusher);\n  },\n\n  createPrivateChannel(name: string, pusher: Pusher): PrivateChannel {\n    return new PrivateChannel(name, pusher);\n  },\n\n  createPresenceChannel(name: string, pusher: Pusher): PresenceChannel {\n    return new PresenceChannel(name, pusher);\n  },\n\n  createEncryptedChannel(\n    name: string,\n    pusher: Pusher,\n    nacl: nacl,\n  ): EncryptedChannel {\n    return new EncryptedChannel(name, pusher, nacl);\n  },\n\n  createTimelineSender(timeline: Timeline, options: TimelineSenderOptions) {\n    return new TimelineSender(timeline, options);\n  },\n\n  createHandshake(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void,\n  ): Handshake {\n    return new Handshake(transport, callback);\n  },\n\n  createAssistantToTheTransportManager(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions,\n  ): AssistantToTheTransportManager {\n    return new AssistantToTheTransportManager(manager, transport, options);\n  },\n};\n\nexport default Factory;\n", "import AssistantToTheTransportManager from './assistant_to_the_transport_manager';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\nimport Factory from '../utils/factory';\n\nexport interface TransportManagerOptions extends PingDelayOptions {\n  lives?: number;\n}\n\n/** Keeps track of the number of lives left for a transport.\n *\n * In the beginning of a session, transports may be assigned a number of\n * lives. When an AssistantToTheTransportManager instance reports a transport\n * connection closed uncleanly, the transport loses a life. When the number\n * of lives drops to zero, the transport gets disabled by its manager.\n *\n * @param {Object} options\n */\nexport default class TransportManager {\n  options: TransportManagerOptions;\n  livesLeft: number;\n\n  constructor(options: TransportManagerOptions) {\n    this.options = options || {};\n    this.livesLeft = this.options.lives || Infinity;\n  }\n\n  /** Creates a assistant for the transport.\n   *\n   * @param {Transport} transport\n   * @returns {AssistantToTheTransportManager}\n   */\n  getAssistant(transport: Transport): AssistantToTheTransportManager {\n    return Factory.createAssistantToTheTransportManager(this, transport, {\n      minPingDelay: this.options.minPingDelay,\n      maxPingDelay: this.options.maxPingDelay,\n    });\n  }\n\n  /** Returns whether the transport has any lives left.\n   *\n   * @returns {Boolean}\n   */\n  isAlive(): boolean {\n    return this.livesLeft > 0;\n  }\n\n  /** Takes one life from the transport. */\n  reportDeath() {\n    this.livesLeft -= 1;\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Loops through strategies with optional timeouts.\n *\n * Options:\n * - loop - whether it should loop through the substrategy list\n * - timeout - initial timeout for a single substrategy\n * - timeoutLimit - maximum timeout\n *\n * @param {Strategy[]} strategies\n * @param {Object} options\n */\nexport default class SequentialStrategy implements Strategy {\n  strategies: Strategy[];\n  loop: boolean;\n  failFast: boolean;\n  timeout: number;\n  timeoutLimit: number;\n\n  constructor(strategies: Strategy[], options: StrategyOptions) {\n    this.strategies = strategies;\n    this.loop = Boolean(options.loop);\n    this.failFast = Boolean(options.failFast);\n    this.timeout = options.timeout;\n    this.timeoutLimit = options.timeoutLimit;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategies = this.strategies;\n    var current = 0;\n    var timeout = this.timeout;\n    var runner = null;\n\n    var tryNextStrategy = (error, handshake) => {\n      if (handshake) {\n        callback(null, handshake);\n      } else {\n        current = current + 1;\n        if (this.loop) {\n          current = current % strategies.length;\n        }\n\n        if (current < strategies.length) {\n          if (timeout) {\n            timeout = timeout * 2;\n            if (this.timeoutLimit) {\n              timeout = Math.min(timeout, this.timeoutLimit);\n            }\n          }\n          runner = this.tryStrategy(\n            strategies[current],\n            minPriority,\n            { timeout, failFast: this.failFast },\n            tryNextStrategy,\n          );\n        } else {\n          callback(true);\n        }\n      }\n    };\n\n    runner = this.tryStrategy(\n      strategies[current],\n      minPriority,\n      { timeout: timeout, failFast: this.failFast },\n      tryNextStrategy,\n    );\n\n    return {\n      abort: function () {\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n\n  private tryStrategy(\n    strategy: Strategy,\n    minPriority: number,\n    options: StrategyOptions,\n    callback: Function,\n  ) {\n    var timer = null;\n    var runner = null;\n\n    if (options.timeout > 0) {\n      timer = new Timer(options.timeout, function () {\n        runner.abort();\n        callback(true);\n      });\n    }\n\n    runner = strategy.connect(minPriority, function (error, handshake) {\n      if (error && timer && timer.isRunning() && !options.failFast) {\n        // advance to the next strategy after the timeout\n        return;\n      }\n      if (timer) {\n        timer.ensureAborted();\n      }\n      callback(error, handshake);\n    });\n\n    return {\n      abort: function () {\n        if (timer) {\n          timer.ensureAborted();\n        }\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        runner.forceMinPriority(p);\n      },\n    };\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport Strategy from './strategy';\n\n/** Launches all substrategies and emits prioritized connected transports.\n *\n * @param {Array} strategies\n */\nexport default class BestConnectedEverStrategy implements Strategy {\n  strategies: Strategy[];\n\n  constructor(strategies: Strategy[]) {\n    this.strategies = strategies;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    return connect(this.strategies, minPriority, function (i, runners) {\n      return function (error, handshake) {\n        runners[i].error = error;\n        if (error) {\n          if (allRunnersFailed(runners)) {\n            callback(true);\n          }\n          return;\n        }\n        Collections.apply(runners, function (runner) {\n          runner.forceMinPriority(handshake.transport.priority);\n        });\n        callback(null, handshake);\n      };\n    });\n  }\n}\n\n/** Connects to all strategies in parallel.\n *\n * Callback builder should be a function that takes two arguments: index\n * and a list of runners. It should return another function that will be\n * passed to the substrategy with given index. Runners can be aborted using\n * abortRunner(s) functions from this class.\n *\n * @param  {Array} strategies\n * @param  {Function} callbackBuilder\n * @return {Object} strategy runner\n */\nfunction connect(\n  strategies: Strategy[],\n  minPriority: number,\n  callbackBuilder: Function,\n) {\n  var runners = Collections.map(strategies, function (strategy, i, _, rs) {\n    return strategy.connect(minPriority, callbackBuilder(i, rs));\n  });\n  return {\n    abort: function () {\n      Collections.apply(runners, abortRunner);\n    },\n    forceMinPriority: function (p) {\n      Collections.apply(runners, function (runner) {\n        runner.forceMinPriority(p);\n      });\n    },\n  };\n}\n\nfunction allRunnersFailed(runners): boolean {\n  return Collections.all(runners, function (runner) {\n    return Boolean(runner.error);\n  });\n}\n\nfunction abortRunner(runner) {\n  if (!runner.error && !runner.aborted) {\n    runner.abort();\n    runner.aborted = true;\n  }\n}\n", "import Util from '../util';\nimport Runtime from 'runtime';\nimport Strategy from './strategy';\nimport SequentialStrategy from './sequential_strategy';\nimport StrategyOptions from './strategy_options';\nimport TransportStrategy from './transport_strategy';\nimport Timeline from '../timeline/timeline';\nimport * as Collections from '../utils/collections';\n\nexport interface TransportStrategyDictionary {\n  [key: string]: TransportStrategy;\n}\n\n/** Caches the last successful transport and, after the first few attempts,\n *  uses the cached transport for subsequent attempts.\n *\n * @param {Strategy} strategy\n * @param {Object} transports\n * @param {Object} options\n */\nexport default class WebSocketPrioritizedCachedStrategy implements Strategy {\n  strategy: Strategy;\n  transports: TransportStrategyDictionary;\n  ttl: number;\n  usingTLS: boolean;\n  timeline: Timeline;\n\n  constructor(\n    strategy: Strategy,\n    transports: TransportStrategyDictionary,\n    options: StrategyOptions,\n  ) {\n    this.strategy = strategy;\n    this.transports = transports;\n    this.ttl = options.ttl || 1800 * 1000;\n    this.usingTLS = options.useTLS;\n    this.timeline = options.timeline;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var usingTLS = this.usingTLS;\n    var info = fetchTransportCache(usingTLS);\n    var cacheSkipCount = info && info.cacheSkipCount ? info.cacheSkipCount : 0;\n\n    var strategies = [this.strategy];\n    if (info && info.timestamp + this.ttl >= Util.now()) {\n      var transport = this.transports[info.transport];\n      if (transport) {\n        if (['ws', 'wss'].includes(info.transport) || cacheSkipCount > 3) {\n          this.timeline.info({\n            cached: true,\n            transport: info.transport,\n            latency: info.latency,\n          });\n          strategies.push(\n            new SequentialStrategy([transport], {\n              timeout: info.latency * 2 + 1000,\n              failFast: true,\n            }),\n          );\n        } else {\n          cacheSkipCount++;\n        }\n      }\n    }\n\n    var startTimestamp = Util.now();\n    var runner = strategies\n      .pop()\n      .connect(minPriority, function cb(error, handshake) {\n        if (error) {\n          flushTransportCache(usingTLS);\n          if (strategies.length > 0) {\n            startTimestamp = Util.now();\n            runner = strategies.pop().connect(minPriority, cb);\n          } else {\n            callback(error);\n          }\n        } else {\n          storeTransportCache(\n            usingTLS,\n            handshake.transport.name,\n            Util.now() - startTimestamp,\n            cacheSkipCount,\n          );\n          callback(null, handshake);\n        }\n      });\n\n    return {\n      abort: function () {\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n}\n\nfunction getTransportCacheKey(usingTLS: boolean): string {\n  return 'pusherTransport' + (usingTLS ? 'TLS' : 'NonTLS');\n}\n\nfunction fetchTransportCache(usingTLS: boolean): any {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      var serializedCache = storage[getTransportCacheKey(usingTLS)];\n      if (serializedCache) {\n        return JSON.parse(serializedCache);\n      }\n    } catch (e) {\n      flushTransportCache(usingTLS);\n    }\n  }\n  return null;\n}\n\nfunction storeTransportCache(\n  usingTLS: boolean,\n  transport: TransportStrategy,\n  latency: number,\n  cacheSkipCount: number,\n) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      storage[getTransportCacheKey(usingTLS)] = Collections.safeJSONStringify({\n        timestamp: Util.now(),\n        transport: transport,\n        latency: latency,\n        cacheSkipCount: cacheSkipCount,\n      });\n    } catch (e) {\n      // catch over quota exceptions raised by localStorage\n    }\n  }\n}\n\nfunction flushTransportCache(usingTLS: boolean) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      delete storage[getTransportCacheKey(usingTLS)];\n    } catch (e) {\n      // catch exceptions raised by localStorage\n    }\n  }\n}\n", "import { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Runs substrategy after specified delay.\n *\n * Options:\n * - delay - time in miliseconds to delay the substrategy attempt\n *\n * @param {Strategy} strategy\n * @param {Object} options\n */\nexport default class DelayedStrategy implements Strategy {\n  strategy: Strategy;\n  options: { delay: number };\n\n  constructor(strategy: Strategy, { delay: number }) {\n    this.strategy = strategy;\n    this.options = { delay: number };\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategy = this.strategy;\n    var runner;\n    var timer = new Timer(this.options.delay, function () {\n      runner = strategy.connect(minPriority, callback);\n    });\n\n    return {\n      abort: function () {\n        timer.ensureAborted();\n        if (runner) {\n          runner.abort();\n        }\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Proxies method calls to one of substrategies basing on the test function.\n *\n * @param {Function} test\n * @param {Strategy} trueBranch strategy used when test returns true\n * @param {Strategy} falseBranch strategy used when test returns false\n */\nexport default class IfStrategy implements Strategy {\n  test: () => boolean;\n  trueBranch: Strategy;\n  falseBranch: Strategy;\n\n  constructor(\n    test: () => boolean,\n    trueBranch: Strategy,\n    falseBranch: Strategy,\n  ) {\n    this.test = test;\n    this.trueBranch = trueBranch;\n    this.falseBranch = falseBranch;\n  }\n\n  isSupported(): boolean {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.connect(minPriority, callback);\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Launches the substrategy and terminates on the first open connection.\n *\n * @param {Strategy} strategy\n */\nexport default class FirstConnectedStrategy implements Strategy {\n  strategy: Strategy;\n\n  constructor(strategy: Strategy) {\n    this.strategy = strategy;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var runner = this.strategy.connect(\n      minPriority,\n      function (error, handshake) {\n        if (handshake) {\n          runner.abort();\n        }\n        callback(error, handshake);\n      },\n    );\n    return runner;\n  }\n}\n", "import * as Collections from 'core/utils/collections';\nimport TransportManager from 'core/transports/transport_manager';\nimport Strategy from 'core/strategies/strategy';\nimport StrategyOptions from 'core/strategies/strategy_options';\nimport SequentialStrategy from 'core/strategies/sequential_strategy';\nimport BestConnectedEverStrategy from 'core/strategies/best_connected_ever_strategy';\nimport WebSocketPrioritizedCachedStrategy, {\n  TransportStrategyDictionary,\n} from 'core/strategies/websocket_prioritized_cached_strategy';\nimport DelayedStrategy from 'core/strategies/delayed_strategy';\nimport IfStrategy from 'core/strategies/if_strategy';\nimport FirstConnectedStrategy from 'core/strategies/first_connected_strategy';\nimport { Config } from 'core/config';\n\nfunction testSupportsStrategy(strategy: Strategy) {\n  return function () {\n    return strategy.isSupported();\n  };\n}\n\nvar getDefaultStrategy = function (\n  config: Config,\n  baseOptions: StrategyOptions,\n  defineTransport: Function,\n): Strategy {\n  var definedTransports = <TransportStrategyDictionary>{};\n\n  function defineTransportStrategy(\n    name: string,\n    type: string,\n    priority: number,\n    options: StrategyOptions,\n    manager?: TransportManager,\n  ) {\n    var transport = defineTransport(\n      config,\n      name,\n      type,\n      priority,\n      options,\n      manager,\n    );\n\n    definedTransports[name] = transport;\n\n    return transport;\n  }\n\n  var ws_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.wsHost + ':' + config.wsPort,\n    hostTLS: config.wsHost + ':' + config.wssPort,\n    httpPath: config.wsPath,\n  });\n  var wss_options: StrategyOptions = Object.assign({}, ws_options, {\n    useTLS: true,\n  });\n  var sockjs_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.httpHost + ':' + config.httpPort,\n    hostTLS: config.httpHost + ':' + config.httpsPort,\n    httpPath: config.httpPath,\n  });\n\n  var timeouts = {\n    loop: true,\n    timeout: 15000,\n    timeoutLimit: 60000,\n  };\n\n  var ws_manager = new TransportManager({\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout,\n  });\n  var streaming_manager = new TransportManager({\n    lives: 2,\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout,\n  });\n\n  var ws_transport = defineTransportStrategy(\n    'ws',\n    'ws',\n    3,\n    ws_options,\n    ws_manager,\n  );\n  var wss_transport = defineTransportStrategy(\n    'wss',\n    'ws',\n    3,\n    wss_options,\n    ws_manager,\n  );\n  var sockjs_transport = defineTransportStrategy(\n    'sockjs',\n    'sockjs',\n    1,\n    sockjs_options,\n  );\n  var xhr_streaming_transport = defineTransportStrategy(\n    'xhr_streaming',\n    'xhr_streaming',\n    1,\n    sockjs_options,\n    streaming_manager,\n  );\n  var xdr_streaming_transport = defineTransportStrategy(\n    'xdr_streaming',\n    'xdr_streaming',\n    1,\n    sockjs_options,\n    streaming_manager,\n  );\n  var xhr_polling_transport = defineTransportStrategy(\n    'xhr_polling',\n    'xhr_polling',\n    1,\n    sockjs_options,\n  );\n  var xdr_polling_transport = defineTransportStrategy(\n    'xdr_polling',\n    'xdr_polling',\n    1,\n    sockjs_options,\n  );\n\n  var ws_loop = new SequentialStrategy([ws_transport], timeouts);\n  var wss_loop = new SequentialStrategy([wss_transport], timeouts);\n  var sockjs_loop = new SequentialStrategy([sockjs_transport], timeouts);\n  var streaming_loop = new SequentialStrategy(\n    [\n      new IfStrategy(\n        testSupportsStrategy(xhr_streaming_transport),\n        xhr_streaming_transport,\n        xdr_streaming_transport,\n      ),\n    ],\n    timeouts,\n  );\n  var polling_loop = new SequentialStrategy(\n    [\n      new IfStrategy(\n        testSupportsStrategy(xhr_polling_transport),\n        xhr_polling_transport,\n        xdr_polling_transport,\n      ),\n    ],\n    timeouts,\n  );\n\n  var http_loop = new SequentialStrategy(\n    [\n      new IfStrategy(\n        testSupportsStrategy(streaming_loop),\n        new BestConnectedEverStrategy([\n          streaming_loop,\n          new DelayedStrategy(polling_loop, { delay: 4000 }),\n        ]),\n        polling_loop,\n      ),\n    ],\n    timeouts,\n  );\n\n  var http_fallback_loop = new IfStrategy(\n    testSupportsStrategy(http_loop),\n    http_loop,\n    sockjs_loop,\n  );\n\n  var wsStrategy;\n  if (baseOptions.useTLS) {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(http_fallback_loop, { delay: 2000 }),\n    ]);\n  } else {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(wss_loop, { delay: 2000 }),\n      new DelayedStrategy(http_fallback_loop, { delay: 5000 }),\n    ]);\n  }\n\n  return new WebSocketPrioritizedCachedStrategy(\n    new FirstConnectedStrategy(\n      new IfStrategy(\n        testSupportsStrategy(ws_transport),\n        wsStrategy,\n        http_fallback_loop,\n      ),\n    ),\n    definedTransports,\n    {\n      ttl: 1800000,\n      timeline: baseOptions.timeline,\n      useTLS: baseOptions.useTLS,\n    },\n  );\n};\n\nexport default getDefaultStrategy;\n", "import HTTPRequest from 'core/http/http_request';\nimport <PERSON><PERSON>Hooks from 'core/http/request_hooks';\nimport <PERSON> from 'core/http/ajax';\nimport * as Errors from 'core/errors';\n\nvar hooks: RequestHooks = {\n  getRequest: function (socket: HTTPRequest): Ajax {\n    var xdr = new (<any>window).XDomainRequest();\n    xdr.ontimeout = function () {\n      socket.emit('error', new Errors.RequestTimedOut());\n      socket.close();\n    };\n    xdr.onerror = function (e) {\n      socket.emit('error', e);\n      socket.close();\n    };\n    xdr.onprogress = function () {\n      if (xdr.responseText && xdr.responseText.length > 0) {\n        socket.onChunk(200, xdr.responseText);\n      }\n    };\n    xdr.onload = function () {\n      if (xdr.responseText && xdr.responseText.length > 0) {\n        socket.onChunk(200, xdr.responseText);\n      }\n      socket.emit('finished', 200);\n      socket.close();\n    };\n    return xdr;\n  },\n  abortRequest: function (xdr: <PERSON>) {\n    xdr.ontimeout = xdr.onerror = xdr.onprogress = xdr.onload = null;\n    xdr.abort();\n  },\n};\n\nexport default hooks;\n", "import Runtime from 'runtime';\nimport RequestHooks from './request_hooks';\nimport <PERSON> from './ajax';\nimport { default as EventsDispatcher } from '../events/dispatcher';\n\nconst MAX_BUFFER_LENGTH = 256 * 1024;\n\nexport default class HTTPRequest extends EventsDispatcher {\n  hooks: RequestHooks;\n  method: string;\n  url: string;\n  position: number;\n  xhr: Ajax;\n  unloader: Function;\n\n  constructor(hooks: RequestHooks, method: string, url: string) {\n    super();\n    this.hooks = hooks;\n    this.method = method;\n    this.url = url;\n  }\n\n  start(payload?: any) {\n    this.position = 0;\n    this.xhr = this.hooks.getRequest(this);\n\n    this.unloader = () => {\n      this.close();\n    };\n    Runtime.addUnloadListener(this.unloader);\n\n    this.xhr.open(this.method, this.url, true);\n\n    if (this.xhr.setRequestHeader) {\n      this.xhr.setRequestHeader('Content-Type', 'application/json'); // ReactNative doesn't set this header by default.\n    }\n    this.xhr.send(payload);\n  }\n\n  close() {\n    if (this.unloader) {\n      Runtime.removeUnloadListener(this.unloader);\n      this.unloader = null;\n    }\n    if (this.xhr) {\n      this.hooks.abortRequest(this.xhr);\n      this.xhr = null;\n    }\n  }\n\n  onChunk(status: number, data: any) {\n    while (true) {\n      var chunk = this.advanceBuffer(data);\n      if (chunk) {\n        this.emit('chunk', { status: status, data: chunk });\n      } else {\n        break;\n      }\n    }\n    if (this.isBufferTooLong(data)) {\n      this.emit('buffer_too_long');\n    }\n  }\n\n  private advanceBuffer(buffer: any[]): any {\n    var unreadData = buffer.slice(this.position);\n    var endOfLinePosition = unreadData.indexOf('\\n');\n\n    if (endOfLinePosition !== -1) {\n      this.position += endOfLinePosition + 1;\n      return unreadData.slice(0, endOfLinePosition);\n    } else {\n      // chunk is not finished yet, don't move the buffer pointer\n      return null;\n    }\n  }\n\n  private isBufferTooLong(buffer: any): boolean {\n    return this.position === buffer.length && buffer.length > MAX_BUFFER_LENGTH;\n  }\n}\n", "enum State {\n  CONNECTING = 0,\n  OPEN = 1,\n  CLOSED = 3,\n}\n\nexport default State;\n", "import URLLocation from './url_location';\nimport State from './state';\nimport Socket from '../socket';\nimport SocketHooks from './socket_hooks';\nimport Util from '../util';\nimport Ajax from './ajax';\nimport HTTPRequest from './http_request';\nimport Runtime from 'runtime';\n\nvar autoIncrement = 1;\n\nclass HTTPSocket implements Socket {\n  hooks: SocketHooks;\n  session: string;\n  location: URLLocation;\n  readyState: State;\n  stream: HTTPRequest;\n\n  onopen: () => void;\n  onerror: (error: any) => void;\n  onclose: (closeEvent: any) => void;\n  onmessage: (message: any) => void;\n  onactivity: () => void;\n\n  constructor(hooks: SocketHooks, url: string) {\n    this.hooks = hooks;\n    this.session = randomNumber(1000) + '/' + randomString(8);\n    this.location = getLocation(url);\n    this.readyState = State.CONNECTING;\n    this.openStream();\n  }\n\n  send(payload: any) {\n    return this.sendRaw(JSON.stringify([payload]));\n  }\n\n  ping() {\n    this.hooks.sendHeartbeat(this);\n  }\n\n  close(code: any, reason: any) {\n    this.onClose(code, reason, true);\n  }\n\n  /** For internal use only */\n  sendRaw(payload: any): boolean {\n    if (this.readyState === State.OPEN) {\n      try {\n        Runtime.createSocketRequest(\n          'POST',\n          getUniqueURL(getSendURL(this.location, this.session)),\n        ).start(payload);\n        return true;\n      } catch (e) {\n        return false;\n      }\n    } else {\n      return false;\n    }\n  }\n\n  /** For internal use only */\n  reconnect() {\n    this.closeStream();\n    this.openStream();\n  }\n\n  /** For internal use only */\n  onClose(code, reason, wasClean) {\n    this.closeStream();\n    this.readyState = State.CLOSED;\n    if (this.onclose) {\n      this.onclose({\n        code: code,\n        reason: reason,\n        wasClean: wasClean,\n      });\n    }\n  }\n\n  private onChunk(chunk) {\n    if (chunk.status !== 200) {\n      return;\n    }\n    if (this.readyState === State.OPEN) {\n      this.onActivity();\n    }\n\n    var payload;\n    var type = chunk.data.slice(0, 1);\n    switch (type) {\n      case 'o':\n        payload = JSON.parse(chunk.data.slice(1) || '{}');\n        this.onOpen(payload);\n        break;\n      case 'a':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        for (var i = 0; i < payload.length; i++) {\n          this.onEvent(payload[i]);\n        }\n        break;\n      case 'm':\n        payload = JSON.parse(chunk.data.slice(1) || 'null');\n        this.onEvent(payload);\n        break;\n      case 'h':\n        this.hooks.onHeartbeat(this);\n        break;\n      case 'c':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        this.onClose(payload[0], payload[1], true);\n        break;\n    }\n  }\n\n  private onOpen(options) {\n    if (this.readyState === State.CONNECTING) {\n      if (options && options.hostname) {\n        this.location.base = replaceHost(this.location.base, options.hostname);\n      }\n      this.readyState = State.OPEN;\n\n      if (this.onopen) {\n        this.onopen();\n      }\n    } else {\n      this.onClose(1006, 'Server lost session', true);\n    }\n  }\n\n  private onEvent(event) {\n    if (this.readyState === State.OPEN && this.onmessage) {\n      this.onmessage({ data: event });\n    }\n  }\n\n  private onActivity() {\n    if (this.onactivity) {\n      this.onactivity();\n    }\n  }\n\n  private onError(error) {\n    if (this.onerror) {\n      this.onerror(error);\n    }\n  }\n\n  private openStream() {\n    this.stream = Runtime.createSocketRequest(\n      'POST',\n      getUniqueURL(this.hooks.getReceiveURL(this.location, this.session)),\n    );\n\n    this.stream.bind('chunk', (chunk) => {\n      this.onChunk(chunk);\n    });\n    this.stream.bind('finished', (status) => {\n      this.hooks.onFinished(this, status);\n    });\n    this.stream.bind('buffer_too_long', () => {\n      this.reconnect();\n    });\n\n    try {\n      this.stream.start();\n    } catch (error) {\n      Util.defer(() => {\n        this.onError(error);\n        this.onClose(1006, 'Could not start streaming', false);\n      });\n    }\n  }\n\n  private closeStream() {\n    if (this.stream) {\n      this.stream.unbind_all();\n      this.stream.close();\n      this.stream = null;\n    }\n  }\n}\n\nfunction getLocation(url): URLLocation {\n  var parts = /([^\\?]*)\\/*(\\??.*)/.exec(url);\n  return {\n    base: parts[1],\n    queryString: parts[2],\n  };\n}\n\nfunction getSendURL(url: URLLocation, session: string): string {\n  return url.base + '/' + session + '/xhr_send';\n}\n\nfunction getUniqueURL(url: string): string {\n  var separator = url.indexOf('?') === -1 ? '?' : '&';\n  return url + separator + 't=' + +new Date() + '&n=' + autoIncrement++;\n}\n\nfunction replaceHost(url: string, hostname: string): string {\n  var urlParts = /(https?:\\/\\/)([^\\/:]+)((\\/|:)?.*)/.exec(url);\n  return urlParts[1] + hostname + urlParts[3];\n}\n\nfunction randomNumber(max: number): number {\n  return Runtime.randomInt(max);\n}\n\nfunction randomString(length: number): string {\n  var result = [];\n\n  for (var i = 0; i < length; i++) {\n    result.push(randomNumber(32).toString(32));\n  }\n\n  return result.join('');\n}\n\nexport default HTTPSocket;\n", "enum TimelineLevel {\n  ERROR = 3,\n  INFO = 6,\n  DEBUG = 7,\n}\n\nexport default TimelineLevel;\n", "import SocketHooks from './socket_hooks';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function (url, session) {\n    return url.base + '/' + session + '/xhr_streaming' + url.queryString;\n  },\n  onHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  sendHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function (socket, status) {\n    socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n  },\n};\n\nexport default hooks;\n", "import SocketHooks from './socket_hooks';\nimport URLLocation from './url_location';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function (url: URLLocation, session: string): string {\n    return url.base + '/' + session + '/xhr' + url.queryString;\n  },\n  onHeartbeat: function () {\n    // next HTTP request will reset server's activity timer\n  },\n  sendHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function (socket, status) {\n    if (status === 200) {\n      socket.reconnect();\n    } else {\n      socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n    }\n  },\n};\n\nexport default hooks;\n", "import HTTPRequest from 'core/http/http_request';\nimport <PERSON>questHooks from 'core/http/request_hooks';\nimport Ajax from 'core/http/ajax';\nimport Runtime from 'runtime';\n\nvar hooks: RequestHooks = {\n  getRequest: function (socket: HTTPRequest): Ajax {\n    var Constructor = Runtime.getXHRAPI();\n    var xhr = new Constructor();\n    xhr.onreadystatechange = xhr.onprogress = function () {\n      switch (xhr.readyState) {\n        case 3:\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          break;\n        case 4:\n          // this happens only on errors, never after calling close\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          socket.emit('finished', xhr.status);\n          socket.close();\n          break;\n      }\n    };\n    return xhr;\n  },\n  abortRequest: function (xhr: Ajax) {\n    xhr.onreadystatechange = null;\n    xhr.abort();\n  },\n};\n\nexport default hooks;\n", "import HTTPRequest from 'core/http/http_request';\nimport HTTPSocket from 'core/http/http_socket';\nimport SocketHooks from 'core/http/socket_hooks';\nimport RequestHooks from 'core/http/request_hooks';\nimport streamingHooks from 'core/http/http_streaming_socket';\nimport pollingHooks from 'core/http/http_polling_socket';\nimport xhrHooks from './http_xhr_request';\nimport HTTPFactory from 'core/http/http_factory';\n\nvar HTTP: HTTPFactory = {\n  createStreamingSocket(url: string): HTTPSocket {\n    return this.createSocket(streamingHooks, url);\n  },\n\n  createPollingSocket(url: string): HTTPSocket {\n    return this.createSocket(pollingHooks, url);\n  },\n\n  createSocket(hooks: SocketHooks, url: string): HTTPSocket {\n    return new HTTPSocket(hooks, url);\n  },\n\n  createXHR(method: string, url: string): HTTPRequest {\n    return this.createRequest(xhrHooks, method, url);\n  },\n\n  createRequest(hooks: RequestHooks, method: string, url: string): HTTPRequest {\n    return new HTTPRequest(hooks, method, url);\n  },\n};\n\nexport default HTTP;\n", "import xdrHooks from './http_xdomain_request';\nimport HTTP from 'isomorphic/http/http';\n\nHTTP.createXDR = function (method, url) {\n  return this.createRequest(xdrHooks, method, url);\n};\n\nexport default HTTP;\n", "import Browser from './browser';\nimport { Dependencies, DependenciesReceivers } from './dom/dependencies';\nimport { AuthTransport, AuthTransports } from 'core/auth/auth_transports';\nimport xhrAuth from 'isomorphic/auth/xhr_auth';\nimport jsonpAuth from './auth/jsonp_auth';\nimport TimelineTransport from 'core/timeline/timeline_transport';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport ScriptRequest from './dom/script_request';\nimport JSONPRequest from './dom/jsonp_request';\nimport * as Collections from 'core/utils/collections';\nimport { ScriptReceivers } from './dom/script_receiver_factory';\nimport jsonpTimeline from './timeline/jsonp_timeline';\nimport Transports from './transports/transports';\nimport Ajax from 'core/http/ajax';\nimport { Network } from './net_info';\nimport getDefaultStrategy from './default_strategy';\nimport transportConnectionInitializer from './transports/transport_connection_initializer';\nimport HTTPFactory from './http/http';\nimport HTTPRequest from 'core/http/http_request';\n\nvar Runtime: Browser = {\n  // for jsonp auth\n  nextAuthCallbackID: 1,\n  auth_callbacks: {},\n  ScriptReceivers,\n  DependenciesReceivers,\n  getDefaultStrategy,\n  Transports,\n  transportConnectionInitializer,\n  HTTPFactory,\n\n  TimelineTransport: jsonpTimeline,\n\n  getXHRAPI() {\n    return window.XMLHttpRequest;\n  },\n\n  getWebSocketAPI() {\n    return window.WebSocket || window.MozWebSocket;\n  },\n\n  setup(PusherClass): void {\n    (<any>window).Pusher = PusherClass; // JSONp requires Pusher to be in the global scope.\n    var initializeOnDocumentBody = () => {\n      this.onDocumentBody(PusherClass.ready);\n    };\n    if (!(<any>window).JSON) {\n      Dependencies.load('json2', {}, initializeOnDocumentBody);\n    } else {\n      initializeOnDocumentBody();\n    }\n  },\n\n  getDocument(): Document {\n    return document;\n  },\n\n  getProtocol(): string {\n    return this.getDocument().location.protocol;\n  },\n\n  getAuthorizers(): AuthTransports {\n    return { ajax: xhrAuth, jsonp: jsonpAuth };\n  },\n\n  onDocumentBody(callback: Function) {\n    if (document.body) {\n      callback();\n    } else {\n      setTimeout(() => {\n        this.onDocumentBody(callback);\n      }, 0);\n    }\n  },\n\n  createJSONPRequest(url: string, data: any): JSONPRequest {\n    return new JSONPRequest(url, data);\n  },\n\n  createScriptRequest(src: string): ScriptRequest {\n    return new ScriptRequest(src);\n  },\n\n  getLocalStorage() {\n    try {\n      return window.localStorage;\n    } catch (e) {\n      return undefined;\n    }\n  },\n\n  createXHR(): Ajax {\n    if (this.getXHRAPI()) {\n      return this.createXMLHttpRequest();\n    } else {\n      return this.createMicrosoftXHR();\n    }\n  },\n\n  createXMLHttpRequest(): Ajax {\n    var Constructor = this.getXHRAPI();\n    return new Constructor();\n  },\n\n  createMicrosoftXHR(): Ajax {\n    return new ActiveXObject('Microsoft.XMLHTTP');\n  },\n\n  getNetwork() {\n    return Network;\n  },\n\n  createWebSocket(url: string): any {\n    var Constructor = this.getWebSocketAPI();\n    return new Constructor(url);\n  },\n\n  createSocketRequest(method: string, url: string): HTTPRequest {\n    if (this.isXHRSupported()) {\n      return this.HTTPFactory.createXHR(method, url);\n    } else if (this.isXDRSupported(url.indexOf('https:') === 0)) {\n      return this.HTTPFactory.createXDR(method, url);\n    } else {\n      throw 'Cross-origin HTTP requests are not supported';\n    }\n  },\n\n  isXHRSupported(): boolean {\n    var Constructor = this.getXHRAPI();\n    return (\n      Boolean(Constructor) && new Constructor().withCredentials !== undefined\n    );\n  },\n\n  isXDRSupported(useTLS?: boolean): boolean {\n    var protocol = useTLS ? 'https:' : 'http:';\n    var documentProtocol = this.getProtocol();\n    return (\n      Boolean(<any>window['XDomainRequest']) && documentProtocol === protocol\n    );\n  },\n\n  addUnloadListener(listener: any) {\n    if (window.addEventListener !== undefined) {\n      window.addEventListener('unload', listener, false);\n    } else if (window.attachEvent !== undefined) {\n      window.attachEvent('onunload', listener);\n    }\n  },\n\n  removeUnloadListener(listener: any) {\n    if (window.addEventListener !== undefined) {\n      window.removeEventListener('unload', listener, false);\n    } else if (window.detachEvent !== undefined) {\n      window.detachEvent('onunload', listener);\n    }\n  },\n\n  randomInt(max: number): number {\n    /**\n     * Return values in the range of [0, 1[\n     */\n    const random = function () {\n      const crypto = window.crypto || window['msCrypto'];\n      const random = crypto.getRandomValues(new Uint32Array(1))[0];\n\n      return random / 2 ** 32;\n    };\n\n    return Math.floor(random() * max);\n  },\n};\n\nexport default Runtime;\n", "import { Dependencies } from '../dom/dependencies';\n\n/** Initializes the transport.\n *\n * Fetches resources if needed and then transitions to initialized.\n */\nexport default function () {\n  var self = this;\n\n  self.timeline.info(\n    self.buildTimelineMessage({\n      transport: self.name + (self.options.useTLS ? 's' : ''),\n    }),\n  );\n\n  if (self.hooks.isInitialized()) {\n    self.changeState('initialized');\n  } else if (self.hooks.file) {\n    self.changeState('initializing');\n    Dependencies.load(\n      self.hooks.file,\n      { useTLS: self.options.useTLS },\n      function (error, callback) {\n        if (self.hooks.isInitialized()) {\n          self.changeState('initialized');\n          callback(true);\n        } else {\n          if (error) {\n            self.onError(error);\n          }\n          self.onClose();\n          callback(false);\n        }\n      },\n    );\n  } else {\n    self.onClose();\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { default as Level } from './level';\n\nexport interface TimelineOptions {\n  level?: Level;\n  limit?: number;\n  version?: string;\n  cluster?: string;\n  features?: string[];\n  params?: any;\n}\n\nexport default class Timeline {\n  key: string;\n  session: number;\n  events: any[];\n  options: TimelineOptions;\n  sent: number;\n  uniqueID: number;\n\n  constructor(key: string, session: number, options: TimelineOptions) {\n    this.key = key;\n    this.session = session;\n    this.events = [];\n    this.options = options || {};\n    this.sent = 0;\n    this.uniqueID = 0;\n  }\n\n  log(level, event) {\n    if (level <= this.options.level) {\n      this.events.push(\n        Collections.extend({}, event, { timestamp: Util.now() }),\n      );\n      if (this.options.limit && this.events.length > this.options.limit) {\n        this.events.shift();\n      }\n    }\n  }\n\n  error(event) {\n    this.log(Level.ERROR, event);\n  }\n\n  info(event) {\n    this.log(Level.INFO, event);\n  }\n\n  debug(event) {\n    this.log(Level.DEBUG, event);\n  }\n\n  isEmpty() {\n    return this.events.length === 0;\n  }\n\n  send(sendfn, callback) {\n    var data = Collections.extend(\n      {\n        session: this.session,\n        bundle: this.sent + 1,\n        key: this.key,\n        lib: 'js',\n        version: this.options.version,\n        cluster: this.options.cluster,\n        features: this.options.features,\n        timeline: this.events,\n      },\n      this.options.params,\n    );\n\n    this.events = [];\n    sendfn(data, (error, result) => {\n      if (!error) {\n        this.sent++;\n      }\n      if (callback) {\n        callback(error, result);\n      }\n    });\n\n    return true;\n  }\n\n  generateUniqueID(): number {\n    this.uniqueID++;\n    return this.uniqueID;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Util from '../util';\nimport * as Errors from '../errors';\nimport * as Collections from '../utils/collections';\nimport Strategy from './strategy';\nimport Transport from '../transports/transport';\nimport StrategyOptions from './strategy_options';\nimport Handshake from '../connection/handshake';\n\n/** Provides a strategy interface for transports.\n *\n * @param {String} name\n * @param {Number} priority\n * @param {Class} transport\n * @param {Object} options\n */\nexport default class TransportStrategy implements Strategy {\n  name: string;\n  priority: number;\n  transport: Transport;\n  options: StrategyOptions;\n\n  constructor(\n    name: string,\n    priority: number,\n    transport: Transport,\n    options: StrategyOptions,\n  ) {\n    this.name = name;\n    this.priority = priority;\n    this.transport = transport;\n    this.options = options || {};\n  }\n\n  /** Returns whether the transport is supported in the browser.\n   *\n   * @returns {Boolean}\n   */\n  isSupported(): boolean {\n    return this.transport.isSupported({\n      useTLS: this.options.useTLS,\n    });\n  }\n\n  /** Launches a connection attempt and returns a strategy runner.\n   *\n   * @param  {Function} callback\n   * @return {Object} strategy runner\n   */\n  connect(minPriority: number, callback: Function) {\n    if (!this.isSupported()) {\n      return failAttempt(new Errors.UnsupportedStrategy(), callback);\n    } else if (this.priority < minPriority) {\n      return failAttempt(new Errors.TransportPriorityTooLow(), callback);\n    }\n\n    var connected = false;\n    var transport = this.transport.createConnection(\n      this.name,\n      this.priority,\n      this.options.key,\n      this.options,\n    );\n    var handshake = null;\n\n    var onInitialized = function () {\n      transport.unbind('initialized', onInitialized);\n      transport.connect();\n    };\n    var onOpen = function () {\n      handshake = Factory.createHandshake(transport, function (result) {\n        connected = true;\n        unbindListeners();\n        callback(null, result);\n      });\n    };\n    var onError = function (error) {\n      unbindListeners();\n      callback(error);\n    };\n    var onClosed = function () {\n      unbindListeners();\n      var serializedTransport;\n\n      // The reason for this try/catch block is that on React Native\n      // the WebSocket object is circular. Therefore transport.socket will\n      // throw errors upon stringification. Collections.safeJSONStringify\n      // discards circular references when serializing.\n      serializedTransport = Collections.safeJSONStringify(transport);\n      callback(new Errors.TransportClosed(serializedTransport));\n    };\n\n    var unbindListeners = function () {\n      transport.unbind('initialized', onInitialized);\n      transport.unbind('open', onOpen);\n      transport.unbind('error', onError);\n      transport.unbind('closed', onClosed);\n    };\n\n    transport.bind('initialized', onInitialized);\n    transport.bind('open', onOpen);\n    transport.bind('error', onError);\n    transport.bind('closed', onClosed);\n\n    // connect will be called automatically after initialization\n    transport.initialize();\n\n    return {\n      abort: () => {\n        if (connected) {\n          return;\n        }\n        unbindListeners();\n        if (handshake) {\n          handshake.close();\n        } else {\n          transport.close();\n        }\n      },\n      forceMinPriority: (p) => {\n        if (connected) {\n          return;\n        }\n        if (this.priority < p) {\n          if (handshake) {\n            handshake.close();\n          } else {\n            transport.close();\n          }\n        }\n      },\n    };\n  }\n}\n\nfunction failAttempt(error: Error, callback: Function) {\n  Util.defer(function () {\n    callback(error);\n  });\n  return {\n    abort: function () {},\n    forceMinPriority: function () {},\n  };\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport TransportManager from '../transports/transport_manager';\nimport * as Errors from '../errors';\nimport Strategy from './strategy';\nimport TransportStrategy from './transport_strategy';\nimport StrategyOptions from '../strategies/strategy_options';\nimport { Config } from '../config';\nimport Runtime from 'runtime';\n\nconst { Transports } = Runtime;\n\nexport var defineTransport = function (\n  config: Config,\n  name: string,\n  type: string,\n  priority: number,\n  options: StrategyOptions,\n  manager?: TransportManager,\n): Strategy {\n  var transportClass = Transports[type];\n  if (!transportClass) {\n    throw new Errors.UnsupportedTransport(type);\n  }\n\n  var enabled =\n    (!config.enabledTransports ||\n      Collections.arrayIndexOf(config.enabledTransports, name) !== -1) &&\n    (!config.disabledTransports ||\n      Collections.arrayIndexOf(config.disabledTransports, name) === -1);\n\n  var transport;\n  if (enabled) {\n    options = Object.assign(\n      { ignoreNullOrigin: config.ignoreNullOrigin },\n      options,\n    );\n\n    transport = new TransportStrategy(\n      name,\n      priority,\n      manager ? manager.getAssistant(transportClass) : transportClass,\n      options,\n    );\n  } else {\n    transport = UnsupportedStrategy;\n  }\n\n  return transport;\n};\n\nvar UnsupportedStrategy: Strategy = {\n  isSupported: function () {\n    return false;\n  },\n  connect: function (_, callback) {\n    var deferred = Util.defer(function () {\n      callback(new Errors.UnsupportedStrategy());\n    });\n    return {\n      abort: function () {\n        deferred.ensureAborted();\n      },\n      forceMinPriority: function () {},\n    };\n  },\n};\n", "import ConnectionManager from './connection/connection_manager';\nimport {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions,\n} from './auth/options';\nimport {\n  ChannelAuthorizerGenerator,\n  DeprecatedAuthOptions,\n} from './auth/deprecated_channel_authorizer';\nimport { AuthTransport, Transport } from './config';\nimport * as nacl from 'tweetnacl';\nimport Logger from './logger';\n\nexport interface Options {\n  activityTimeout?: number;\n\n  auth?: DeprecatedAuthOptions; // DEPRECATED use channelAuthorization instead\n  authEndpoint?: string; // DEPRECATED use channelAuthorization instead\n  authTransport?: AuthTransport; // DEPRECATED use channelAuthorization instead\n  authorizer?: ChannelAuthorizerGenerator; // DEPRECATED use channelAuthorization instead\n\n  channelAuthorization?: ChannelAuthorizationOptions;\n  userAuthentication?: UserAuthenticationOptions;\n\n  cluster: string;\n  enableStats?: boolean;\n  disableStats?: boolean;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  forceTLS?: boolean;\n  httpHost?: string;\n  httpPath?: string;\n  httpPort?: number;\n  httpsPort?: number;\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  pongTimeout?: number;\n  statsHost?: string;\n  timelineParams?: any;\n  unavailableTimeout?: number;\n  wsHost?: string;\n  wsPath?: string;\n  wsPort?: number;\n  wssPort?: number;\n}\n\nexport function validateOptions(options) {\n  if (options == null) {\n    throw 'You must pass an options object';\n  }\n  if (options.cluster == null) {\n    throw 'Options object must provide a cluster';\n  }\n  if ('disableStats' in options) {\n    Logger.warn(\n      'The disableStats option is deprecated in favor of enableStats',\n    );\n  }\n}\n", "import {\n  UserAuthenticationCallback,\n  InternalAuthOptions,\n  UserAuthenticationHandler,\n  UserAuthenticationRequestParams,\n  AuthRequestType,\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: UserAuthenticationRequestParams,\n  authOptions: InternalAuthOptions,\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst UserAuthenticator = (\n  authOptions: InternalAuthOptions,\n): UserAuthenticationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback,\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.UserAuthentication,\n      callback,\n    );\n  };\n};\n\nexport default UserAuthenticator;\n", "import {\n  AuthRequestType,\n  InternalAuthOptions,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  ChannelAuthorizationCallback,\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: ChannelAuthorizationRequestParams,\n  authOptions: InternalAuthOptions,\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  query += '&channel_name=' + encodeURIComponent(params.channelName);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst ChannelAuthorizer = (\n  authOptions: InternalAuthOptions,\n): ChannelAuthorizationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.ChannelAuthorization,\n      callback,\n    );\n  };\n};\n\nexport default ChannelAuthorizer;\n", "import { Options } from './options';\nimport Defaults from './defaults';\nimport {\n  Channel<PERSON>uth<PERSON>zation<PERSON><PERSON><PERSON>,\n  UserAuthenticationHandler,\n  ChannelAuthorizationOptions,\n} from './auth/options';\nimport UserAuthenticator from './auth/user_authenticator';\nimport ChannelAuthorizer from './auth/channel_authorizer';\nimport { ChannelAuthorizerProxy } from './auth/deprecated_channel_authorizer';\nimport Runtime from 'runtime';\nimport * as nacl from 'tweetnacl';\n\nexport type AuthTransport = 'ajax' | 'jsonp';\nexport type Transport =\n  | 'ws'\n  | 'wss'\n  | 'xhr_streaming'\n  | 'xhr_polling'\n  | 'sockjs';\n\nexport interface Config {\n  // these are all 'required' config parameters, it's not necessary for the user\n  // to set them, but they have configured defaults.\n  activityTimeout: number;\n  enableStats: boolean;\n  httpHost: string;\n  httpPath: string;\n  httpPort: number;\n  httpsPort: number;\n  pongTimeout: number;\n  statsHost: string;\n  unavailableTimeout: number;\n  useTLS: boolean;\n  wsHost: string;\n  wsPath: string;\n  wsPort: number;\n  wssPort: number;\n  userAuthenticator: UserAuthenticationHandler;\n  channelAuthorizer: ChannelAuthorizationHandler;\n\n  // these are all optional parameters or overrrides. The customer can set these\n  // but it's not strictly necessary\n  forceTLS?: boolean;\n  cluster?: string;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  timelineParams?: any;\n}\n\n// getConfig mainly sets the defaults for the options that are not provided\nexport function getConfig(opts: Options, pusher): Config {\n  let config: Config = {\n    activityTimeout: opts.activityTimeout || Defaults.activityTimeout,\n    cluster: opts.cluster,\n    httpPath: opts.httpPath || Defaults.httpPath,\n    httpPort: opts.httpPort || Defaults.httpPort,\n    httpsPort: opts.httpsPort || Defaults.httpsPort,\n    pongTimeout: opts.pongTimeout || Defaults.pongTimeout,\n    statsHost: opts.statsHost || Defaults.stats_host,\n    unavailableTimeout: opts.unavailableTimeout || Defaults.unavailableTimeout,\n    wsPath: opts.wsPath || Defaults.wsPath,\n    wsPort: opts.wsPort || Defaults.wsPort,\n    wssPort: opts.wssPort || Defaults.wssPort,\n\n    enableStats: getEnableStatsConfig(opts),\n    httpHost: getHttpHost(opts),\n    useTLS: shouldUseTLS(opts),\n    wsHost: getWebsocketHost(opts),\n\n    userAuthenticator: buildUserAuthenticator(opts),\n    channelAuthorizer: buildChannelAuthorizer(opts, pusher),\n  };\n\n  if ('disabledTransports' in opts)\n    config.disabledTransports = opts.disabledTransports;\n  if ('enabledTransports' in opts)\n    config.enabledTransports = opts.enabledTransports;\n  if ('ignoreNullOrigin' in opts)\n    config.ignoreNullOrigin = opts.ignoreNullOrigin;\n  if ('timelineParams' in opts) config.timelineParams = opts.timelineParams;\n  if ('nacl' in opts) {\n    config.nacl = opts.nacl;\n  }\n\n  return config;\n}\n\nfunction getHttpHost(opts: Options): string {\n  if (opts.httpHost) {\n    return opts.httpHost;\n  }\n  if (opts.cluster) {\n    return `sockjs-${opts.cluster}.pusher.com`;\n  }\n  return Defaults.httpHost;\n}\n\nfunction getWebsocketHost(opts: Options): string {\n  if (opts.wsHost) {\n    return opts.wsHost;\n  }\n  return getWebsocketHostFromCluster(opts.cluster);\n}\n\nfunction getWebsocketHostFromCluster(cluster: string): string {\n  return `ws-${cluster}.pusher.com`;\n}\n\nfunction shouldUseTLS(opts: Options): boolean {\n  if (Runtime.getProtocol() === 'https:') {\n    return true;\n  } else if (opts.forceTLS === false) {\n    return false;\n  }\n  return true;\n}\n\n// if enableStats is set take the value\n// if disableStats is set take the inverse\n// otherwise default to false\nfunction getEnableStatsConfig(opts: Options): boolean {\n  if ('enableStats' in opts) {\n    return opts.enableStats;\n  }\n  if ('disableStats' in opts) {\n    return !opts.disableStats;\n  }\n  return false;\n}\n\nfunction buildUserAuthenticator(opts: Options): UserAuthenticationHandler {\n  const userAuthentication = {\n    ...Defaults.userAuthentication,\n    ...opts.userAuthentication,\n  };\n  if (\n    'customHandler' in userAuthentication &&\n    userAuthentication['customHandler'] != null\n  ) {\n    return userAuthentication['customHandler'];\n  }\n\n  return UserAuthenticator(userAuthentication);\n}\n\nfunction buildChannelAuth(opts: Options, pusher): ChannelAuthorizationOptions {\n  let channelAuthorization: ChannelAuthorizationOptions;\n  if ('channelAuthorization' in opts) {\n    channelAuthorization = {\n      ...Defaults.channelAuthorization,\n      ...opts.channelAuthorization,\n    };\n  } else {\n    channelAuthorization = {\n      transport: opts.authTransport || Defaults.authTransport,\n      endpoint: opts.authEndpoint || Defaults.authEndpoint,\n    };\n    if ('auth' in opts) {\n      if ('params' in opts.auth) channelAuthorization.params = opts.auth.params;\n      if ('headers' in opts.auth)\n        channelAuthorization.headers = opts.auth.headers;\n    }\n    if ('authorizer' in opts)\n      channelAuthorization.customHandler = ChannelAuthorizerProxy(\n        pusher,\n        channelAuthorization,\n        opts.authorizer,\n      );\n  }\n  return channelAuthorization;\n}\n\nfunction buildChannelAuthorizer(\n  opts: Options,\n  pusher,\n): ChannelAuthorizationHandler {\n  const channelAuthorization = buildChannelAuth(opts, pusher);\n  if (\n    'customHandler' in channelAuthorization &&\n    channelAuthorization['customHandler'] != null\n  ) {\n    return channelAuthorization['customHandler'];\n  }\n\n  return ChannelAuthorizer(channelAuthorization);\n}\n", "import Channel from '../channels/channel';\nimport {\n  ChannelAuthorizationCallback,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  InternalAuthOptions,\n} from './options';\n\nexport interface DeprecatedChannelAuthorizer {\n  authorize(socketId: string, callback: ChannelAuthorizationCallback): void;\n}\n\nexport interface ChannelAuthorizerGenerator {\n  (\n    channel: Channel,\n    options: DeprecatedAuthorizerOptions,\n  ): DeprecatedChannelAuthorizer;\n}\n\nexport interface DeprecatedAuthOptions {\n  params?: any;\n  headers?: any;\n}\n\nexport interface DeprecatedAuthorizerOptions {\n  authTransport: 'ajax' | 'jsonp';\n  authEndpoint: string;\n  auth?: DeprecatedAuthOptions;\n}\n\nexport const ChannelAuthorizerProxy = (\n  pusher,\n  authOptions: InternalAuthOptions,\n  channelAuthorizerGenerator: ChannelAuthorizerGenerator,\n): ChannelAuthorizationHandler => {\n  const deprecatedAuthorizerOptions: DeprecatedAuthorizerOptions = {\n    authTransport: authOptions.transport,\n    authEndpoint: authOptions.endpoint,\n    auth: {\n      params: authOptions.params,\n      headers: authOptions.headers,\n    },\n  };\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ) => {\n    const channel = pusher.channel(params.channelName);\n    // This line creates a new channel authorizer every time.\n    // In the past, this was only done once per channel and reused.\n    // We can do that again if we want to keep this behavior intact.\n    const channelAuthorizer: DeprecatedChannelAuthorizer =\n      channelAuthorizerGenerator(channel, deprecatedAuthorizerOptions);\n    channelAuthorizer.authorize(params.socketId, callback);\n  };\n};\n", "import Logger from './logger';\nimport Pusher from './pusher';\nimport EventsDispatcher from './events/dispatcher';\n\nexport default class WatchlistFacade extends EventsDispatcher {\n  private pusher: Pusher;\n\n  public constructor(pusher: Pusher) {\n    super(function (eventName, data) {\n      Logger.debug(`No callbacks on watchlist events for ${eventName}`);\n    });\n\n    this.pusher = pusher;\n    this.bindWatchlistInternalEvent();\n  }\n\n  handleEvent(pusherEvent) {\n    pusherEvent.data.events.forEach((watchlistEvent) => {\n      this.emit(watchlistEvent.name, watchlistEvent);\n    });\n  }\n\n  private bindWatchlistInternalEvent() {\n    this.pusher.connection.bind('message', (pusherEvent) => {\n      var eventName = pusherEvent.event;\n      if (eventName === 'pusher_internal:watchlist_events') {\n        this.handleEvent(pusherEvent);\n      }\n    });\n  }\n}\n", "function flatPromise() {\n  let resolve, reject;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n}\n\nexport default flatPromise;\n", "import Pusher from './pusher';\nimport Logger from './logger';\nimport {\n  UserAuthenticationData,\n  UserAuthenticationCallback,\n} from './auth/options';\nimport Channel from './channels/channel';\nimport WatchlistFacade from './watchlist';\nimport EventsDispatcher from './events/dispatcher';\nimport flatPromise from './utils/flat_promise';\n\nexport default class UserFacade extends EventsDispatcher {\n  pusher: Pusher;\n  signin_requested: boolean = false;\n  user_data: any = null;\n  serverToUserChannel: Channel = null;\n  signinDonePromise: Promise<any> = null;\n  watchlist: WatchlistFacade;\n  private _signinDoneResolve: Function = null;\n\n  public constructor(pusher: Pusher) {\n    super(function (eventName, data) {\n      Logger.debug('No callbacks on user for ' + eventName);\n    });\n    this.pusher = pusher;\n    this.pusher.connection.bind('state_change', ({ previous, current }) => {\n      if (previous !== 'connected' && current === 'connected') {\n        this._signin();\n      }\n      if (previous === 'connected' && current !== 'connected') {\n        this._cleanup();\n        this._newSigninPromiseIfNeeded();\n      }\n    });\n\n    this.watchlist = new WatchlistFacade(pusher);\n\n    this.pusher.connection.bind('message', (event) => {\n      var eventName = event.event;\n      if (eventName === 'pusher:signin_success') {\n        this._onSigninSuccess(event.data);\n      }\n      if (\n        this.serverToUserChannel &&\n        this.serverToUserChannel.name === event.channel\n      ) {\n        this.serverToUserChannel.handleEvent(event);\n      }\n    });\n  }\n\n  public signin() {\n    if (this.signin_requested) {\n      return;\n    }\n\n    this.signin_requested = true;\n    this._signin();\n  }\n\n  private _signin() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    this._newSigninPromiseIfNeeded();\n\n    if (this.pusher.connection.state !== 'connected') {\n      // Signin will be attempted when the connection is connected\n      return;\n    }\n\n    this.pusher.config.userAuthenticator(\n      {\n        socketId: this.pusher.connection.socket_id,\n      },\n      this._onAuthorize,\n    );\n  }\n\n  private _onAuthorize: UserAuthenticationCallback = (\n    err,\n    authData: UserAuthenticationData,\n  ) => {\n    if (err) {\n      Logger.warn(`Error during signin: ${err}`);\n      this._cleanup();\n      return;\n    }\n\n    this.pusher.send_event('pusher:signin', {\n      auth: authData.auth,\n      user_data: authData.user_data,\n    });\n\n    // Later when we get pusher:singin_success event, the user will be marked as signed in\n  };\n\n  private _onSigninSuccess(data: any) {\n    try {\n      this.user_data = JSON.parse(data.user_data);\n    } catch (e) {\n      Logger.error(`Failed parsing user data after signin: ${data.user_data}`);\n      this._cleanup();\n      return;\n    }\n\n    if (typeof this.user_data.id !== 'string' || this.user_data.id === '') {\n      Logger.error(\n        `user_data doesn't contain an id. user_data: ${this.user_data}`,\n      );\n      this._cleanup();\n      return;\n    }\n\n    // Signin succeeded\n    this._signinDoneResolve();\n    this._subscribeChannels();\n  }\n\n  private _subscribeChannels() {\n    const ensure_subscribed = (channel) => {\n      if (channel.subscriptionPending && channel.subscriptionCancelled) {\n        channel.reinstateSubscription();\n      } else if (\n        !channel.subscriptionPending &&\n        this.pusher.connection.state === 'connected'\n      ) {\n        channel.subscribe();\n      }\n    };\n\n    this.serverToUserChannel = new Channel(\n      `#server-to-user-${this.user_data.id}`,\n      this.pusher,\n    );\n    this.serverToUserChannel.bind_global((eventName, data) => {\n      if (\n        eventName.indexOf('pusher_internal:') === 0 ||\n        eventName.indexOf('pusher:') === 0\n      ) {\n        // ignore internal events\n        return;\n      }\n      this.emit(eventName, data);\n    });\n    ensure_subscribed(this.serverToUserChannel);\n  }\n\n  private _cleanup() {\n    this.user_data = null;\n    if (this.serverToUserChannel) {\n      this.serverToUserChannel.unbind_all();\n      this.serverToUserChannel.disconnect();\n      this.serverToUserChannel = null;\n    }\n\n    if (this.signin_requested) {\n      // If signin is in progress and cleanup is called,\n      // Mark the current signin process as done.\n      this._signinDoneResolve();\n    }\n  }\n\n  private _newSigninPromiseIfNeeded() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    // If there is a promise and it is not resolved, return without creating a new one.\n    if (this.signinDonePromise && !(this.signinDonePromise as any).done) {\n      return;\n    }\n\n    // This promise is never rejected.\n    // It gets resolved when the signin process is done whether it failed or succeeded\n    const { promise, resolve, reject: _ } = flatPromise();\n    (promise as any).done = false;\n    const setDone = () => {\n      (promise as any).done = true;\n    };\n    promise.then(setDone).catch(setDone);\n    this.signinDonePromise = promise;\n    this._signinDoneResolve = resolve;\n  }\n}\n", "import AbstractRuntime from '../runtimes/interface';\nimport Runtime from 'runtime';\nimport Util from './util';\nimport * as Collections from './utils/collections';\nimport Channels from './channels/channels';\nimport Channel from './channels/channel';\nimport { default as EventsDispatcher } from './events/dispatcher';\nimport Timeline from './timeline/timeline';\nimport TimelineSender from './timeline/timeline_sender';\nimport TimelineLevel from './timeline/level';\nimport { defineTransport } from './strategies/strategy_builder';\nimport ConnectionManager from './connection/connection_manager';\nimport ConnectionManagerOptions from './connection/connection_manager_options';\nimport { PeriodicTimer } from './utils/timers';\nimport Defaults from './defaults';\nimport * as DefaultConfig from './config';\nimport Logger from './logger';\nimport Factory from './utils/factory';\nimport UrlStore from 'core/utils/url_store';\nimport { Options, validateOptions } from './options';\nimport { Config, getConfig } from './config';\nimport StrategyOptions from './strategies/strategy_options';\nimport UserFacade from './user';\n\nexport default class Pusher {\n  /*  STATIC PROPERTIES */\n  static instances: Pusher[] = [];\n  static isReady: boolean = false;\n  static logToConsole: boolean = false;\n\n  // for jsonp\n  static Runtime: AbstractRuntime = Runtime;\n  static ScriptReceivers: any = (<any>Runtime).ScriptReceivers;\n  static DependenciesReceivers: any = (<any>Runtime).DependenciesReceivers;\n  static auth_callbacks: any = (<any>Runtime).auth_callbacks;\n\n  static ready() {\n    Pusher.isReady = true;\n    for (var i = 0, l = Pusher.instances.length; i < l; i++) {\n      Pusher.instances[i].connect();\n    }\n  }\n\n  static log: (message: any) => void;\n\n  private static getClientFeatures(): string[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Runtime.Transports.ws }, function (t) {\n        return t.isSupported({});\n      }),\n    );\n  }\n\n  /* INSTANCE PROPERTIES */\n  key: string;\n  config: Config;\n  channels: Channels;\n  global_emitter: EventsDispatcher;\n  sessionID: number;\n  timeline: Timeline;\n  timelineSender: TimelineSender;\n  connection: ConnectionManager;\n  timelineSenderTimer: PeriodicTimer;\n  user: UserFacade;\n  constructor(app_key: string, options: Options) {\n    checkAppKey(app_key);\n    validateOptions(options);\n    this.key = app_key;\n    this.config = getConfig(options, this);\n\n    this.channels = Factory.createChannels();\n    this.global_emitter = new EventsDispatcher();\n    this.sessionID = Runtime.randomInt(1000000000);\n\n    this.timeline = new Timeline(this.key, this.sessionID, {\n      cluster: this.config.cluster,\n      features: Pusher.getClientFeatures(),\n      params: this.config.timelineParams || {},\n      limit: 50,\n      level: TimelineLevel.INFO,\n      version: Defaults.VERSION,\n    });\n    if (this.config.enableStats) {\n      this.timelineSender = Factory.createTimelineSender(this.timeline, {\n        host: this.config.statsHost,\n        path: '/timeline/v2/' + Runtime.TimelineTransport.name,\n      });\n    }\n\n    var getStrategy = (options: StrategyOptions) => {\n      return Runtime.getDefaultStrategy(this.config, options, defineTransport);\n    };\n\n    this.connection = Factory.createConnectionManager(this.key, {\n      getStrategy: getStrategy,\n      timeline: this.timeline,\n      activityTimeout: this.config.activityTimeout,\n      pongTimeout: this.config.pongTimeout,\n      unavailableTimeout: this.config.unavailableTimeout,\n      useTLS: Boolean(this.config.useTLS),\n    });\n\n    this.connection.bind('connected', () => {\n      this.subscribeAll();\n      if (this.timelineSender) {\n        this.timelineSender.send(this.connection.isUsingTLS());\n      }\n    });\n\n    this.connection.bind('message', (event) => {\n      var eventName = event.event;\n      var internal = eventName.indexOf('pusher_internal:') === 0;\n      if (event.channel) {\n        var channel = this.channel(event.channel);\n        if (channel) {\n          channel.handleEvent(event);\n        }\n      }\n      // Emit globally [deprecated]\n      if (!internal) {\n        this.global_emitter.emit(event.event, event.data);\n      }\n    });\n    this.connection.bind('connecting', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('disconnected', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('error', (err) => {\n      Logger.warn(err);\n    });\n\n    Pusher.instances.push(this);\n    this.timeline.info({ instances: Pusher.instances.length });\n\n    this.user = new UserFacade(this);\n\n    if (Pusher.isReady) {\n      this.connect();\n    }\n  }\n\n  channel(name: string): Channel {\n    return this.channels.find(name);\n  }\n\n  allChannels(): Channel[] {\n    return this.channels.all();\n  }\n\n  connect() {\n    this.connection.connect();\n\n    if (this.timelineSender) {\n      if (!this.timelineSenderTimer) {\n        var usingTLS = this.connection.isUsingTLS();\n        var timelineSender = this.timelineSender;\n        this.timelineSenderTimer = new PeriodicTimer(60000, function () {\n          timelineSender.send(usingTLS);\n        });\n      }\n    }\n  }\n\n  disconnect() {\n    this.connection.disconnect();\n\n    if (this.timelineSenderTimer) {\n      this.timelineSenderTimer.ensureAborted();\n      this.timelineSenderTimer = null;\n    }\n  }\n\n  bind(event_name: string, callback: Function, context?: any): Pusher {\n    this.global_emitter.bind(event_name, callback, context);\n    return this;\n  }\n\n  unbind(event_name?: string, callback?: Function, context?: any): Pusher {\n    this.global_emitter.unbind(event_name, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function): Pusher {\n    this.global_emitter.bind_global(callback);\n    return this;\n  }\n\n  unbind_global(callback?: Function): Pusher {\n    this.global_emitter.unbind_global(callback);\n    return this;\n  }\n\n  unbind_all(callback?: Function): Pusher {\n    this.global_emitter.unbind_all();\n    return this;\n  }\n\n  subscribeAll() {\n    var channelName;\n    for (channelName in this.channels.channels) {\n      if (this.channels.channels.hasOwnProperty(channelName)) {\n        this.subscribe(channelName);\n      }\n    }\n  }\n\n  subscribe(channel_name: string) {\n    var channel = this.channels.add(channel_name, this);\n    if (channel.subscriptionPending && channel.subscriptionCancelled) {\n      channel.reinstateSubscription();\n    } else if (\n      !channel.subscriptionPending &&\n      this.connection.state === 'connected'\n    ) {\n      channel.subscribe();\n    }\n    return channel;\n  }\n\n  unsubscribe(channel_name: string) {\n    var channel = this.channels.find(channel_name);\n    if (channel && channel.subscriptionPending) {\n      channel.cancelSubscription();\n    } else {\n      channel = this.channels.remove(channel_name);\n      if (channel && channel.subscribed) {\n        channel.unsubscribe();\n      }\n    }\n  }\n\n  send_event(event_name: string, data: any, channel?: string) {\n    return this.connection.send_event(event_name, data, channel);\n  }\n\n  shouldUseTLS(): boolean {\n    return this.config.useTLS;\n  }\n\n  signin() {\n    this.user.signin();\n  }\n}\n\nfunction checkAppKey(key) {\n  if (key === null || key === undefined) {\n    throw 'You must pass your app key when you instantiate Pusher.';\n  }\n}\n\nRuntime.setup(Pusher);\n", "import Pusher from './pusher';\nimport { Options, validateOptions } from './options';\nimport * as nacl from 'tweetnacl';\n\nexport default class PusherWithEncryption extends Pusher {\n  constructor(app_key: string, options: Options) {\n    Pusher.logToConsole = PusherWithEncryption.logToConsole;\n    Pusher.log = PusherWithEncryption.log;\n\n    validateOptions(options);\n    options.nacl = nacl;\n    super(app_key, options);\n  }\n}\n"], "sourceRoot": ""}