/*!
 * Pusher JavaScript Library v8.4.0
 * https://pusher.com/
 *
 * Copyright 2020, <PERSON>usher
 * Released under the MIT licence.
 */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Pusher=e():t.Pusher=e()}(window,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=3)}([function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var s=function(){function t(t){void 0===t&&(t="="),this._paddingCharacter=t}return t.prototype.encodedLength=function(t){return this._paddingCharacter?(t+2)/3*4|0:(8*t+5)/6|0},t.prototype.encode=function(t){for(var e="",n=0;n<t.length-2;n+=3){var r=t[n]<<16|t[n+1]<<8|t[n+2];e+=this._encodeByte(r>>>18&63),e+=this._encodeByte(r>>>12&63),e+=this._encodeByte(r>>>6&63),e+=this._encodeByte(r>>>0&63)}var i=t.length-n;if(i>0){r=t[n]<<16|(2===i?t[n+1]<<8:0);e+=this._encodeByte(r>>>18&63),e+=this._encodeByte(r>>>12&63),e+=2===i?this._encodeByte(r>>>6&63):this._paddingCharacter||"",e+=this._paddingCharacter||""}return e},t.prototype.maxDecodedLength=function(t){return this._paddingCharacter?t/4*3|0:(6*t+7)/8|0},t.prototype.decodedLength=function(t){return this.maxDecodedLength(t.length-this._getPaddingLength(t))},t.prototype.decode=function(t){if(0===t.length)return new Uint8Array(0);for(var e=this._getPaddingLength(t),n=t.length-e,r=new Uint8Array(this.maxDecodedLength(n)),i=0,s=0,o=0,a=0,c=0,h=0,u=0;s<n-4;s+=4)a=this._decodeChar(t.charCodeAt(s+0)),c=this._decodeChar(t.charCodeAt(s+1)),h=this._decodeChar(t.charCodeAt(s+2)),u=this._decodeChar(t.charCodeAt(s+3)),r[i++]=a<<2|c>>>4,r[i++]=c<<4|h>>>2,r[i++]=h<<6|u,o|=256&a,o|=256&c,o|=256&h,o|=256&u;if(s<n-1&&(a=this._decodeChar(t.charCodeAt(s)),c=this._decodeChar(t.charCodeAt(s+1)),r[i++]=a<<2|c>>>4,o|=256&a,o|=256&c),s<n-2&&(h=this._decodeChar(t.charCodeAt(s+2)),r[i++]=c<<4|h>>>2,o|=256&h),s<n-3&&(u=this._decodeChar(t.charCodeAt(s+3)),r[i++]=h<<6|u,o|=256&u),0!==o)throw new Error("Base64Coder: incorrect characters for decoding");return r},t.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-15,e+=62-t>>>8&3,String.fromCharCode(e)},t.prototype._decodeChar=function(t){var e=256;return e+=(42-t&t-44)>>>8&-256+t-43+62,e+=(46-t&t-48)>>>8&-256+t-47+63,e+=(47-t&t-58)>>>8&-256+t-48+52,e+=(64-t&t-91)>>>8&-256+t-65+0,e+=(96-t&t-123)>>>8&-256+t-97+26},t.prototype._getPaddingLength=function(t){var e=0;if(this._paddingCharacter){for(var n=t.length-1;n>=0&&t[n]===this._paddingCharacter;n--)e++;if(t.length<4||e>2)throw new Error("Base64Coder: incorrect padding")}return e},t}();e.Coder=s;var o=new s;e.encode=function(t){return o.encode(t)},e.decode=function(t){return o.decode(t)};var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-13,e+=62-t>>>8&49,String.fromCharCode(e)},e.prototype._decodeChar=function(t){var e=256;return e+=(44-t&t-46)>>>8&-256+t-45+62,e+=(94-t&t-96)>>>8&-256+t-95+63,e+=(47-t&t-58)>>>8&-256+t-48+52,e+=(64-t&t-91)>>>8&-256+t-65+0,e+=(96-t&t-123)>>>8&-256+t-97+26},e}(s);e.URLSafeCoder=a;var c=new a;e.encodeURLSafe=function(t){return c.encode(t)},e.decodeURLSafe=function(t){return c.decode(t)},e.encodedLength=function(t){return o.encodedLength(t)},e.maxDecodedLength=function(t){return o.maxDecodedLength(t)},e.decodedLength=function(t){return o.decodedLength(t)}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r="utf8: invalid source encoding";function i(t){for(var e=0,n=0;n<t.length;n++){var r=t.charCodeAt(n);if(r<128)e+=1;else if(r<2048)e+=2;else if(r<55296)e+=3;else{if(!(r<=57343))throw new Error("utf8: invalid string");if(n>=t.length-1)throw new Error("utf8: invalid string");n++,e+=4}}return e}e.encode=function(t){for(var e=new Uint8Array(i(t)),n=0,r=0;r<t.length;r++){var s=t.charCodeAt(r);s<128?e[n++]=s:s<2048?(e[n++]=192|s>>6,e[n++]=128|63&s):s<55296?(e[n++]=224|s>>12,e[n++]=128|s>>6&63,e[n++]=128|63&s):(r++,s=(1023&s)<<10,s|=1023&t.charCodeAt(r),s+=65536,e[n++]=240|s>>18,e[n++]=128|s>>12&63,e[n++]=128|s>>6&63,e[n++]=128|63&s)}return e},e.encodedLength=i,e.decode=function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(128&i){var s=void 0;if(i<224){if(n>=t.length)throw new Error(r);if(128!=(192&(o=t[++n])))throw new Error(r);i=(31&i)<<6|63&o,s=128}else if(i<240){if(n>=t.length-1)throw new Error(r);var o=t[++n],a=t[++n];if(128!=(192&o)||128!=(192&a))throw new Error(r);i=(15&i)<<12|(63&o)<<6|63&a,s=2048}else{if(!(i<248))throw new Error(r);if(n>=t.length-2)throw new Error(r);o=t[++n],a=t[++n];var c=t[++n];if(128!=(192&o)||128!=(192&a)||128!=(192&c))throw new Error(r);i=(15&i)<<18|(63&o)<<12|(63&a)<<6|63&c,s=65536}if(i<s||i>=55296&&i<=57343)throw new Error(r);if(i>=65536){if(i>1114111)throw new Error(r);i-=65536,e.push(String.fromCharCode(55296|i>>10)),i=56320|1023&i}}e.push(String.fromCharCode(i))}return e.join("")}},function(t,e,n){!function(t){"use strict";var e=function(t){var e,n=new Float64Array(16);if(t)for(e=0;e<t.length;e++)n[e]=t[e];return n},r=function(){throw new Error("no PRNG")},i=new Uint8Array(16),s=new Uint8Array(32);s[0]=9;var o=e(),a=e([1]),c=e([56129,1]),h=e([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),u=e([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),l=e([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),d=e([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),p=e([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function f(t,e,n,r){t[e]=n>>24&255,t[e+1]=n>>16&255,t[e+2]=n>>8&255,t[e+3]=255&n,t[e+4]=r>>24&255,t[e+5]=r>>16&255,t[e+6]=r>>8&255,t[e+7]=255&r}function g(t,e,n,r,i){var s,o=0;for(s=0;s<i;s++)o|=t[e+s]^n[r+s];return(1&o-1>>>8)-1}function v(t,e,n,r){return g(t,e,n,r,16)}function b(t,e,n,r){return g(t,e,n,r,32)}function y(t,e,n,r){!function(t,e,n,r){for(var i,s=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,c=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,h=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,u=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,l=255&e[0]|(255&e[1])<<8|(255&e[2])<<16|(255&e[3])<<24,d=255&e[4]|(255&e[5])<<8|(255&e[6])<<16|(255&e[7])<<24,p=255&e[8]|(255&e[9])<<8|(255&e[10])<<16|(255&e[11])<<24,f=255&e[12]|(255&e[13])<<8|(255&e[14])<<16|(255&e[15])<<24,g=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,v=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,b=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,y=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,m=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,w=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,_=s,S=o,k=a,C=c,T=h,E=u,P=l,x=d,A=p,O=f,L=g,R=v,U=b,M=y,I=m,D=w,N=0;N<20;N+=2)_^=(i=(U^=(i=(A^=(i=(T^=(i=_+U|0)<<7|i>>>25)+_|0)<<9|i>>>23)+T|0)<<13|i>>>19)+A|0)<<18|i>>>14,E^=(i=(S^=(i=(M^=(i=(O^=(i=E+S|0)<<7|i>>>25)+E|0)<<9|i>>>23)+O|0)<<13|i>>>19)+M|0)<<18|i>>>14,L^=(i=(P^=(i=(k^=(i=(I^=(i=L+P|0)<<7|i>>>25)+L|0)<<9|i>>>23)+I|0)<<13|i>>>19)+k|0)<<18|i>>>14,D^=(i=(R^=(i=(x^=(i=(C^=(i=D+R|0)<<7|i>>>25)+D|0)<<9|i>>>23)+C|0)<<13|i>>>19)+x|0)<<18|i>>>14,_^=(i=(C^=(i=(k^=(i=(S^=(i=_+C|0)<<7|i>>>25)+_|0)<<9|i>>>23)+S|0)<<13|i>>>19)+k|0)<<18|i>>>14,E^=(i=(T^=(i=(x^=(i=(P^=(i=E+T|0)<<7|i>>>25)+E|0)<<9|i>>>23)+P|0)<<13|i>>>19)+x|0)<<18|i>>>14,L^=(i=(O^=(i=(A^=(i=(R^=(i=L+O|0)<<7|i>>>25)+L|0)<<9|i>>>23)+R|0)<<13|i>>>19)+A|0)<<18|i>>>14,D^=(i=(I^=(i=(M^=(i=(U^=(i=D+I|0)<<7|i>>>25)+D|0)<<9|i>>>23)+U|0)<<13|i>>>19)+M|0)<<18|i>>>14;_=_+s|0,S=S+o|0,k=k+a|0,C=C+c|0,T=T+h|0,E=E+u|0,P=P+l|0,x=x+d|0,A=A+p|0,O=O+f|0,L=L+g|0,R=R+v|0,U=U+b|0,M=M+y|0,I=I+m|0,D=D+w|0,t[0]=_>>>0&255,t[1]=_>>>8&255,t[2]=_>>>16&255,t[3]=_>>>24&255,t[4]=S>>>0&255,t[5]=S>>>8&255,t[6]=S>>>16&255,t[7]=S>>>24&255,t[8]=k>>>0&255,t[9]=k>>>8&255,t[10]=k>>>16&255,t[11]=k>>>24&255,t[12]=C>>>0&255,t[13]=C>>>8&255,t[14]=C>>>16&255,t[15]=C>>>24&255,t[16]=T>>>0&255,t[17]=T>>>8&255,t[18]=T>>>16&255,t[19]=T>>>24&255,t[20]=E>>>0&255,t[21]=E>>>8&255,t[22]=E>>>16&255,t[23]=E>>>24&255,t[24]=P>>>0&255,t[25]=P>>>8&255,t[26]=P>>>16&255,t[27]=P>>>24&255,t[28]=x>>>0&255,t[29]=x>>>8&255,t[30]=x>>>16&255,t[31]=x>>>24&255,t[32]=A>>>0&255,t[33]=A>>>8&255,t[34]=A>>>16&255,t[35]=A>>>24&255,t[36]=O>>>0&255,t[37]=O>>>8&255,t[38]=O>>>16&255,t[39]=O>>>24&255,t[40]=L>>>0&255,t[41]=L>>>8&255,t[42]=L>>>16&255,t[43]=L>>>24&255,t[44]=R>>>0&255,t[45]=R>>>8&255,t[46]=R>>>16&255,t[47]=R>>>24&255,t[48]=U>>>0&255,t[49]=U>>>8&255,t[50]=U>>>16&255,t[51]=U>>>24&255,t[52]=M>>>0&255,t[53]=M>>>8&255,t[54]=M>>>16&255,t[55]=M>>>24&255,t[56]=I>>>0&255,t[57]=I>>>8&255,t[58]=I>>>16&255,t[59]=I>>>24&255,t[60]=D>>>0&255,t[61]=D>>>8&255,t[62]=D>>>16&255,t[63]=D>>>24&255}(t,e,n,r)}function m(t,e,n,r){!function(t,e,n,r){for(var i,s=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,c=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,h=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,u=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,l=255&e[0]|(255&e[1])<<8|(255&e[2])<<16|(255&e[3])<<24,d=255&e[4]|(255&e[5])<<8|(255&e[6])<<16|(255&e[7])<<24,p=255&e[8]|(255&e[9])<<8|(255&e[10])<<16|(255&e[11])<<24,f=255&e[12]|(255&e[13])<<8|(255&e[14])<<16|(255&e[15])<<24,g=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,v=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,b=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,y=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,m=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,w=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,_=0;_<20;_+=2)s^=(i=(b^=(i=(p^=(i=(h^=(i=s+b|0)<<7|i>>>25)+s|0)<<9|i>>>23)+h|0)<<13|i>>>19)+p|0)<<18|i>>>14,u^=(i=(o^=(i=(y^=(i=(f^=(i=u+o|0)<<7|i>>>25)+u|0)<<9|i>>>23)+f|0)<<13|i>>>19)+y|0)<<18|i>>>14,g^=(i=(l^=(i=(a^=(i=(m^=(i=g+l|0)<<7|i>>>25)+g|0)<<9|i>>>23)+m|0)<<13|i>>>19)+a|0)<<18|i>>>14,w^=(i=(v^=(i=(d^=(i=(c^=(i=w+v|0)<<7|i>>>25)+w|0)<<9|i>>>23)+c|0)<<13|i>>>19)+d|0)<<18|i>>>14,s^=(i=(c^=(i=(a^=(i=(o^=(i=s+c|0)<<7|i>>>25)+s|0)<<9|i>>>23)+o|0)<<13|i>>>19)+a|0)<<18|i>>>14,u^=(i=(h^=(i=(d^=(i=(l^=(i=u+h|0)<<7|i>>>25)+u|0)<<9|i>>>23)+l|0)<<13|i>>>19)+d|0)<<18|i>>>14,g^=(i=(f^=(i=(p^=(i=(v^=(i=g+f|0)<<7|i>>>25)+g|0)<<9|i>>>23)+v|0)<<13|i>>>19)+p|0)<<18|i>>>14,w^=(i=(m^=(i=(y^=(i=(b^=(i=w+m|0)<<7|i>>>25)+w|0)<<9|i>>>23)+b|0)<<13|i>>>19)+y|0)<<18|i>>>14;t[0]=s>>>0&255,t[1]=s>>>8&255,t[2]=s>>>16&255,t[3]=s>>>24&255,t[4]=u>>>0&255,t[5]=u>>>8&255,t[6]=u>>>16&255,t[7]=u>>>24&255,t[8]=g>>>0&255,t[9]=g>>>8&255,t[10]=g>>>16&255,t[11]=g>>>24&255,t[12]=w>>>0&255,t[13]=w>>>8&255,t[14]=w>>>16&255,t[15]=w>>>24&255,t[16]=l>>>0&255,t[17]=l>>>8&255,t[18]=l>>>16&255,t[19]=l>>>24&255,t[20]=d>>>0&255,t[21]=d>>>8&255,t[22]=d>>>16&255,t[23]=d>>>24&255,t[24]=p>>>0&255,t[25]=p>>>8&255,t[26]=p>>>16&255,t[27]=p>>>24&255,t[28]=f>>>0&255,t[29]=f>>>8&255,t[30]=f>>>16&255,t[31]=f>>>24&255}(t,e,n,r)}var w=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function _(t,e,n,r,i,s,o){var a,c,h=new Uint8Array(16),u=new Uint8Array(64);for(c=0;c<16;c++)h[c]=0;for(c=0;c<8;c++)h[c]=s[c];for(;i>=64;){for(y(u,h,o,w),c=0;c<64;c++)t[e+c]=n[r+c]^u[c];for(a=1,c=8;c<16;c++)a=a+(255&h[c])|0,h[c]=255&a,a>>>=8;i-=64,e+=64,r+=64}if(i>0)for(y(u,h,o,w),c=0;c<i;c++)t[e+c]=n[r+c]^u[c];return 0}function S(t,e,n,r,i){var s,o,a=new Uint8Array(16),c=new Uint8Array(64);for(o=0;o<16;o++)a[o]=0;for(o=0;o<8;o++)a[o]=r[o];for(;n>=64;){for(y(c,a,i,w),o=0;o<64;o++)t[e+o]=c[o];for(s=1,o=8;o<16;o++)s=s+(255&a[o])|0,a[o]=255&s,s>>>=8;n-=64,e+=64}if(n>0)for(y(c,a,i,w),o=0;o<n;o++)t[e+o]=c[o];return 0}function k(t,e,n,r,i){var s=new Uint8Array(32);m(s,r,i,w);for(var o=new Uint8Array(8),a=0;a<8;a++)o[a]=r[a+16];return S(t,e,n,o,s)}function C(t,e,n,r,i,s,o){var a=new Uint8Array(32);m(a,s,o,w);for(var c=new Uint8Array(8),h=0;h<8;h++)c[h]=s[h+16];return _(t,e,n,r,i,c,a)}var T=function(t){var e,n,r,i,s,o,a,c;this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0,e=255&t[0]|(255&t[1])<<8,this.r[0]=8191&e,n=255&t[2]|(255&t[3])<<8,this.r[1]=8191&(e>>>13|n<<3),r=255&t[4]|(255&t[5])<<8,this.r[2]=7939&(n>>>10|r<<6),i=255&t[6]|(255&t[7])<<8,this.r[3]=8191&(r>>>7|i<<9),s=255&t[8]|(255&t[9])<<8,this.r[4]=255&(i>>>4|s<<12),this.r[5]=s>>>1&8190,o=255&t[10]|(255&t[11])<<8,this.r[6]=8191&(s>>>14|o<<2),a=255&t[12]|(255&t[13])<<8,this.r[7]=8065&(o>>>11|a<<5),c=255&t[14]|(255&t[15])<<8,this.r[8]=8191&(a>>>8|c<<8),this.r[9]=c>>>5&127,this.pad[0]=255&t[16]|(255&t[17])<<8,this.pad[1]=255&t[18]|(255&t[19])<<8,this.pad[2]=255&t[20]|(255&t[21])<<8,this.pad[3]=255&t[22]|(255&t[23])<<8,this.pad[4]=255&t[24]|(255&t[25])<<8,this.pad[5]=255&t[26]|(255&t[27])<<8,this.pad[6]=255&t[28]|(255&t[29])<<8,this.pad[7]=255&t[30]|(255&t[31])<<8};function E(t,e,n,r,i,s){var o=new T(s);return o.update(n,r,i),o.finish(t,e),0}function P(t,e,n,r,i,s){var o=new Uint8Array(16);return E(o,0,n,r,i,s),v(t,e,o,0)}function x(t,e,n,r,i){var s;if(n<32)return-1;for(C(t,0,e,0,n,r,i),E(t,16,t,32,n-32,t),s=0;s<16;s++)t[s]=0;return 0}function A(t,e,n,r,i){var s,o=new Uint8Array(32);if(n<32)return-1;if(k(o,0,32,r,i),0!==P(e,16,e,32,n-32,o))return-1;for(C(t,0,e,0,n,r,i),s=0;s<32;s++)t[s]=0;return 0}function O(t,e){var n;for(n=0;n<16;n++)t[n]=0|e[n]}function L(t){var e,n,r=1;for(e=0;e<16;e++)n=t[e]+r+65535,r=Math.floor(n/65536),t[e]=n-65536*r;t[0]+=r-1+37*(r-1)}function R(t,e,n){for(var r,i=~(n-1),s=0;s<16;s++)r=i&(t[s]^e[s]),t[s]^=r,e[s]^=r}function U(t,n){var r,i,s,o=e(),a=e();for(r=0;r<16;r++)a[r]=n[r];for(L(a),L(a),L(a),i=0;i<2;i++){for(o[0]=a[0]-65517,r=1;r<15;r++)o[r]=a[r]-65535-(o[r-1]>>16&1),o[r-1]&=65535;o[15]=a[15]-32767-(o[14]>>16&1),s=o[15]>>16&1,o[14]&=65535,R(a,o,1-s)}for(r=0;r<16;r++)t[2*r]=255&a[r],t[2*r+1]=a[r]>>8}function M(t,e){var n=new Uint8Array(32),r=new Uint8Array(32);return U(n,t),U(r,e),b(n,0,r,0)}function I(t){var e=new Uint8Array(32);return U(e,t),1&e[0]}function D(t,e){var n;for(n=0;n<16;n++)t[n]=e[2*n]+(e[2*n+1]<<8);t[15]&=32767}function N(t,e,n){for(var r=0;r<16;r++)t[r]=e[r]+n[r]}function j(t,e,n){for(var r=0;r<16;r++)t[r]=e[r]-n[r]}function B(t,e,n){var r,i,s=0,o=0,a=0,c=0,h=0,u=0,l=0,d=0,p=0,f=0,g=0,v=0,b=0,y=0,m=0,w=0,_=0,S=0,k=0,C=0,T=0,E=0,P=0,x=0,A=0,O=0,L=0,R=0,U=0,M=0,I=0,D=n[0],N=n[1],j=n[2],B=n[3],z=n[4],H=n[5],q=n[6],F=n[7],X=n[8],J=n[9],Y=n[10],K=n[11],$=n[12],W=n[13],G=n[14],V=n[15];s+=(r=e[0])*D,o+=r*N,a+=r*j,c+=r*B,h+=r*z,u+=r*H,l+=r*q,d+=r*F,p+=r*X,f+=r*J,g+=r*Y,v+=r*K,b+=r*$,y+=r*W,m+=r*G,w+=r*V,o+=(r=e[1])*D,a+=r*N,c+=r*j,h+=r*B,u+=r*z,l+=r*H,d+=r*q,p+=r*F,f+=r*X,g+=r*J,v+=r*Y,b+=r*K,y+=r*$,m+=r*W,w+=r*G,_+=r*V,a+=(r=e[2])*D,c+=r*N,h+=r*j,u+=r*B,l+=r*z,d+=r*H,p+=r*q,f+=r*F,g+=r*X,v+=r*J,b+=r*Y,y+=r*K,m+=r*$,w+=r*W,_+=r*G,S+=r*V,c+=(r=e[3])*D,h+=r*N,u+=r*j,l+=r*B,d+=r*z,p+=r*H,f+=r*q,g+=r*F,v+=r*X,b+=r*J,y+=r*Y,m+=r*K,w+=r*$,_+=r*W,S+=r*G,k+=r*V,h+=(r=e[4])*D,u+=r*N,l+=r*j,d+=r*B,p+=r*z,f+=r*H,g+=r*q,v+=r*F,b+=r*X,y+=r*J,m+=r*Y,w+=r*K,_+=r*$,S+=r*W,k+=r*G,C+=r*V,u+=(r=e[5])*D,l+=r*N,d+=r*j,p+=r*B,f+=r*z,g+=r*H,v+=r*q,b+=r*F,y+=r*X,m+=r*J,w+=r*Y,_+=r*K,S+=r*$,k+=r*W,C+=r*G,T+=r*V,l+=(r=e[6])*D,d+=r*N,p+=r*j,f+=r*B,g+=r*z,v+=r*H,b+=r*q,y+=r*F,m+=r*X,w+=r*J,_+=r*Y,S+=r*K,k+=r*$,C+=r*W,T+=r*G,E+=r*V,d+=(r=e[7])*D,p+=r*N,f+=r*j,g+=r*B,v+=r*z,b+=r*H,y+=r*q,m+=r*F,w+=r*X,_+=r*J,S+=r*Y,k+=r*K,C+=r*$,T+=r*W,E+=r*G,P+=r*V,p+=(r=e[8])*D,f+=r*N,g+=r*j,v+=r*B,b+=r*z,y+=r*H,m+=r*q,w+=r*F,_+=r*X,S+=r*J,k+=r*Y,C+=r*K,T+=r*$,E+=r*W,P+=r*G,x+=r*V,f+=(r=e[9])*D,g+=r*N,v+=r*j,b+=r*B,y+=r*z,m+=r*H,w+=r*q,_+=r*F,S+=r*X,k+=r*J,C+=r*Y,T+=r*K,E+=r*$,P+=r*W,x+=r*G,A+=r*V,g+=(r=e[10])*D,v+=r*N,b+=r*j,y+=r*B,m+=r*z,w+=r*H,_+=r*q,S+=r*F,k+=r*X,C+=r*J,T+=r*Y,E+=r*K,P+=r*$,x+=r*W,A+=r*G,O+=r*V,v+=(r=e[11])*D,b+=r*N,y+=r*j,m+=r*B,w+=r*z,_+=r*H,S+=r*q,k+=r*F,C+=r*X,T+=r*J,E+=r*Y,P+=r*K,x+=r*$,A+=r*W,O+=r*G,L+=r*V,b+=(r=e[12])*D,y+=r*N,m+=r*j,w+=r*B,_+=r*z,S+=r*H,k+=r*q,C+=r*F,T+=r*X,E+=r*J,P+=r*Y,x+=r*K,A+=r*$,O+=r*W,L+=r*G,R+=r*V,y+=(r=e[13])*D,m+=r*N,w+=r*j,_+=r*B,S+=r*z,k+=r*H,C+=r*q,T+=r*F,E+=r*X,P+=r*J,x+=r*Y,A+=r*K,O+=r*$,L+=r*W,R+=r*G,U+=r*V,m+=(r=e[14])*D,w+=r*N,_+=r*j,S+=r*B,k+=r*z,C+=r*H,T+=r*q,E+=r*F,P+=r*X,x+=r*J,A+=r*Y,O+=r*K,L+=r*$,R+=r*W,U+=r*G,M+=r*V,w+=(r=e[15])*D,o+=38*(S+=r*j),a+=38*(k+=r*B),c+=38*(C+=r*z),h+=38*(T+=r*H),u+=38*(E+=r*q),l+=38*(P+=r*F),d+=38*(x+=r*X),p+=38*(A+=r*J),f+=38*(O+=r*Y),g+=38*(L+=r*K),v+=38*(R+=r*$),b+=38*(U+=r*W),y+=38*(M+=r*G),m+=38*(I+=r*V),s=(r=(s+=38*(_+=r*N))+(i=1)+65535)-65536*(i=Math.floor(r/65536)),o=(r=o+i+65535)-65536*(i=Math.floor(r/65536)),a=(r=a+i+65535)-65536*(i=Math.floor(r/65536)),c=(r=c+i+65535)-65536*(i=Math.floor(r/65536)),h=(r=h+i+65535)-65536*(i=Math.floor(r/65536)),u=(r=u+i+65535)-65536*(i=Math.floor(r/65536)),l=(r=l+i+65535)-65536*(i=Math.floor(r/65536)),d=(r=d+i+65535)-65536*(i=Math.floor(r/65536)),p=(r=p+i+65535)-65536*(i=Math.floor(r/65536)),f=(r=f+i+65535)-65536*(i=Math.floor(r/65536)),g=(r=g+i+65535)-65536*(i=Math.floor(r/65536)),v=(r=v+i+65535)-65536*(i=Math.floor(r/65536)),b=(r=b+i+65535)-65536*(i=Math.floor(r/65536)),y=(r=y+i+65535)-65536*(i=Math.floor(r/65536)),m=(r=m+i+65535)-65536*(i=Math.floor(r/65536)),w=(r=w+i+65535)-65536*(i=Math.floor(r/65536)),s=(r=(s+=i-1+37*(i-1))+(i=1)+65535)-65536*(i=Math.floor(r/65536)),o=(r=o+i+65535)-65536*(i=Math.floor(r/65536)),a=(r=a+i+65535)-65536*(i=Math.floor(r/65536)),c=(r=c+i+65535)-65536*(i=Math.floor(r/65536)),h=(r=h+i+65535)-65536*(i=Math.floor(r/65536)),u=(r=u+i+65535)-65536*(i=Math.floor(r/65536)),l=(r=l+i+65535)-65536*(i=Math.floor(r/65536)),d=(r=d+i+65535)-65536*(i=Math.floor(r/65536)),p=(r=p+i+65535)-65536*(i=Math.floor(r/65536)),f=(r=f+i+65535)-65536*(i=Math.floor(r/65536)),g=(r=g+i+65535)-65536*(i=Math.floor(r/65536)),v=(r=v+i+65535)-65536*(i=Math.floor(r/65536)),b=(r=b+i+65535)-65536*(i=Math.floor(r/65536)),y=(r=y+i+65535)-65536*(i=Math.floor(r/65536)),m=(r=m+i+65535)-65536*(i=Math.floor(r/65536)),w=(r=w+i+65535)-65536*(i=Math.floor(r/65536)),s+=i-1+37*(i-1),t[0]=s,t[1]=o,t[2]=a,t[3]=c,t[4]=h,t[5]=u,t[6]=l,t[7]=d,t[8]=p,t[9]=f,t[10]=g,t[11]=v,t[12]=b,t[13]=y,t[14]=m,t[15]=w}function z(t,e){B(t,e,e)}function H(t,n){var r,i=e();for(r=0;r<16;r++)i[r]=n[r];for(r=253;r>=0;r--)z(i,i),2!==r&&4!==r&&B(i,i,n);for(r=0;r<16;r++)t[r]=i[r]}function q(t,n){var r,i=e();for(r=0;r<16;r++)i[r]=n[r];for(r=250;r>=0;r--)z(i,i),1!==r&&B(i,i,n);for(r=0;r<16;r++)t[r]=i[r]}function F(t,n,r){var i,s,o=new Uint8Array(32),a=new Float64Array(80),h=e(),u=e(),l=e(),d=e(),p=e(),f=e();for(s=0;s<31;s++)o[s]=n[s];for(o[31]=127&n[31]|64,o[0]&=248,D(a,r),s=0;s<16;s++)u[s]=a[s],d[s]=h[s]=l[s]=0;for(h[0]=d[0]=1,s=254;s>=0;--s)R(h,u,i=o[s>>>3]>>>(7&s)&1),R(l,d,i),N(p,h,l),j(h,h,l),N(l,u,d),j(u,u,d),z(d,p),z(f,h),B(h,l,h),B(l,u,p),N(p,h,l),j(h,h,l),z(u,h),j(l,d,f),B(h,l,c),N(h,h,d),B(l,l,h),B(h,d,f),B(d,u,a),z(u,p),R(h,u,i),R(l,d,i);for(s=0;s<16;s++)a[s+16]=h[s],a[s+32]=l[s],a[s+48]=u[s],a[s+64]=d[s];var g=a.subarray(32),v=a.subarray(16);return H(g,g),B(v,v,g),U(t,v),0}function X(t,e){return F(t,e,s)}function J(t,e){return r(e,32),X(t,e)}function Y(t,e,n){var r=new Uint8Array(32);return F(r,n,e),m(t,i,r,w)}T.prototype.blocks=function(t,e,n){for(var r,i,s,o,a,c,h,u,l,d,p,f,g,v,b,y,m,w,_,S=this.fin?0:2048,k=this.h[0],C=this.h[1],T=this.h[2],E=this.h[3],P=this.h[4],x=this.h[5],A=this.h[6],O=this.h[7],L=this.h[8],R=this.h[9],U=this.r[0],M=this.r[1],I=this.r[2],D=this.r[3],N=this.r[4],j=this.r[5],B=this.r[6],z=this.r[7],H=this.r[8],q=this.r[9];n>=16;)d=l=0,d+=(k+=8191&(r=255&t[e+0]|(255&t[e+1])<<8))*U,d+=(C+=8191&(r>>>13|(i=255&t[e+2]|(255&t[e+3])<<8)<<3))*(5*q),d+=(T+=8191&(i>>>10|(s=255&t[e+4]|(255&t[e+5])<<8)<<6))*(5*H),d+=(E+=8191&(s>>>7|(o=255&t[e+6]|(255&t[e+7])<<8)<<9))*(5*z),l=(d+=(P+=8191&(o>>>4|(a=255&t[e+8]|(255&t[e+9])<<8)<<12))*(5*B))>>>13,d&=8191,d+=(x+=a>>>1&8191)*(5*j),d+=(A+=8191&(a>>>14|(c=255&t[e+10]|(255&t[e+11])<<8)<<2))*(5*N),d+=(O+=8191&(c>>>11|(h=255&t[e+12]|(255&t[e+13])<<8)<<5))*(5*D),d+=(L+=8191&(h>>>8|(u=255&t[e+14]|(255&t[e+15])<<8)<<8))*(5*I),p=l+=(d+=(R+=u>>>5|S)*(5*M))>>>13,p+=k*M,p+=C*U,p+=T*(5*q),p+=E*(5*H),l=(p+=P*(5*z))>>>13,p&=8191,p+=x*(5*B),p+=A*(5*j),p+=O*(5*N),p+=L*(5*D),l+=(p+=R*(5*I))>>>13,p&=8191,f=l,f+=k*I,f+=C*M,f+=T*U,f+=E*(5*q),l=(f+=P*(5*H))>>>13,f&=8191,f+=x*(5*z),f+=A*(5*B),f+=O*(5*j),f+=L*(5*N),g=l+=(f+=R*(5*D))>>>13,g+=k*D,g+=C*I,g+=T*M,g+=E*U,l=(g+=P*(5*q))>>>13,g&=8191,g+=x*(5*H),g+=A*(5*z),g+=O*(5*B),g+=L*(5*j),v=l+=(g+=R*(5*N))>>>13,v+=k*N,v+=C*D,v+=T*I,v+=E*M,l=(v+=P*U)>>>13,v&=8191,v+=x*(5*q),v+=A*(5*H),v+=O*(5*z),v+=L*(5*B),b=l+=(v+=R*(5*j))>>>13,b+=k*j,b+=C*N,b+=T*D,b+=E*I,l=(b+=P*M)>>>13,b&=8191,b+=x*U,b+=A*(5*q),b+=O*(5*H),b+=L*(5*z),y=l+=(b+=R*(5*B))>>>13,y+=k*B,y+=C*j,y+=T*N,y+=E*D,l=(y+=P*I)>>>13,y&=8191,y+=x*M,y+=A*U,y+=O*(5*q),y+=L*(5*H),m=l+=(y+=R*(5*z))>>>13,m+=k*z,m+=C*B,m+=T*j,m+=E*N,l=(m+=P*D)>>>13,m&=8191,m+=x*I,m+=A*M,m+=O*U,m+=L*(5*q),w=l+=(m+=R*(5*H))>>>13,w+=k*H,w+=C*z,w+=T*B,w+=E*j,l=(w+=P*N)>>>13,w&=8191,w+=x*D,w+=A*I,w+=O*M,w+=L*U,_=l+=(w+=R*(5*q))>>>13,_+=k*q,_+=C*H,_+=T*z,_+=E*B,l=(_+=P*j)>>>13,_&=8191,_+=x*N,_+=A*D,_+=O*I,_+=L*M,k=d=8191&(l=(l=((l+=(_+=R*U)>>>13)<<2)+l|0)+(d&=8191)|0),C=p+=l>>>=13,T=f&=8191,E=g&=8191,P=v&=8191,x=b&=8191,A=y&=8191,O=m&=8191,L=w&=8191,R=_&=8191,e+=16,n-=16;this.h[0]=k,this.h[1]=C,this.h[2]=T,this.h[3]=E,this.h[4]=P,this.h[5]=x,this.h[6]=A,this.h[7]=O,this.h[8]=L,this.h[9]=R},T.prototype.finish=function(t,e){var n,r,i,s,o=new Uint16Array(10);if(this.leftover){for(s=this.leftover,this.buffer[s++]=1;s<16;s++)this.buffer[s]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(n=this.h[1]>>>13,this.h[1]&=8191,s=2;s<10;s++)this.h[s]+=n,n=this.h[s]>>>13,this.h[s]&=8191;for(this.h[0]+=5*n,n=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=n,n=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=n,o[0]=this.h[0]+5,n=o[0]>>>13,o[0]&=8191,s=1;s<10;s++)o[s]=this.h[s]+n,n=o[s]>>>13,o[s]&=8191;for(o[9]-=8192,r=(1^n)-1,s=0;s<10;s++)o[s]&=r;for(r=~r,s=0;s<10;s++)this.h[s]=this.h[s]&r|o[s];for(this.h[0]=65535&(this.h[0]|this.h[1]<<13),this.h[1]=65535&(this.h[1]>>>3|this.h[2]<<10),this.h[2]=65535&(this.h[2]>>>6|this.h[3]<<7),this.h[3]=65535&(this.h[3]>>>9|this.h[4]<<4),this.h[4]=65535&(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14),this.h[5]=65535&(this.h[6]>>>2|this.h[7]<<11),this.h[6]=65535&(this.h[7]>>>5|this.h[8]<<8),this.h[7]=65535&(this.h[8]>>>8|this.h[9]<<5),i=this.h[0]+this.pad[0],this.h[0]=65535&i,s=1;s<8;s++)i=(this.h[s]+this.pad[s]|0)+(i>>>16)|0,this.h[s]=65535&i;t[e+0]=this.h[0]>>>0&255,t[e+1]=this.h[0]>>>8&255,t[e+2]=this.h[1]>>>0&255,t[e+3]=this.h[1]>>>8&255,t[e+4]=this.h[2]>>>0&255,t[e+5]=this.h[2]>>>8&255,t[e+6]=this.h[3]>>>0&255,t[e+7]=this.h[3]>>>8&255,t[e+8]=this.h[4]>>>0&255,t[e+9]=this.h[4]>>>8&255,t[e+10]=this.h[5]>>>0&255,t[e+11]=this.h[5]>>>8&255,t[e+12]=this.h[6]>>>0&255,t[e+13]=this.h[6]>>>8&255,t[e+14]=this.h[7]>>>0&255,t[e+15]=this.h[7]>>>8&255},T.prototype.update=function(t,e,n){var r,i;if(this.leftover){for((i=16-this.leftover)>n&&(i=n),r=0;r<i;r++)this.buffer[this.leftover+r]=t[e+r];if(n-=i,e+=i,this.leftover+=i,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(n>=16&&(i=n-n%16,this.blocks(t,e,i),e+=i,n-=i),n){for(r=0;r<n;r++)this.buffer[this.leftover+r]=t[e+r];this.leftover+=n}};var K=x,$=A;var W=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function G(t,e,n,r){for(var i,s,o,a,c,h,u,l,d,p,f,g,v,b,y,m,w,_,S,k,C,T,E,P,x,A,O=new Int32Array(16),L=new Int32Array(16),R=t[0],U=t[1],M=t[2],I=t[3],D=t[4],N=t[5],j=t[6],B=t[7],z=e[0],H=e[1],q=e[2],F=e[3],X=e[4],J=e[5],Y=e[6],K=e[7],$=0;r>=128;){for(S=0;S<16;S++)k=8*S+$,O[S]=n[k+0]<<24|n[k+1]<<16|n[k+2]<<8|n[k+3],L[S]=n[k+4]<<24|n[k+5]<<16|n[k+6]<<8|n[k+7];for(S=0;S<80;S++)if(i=R,s=U,o=M,a=I,c=D,h=N,u=j,B,d=z,p=H,f=q,g=F,v=X,b=J,y=Y,K,E=65535&(T=K),P=T>>>16,x=65535&(C=B),A=C>>>16,E+=65535&(T=(X>>>14|D<<18)^(X>>>18|D<<14)^(D>>>9|X<<23)),P+=T>>>16,x+=65535&(C=(D>>>14|X<<18)^(D>>>18|X<<14)^(X>>>9|D<<23)),A+=C>>>16,E+=65535&(T=X&J^~X&Y),P+=T>>>16,x+=65535&(C=D&N^~D&j),A+=C>>>16,E+=65535&(T=W[2*S+1]),P+=T>>>16,x+=65535&(C=W[2*S]),A+=C>>>16,C=O[S%16],P+=(T=L[S%16])>>>16,x+=65535&C,A+=C>>>16,x+=(P+=(E+=65535&T)>>>16)>>>16,E=65535&(T=_=65535&E|P<<16),P=T>>>16,x=65535&(C=w=65535&x|(A+=x>>>16)<<16),A=C>>>16,E+=65535&(T=(z>>>28|R<<4)^(R>>>2|z<<30)^(R>>>7|z<<25)),P+=T>>>16,x+=65535&(C=(R>>>28|z<<4)^(z>>>2|R<<30)^(z>>>7|R<<25)),A+=C>>>16,P+=(T=z&H^z&q^H&q)>>>16,x+=65535&(C=R&U^R&M^U&M),A+=C>>>16,l=65535&(x+=(P+=(E+=65535&T)>>>16)>>>16)|(A+=x>>>16)<<16,m=65535&E|P<<16,E=65535&(T=g),P=T>>>16,x=65535&(C=a),A=C>>>16,P+=(T=_)>>>16,x+=65535&(C=w),A+=C>>>16,U=i,M=s,I=o,D=a=65535&(x+=(P+=(E+=65535&T)>>>16)>>>16)|(A+=x>>>16)<<16,N=c,j=h,B=u,R=l,H=d,q=p,F=f,X=g=65535&E|P<<16,J=v,Y=b,K=y,z=m,S%16==15)for(k=0;k<16;k++)C=O[k],E=65535&(T=L[k]),P=T>>>16,x=65535&C,A=C>>>16,C=O[(k+9)%16],E+=65535&(T=L[(k+9)%16]),P+=T>>>16,x+=65535&C,A+=C>>>16,w=O[(k+1)%16],E+=65535&(T=((_=L[(k+1)%16])>>>1|w<<31)^(_>>>8|w<<24)^(_>>>7|w<<25)),P+=T>>>16,x+=65535&(C=(w>>>1|_<<31)^(w>>>8|_<<24)^w>>>7),A+=C>>>16,w=O[(k+14)%16],P+=(T=((_=L[(k+14)%16])>>>19|w<<13)^(w>>>29|_<<3)^(_>>>6|w<<26))>>>16,x+=65535&(C=(w>>>19|_<<13)^(_>>>29|w<<3)^w>>>6),A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,O[k]=65535&x|A<<16,L[k]=65535&E|P<<16;E=65535&(T=z),P=T>>>16,x=65535&(C=R),A=C>>>16,C=t[0],P+=(T=e[0])>>>16,x+=65535&C,A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,t[0]=R=65535&x|A<<16,e[0]=z=65535&E|P<<16,E=65535&(T=H),P=T>>>16,x=65535&(C=U),A=C>>>16,C=t[1],P+=(T=e[1])>>>16,x+=65535&C,A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,t[1]=U=65535&x|A<<16,e[1]=H=65535&E|P<<16,E=65535&(T=q),P=T>>>16,x=65535&(C=M),A=C>>>16,C=t[2],P+=(T=e[2])>>>16,x+=65535&C,A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,t[2]=M=65535&x|A<<16,e[2]=q=65535&E|P<<16,E=65535&(T=F),P=T>>>16,x=65535&(C=I),A=C>>>16,C=t[3],P+=(T=e[3])>>>16,x+=65535&C,A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,t[3]=I=65535&x|A<<16,e[3]=F=65535&E|P<<16,E=65535&(T=X),P=T>>>16,x=65535&(C=D),A=C>>>16,C=t[4],P+=(T=e[4])>>>16,x+=65535&C,A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,t[4]=D=65535&x|A<<16,e[4]=X=65535&E|P<<16,E=65535&(T=J),P=T>>>16,x=65535&(C=N),A=C>>>16,C=t[5],P+=(T=e[5])>>>16,x+=65535&C,A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,t[5]=N=65535&x|A<<16,e[5]=J=65535&E|P<<16,E=65535&(T=Y),P=T>>>16,x=65535&(C=j),A=C>>>16,C=t[6],P+=(T=e[6])>>>16,x+=65535&C,A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,t[6]=j=65535&x|A<<16,e[6]=Y=65535&E|P<<16,E=65535&(T=K),P=T>>>16,x=65535&(C=B),A=C>>>16,C=t[7],P+=(T=e[7])>>>16,x+=65535&C,A+=C>>>16,A+=(x+=(P+=(E+=65535&T)>>>16)>>>16)>>>16,t[7]=B=65535&x|A<<16,e[7]=K=65535&E|P<<16,$+=128,r-=128}return r}function V(t,e,n){var r,i=new Int32Array(8),s=new Int32Array(8),o=new Uint8Array(256),a=n;for(i[0]=1779033703,i[1]=3144134277,i[2]=1013904242,i[3]=2773480762,i[4]=1359893119,i[5]=2600822924,i[6]=528734635,i[7]=1541459225,s[0]=4089235720,s[1]=2227873595,s[2]=4271175723,s[3]=1595750129,s[4]=2917565137,s[5]=725511199,s[6]=4215389547,s[7]=327033209,G(i,s,e,n),n%=128,r=0;r<n;r++)o[r]=e[a-n+r];for(o[n]=128,o[(n=256-128*(n<112?1:0))-9]=0,f(o,n-8,a/536870912|0,a<<3),G(i,s,o,n),r=0;r<8;r++)f(t,8*r,i[r],s[r]);return 0}function Z(t,n){var r=e(),i=e(),s=e(),o=e(),a=e(),c=e(),h=e(),l=e(),d=e();j(r,t[1],t[0]),j(d,n[1],n[0]),B(r,r,d),N(i,t[0],t[1]),N(d,n[0],n[1]),B(i,i,d),B(s,t[3],n[3]),B(s,s,u),B(o,t[2],n[2]),N(o,o,o),j(a,i,r),j(c,o,s),N(h,o,s),N(l,i,r),B(t[0],a,c),B(t[1],l,h),B(t[2],h,c),B(t[3],a,l)}function Q(t,e,n){var r;for(r=0;r<4;r++)R(t[r],e[r],n)}function tt(t,n){var r=e(),i=e(),s=e();H(s,n[2]),B(r,n[0],s),B(i,n[1],s),U(t,i),t[31]^=I(r)<<7}function et(t,e,n){var r,i;for(O(t[0],o),O(t[1],a),O(t[2],a),O(t[3],o),i=255;i>=0;--i)Q(t,e,r=n[i/8|0]>>(7&i)&1),Z(e,t),Z(t,t),Q(t,e,r)}function nt(t,n){var r=[e(),e(),e(),e()];O(r[0],l),O(r[1],d),O(r[2],a),B(r[3],l,d),et(t,r,n)}function rt(t,n,i){var s,o=new Uint8Array(64),a=[e(),e(),e(),e()];for(i||r(n,32),V(o,n,32),o[0]&=248,o[31]&=127,o[31]|=64,nt(a,o),tt(t,a),s=0;s<32;s++)n[s+32]=t[s];return 0}var it=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function st(t,e){var n,r,i,s;for(r=63;r>=32;--r){for(n=0,i=r-32,s=r-12;i<s;++i)e[i]+=n-16*e[r]*it[i-(r-32)],n=Math.floor((e[i]+128)/256),e[i]-=256*n;e[i]+=n,e[r]=0}for(n=0,i=0;i<32;i++)e[i]+=n-(e[31]>>4)*it[i],n=e[i]>>8,e[i]&=255;for(i=0;i<32;i++)e[i]-=n*it[i];for(r=0;r<32;r++)e[r+1]+=e[r]>>8,t[r]=255&e[r]}function ot(t){var e,n=new Float64Array(64);for(e=0;e<64;e++)n[e]=t[e];for(e=0;e<64;e++)t[e]=0;st(t,n)}function at(t,n,r,i){var s,o,a=new Uint8Array(64),c=new Uint8Array(64),h=new Uint8Array(64),u=new Float64Array(64),l=[e(),e(),e(),e()];V(a,i,32),a[0]&=248,a[31]&=127,a[31]|=64;var d=r+64;for(s=0;s<r;s++)t[64+s]=n[s];for(s=0;s<32;s++)t[32+s]=a[32+s];for(V(h,t.subarray(32),r+32),ot(h),nt(l,h),tt(t,l),s=32;s<64;s++)t[s]=i[s];for(V(c,t,r+64),ot(c),s=0;s<64;s++)u[s]=0;for(s=0;s<32;s++)u[s]=h[s];for(s=0;s<32;s++)for(o=0;o<32;o++)u[s+o]+=c[s]*a[o];return st(t.subarray(32),u),d}function ct(t,n,r,i){var s,c=new Uint8Array(32),u=new Uint8Array(64),l=[e(),e(),e(),e()],d=[e(),e(),e(),e()];if(r<64)return-1;if(function(t,n){var r=e(),i=e(),s=e(),c=e(),u=e(),l=e(),d=e();return O(t[2],a),D(t[1],n),z(s,t[1]),B(c,s,h),j(s,s,t[2]),N(c,t[2],c),z(u,c),z(l,u),B(d,l,u),B(r,d,s),B(r,r,c),q(r,r),B(r,r,s),B(r,r,c),B(r,r,c),B(t[0],r,c),z(i,t[0]),B(i,i,c),M(i,s)&&B(t[0],t[0],p),z(i,t[0]),B(i,i,c),M(i,s)?-1:(I(t[0])===n[31]>>7&&j(t[0],o,t[0]),B(t[3],t[0],t[1]),0)}(d,i))return-1;for(s=0;s<r;s++)t[s]=n[s];for(s=0;s<32;s++)t[s+32]=i[s];if(V(u,t,r),ot(u),et(l,d,u),nt(d,n.subarray(32)),Z(l,d),tt(c,l),r-=64,b(n,0,c,0)){for(s=0;s<r;s++)t[s]=0;return-1}for(s=0;s<r;s++)t[s]=n[s+64];return r}function ht(t,e){if(32!==t.length)throw new Error("bad key size");if(24!==e.length)throw new Error("bad nonce size")}function ut(){for(var t=0;t<arguments.length;t++)if(!(arguments[t]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function lt(t){for(var e=0;e<t.length;e++)t[e]=0}t.lowlevel={crypto_core_hsalsa20:m,crypto_stream_xor:C,crypto_stream:k,crypto_stream_salsa20_xor:_,crypto_stream_salsa20:S,crypto_onetimeauth:E,crypto_onetimeauth_verify:P,crypto_verify_16:v,crypto_verify_32:b,crypto_secretbox:x,crypto_secretbox_open:A,crypto_scalarmult:F,crypto_scalarmult_base:X,crypto_box_beforenm:Y,crypto_box_afternm:K,crypto_box:function(t,e,n,r,i,s){var o=new Uint8Array(32);return Y(o,i,s),K(t,e,n,r,o)},crypto_box_open:function(t,e,n,r,i,s){var o=new Uint8Array(32);return Y(o,i,s),$(t,e,n,r,o)},crypto_box_keypair:J,crypto_hash:V,crypto_sign:at,crypto_sign_keypair:rt,crypto_sign_open:ct,crypto_secretbox_KEYBYTES:32,crypto_secretbox_NONCEBYTES:24,crypto_secretbox_ZEROBYTES:32,crypto_secretbox_BOXZEROBYTES:16,crypto_scalarmult_BYTES:32,crypto_scalarmult_SCALARBYTES:32,crypto_box_PUBLICKEYBYTES:32,crypto_box_SECRETKEYBYTES:32,crypto_box_BEFORENMBYTES:32,crypto_box_NONCEBYTES:24,crypto_box_ZEROBYTES:32,crypto_box_BOXZEROBYTES:16,crypto_sign_BYTES:64,crypto_sign_PUBLICKEYBYTES:32,crypto_sign_SECRETKEYBYTES:64,crypto_sign_SEEDBYTES:32,crypto_hash_BYTES:64,gf:e,D:h,L:it,pack25519:U,unpack25519:D,M:B,A:N,S:z,Z:j,pow2523:q,add:Z,set25519:O,modL:st,scalarmult:et,scalarbase:nt},t.randomBytes=function(t){var e=new Uint8Array(t);return r(e,t),e},t.secretbox=function(t,e,n){ut(t,e,n),ht(n,e);for(var r=new Uint8Array(32+t.length),i=new Uint8Array(r.length),s=0;s<t.length;s++)r[s+32]=t[s];return x(i,r,r.length,e,n),i.subarray(16)},t.secretbox.open=function(t,e,n){ut(t,e,n),ht(n,e);for(var r=new Uint8Array(16+t.length),i=new Uint8Array(r.length),s=0;s<t.length;s++)r[s+16]=t[s];return r.length<32||0!==A(i,r,r.length,e,n)?null:i.subarray(32)},t.secretbox.keyLength=32,t.secretbox.nonceLength=24,t.secretbox.overheadLength=16,t.scalarMult=function(t,e){if(ut(t,e),32!==t.length)throw new Error("bad n size");if(32!==e.length)throw new Error("bad p size");var n=new Uint8Array(32);return F(n,t,e),n},t.scalarMult.base=function(t){if(ut(t),32!==t.length)throw new Error("bad n size");var e=new Uint8Array(32);return X(e,t),e},t.scalarMult.scalarLength=32,t.scalarMult.groupElementLength=32,t.box=function(e,n,r,i){var s=t.box.before(r,i);return t.secretbox(e,n,s)},t.box.before=function(t,e){ut(t,e),function(t,e){if(32!==t.length)throw new Error("bad public key size");if(32!==e.length)throw new Error("bad secret key size")}(t,e);var n=new Uint8Array(32);return Y(n,t,e),n},t.box.after=t.secretbox,t.box.open=function(e,n,r,i){var s=t.box.before(r,i);return t.secretbox.open(e,n,s)},t.box.open.after=t.secretbox.open,t.box.keyPair=function(){var t=new Uint8Array(32),e=new Uint8Array(32);return J(t,e),{publicKey:t,secretKey:e}},t.box.keyPair.fromSecretKey=function(t){if(ut(t),32!==t.length)throw new Error("bad secret key size");var e=new Uint8Array(32);return X(e,t),{publicKey:e,secretKey:new Uint8Array(t)}},t.box.publicKeyLength=32,t.box.secretKeyLength=32,t.box.sharedKeyLength=32,t.box.nonceLength=24,t.box.overheadLength=t.secretbox.overheadLength,t.sign=function(t,e){if(ut(t,e),64!==e.length)throw new Error("bad secret key size");var n=new Uint8Array(64+t.length);return at(n,t,t.length,e),n},t.sign.open=function(t,e){if(ut(t,e),32!==e.length)throw new Error("bad public key size");var n=new Uint8Array(t.length),r=ct(n,t,t.length,e);if(r<0)return null;for(var i=new Uint8Array(r),s=0;s<i.length;s++)i[s]=n[s];return i},t.sign.detached=function(e,n){for(var r=t.sign(e,n),i=new Uint8Array(64),s=0;s<i.length;s++)i[s]=r[s];return i},t.sign.detached.verify=function(t,e,n){if(ut(t,e,n),64!==e.length)throw new Error("bad signature size");if(32!==n.length)throw new Error("bad public key size");var r,i=new Uint8Array(64+t.length),s=new Uint8Array(64+t.length);for(r=0;r<64;r++)i[r]=e[r];for(r=0;r<t.length;r++)i[r+64]=t[r];return ct(s,i,i.length,n)>=0},t.sign.keyPair=function(){var t=new Uint8Array(32),e=new Uint8Array(64);return rt(t,e),{publicKey:t,secretKey:e}},t.sign.keyPair.fromSecretKey=function(t){if(ut(t),64!==t.length)throw new Error("bad secret key size");for(var e=new Uint8Array(32),n=0;n<e.length;n++)e[n]=t[32+n];return{publicKey:e,secretKey:new Uint8Array(t)}},t.sign.keyPair.fromSeed=function(t){if(ut(t),32!==t.length)throw new Error("bad seed size");for(var e=new Uint8Array(32),n=new Uint8Array(64),r=0;r<32;r++)n[r]=t[r];return rt(e,n,!0),{publicKey:e,secretKey:n}},t.sign.publicKeyLength=32,t.sign.secretKeyLength=64,t.sign.seedLength=32,t.sign.signatureLength=64,t.hash=function(t){ut(t);var e=new Uint8Array(64);return V(e,t,t.length),e},t.hash.hashLength=64,t.verify=function(t,e){return ut(t,e),0!==t.length&&0!==e.length&&(t.length===e.length&&0===g(t,0,e,0,t.length))},t.setPRNG=function(t){r=t},function(){var e="undefined"!=typeof self?self.crypto||self.msCrypto:null;if(e&&e.getRandomValues){t.setPRNG((function(t,n){var r,i=new Uint8Array(n);for(r=0;r<n;r+=65536)e.getRandomValues(i.subarray(r,r+Math.min(n-r,65536)));for(r=0;r<n;r++)t[r]=i[r];lt(i)}))}else(e=n(4))&&e.randomBytes&&t.setPRNG((function(t,n){var r,i=e.randomBytes(n);for(r=0;r<n;r++)t[r]=i[r];lt(i)}))}()}(t.exports?t.exports:self.nacl=self.nacl||{})},function(t,e,n){t.exports=n(5).default},function(t,e){},function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return Ue}));class r{constructor(t,e){this.lastId=0,this.prefix=t,this.name=e}create(t){this.lastId++;var e=this.lastId,n=this.prefix+e,r=this.name+"["+e+"]",i=!1,s=function(){i||(t.apply(null,arguments),i=!0)};return this[e]=s,{number:e,id:n,name:r,callback:s}}remove(t){delete this[t.number]}}var i=new r("_pusher_script_","Pusher.ScriptReceivers"),s={VERSION:"8.4.0",PROTOCOL:7,wsPort:80,wssPort:443,wsPath:"",httpHost:"sockjs.pusher.com",httpPort:80,httpsPort:443,httpPath:"/pusher",stats_host:"stats.pusher.com",authEndpoint:"/pusher/auth",authTransport:"ajax",activityTimeout:12e4,pongTimeout:3e4,unavailableTimeout:1e4,userAuthentication:{endpoint:"/pusher/user-auth",transport:"ajax"},channelAuthorization:{endpoint:"/pusher/auth",transport:"ajax"},cdn_http:"http://js.pusher.com",cdn_https:"https://js.pusher.com",dependency_suffix:""};var o=new r("_pusher_dependencies","Pusher.DependenciesReceivers"),a=new class{constructor(t){this.options=t,this.receivers=t.receivers||i,this.loading={}}load(t,e,n){var r=this;if(r.loading[t]&&r.loading[t].length>0)r.loading[t].push(n);else{r.loading[t]=[n];var i=ue.createScriptRequest(r.getPath(t,e)),s=r.receivers.create((function(e){if(r.receivers.remove(s),r.loading[t]){var n=r.loading[t];delete r.loading[t];for(var o=function(t){t||i.cleanup()},a=0;a<n.length;a++)n[a](e,o)}}));i.send(s)}}getRoot(t){var e=ue.getDocument().location.protocol;return(t&&t.useTLS||"https:"===e?this.options.cdn_https:this.options.cdn_http).replace(/\/*$/,"")+"/"+this.options.version}getPath(t,e){return this.getRoot(e)+"/"+t+this.options.suffix+".js"}}({cdn_http:s.cdn_http,cdn_https:s.cdn_https,version:s.VERSION,suffix:s.dependency_suffix,receivers:o});const c={baseUrl:"https://pusher.com",urls:{authenticationEndpoint:{path:"/docs/channels/server_api/authenticating_users"},authorizationEndpoint:{path:"/docs/channels/server_api/authorizing-users/"},javascriptQuickStart:{path:"/docs/javascript_quick_start"},triggeringClientEvents:{path:"/docs/client_api_guide/client_events#trigger-events"},encryptedChannelSupport:{fullUrl:"https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support"}}};var h,u=function(t){const e=c.urls[t];if(!e)return"";let n;return e.fullUrl?n=e.fullUrl:e.path&&(n=c.baseUrl+e.path),n?"See: "+n:""};!function(t){t.UserAuthentication="user-authentication",t.ChannelAuthorization="channel-authorization"}(h||(h={}));class l extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class d extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class p extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class f extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class g extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class v extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class b extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class y extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class m extends Error{constructor(t,e){super(e),this.status=t,Object.setPrototypeOf(this,new.target.prototype)}}var w=function(t,e,n,r,i){const s=ue.createXHR();for(var o in s.open("POST",n.endpoint,!0),s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),n.headers)s.setRequestHeader(o,n.headers[o]);if(null!=n.headersProvider){let t=n.headersProvider();for(var o in t)s.setRequestHeader(o,t[o])}return s.onreadystatechange=function(){if(4===s.readyState)if(200===s.status){let t,e=!1;try{t=JSON.parse(s.responseText),e=!0}catch(t){i(new m(200,`JSON returned from ${r.toString()} endpoint was invalid, yet status code was 200. Data was: ${s.responseText}`),null)}e&&i(null,t)}else{let t="";switch(r){case h.UserAuthentication:t=u("authenticationEndpoint");break;case h.ChannelAuthorization:t="Clients must be authorized to join private or presence channels. "+u("authorizationEndpoint")}i(new m(s.status,`Unable to retrieve auth string from ${r.toString()} endpoint - received status: ${s.status} from ${n.endpoint}. ${t}`),null)}},s.send(e),s};for(var _=String.fromCharCode,S="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",k={},C=0,T=S.length;C<T;C++)k[S.charAt(C)]=C;var E=function(t){var e=t.charCodeAt(0);return e<128?t:e<2048?_(192|e>>>6)+_(128|63&e):_(224|e>>>12&15)+_(128|e>>>6&63)+_(128|63&e)},P=function(t){return t.replace(/[^\x00-\x7F]/g,E)},x=function(t){var e=[0,2,1][t.length%3],n=t.charCodeAt(0)<<16|(t.length>1?t.charCodeAt(1):0)<<8|(t.length>2?t.charCodeAt(2):0);return[S.charAt(n>>>18),S.charAt(n>>>12&63),e>=2?"=":S.charAt(n>>>6&63),e>=1?"=":S.charAt(63&n)].join("")},A=window.btoa||function(t){return t.replace(/[\s\S]{1,3}/g,x)};var O=class{constructor(t,e,n,r){this.clear=e,this.timer=t(()=>{this.timer&&(this.timer=r(this.timer))},n)}isRunning(){return null!==this.timer}ensureAborted(){this.timer&&(this.clear(this.timer),this.timer=null)}};function L(t){window.clearTimeout(t)}function R(t){window.clearInterval(t)}class U extends O{constructor(t,e){super(setTimeout,L,t,(function(t){return e(),null}))}}class M extends O{constructor(t,e){super(setInterval,R,t,(function(t){return e(),t}))}}var I={now:()=>Date.now?Date.now():(new Date).valueOf(),defer:t=>new U(0,t),method(t,...e){var n=Array.prototype.slice.call(arguments,1);return function(e){return e[t].apply(e,n.concat(arguments))}}};function D(t,...e){for(var n=0;n<e.length;n++){var r=e[n];for(var i in r)r[i]&&r[i].constructor&&r[i].constructor===Object?t[i]=D(t[i]||{},r[i]):t[i]=r[i]}return t}function N(){for(var t=["Pusher"],e=0;e<arguments.length;e++)"string"==typeof arguments[e]?t.push(arguments[e]):t.push($(arguments[e]));return t.join(" : ")}function j(t,e){var n=Array.prototype.indexOf;if(null===t)return-1;if(n&&t.indexOf===n)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r;return-1}function B(t,e){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(t[n],n,t)}function z(t){var e=[];return B(t,(function(t,n){e.push(n)})),e}function H(t,e,n){for(var r=0;r<t.length;r++)e.call(n||window,t[r],r,t)}function q(t,e){for(var n=[],r=0;r<t.length;r++)n.push(e(t[r],r,t,n));return n}function F(t,e){e=e||function(t){return!!t};for(var n=[],r=0;r<t.length;r++)e(t[r],r,t,n)&&n.push(t[r]);return n}function X(t,e){var n={};return B(t,(function(r,i){(e&&e(r,i,t,n)||Boolean(r))&&(n[i]=r)})),n}function J(t,e){for(var n=0;n<t.length;n++)if(e(t[n],n,t))return!0;return!1}function Y(t){return e=function(t){return"object"==typeof t&&(t=$(t)),encodeURIComponent((e=t.toString(),A(P(e))));var e},n={},B(t,(function(t,r){n[r]=e(t)})),n;var e,n}function K(t){var e,n,r=X(t,(function(t){return void 0!==t}));return q((e=Y(r),n=[],B(e,(function(t,e){n.push([e,t])})),n),I.method("join","=")).join("&")}function $(t){try{return JSON.stringify(t)}catch(r){return JSON.stringify((e=[],n=[],function t(r,i){var s,o,a;switch(typeof r){case"object":if(!r)return null;for(s=0;s<e.length;s+=1)if(e[s]===r)return{$ref:n[s]};if(e.push(r),n.push(i),"[object Array]"===Object.prototype.toString.apply(r))for(a=[],s=0;s<r.length;s+=1)a[s]=t(r[s],i+"["+s+"]");else for(o in a={},r)Object.prototype.hasOwnProperty.call(r,o)&&(a[o]=t(r[o],i+"["+JSON.stringify(o)+"]"));return a;case"number":case"string":case"boolean":return r}}(t,"$")))}var e,n}var W=new class{constructor(){this.globalLog=t=>{window.console&&window.console.log&&window.console.log(t)}}debug(...t){this.log(this.globalLog,t)}warn(...t){this.log(this.globalLogWarn,t)}error(...t){this.log(this.globalLogError,t)}globalLogWarn(t){window.console&&window.console.warn?window.console.warn(t):this.globalLog(t)}globalLogError(t){window.console&&window.console.error?window.console.error(t):this.globalLogWarn(t)}log(t,...e){var n=N.apply(this,arguments);if(Le.log)Le.log(n);else if(Le.logToConsole){t.bind(this)(n)}}},G=function(t,e,n,r,i){void 0===n.headers&&null==n.headersProvider||W.warn(`To send headers with the ${r.toString()} request, you must use AJAX, rather than JSONP.`);var s=t.nextAuthCallbackID.toString();t.nextAuthCallbackID++;var o=t.getDocument(),a=o.createElement("script");t.auth_callbacks[s]=function(t){i(null,t)};var c="Pusher.auth_callbacks['"+s+"']";a.src=n.endpoint+"?callback="+encodeURIComponent(c)+"&"+e;var h=o.getElementsByTagName("head")[0]||o.documentElement;h.insertBefore(a,h.firstChild)};class V{constructor(t){this.src=t}send(t){var e=this,n="Error loading "+e.src;e.script=document.createElement("script"),e.script.id=t.id,e.script.src=e.src,e.script.type="text/javascript",e.script.charset="UTF-8",e.script.addEventListener?(e.script.onerror=function(){t.callback(n)},e.script.onload=function(){t.callback(null)}):e.script.onreadystatechange=function(){"loaded"!==e.script.readyState&&"complete"!==e.script.readyState||t.callback(null)},void 0===e.script.async&&document.attachEvent&&/opera/i.test(navigator.userAgent)?(e.errorScript=document.createElement("script"),e.errorScript.id=t.id+"_error",e.errorScript.text=t.name+"('"+n+"');",e.script.async=e.errorScript.async=!1):e.script.async=!0;var r=document.getElementsByTagName("head")[0];r.insertBefore(e.script,r.firstChild),e.errorScript&&r.insertBefore(e.errorScript,e.script.nextSibling)}cleanup(){this.script&&(this.script.onload=this.script.onerror=null,this.script.onreadystatechange=null),this.script&&this.script.parentNode&&this.script.parentNode.removeChild(this.script),this.errorScript&&this.errorScript.parentNode&&this.errorScript.parentNode.removeChild(this.errorScript),this.script=null,this.errorScript=null}}class Z{constructor(t,e){this.url=t,this.data=e}send(t){if(!this.request){var e=K(this.data),n=this.url+"/"+t.number+"?"+e;this.request=ue.createScriptRequest(n),this.request.send(t)}}cleanup(){this.request&&this.request.cleanup()}}var Q={name:"jsonp",getAgent:function(t,e){return function(n,r){var s="http"+(e?"s":"")+"://"+(t.host||t.options.host)+t.options.path,o=ue.createJSONPRequest(s,n),a=ue.ScriptReceivers.create((function(e,n){i.remove(a),o.cleanup(),n&&n.host&&(t.host=n.host),r&&r(e,n)}));o.send(a)}}};function tt(t,e,n){return t+(e.useTLS?"s":"")+"://"+(e.useTLS?e.hostTLS:e.hostNonTLS)+n}function et(t,e){return"/app/"+t+("?protocol="+s.PROTOCOL+"&client=js&version="+s.VERSION+(e?"&"+e:""))}var nt={getInitial:function(t,e){return tt("ws",e,(e.httpPath||"")+et(t,"flash=false"))}},rt={getInitial:function(t,e){return tt("http",e,(e.httpPath||"/pusher")+et(t))}},it={getInitial:function(t,e){return tt("http",e,e.httpPath||"/pusher")},getPath:function(t,e){return et(t)}};class st{constructor(){this._callbacks={}}get(t){return this._callbacks[ot(t)]}add(t,e,n){var r=ot(t);this._callbacks[r]=this._callbacks[r]||[],this._callbacks[r].push({fn:e,context:n})}remove(t,e,n){if(t||e||n){var r=t?[ot(t)]:z(this._callbacks);e||n?this.removeCallback(r,e,n):this.removeAllCallbacks(r)}else this._callbacks={}}removeCallback(t,e,n){H(t,(function(t){this._callbacks[t]=F(this._callbacks[t]||[],(function(t){return e&&e!==t.fn||n&&n!==t.context})),0===this._callbacks[t].length&&delete this._callbacks[t]}),this)}removeAllCallbacks(t){H(t,(function(t){delete this._callbacks[t]}),this)}}function ot(t){return"_"+t}class at{constructor(t){this.callbacks=new st,this.global_callbacks=[],this.failThrough=t}bind(t,e,n){return this.callbacks.add(t,e,n),this}bind_global(t){return this.global_callbacks.push(t),this}unbind(t,e,n){return this.callbacks.remove(t,e,n),this}unbind_global(t){return t?(this.global_callbacks=F(this.global_callbacks||[],e=>e!==t),this):(this.global_callbacks=[],this)}unbind_all(){return this.unbind(),this.unbind_global(),this}emit(t,e,n){for(var r=0;r<this.global_callbacks.length;r++)this.global_callbacks[r](t,e);var i=this.callbacks.get(t),s=[];if(n?s.push(e,n):e&&s.push(e),i&&i.length>0)for(r=0;r<i.length;r++)i[r].fn.apply(i[r].context||window,s);else this.failThrough&&this.failThrough(t,e);return this}}class ct extends at{constructor(t,e,n,r,i){super(),this.initialize=ue.transportConnectionInitializer,this.hooks=t,this.name=e,this.priority=n,this.key=r,this.options=i,this.state="new",this.timeline=i.timeline,this.activityTimeout=i.activityTimeout,this.id=this.timeline.generateUniqueID()}handlesActivityChecks(){return Boolean(this.hooks.handlesActivityChecks)}supportsPing(){return Boolean(this.hooks.supportsPing)}connect(){if(this.socket||"initialized"!==this.state)return!1;var t=this.hooks.urls.getInitial(this.key,this.options);try{this.socket=this.hooks.getSocket(t,this.options)}catch(t){return I.defer(()=>{this.onError(t),this.changeState("closed")}),!1}return this.bindListeners(),W.debug("Connecting",{transport:this.name,url:t}),this.changeState("connecting"),!0}close(){return!!this.socket&&(this.socket.close(),!0)}send(t){return"open"===this.state&&(I.defer(()=>{this.socket&&this.socket.send(t)}),!0)}ping(){"open"===this.state&&this.supportsPing()&&this.socket.ping()}onOpen(){this.hooks.beforeOpen&&this.hooks.beforeOpen(this.socket,this.hooks.urls.getPath(this.key,this.options)),this.changeState("open"),this.socket.onopen=void 0}onError(t){this.emit("error",{type:"WebSocketError",error:t}),this.timeline.error(this.buildTimelineMessage({error:t.toString()}))}onClose(t){t?this.changeState("closed",{code:t.code,reason:t.reason,wasClean:t.wasClean}):this.changeState("closed"),this.unbindListeners(),this.socket=void 0}onMessage(t){this.emit("message",t)}onActivity(){this.emit("activity")}bindListeners(){this.socket.onopen=()=>{this.onOpen()},this.socket.onerror=t=>{this.onError(t)},this.socket.onclose=t=>{this.onClose(t)},this.socket.onmessage=t=>{this.onMessage(t)},this.supportsPing()&&(this.socket.onactivity=()=>{this.onActivity()})}unbindListeners(){this.socket&&(this.socket.onopen=void 0,this.socket.onerror=void 0,this.socket.onclose=void 0,this.socket.onmessage=void 0,this.supportsPing()&&(this.socket.onactivity=void 0))}changeState(t,e){this.state=t,this.timeline.info(this.buildTimelineMessage({state:t,params:e})),this.emit(t,e)}buildTimelineMessage(t){return D({cid:this.id},t)}}class ht{constructor(t){this.hooks=t}isSupported(t){return this.hooks.isSupported(t)}createConnection(t,e,n,r){return new ct(this.hooks,t,e,n,r)}}var ut=new ht({urls:nt,handlesActivityChecks:!1,supportsPing:!1,isInitialized:function(){return Boolean(ue.getWebSocketAPI())},isSupported:function(){return Boolean(ue.getWebSocketAPI())},getSocket:function(t){return ue.createWebSocket(t)}}),lt={urls:rt,handlesActivityChecks:!1,supportsPing:!0,isInitialized:function(){return!0}},dt=D({getSocket:function(t){return ue.HTTPFactory.createStreamingSocket(t)}},lt),pt=D({getSocket:function(t){return ue.HTTPFactory.createPollingSocket(t)}},lt),ft={isSupported:function(){return ue.isXHRSupported()}},gt={ws:ut,xhr_streaming:new ht(D({},dt,ft)),xhr_polling:new ht(D({},pt,ft))},vt=new ht({file:"sockjs",urls:it,handlesActivityChecks:!0,supportsPing:!1,isSupported:function(){return!0},isInitialized:function(){return void 0!==window.SockJS},getSocket:function(t,e){return new window.SockJS(t,null,{js_path:a.getPath("sockjs",{useTLS:e.useTLS}),ignore_null_origin:e.ignoreNullOrigin})},beforeOpen:function(t,e){t.send(JSON.stringify({path:e}))}}),bt={isSupported:function(t){return ue.isXDRSupported(t.useTLS)}},yt=new ht(D({},dt,bt)),mt=new ht(D({},pt,bt));gt.xdr_streaming=yt,gt.xdr_polling=mt,gt.sockjs=vt;var wt=gt;var _t=new class extends at{constructor(){super();var t=this;void 0!==window.addEventListener&&(window.addEventListener("online",(function(){t.emit("online")}),!1),window.addEventListener("offline",(function(){t.emit("offline")}),!1))}isOnline(){return void 0===window.navigator.onLine||window.navigator.onLine}};class St{constructor(t,e,n){this.manager=t,this.transport=e,this.minPingDelay=n.minPingDelay,this.maxPingDelay=n.maxPingDelay,this.pingDelay=void 0}createConnection(t,e,n,r){r=D({},r,{activityTimeout:this.pingDelay});var i=this.transport.createConnection(t,e,n,r),s=null,o=function(){i.unbind("open",o),i.bind("closed",a),s=I.now()},a=t=>{if(i.unbind("closed",a),1002===t.code||1003===t.code)this.manager.reportDeath();else if(!t.wasClean&&s){var e=I.now()-s;e<2*this.maxPingDelay&&(this.manager.reportDeath(),this.pingDelay=Math.max(e/2,this.minPingDelay))}};return i.bind("open",o),i}isSupported(t){return this.manager.isAlive()&&this.transport.isSupported(t)}}const kt={decodeMessage:function(t){try{var e=JSON.parse(t.data),n=e.data;if("string"==typeof n)try{n=JSON.parse(e.data)}catch(t){}var r={event:e.event,channel:e.channel,data:n};return e.user_id&&(r.user_id=e.user_id),r}catch(e){throw{type:"MessageParseError",error:e,data:t.data}}},encodeMessage:function(t){return JSON.stringify(t)},processHandshake:function(t){var e=kt.decodeMessage(t);if("pusher:connection_established"===e.event){if(!e.data.activity_timeout)throw"No activity timeout specified in handshake";return{action:"connected",id:e.data.socket_id,activityTimeout:1e3*e.data.activity_timeout}}if("pusher:error"===e.event)return{action:this.getCloseAction(e.data),error:this.getCloseError(e.data)};throw"Invalid handshake"},getCloseAction:function(t){return t.code<4e3?t.code>=1002&&t.code<=1004?"backoff":null:4e3===t.code?"tls_only":t.code<4100?"refused":t.code<4200?"backoff":t.code<4300?"retry":"refused"},getCloseError:function(t){return 1e3!==t.code&&1001!==t.code?{type:"PusherError",data:{code:t.code,message:t.reason||t.message}}:null}};var Ct=kt;class Tt extends at{constructor(t,e){super(),this.id=t,this.transport=e,this.activityTimeout=e.activityTimeout,this.bindListeners()}handlesActivityChecks(){return this.transport.handlesActivityChecks()}send(t){return this.transport.send(t)}send_event(t,e,n){var r={event:t,data:e};return n&&(r.channel=n),W.debug("Event sent",r),this.send(Ct.encodeMessage(r))}ping(){this.transport.supportsPing()?this.transport.ping():this.send_event("pusher:ping",{})}close(){this.transport.close()}bindListeners(){var t={message:t=>{var e;try{e=Ct.decodeMessage(t)}catch(e){this.emit("error",{type:"MessageParseError",error:e,data:t.data})}if(void 0!==e){switch(W.debug("Event recd",e),e.event){case"pusher:error":this.emit("error",{type:"PusherError",data:e.data});break;case"pusher:ping":this.emit("ping");break;case"pusher:pong":this.emit("pong")}this.emit("message",e)}},activity:()=>{this.emit("activity")},error:t=>{this.emit("error",t)},closed:t=>{e(),t&&t.code&&this.handleCloseEvent(t),this.transport=null,this.emit("closed")}},e=()=>{B(t,(t,e)=>{this.transport.unbind(e,t)})};B(t,(t,e)=>{this.transport.bind(e,t)})}handleCloseEvent(t){var e=Ct.getCloseAction(t),n=Ct.getCloseError(t);n&&this.emit("error",n),e&&this.emit(e,{action:e,error:n})}}class Et{constructor(t,e){this.transport=t,this.callback=e,this.bindListeners()}close(){this.unbindListeners(),this.transport.close()}bindListeners(){this.onMessage=t=>{var e;this.unbindListeners();try{e=Ct.processHandshake(t)}catch(t){return this.finish("error",{error:t}),void this.transport.close()}"connected"===e.action?this.finish("connected",{connection:new Tt(e.id,this.transport),activityTimeout:e.activityTimeout}):(this.finish(e.action,{error:e.error}),this.transport.close())},this.onClosed=t=>{this.unbindListeners();var e=Ct.getCloseAction(t)||"backoff",n=Ct.getCloseError(t);this.finish(e,{error:n})},this.transport.bind("message",this.onMessage),this.transport.bind("closed",this.onClosed)}unbindListeners(){this.transport.unbind("message",this.onMessage),this.transport.unbind("closed",this.onClosed)}finish(t,e){this.callback(D({transport:this.transport,action:t},e))}}class Pt{constructor(t,e){this.timeline=t,this.options=e||{}}send(t,e){this.timeline.isEmpty()||this.timeline.send(ue.TimelineTransport.getAgent(this,t),e)}}class xt extends at{constructor(t,e){super((function(e,n){W.debug("No callbacks on "+t+" for "+e)})),this.name=t,this.pusher=e,this.subscribed=!1,this.subscriptionPending=!1,this.subscriptionCancelled=!1}authorize(t,e){return e(null,{auth:""})}trigger(t,e){if(0!==t.indexOf("client-"))throw new l("Event '"+t+"' does not start with 'client-'");if(!this.subscribed){var n=u("triggeringClientEvents");W.warn("Client event triggered before channel 'subscription_succeeded' event . "+n)}return this.pusher.send_event(t,e,this.name)}disconnect(){this.subscribed=!1,this.subscriptionPending=!1}handleEvent(t){var e=t.event,n=t.data;if("pusher_internal:subscription_succeeded"===e)this.handleSubscriptionSucceededEvent(t);else if("pusher_internal:subscription_count"===e)this.handleSubscriptionCountEvent(t);else if(0!==e.indexOf("pusher_internal:")){this.emit(e,n,{})}}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):this.emit("pusher:subscription_succeeded",t.data)}handleSubscriptionCountEvent(t){t.data.subscription_count&&(this.subscriptionCount=t.data.subscription_count),this.emit("pusher:subscription_count",t.data)}subscribe(){this.subscribed||(this.subscriptionPending=!0,this.subscriptionCancelled=!1,this.authorize(this.pusher.connection.socket_id,(t,e)=>{t?(this.subscriptionPending=!1,W.error(t.toString()),this.emit("pusher:subscription_error",Object.assign({},{type:"AuthError",error:t.message},t instanceof m?{status:t.status}:{}))):this.pusher.send_event("pusher:subscribe",{auth:e.auth,channel_data:e.channel_data,channel:this.name})}))}unsubscribe(){this.subscribed=!1,this.pusher.send_event("pusher:unsubscribe",{channel:this.name})}cancelSubscription(){this.subscriptionCancelled=!0}reinstateSubscription(){this.subscriptionCancelled=!1}}class At extends xt{authorize(t,e){return this.pusher.config.channelAuthorizer({channelName:this.name,socketId:t},e)}}class Ot{constructor(){this.reset()}get(t){return Object.prototype.hasOwnProperty.call(this.members,t)?{id:t,info:this.members[t]}:null}each(t){B(this.members,(e,n)=>{t(this.get(n))})}setMyID(t){this.myID=t}onSubscription(t){this.members=t.presence.hash,this.count=t.presence.count,this.me=this.get(this.myID)}addMember(t){return null===this.get(t.user_id)&&this.count++,this.members[t.user_id]=t.user_info,this.get(t.user_id)}removeMember(t){var e=this.get(t.user_id);return e&&(delete this.members[t.user_id],this.count--),e}reset(){this.members={},this.count=0,this.myID=null,this.me=null}}var Lt=function(t,e,n,r){return new(n||(n=Promise))((function(i,s){function o(t){try{c(r.next(t))}catch(t){s(t)}}function a(t){try{c(r.throw(t))}catch(t){s(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(o,a)}c((r=r.apply(t,e||[])).next())}))};class Rt extends At{constructor(t,e){super(t,e),this.members=new Ot}authorize(t,e){super.authorize(t,(t,n)=>Lt(this,void 0,void 0,(function*(){if(!t)if(null!=(n=n).channel_data){var r=JSON.parse(n.channel_data);this.members.setMyID(r.user_id)}else{if(yield this.pusher.user.signinDonePromise,null==this.pusher.user.user_data){let t=u("authorizationEndpoint");return W.error(`Invalid auth response for channel '${this.name}', expected 'channel_data' field. ${t}, or the user should be signed in.`),void e("Invalid auth response")}this.members.setMyID(this.pusher.user.user_data.id)}e(t,n)})))}handleEvent(t){var e=t.event;if(0===e.indexOf("pusher_internal:"))this.handleInternalEvent(t);else{var n=t.data,r={};t.user_id&&(r.user_id=t.user_id),this.emit(e,n,r)}}handleInternalEvent(t){var e=t.event,n=t.data;switch(e){case"pusher_internal:subscription_succeeded":this.handleSubscriptionSucceededEvent(t);break;case"pusher_internal:subscription_count":this.handleSubscriptionCountEvent(t);break;case"pusher_internal:member_added":var r=this.members.addMember(n);this.emit("pusher:member_added",r);break;case"pusher_internal:member_removed":var i=this.members.removeMember(n);i&&this.emit("pusher:member_removed",i)}}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):(this.members.onSubscription(t.data),this.emit("pusher:subscription_succeeded",this.members))}disconnect(){this.members.reset(),super.disconnect()}}var Ut=n(1),Mt=n(0);class It extends At{constructor(t,e,n){super(t,e),this.key=null,this.nacl=n}authorize(t,e){super.authorize(t,(t,n)=>{if(t)return void e(t,n);let r=n.shared_secret;r?(this.key=Object(Mt.decode)(r),delete n.shared_secret,e(null,n)):e(new Error("No shared_secret key in auth payload for encrypted channel: "+this.name),null)})}trigger(t,e){throw new v("Client events are not currently supported for encrypted channels")}handleEvent(t){var e=t.event,n=t.data;0!==e.indexOf("pusher_internal:")&&0!==e.indexOf("pusher:")?this.handleEncryptedEvent(e,n):super.handleEvent(t)}handleEncryptedEvent(t,e){if(!this.key)return void W.debug("Received encrypted event before key has been retrieved from the authEndpoint");if(!e.ciphertext||!e.nonce)return void W.error("Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: "+e);let n=Object(Mt.decode)(e.ciphertext);if(n.length<this.nacl.secretbox.overheadLength)return void W.error(`Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${n.length}`);let r=Object(Mt.decode)(e.nonce);if(r.length<this.nacl.secretbox.nonceLength)return void W.error(`Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${r.length}`);let i=this.nacl.secretbox.open(n,r,this.key);if(null===i)return W.debug("Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint..."),void this.authorize(this.pusher.connection.socket_id,(e,s)=>{e?W.error(`Failed to make a request to the authEndpoint: ${s}. Unable to fetch new key, so dropping encrypted event`):(i=this.nacl.secretbox.open(n,r,this.key),null!==i?this.emit(t,this.getDataToEmit(i)):W.error("Failed to decrypt event with new key. Dropping encrypted event"))});this.emit(t,this.getDataToEmit(i))}getDataToEmit(t){let e=Object(Ut.decode)(t);try{return JSON.parse(e)}catch(t){return e}}}class Dt extends at{constructor(t,e){super(),this.state="initialized",this.connection=null,this.key=t,this.options=e,this.timeline=this.options.timeline,this.usingTLS=this.options.useTLS,this.errorCallbacks=this.buildErrorCallbacks(),this.connectionCallbacks=this.buildConnectionCallbacks(this.errorCallbacks),this.handshakeCallbacks=this.buildHandshakeCallbacks(this.errorCallbacks);var n=ue.getNetwork();n.bind("online",()=>{this.timeline.info({netinfo:"online"}),"connecting"!==this.state&&"unavailable"!==this.state||this.retryIn(0)}),n.bind("offline",()=>{this.timeline.info({netinfo:"offline"}),this.connection&&this.sendActivityCheck()}),this.updateStrategy()}connect(){this.connection||this.runner||(this.strategy.isSupported()?(this.updateState("connecting"),this.startConnecting(),this.setUnavailableTimer()):this.updateState("failed"))}send(t){return!!this.connection&&this.connection.send(t)}send_event(t,e,n){return!!this.connection&&this.connection.send_event(t,e,n)}disconnect(){this.disconnectInternally(),this.updateState("disconnected")}isUsingTLS(){return this.usingTLS}startConnecting(){var t=(e,n)=>{e?this.runner=this.strategy.connect(0,t):"error"===n.action?(this.emit("error",{type:"HandshakeError",error:n.error}),this.timeline.error({handshakeError:n.error})):(this.abortConnecting(),this.handshakeCallbacks[n.action](n))};this.runner=this.strategy.connect(0,t)}abortConnecting(){this.runner&&(this.runner.abort(),this.runner=null)}disconnectInternally(){(this.abortConnecting(),this.clearRetryTimer(),this.clearUnavailableTimer(),this.connection)&&this.abandonConnection().close()}updateStrategy(){this.strategy=this.options.getStrategy({key:this.key,timeline:this.timeline,useTLS:this.usingTLS})}retryIn(t){this.timeline.info({action:"retry",delay:t}),t>0&&this.emit("connecting_in",Math.round(t/1e3)),this.retryTimer=new U(t||0,()=>{this.disconnectInternally(),this.connect()})}clearRetryTimer(){this.retryTimer&&(this.retryTimer.ensureAborted(),this.retryTimer=null)}setUnavailableTimer(){this.unavailableTimer=new U(this.options.unavailableTimeout,()=>{this.updateState("unavailable")})}clearUnavailableTimer(){this.unavailableTimer&&this.unavailableTimer.ensureAborted()}sendActivityCheck(){this.stopActivityCheck(),this.connection.ping(),this.activityTimer=new U(this.options.pongTimeout,()=>{this.timeline.error({pong_timed_out:this.options.pongTimeout}),this.retryIn(0)})}resetActivityCheck(){this.stopActivityCheck(),this.connection&&!this.connection.handlesActivityChecks()&&(this.activityTimer=new U(this.activityTimeout,()=>{this.sendActivityCheck()}))}stopActivityCheck(){this.activityTimer&&this.activityTimer.ensureAborted()}buildConnectionCallbacks(t){return D({},t,{message:t=>{this.resetActivityCheck(),this.emit("message",t)},ping:()=>{this.send_event("pusher:pong",{})},activity:()=>{this.resetActivityCheck()},error:t=>{this.emit("error",t)},closed:()=>{this.abandonConnection(),this.shouldRetry()&&this.retryIn(1e3)}})}buildHandshakeCallbacks(t){return D({},t,{connected:t=>{this.activityTimeout=Math.min(this.options.activityTimeout,t.activityTimeout,t.connection.activityTimeout||1/0),this.clearUnavailableTimer(),this.setConnection(t.connection),this.socket_id=this.connection.id,this.updateState("connected",{socket_id:this.socket_id})}})}buildErrorCallbacks(){let t=t=>e=>{e.error&&this.emit("error",{type:"WebSocketError",error:e.error}),t(e)};return{tls_only:t(()=>{this.usingTLS=!0,this.updateStrategy(),this.retryIn(0)}),refused:t(()=>{this.disconnect()}),backoff:t(()=>{this.retryIn(1e3)}),retry:t(()=>{this.retryIn(0)})}}setConnection(t){for(var e in this.connection=t,this.connectionCallbacks)this.connection.bind(e,this.connectionCallbacks[e]);this.resetActivityCheck()}abandonConnection(){if(this.connection){for(var t in this.stopActivityCheck(),this.connectionCallbacks)this.connection.unbind(t,this.connectionCallbacks[t]);var e=this.connection;return this.connection=null,e}}updateState(t,e){var n=this.state;if(this.state=t,n!==t){var r=t;"connected"===r&&(r+=" with new socket ID "+e.socket_id),W.debug("State changed",n+" -> "+r),this.timeline.info({state:t,params:e}),this.emit("state_change",{previous:n,current:t}),this.emit(t,e)}}shouldRetry(){return"connecting"===this.state||"connected"===this.state}}class Nt{constructor(){this.channels={}}add(t,e){return this.channels[t]||(this.channels[t]=function(t,e){if(0===t.indexOf("private-encrypted-")){if(e.config.nacl)return jt.createEncryptedChannel(t,e,e.config.nacl);let n="Tried to subscribe to a private-encrypted- channel but no nacl implementation available",r=u("encryptedChannelSupport");throw new v(`${n}. ${r}`)}if(0===t.indexOf("private-"))return jt.createPrivateChannel(t,e);if(0===t.indexOf("presence-"))return jt.createPresenceChannel(t,e);if(0===t.indexOf("#"))throw new d('Cannot create a channel with name "'+t+'".');return jt.createChannel(t,e)}(t,e)),this.channels[t]}all(){return function(t){var e=[];return B(t,(function(t){e.push(t)})),e}(this.channels)}find(t){return this.channels[t]}remove(t){var e=this.channels[t];return delete this.channels[t],e}disconnect(){B(this.channels,(function(t){t.disconnect()}))}}var jt={createChannels:()=>new Nt,createConnectionManager:(t,e)=>new Dt(t,e),createChannel:(t,e)=>new xt(t,e),createPrivateChannel:(t,e)=>new At(t,e),createPresenceChannel:(t,e)=>new Rt(t,e),createEncryptedChannel:(t,e,n)=>new It(t,e,n),createTimelineSender:(t,e)=>new Pt(t,e),createHandshake:(t,e)=>new Et(t,e),createAssistantToTheTransportManager:(t,e,n)=>new St(t,e,n)};class Bt{constructor(t){this.options=t||{},this.livesLeft=this.options.lives||1/0}getAssistant(t){return jt.createAssistantToTheTransportManager(this,t,{minPingDelay:this.options.minPingDelay,maxPingDelay:this.options.maxPingDelay})}isAlive(){return this.livesLeft>0}reportDeath(){this.livesLeft-=1}}class zt{constructor(t,e){this.strategies=t,this.loop=Boolean(e.loop),this.failFast=Boolean(e.failFast),this.timeout=e.timeout,this.timeoutLimit=e.timeoutLimit}isSupported(){return J(this.strategies,I.method("isSupported"))}connect(t,e){var n=this.strategies,r=0,i=this.timeout,s=null,o=(a,c)=>{c?e(null,c):(r+=1,this.loop&&(r%=n.length),r<n.length?(i&&(i*=2,this.timeoutLimit&&(i=Math.min(i,this.timeoutLimit))),s=this.tryStrategy(n[r],t,{timeout:i,failFast:this.failFast},o)):e(!0))};return s=this.tryStrategy(n[r],t,{timeout:i,failFast:this.failFast},o),{abort:function(){s.abort()},forceMinPriority:function(e){t=e,s&&s.forceMinPriority(e)}}}tryStrategy(t,e,n,r){var i=null,s=null;return n.timeout>0&&(i=new U(n.timeout,(function(){s.abort(),r(!0)}))),s=t.connect(e,(function(t,e){t&&i&&i.isRunning()&&!n.failFast||(i&&i.ensureAborted(),r(t,e))})),{abort:function(){i&&i.ensureAborted(),s.abort()},forceMinPriority:function(t){s.forceMinPriority(t)}}}}class Ht{constructor(t){this.strategies=t}isSupported(){return J(this.strategies,I.method("isSupported"))}connect(t,e){return function(t,e,n){var r=q(t,(function(t,r,i,s){return t.connect(e,n(r,s))}));return{abort:function(){H(r,qt)},forceMinPriority:function(t){H(r,(function(e){e.forceMinPriority(t)}))}}}(this.strategies,t,(function(t,n){return function(r,i){n[t].error=r,r?function(t){return function(t,e){for(var n=0;n<t.length;n++)if(!e(t[n],n,t))return!1;return!0}(t,(function(t){return Boolean(t.error)}))}(n)&&e(!0):(H(n,(function(t){t.forceMinPriority(i.transport.priority)})),e(null,i))}}))}}function qt(t){t.error||t.aborted||(t.abort(),t.aborted=!0)}class Ft{constructor(t,e,n){this.strategy=t,this.transports=e,this.ttl=n.ttl||18e5,this.usingTLS=n.useTLS,this.timeline=n.timeline}isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.usingTLS,r=function(t){var e=ue.getLocalStorage();if(e)try{var n=e[Xt(t)];if(n)return JSON.parse(n)}catch(e){Jt(t)}return null}(n),i=r&&r.cacheSkipCount?r.cacheSkipCount:0,s=[this.strategy];if(r&&r.timestamp+this.ttl>=I.now()){var o=this.transports[r.transport];o&&(["ws","wss"].includes(r.transport)||i>3?(this.timeline.info({cached:!0,transport:r.transport,latency:r.latency}),s.push(new zt([o],{timeout:2*r.latency+1e3,failFast:!0}))):i++)}var a=I.now(),c=s.pop().connect(t,(function r(o,h){o?(Jt(n),s.length>0?(a=I.now(),c=s.pop().connect(t,r)):e(o)):(!function(t,e,n,r){var i=ue.getLocalStorage();if(i)try{i[Xt(t)]=$({timestamp:I.now(),transport:e,latency:n,cacheSkipCount:r})}catch(t){}}(n,h.transport.name,I.now()-a,i),e(null,h))}));return{abort:function(){c.abort()},forceMinPriority:function(e){t=e,c&&c.forceMinPriority(e)}}}}function Xt(t){return"pusherTransport"+(t?"TLS":"NonTLS")}function Jt(t){var e=ue.getLocalStorage();if(e)try{delete e[Xt(t)]}catch(t){}}class Yt{constructor(t,{delay:e}){this.strategy=t,this.options={delay:e}}isSupported(){return this.strategy.isSupported()}connect(t,e){var n,r=this.strategy,i=new U(this.options.delay,(function(){n=r.connect(t,e)}));return{abort:function(){i.ensureAborted(),n&&n.abort()},forceMinPriority:function(e){t=e,n&&n.forceMinPriority(e)}}}}class Kt{constructor(t,e,n){this.test=t,this.trueBranch=e,this.falseBranch=n}isSupported(){return(this.test()?this.trueBranch:this.falseBranch).isSupported()}connect(t,e){return(this.test()?this.trueBranch:this.falseBranch).connect(t,e)}}class $t{constructor(t){this.strategy=t}isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.strategy.connect(t,(function(t,r){r&&n.abort(),e(t,r)}));return n}}function Wt(t){return function(){return t.isSupported()}}var Gt=function(t,e,n){var r={};function i(e,i,s,o,a){var c=n(t,e,i,s,o,a);return r[e]=c,c}var s,o=Object.assign({},e,{hostNonTLS:t.wsHost+":"+t.wsPort,hostTLS:t.wsHost+":"+t.wssPort,httpPath:t.wsPath}),a=Object.assign({},o,{useTLS:!0}),c=Object.assign({},e,{hostNonTLS:t.httpHost+":"+t.httpPort,hostTLS:t.httpHost+":"+t.httpsPort,httpPath:t.httpPath}),h={loop:!0,timeout:15e3,timeoutLimit:6e4},u=new Bt({minPingDelay:1e4,maxPingDelay:t.activityTimeout}),l=new Bt({lives:2,minPingDelay:1e4,maxPingDelay:t.activityTimeout}),d=i("ws","ws",3,o,u),p=i("wss","ws",3,a,u),f=i("sockjs","sockjs",1,c),g=i("xhr_streaming","xhr_streaming",1,c,l),v=i("xdr_streaming","xdr_streaming",1,c,l),b=i("xhr_polling","xhr_polling",1,c),y=i("xdr_polling","xdr_polling",1,c),m=new zt([d],h),w=new zt([p],h),_=new zt([f],h),S=new zt([new Kt(Wt(g),g,v)],h),k=new zt([new Kt(Wt(b),b,y)],h),C=new zt([new Kt(Wt(S),new Ht([S,new Yt(k,{delay:4e3})]),k)],h),T=new Kt(Wt(C),C,_);return s=e.useTLS?new Ht([m,new Yt(T,{delay:2e3})]):new Ht([m,new Yt(w,{delay:2e3}),new Yt(T,{delay:5e3})]),new Ft(new $t(new Kt(Wt(d),s,T)),r,{ttl:18e5,timeline:e.timeline,useTLS:e.useTLS})},Vt={getRequest:function(t){var e=new window.XDomainRequest;return e.ontimeout=function(){t.emit("error",new p),t.close()},e.onerror=function(e){t.emit("error",e),t.close()},e.onprogress=function(){e.responseText&&e.responseText.length>0&&t.onChunk(200,e.responseText)},e.onload=function(){e.responseText&&e.responseText.length>0&&t.onChunk(200,e.responseText),t.emit("finished",200),t.close()},e},abortRequest:function(t){t.ontimeout=t.onerror=t.onprogress=t.onload=null,t.abort()}};class Zt extends at{constructor(t,e,n){super(),this.hooks=t,this.method=e,this.url=n}start(t){this.position=0,this.xhr=this.hooks.getRequest(this),this.unloader=()=>{this.close()},ue.addUnloadListener(this.unloader),this.xhr.open(this.method,this.url,!0),this.xhr.setRequestHeader&&this.xhr.setRequestHeader("Content-Type","application/json"),this.xhr.send(t)}close(){this.unloader&&(ue.removeUnloadListener(this.unloader),this.unloader=null),this.xhr&&(this.hooks.abortRequest(this.xhr),this.xhr=null)}onChunk(t,e){for(;;){var n=this.advanceBuffer(e);if(!n)break;this.emit("chunk",{status:t,data:n})}this.isBufferTooLong(e)&&this.emit("buffer_too_long")}advanceBuffer(t){var e=t.slice(this.position),n=e.indexOf("\n");return-1!==n?(this.position+=n+1,e.slice(0,n)):null}isBufferTooLong(t){return this.position===t.length&&t.length>262144}}var Qt;!function(t){t[t.CONNECTING=0]="CONNECTING",t[t.OPEN=1]="OPEN",t[t.CLOSED=3]="CLOSED"}(Qt||(Qt={}));var te=Qt,ee=1;function ne(t){var e=-1===t.indexOf("?")?"?":"&";return t+e+"t="+ +new Date+"&n="+ee++}function re(t){return ue.randomInt(t)}var ie,se=class{constructor(t,e){this.hooks=t,this.session=re(1e3)+"/"+function(t){for(var e=[],n=0;n<t;n++)e.push(re(32).toString(32));return e.join("")}(8),this.location=function(t){var e=/([^\?]*)\/*(\??.*)/.exec(t);return{base:e[1],queryString:e[2]}}(e),this.readyState=te.CONNECTING,this.openStream()}send(t){return this.sendRaw(JSON.stringify([t]))}ping(){this.hooks.sendHeartbeat(this)}close(t,e){this.onClose(t,e,!0)}sendRaw(t){if(this.readyState!==te.OPEN)return!1;try{return ue.createSocketRequest("POST",ne((e=this.location,n=this.session,e.base+"/"+n+"/xhr_send"))).start(t),!0}catch(t){return!1}var e,n}reconnect(){this.closeStream(),this.openStream()}onClose(t,e,n){this.closeStream(),this.readyState=te.CLOSED,this.onclose&&this.onclose({code:t,reason:e,wasClean:n})}onChunk(t){var e;if(200===t.status)switch(this.readyState===te.OPEN&&this.onActivity(),t.data.slice(0,1)){case"o":e=JSON.parse(t.data.slice(1)||"{}"),this.onOpen(e);break;case"a":e=JSON.parse(t.data.slice(1)||"[]");for(var n=0;n<e.length;n++)this.onEvent(e[n]);break;case"m":e=JSON.parse(t.data.slice(1)||"null"),this.onEvent(e);break;case"h":this.hooks.onHeartbeat(this);break;case"c":e=JSON.parse(t.data.slice(1)||"[]"),this.onClose(e[0],e[1],!0)}}onOpen(t){var e,n,r;this.readyState===te.CONNECTING?(t&&t.hostname&&(this.location.base=(e=this.location.base,n=t.hostname,(r=/(https?:\/\/)([^\/:]+)((\/|:)?.*)/.exec(e))[1]+n+r[3])),this.readyState=te.OPEN,this.onopen&&this.onopen()):this.onClose(1006,"Server lost session",!0)}onEvent(t){this.readyState===te.OPEN&&this.onmessage&&this.onmessage({data:t})}onActivity(){this.onactivity&&this.onactivity()}onError(t){this.onerror&&this.onerror(t)}openStream(){this.stream=ue.createSocketRequest("POST",ne(this.hooks.getReceiveURL(this.location,this.session))),this.stream.bind("chunk",t=>{this.onChunk(t)}),this.stream.bind("finished",t=>{this.hooks.onFinished(this,t)}),this.stream.bind("buffer_too_long",()=>{this.reconnect()});try{this.stream.start()}catch(t){I.defer(()=>{this.onError(t),this.onClose(1006,"Could not start streaming",!1)})}}closeStream(){this.stream&&(this.stream.unbind_all(),this.stream.close(),this.stream=null)}},oe={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr_streaming"+t.queryString},onHeartbeat:function(t){t.sendRaw("[]")},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){t.onClose(1006,"Connection interrupted ("+e+")",!1)}},ae={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr"+t.queryString},onHeartbeat:function(){},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){200===e?t.reconnect():t.onClose(1006,"Connection interrupted ("+e+")",!1)}},ce={getRequest:function(t){var e=new(ue.getXHRAPI());return e.onreadystatechange=e.onprogress=function(){switch(e.readyState){case 3:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText);break;case 4:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText),t.emit("finished",e.status),t.close()}},e},abortRequest:function(t){t.onreadystatechange=null,t.abort()}},he={createStreamingSocket(t){return this.createSocket(oe,t)},createPollingSocket(t){return this.createSocket(ae,t)},createSocket:(t,e)=>new se(t,e),createXHR(t,e){return this.createRequest(ce,t,e)},createRequest:(t,e,n)=>new Zt(t,e,n),createXDR:function(t,e){return this.createRequest(Vt,t,e)}},ue={nextAuthCallbackID:1,auth_callbacks:{},ScriptReceivers:i,DependenciesReceivers:o,getDefaultStrategy:Gt,Transports:wt,transportConnectionInitializer:function(){var t=this;t.timeline.info(t.buildTimelineMessage({transport:t.name+(t.options.useTLS?"s":"")})),t.hooks.isInitialized()?t.changeState("initialized"):t.hooks.file?(t.changeState("initializing"),a.load(t.hooks.file,{useTLS:t.options.useTLS},(function(e,n){t.hooks.isInitialized()?(t.changeState("initialized"),n(!0)):(e&&t.onError(e),t.onClose(),n(!1))}))):t.onClose()},HTTPFactory:he,TimelineTransport:Q,getXHRAPI:()=>window.XMLHttpRequest,getWebSocketAPI:()=>window.WebSocket||window.MozWebSocket,setup(t){window.Pusher=t;var e=()=>{this.onDocumentBody(t.ready)};window.JSON?e():a.load("json2",{},e)},getDocument:()=>document,getProtocol(){return this.getDocument().location.protocol},getAuthorizers:()=>({ajax:w,jsonp:G}),onDocumentBody(t){document.body?t():setTimeout(()=>{this.onDocumentBody(t)},0)},createJSONPRequest:(t,e)=>new Z(t,e),createScriptRequest:t=>new V(t),getLocalStorage(){try{return window.localStorage}catch(t){return}},createXHR(){return this.getXHRAPI()?this.createXMLHttpRequest():this.createMicrosoftXHR()},createXMLHttpRequest(){return new(this.getXHRAPI())},createMicrosoftXHR:()=>new ActiveXObject("Microsoft.XMLHTTP"),getNetwork:()=>_t,createWebSocket(t){return new(this.getWebSocketAPI())(t)},createSocketRequest(t,e){if(this.isXHRSupported())return this.HTTPFactory.createXHR(t,e);if(this.isXDRSupported(0===e.indexOf("https:")))return this.HTTPFactory.createXDR(t,e);throw"Cross-origin HTTP requests are not supported"},isXHRSupported(){var t=this.getXHRAPI();return Boolean(t)&&void 0!==(new t).withCredentials},isXDRSupported(t){var e=t?"https:":"http:",n=this.getProtocol();return Boolean(window.XDomainRequest)&&n===e},addUnloadListener(t){void 0!==window.addEventListener?window.addEventListener("unload",t,!1):void 0!==window.attachEvent&&window.attachEvent("onunload",t)},removeUnloadListener(t){void 0!==window.addEventListener?window.removeEventListener("unload",t,!1):void 0!==window.detachEvent&&window.detachEvent("onunload",t)},randomInt:t=>Math.floor((window.crypto||window.msCrypto).getRandomValues(new Uint32Array(1))[0]/Math.pow(2,32)*t)};!function(t){t[t.ERROR=3]="ERROR",t[t.INFO=6]="INFO",t[t.DEBUG=7]="DEBUG"}(ie||(ie={}));var le=ie;class de{constructor(t,e,n){this.key=t,this.session=e,this.events=[],this.options=n||{},this.sent=0,this.uniqueID=0}log(t,e){t<=this.options.level&&(this.events.push(D({},e,{timestamp:I.now()})),this.options.limit&&this.events.length>this.options.limit&&this.events.shift())}error(t){this.log(le.ERROR,t)}info(t){this.log(le.INFO,t)}debug(t){this.log(le.DEBUG,t)}isEmpty(){return 0===this.events.length}send(t,e){var n=D({session:this.session,bundle:this.sent+1,key:this.key,lib:"js",version:this.options.version,cluster:this.options.cluster,features:this.options.features,timeline:this.events},this.options.params);return this.events=[],t(n,(t,n)=>{t||this.sent++,e&&e(t,n)}),!0}generateUniqueID(){return this.uniqueID++,this.uniqueID}}class pe{constructor(t,e,n,r){this.name=t,this.priority=e,this.transport=n,this.options=r||{}}isSupported(){return this.transport.isSupported({useTLS:this.options.useTLS})}connect(t,e){if(!this.isSupported())return fe(new y,e);if(this.priority<t)return fe(new f,e);var n=!1,r=this.transport.createConnection(this.name,this.priority,this.options.key,this.options),i=null,s=function(){r.unbind("initialized",s),r.connect()},o=function(){i=jt.createHandshake(r,(function(t){n=!0,h(),e(null,t)}))},a=function(t){h(),e(t)},c=function(){var t;h(),t=$(r),e(new g(t))},h=function(){r.unbind("initialized",s),r.unbind("open",o),r.unbind("error",a),r.unbind("closed",c)};return r.bind("initialized",s),r.bind("open",o),r.bind("error",a),r.bind("closed",c),r.initialize(),{abort:()=>{n||(h(),i?i.close():r.close())},forceMinPriority:t=>{n||this.priority<t&&(i?i.close():r.close())}}}}function fe(t,e){return I.defer((function(){e(t)})),{abort:function(){},forceMinPriority:function(){}}}const{Transports:ge}=ue;var ve=function(t,e,n,r,i,s){var o,a=ge[n];if(!a)throw new b(n);return!(t.enabledTransports&&-1===j(t.enabledTransports,e)||t.disabledTransports&&-1!==j(t.disabledTransports,e))?(i=Object.assign({ignoreNullOrigin:t.ignoreNullOrigin},i),o=new pe(e,r,s?s.getAssistant(a):a,i)):o=be,o},be={isSupported:function(){return!1},connect:function(t,e){var n=I.defer((function(){e(new y)}));return{abort:function(){n.ensureAborted()},forceMinPriority:function(){}}}};function ye(t){if(null==t)throw"You must pass an options object";if(null==t.cluster)throw"Options object must provide a cluster";"disableStats"in t&&W.warn("The disableStats option is deprecated in favor of enableStats")}var me=t=>{if(void 0===ue.getAuthorizers()[t.transport])throw`'${t.transport}' is not a recognized auth transport`;return(e,n)=>{const r=((t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var r in e.params)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(e.params[r]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r])}return n})(e,t);ue.getAuthorizers()[t.transport](ue,r,t,h.UserAuthentication,n)}};var we=t=>{if(void 0===ue.getAuthorizers()[t.transport])throw`'${t.transport}' is not a recognized auth transport`;return(e,n)=>{const r=((t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var r in n+="&channel_name="+encodeURIComponent(t.channelName),e.params)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(e.params[r]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r])}return n})(e,t);ue.getAuthorizers()[t.transport](ue,r,t,h.ChannelAuthorization,n)}};function _e(t){return t.httpHost?t.httpHost:t.cluster?`sockjs-${t.cluster}.pusher.com`:s.httpHost}function Se(t){return t.wsHost?t.wsHost:`ws-${t.cluster}.pusher.com`}function ke(t){return"https:"===ue.getProtocol()||!1!==t.forceTLS}function Ce(t){return"enableStats"in t?t.enableStats:"disableStats"in t&&!t.disableStats}function Te(t){const e=Object.assign(Object.assign({},s.userAuthentication),t.userAuthentication);return"customHandler"in e&&null!=e.customHandler?e.customHandler:me(e)}function Ee(t,e){const n=function(t,e){let n;return"channelAuthorization"in t?n=Object.assign(Object.assign({},s.channelAuthorization),t.channelAuthorization):(n={transport:t.authTransport||s.authTransport,endpoint:t.authEndpoint||s.authEndpoint},"auth"in t&&("params"in t.auth&&(n.params=t.auth.params),"headers"in t.auth&&(n.headers=t.auth.headers)),"authorizer"in t&&(n.customHandler=((t,e,n)=>{const r={authTransport:e.transport,authEndpoint:e.endpoint,auth:{params:e.params,headers:e.headers}};return(e,i)=>{const s=t.channel(e.channelName);n(s,r).authorize(e.socketId,i)}})(e,n,t.authorizer))),n}(t,e);return"customHandler"in n&&null!=n.customHandler?n.customHandler:we(n)}class Pe extends at{constructor(t){super((function(t,e){W.debug("No callbacks on watchlist events for "+t)})),this.pusher=t,this.bindWatchlistInternalEvent()}handleEvent(t){t.data.events.forEach(t=>{this.emit(t.name,t)})}bindWatchlistInternalEvent(){this.pusher.connection.bind("message",t=>{"pusher_internal:watchlist_events"===t.event&&this.handleEvent(t)})}}var xe=function(){let t,e;return{promise:new Promise((n,r)=>{t=n,e=r}),resolve:t,reject:e}};class Ae extends at{constructor(t){super((function(t,e){W.debug("No callbacks on user for "+t)})),this.signin_requested=!1,this.user_data=null,this.serverToUserChannel=null,this.signinDonePromise=null,this._signinDoneResolve=null,this._onAuthorize=(t,e)=>{if(t)return W.warn("Error during signin: "+t),void this._cleanup();this.pusher.send_event("pusher:signin",{auth:e.auth,user_data:e.user_data})},this.pusher=t,this.pusher.connection.bind("state_change",({previous:t,current:e})=>{"connected"!==t&&"connected"===e&&this._signin(),"connected"===t&&"connected"!==e&&(this._cleanup(),this._newSigninPromiseIfNeeded())}),this.watchlist=new Pe(t),this.pusher.connection.bind("message",t=>{"pusher:signin_success"===t.event&&this._onSigninSuccess(t.data),this.serverToUserChannel&&this.serverToUserChannel.name===t.channel&&this.serverToUserChannel.handleEvent(t)})}signin(){this.signin_requested||(this.signin_requested=!0,this._signin())}_signin(){this.signin_requested&&(this._newSigninPromiseIfNeeded(),"connected"===this.pusher.connection.state&&this.pusher.config.userAuthenticator({socketId:this.pusher.connection.socket_id},this._onAuthorize))}_onSigninSuccess(t){try{this.user_data=JSON.parse(t.user_data)}catch(e){return W.error("Failed parsing user data after signin: "+t.user_data),void this._cleanup()}if("string"!=typeof this.user_data.id||""===this.user_data.id)return W.error("user_data doesn't contain an id. user_data: "+this.user_data),void this._cleanup();this._signinDoneResolve(),this._subscribeChannels()}_subscribeChannels(){this.serverToUserChannel=new xt("#server-to-user-"+this.user_data.id,this.pusher),this.serverToUserChannel.bind_global((t,e)=>{0!==t.indexOf("pusher_internal:")&&0!==t.indexOf("pusher:")&&this.emit(t,e)}),(t=>{t.subscriptionPending&&t.subscriptionCancelled?t.reinstateSubscription():t.subscriptionPending||"connected"!==this.pusher.connection.state||t.subscribe()})(this.serverToUserChannel)}_cleanup(){this.user_data=null,this.serverToUserChannel&&(this.serverToUserChannel.unbind_all(),this.serverToUserChannel.disconnect(),this.serverToUserChannel=null),this.signin_requested&&this._signinDoneResolve()}_newSigninPromiseIfNeeded(){if(!this.signin_requested)return;if(this.signinDonePromise&&!this.signinDonePromise.done)return;const{promise:t,resolve:e,reject:n}=xe();t.done=!1;const r=()=>{t.done=!0};t.then(r).catch(r),this.signinDonePromise=t,this._signinDoneResolve=e}}class Oe{static ready(){Oe.isReady=!0;for(var t=0,e=Oe.instances.length;t<e;t++)Oe.instances[t].connect()}static getClientFeatures(){return z(X({ws:ue.Transports.ws},(function(t){return t.isSupported({})})))}constructor(t,e){!function(t){if(null==t)throw"You must pass your app key when you instantiate Pusher."}(t),ye(e),this.key=t,this.config=function(t,e){let n={activityTimeout:t.activityTimeout||s.activityTimeout,cluster:t.cluster,httpPath:t.httpPath||s.httpPath,httpPort:t.httpPort||s.httpPort,httpsPort:t.httpsPort||s.httpsPort,pongTimeout:t.pongTimeout||s.pongTimeout,statsHost:t.statsHost||s.stats_host,unavailableTimeout:t.unavailableTimeout||s.unavailableTimeout,wsPath:t.wsPath||s.wsPath,wsPort:t.wsPort||s.wsPort,wssPort:t.wssPort||s.wssPort,enableStats:Ce(t),httpHost:_e(t),useTLS:ke(t),wsHost:Se(t),userAuthenticator:Te(t),channelAuthorizer:Ee(t,e)};return"disabledTransports"in t&&(n.disabledTransports=t.disabledTransports),"enabledTransports"in t&&(n.enabledTransports=t.enabledTransports),"ignoreNullOrigin"in t&&(n.ignoreNullOrigin=t.ignoreNullOrigin),"timelineParams"in t&&(n.timelineParams=t.timelineParams),"nacl"in t&&(n.nacl=t.nacl),n}(e,this),this.channels=jt.createChannels(),this.global_emitter=new at,this.sessionID=ue.randomInt(1e9),this.timeline=new de(this.key,this.sessionID,{cluster:this.config.cluster,features:Oe.getClientFeatures(),params:this.config.timelineParams||{},limit:50,level:le.INFO,version:s.VERSION}),this.config.enableStats&&(this.timelineSender=jt.createTimelineSender(this.timeline,{host:this.config.statsHost,path:"/timeline/v2/"+ue.TimelineTransport.name}));this.connection=jt.createConnectionManager(this.key,{getStrategy:t=>ue.getDefaultStrategy(this.config,t,ve),timeline:this.timeline,activityTimeout:this.config.activityTimeout,pongTimeout:this.config.pongTimeout,unavailableTimeout:this.config.unavailableTimeout,useTLS:Boolean(this.config.useTLS)}),this.connection.bind("connected",()=>{this.subscribeAll(),this.timelineSender&&this.timelineSender.send(this.connection.isUsingTLS())}),this.connection.bind("message",t=>{var e=0===t.event.indexOf("pusher_internal:");if(t.channel){var n=this.channel(t.channel);n&&n.handleEvent(t)}e||this.global_emitter.emit(t.event,t.data)}),this.connection.bind("connecting",()=>{this.channels.disconnect()}),this.connection.bind("disconnected",()=>{this.channels.disconnect()}),this.connection.bind("error",t=>{W.warn(t)}),Oe.instances.push(this),this.timeline.info({instances:Oe.instances.length}),this.user=new Ae(this),Oe.isReady&&this.connect()}channel(t){return this.channels.find(t)}allChannels(){return this.channels.all()}connect(){if(this.connection.connect(),this.timelineSender&&!this.timelineSenderTimer){var t=this.connection.isUsingTLS(),e=this.timelineSender;this.timelineSenderTimer=new M(6e4,(function(){e.send(t)}))}}disconnect(){this.connection.disconnect(),this.timelineSenderTimer&&(this.timelineSenderTimer.ensureAborted(),this.timelineSenderTimer=null)}bind(t,e,n){return this.global_emitter.bind(t,e,n),this}unbind(t,e,n){return this.global_emitter.unbind(t,e,n),this}bind_global(t){return this.global_emitter.bind_global(t),this}unbind_global(t){return this.global_emitter.unbind_global(t),this}unbind_all(t){return this.global_emitter.unbind_all(),this}subscribeAll(){var t;for(t in this.channels.channels)this.channels.channels.hasOwnProperty(t)&&this.subscribe(t)}subscribe(t){var e=this.channels.add(t,this);return e.subscriptionPending&&e.subscriptionCancelled?e.reinstateSubscription():e.subscriptionPending||"connected"!==this.connection.state||e.subscribe(),e}unsubscribe(t){var e=this.channels.find(t);e&&e.subscriptionPending?e.cancelSubscription():(e=this.channels.remove(t))&&e.subscribed&&e.unsubscribe()}send_event(t,e,n){return this.connection.send_event(t,e,n)}shouldUseTLS(){return this.config.useTLS}signin(){this.user.signin()}}Oe.instances=[],Oe.isReady=!1,Oe.logToConsole=!1,Oe.Runtime=ue,Oe.ScriptReceivers=ue.ScriptReceivers,Oe.DependenciesReceivers=ue.DependenciesReceivers,Oe.auth_callbacks=ue.auth_callbacks;var Le=Oe;ue.setup(Oe);var Re=n(2);class Ue extends Le{constructor(t,e){Le.logToConsole=Ue.logToConsole,Le.log=Ue.log,ye(e),e.nacl=Re,super(t,e)}}}])}));
//# sourceMappingURL=pusher-with-encryption.min.js.map