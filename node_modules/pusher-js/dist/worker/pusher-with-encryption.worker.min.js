/*!
 * Pusher JavaScript Library v8.4.0
 * https://pusher.com/
 *
 * Copyright 2020, <PERSON>usher
 * Released under the MIT licence.
 */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Pusher=e():t.Pusher=e()}(this,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=3)}([function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var s=function(){function t(t){void 0===t&&(t="="),this._paddingCharacter=t}return t.prototype.encodedLength=function(t){return this._paddingCharacter?(t+2)/3*4|0:(8*t+5)/6|0},t.prototype.encode=function(t){for(var e="",n=0;n<t.length-2;n+=3){var r=t[n]<<16|t[n+1]<<8|t[n+2];e+=this._encodeByte(r>>>18&63),e+=this._encodeByte(r>>>12&63),e+=this._encodeByte(r>>>6&63),e+=this._encodeByte(r>>>0&63)}var i=t.length-n;if(i>0){r=t[n]<<16|(2===i?t[n+1]<<8:0);e+=this._encodeByte(r>>>18&63),e+=this._encodeByte(r>>>12&63),e+=2===i?this._encodeByte(r>>>6&63):this._paddingCharacter||"",e+=this._paddingCharacter||""}return e},t.prototype.maxDecodedLength=function(t){return this._paddingCharacter?t/4*3|0:(6*t+7)/8|0},t.prototype.decodedLength=function(t){return this.maxDecodedLength(t.length-this._getPaddingLength(t))},t.prototype.decode=function(t){if(0===t.length)return new Uint8Array(0);for(var e=this._getPaddingLength(t),n=t.length-e,r=new Uint8Array(this.maxDecodedLength(n)),i=0,s=0,o=0,a=0,h=0,c=0,u=0;s<n-4;s+=4)a=this._decodeChar(t.charCodeAt(s+0)),h=this._decodeChar(t.charCodeAt(s+1)),c=this._decodeChar(t.charCodeAt(s+2)),u=this._decodeChar(t.charCodeAt(s+3)),r[i++]=a<<2|h>>>4,r[i++]=h<<4|c>>>2,r[i++]=c<<6|u,o|=256&a,o|=256&h,o|=256&c,o|=256&u;if(s<n-1&&(a=this._decodeChar(t.charCodeAt(s)),h=this._decodeChar(t.charCodeAt(s+1)),r[i++]=a<<2|h>>>4,o|=256&a,o|=256&h),s<n-2&&(c=this._decodeChar(t.charCodeAt(s+2)),r[i++]=h<<4|c>>>2,o|=256&c),s<n-3&&(u=this._decodeChar(t.charCodeAt(s+3)),r[i++]=c<<6|u,o|=256&u),0!==o)throw new Error("Base64Coder: incorrect characters for decoding");return r},t.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-15,e+=62-t>>>8&3,String.fromCharCode(e)},t.prototype._decodeChar=function(t){var e=256;return e+=(42-t&t-44)>>>8&-256+t-43+62,e+=(46-t&t-48)>>>8&-256+t-47+63,e+=(47-t&t-58)>>>8&-256+t-48+52,e+=(64-t&t-91)>>>8&-256+t-65+0,e+=(96-t&t-123)>>>8&-256+t-97+26},t.prototype._getPaddingLength=function(t){var e=0;if(this._paddingCharacter){for(var n=t.length-1;n>=0&&t[n]===this._paddingCharacter;n--)e++;if(t.length<4||e>2)throw new Error("Base64Coder: incorrect padding")}return e},t}();e.Coder=s;var o=new s;e.encode=function(t){return o.encode(t)},e.decode=function(t){return o.decode(t)};var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-13,e+=62-t>>>8&49,String.fromCharCode(e)},e.prototype._decodeChar=function(t){var e=256;return e+=(44-t&t-46)>>>8&-256+t-45+62,e+=(94-t&t-96)>>>8&-256+t-95+63,e+=(47-t&t-58)>>>8&-256+t-48+52,e+=(64-t&t-91)>>>8&-256+t-65+0,e+=(96-t&t-123)>>>8&-256+t-97+26},e}(s);e.URLSafeCoder=a;var h=new a;e.encodeURLSafe=function(t){return h.encode(t)},e.decodeURLSafe=function(t){return h.decode(t)},e.encodedLength=function(t){return o.encodedLength(t)},e.maxDecodedLength=function(t){return o.maxDecodedLength(t)},e.decodedLength=function(t){return o.decodedLength(t)}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r="utf8: invalid source encoding";function i(t){for(var e=0,n=0;n<t.length;n++){var r=t.charCodeAt(n);if(r<128)e+=1;else if(r<2048)e+=2;else if(r<55296)e+=3;else{if(!(r<=57343))throw new Error("utf8: invalid string");if(n>=t.length-1)throw new Error("utf8: invalid string");n++,e+=4}}return e}e.encode=function(t){for(var e=new Uint8Array(i(t)),n=0,r=0;r<t.length;r++){var s=t.charCodeAt(r);s<128?e[n++]=s:s<2048?(e[n++]=192|s>>6,e[n++]=128|63&s):s<55296?(e[n++]=224|s>>12,e[n++]=128|s>>6&63,e[n++]=128|63&s):(r++,s=(1023&s)<<10,s|=1023&t.charCodeAt(r),s+=65536,e[n++]=240|s>>18,e[n++]=128|s>>12&63,e[n++]=128|s>>6&63,e[n++]=128|63&s)}return e},e.encodedLength=i,e.decode=function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(128&i){var s=void 0;if(i<224){if(n>=t.length)throw new Error(r);if(128!=(192&(o=t[++n])))throw new Error(r);i=(31&i)<<6|63&o,s=128}else if(i<240){if(n>=t.length-1)throw new Error(r);var o=t[++n],a=t[++n];if(128!=(192&o)||128!=(192&a))throw new Error(r);i=(15&i)<<12|(63&o)<<6|63&a,s=2048}else{if(!(i<248))throw new Error(r);if(n>=t.length-2)throw new Error(r);o=t[++n],a=t[++n];var h=t[++n];if(128!=(192&o)||128!=(192&a)||128!=(192&h))throw new Error(r);i=(15&i)<<18|(63&o)<<12|(63&a)<<6|63&h,s=65536}if(i<s||i>=55296&&i<=57343)throw new Error(r);if(i>=65536){if(i>1114111)throw new Error(r);i-=65536,e.push(String.fromCharCode(55296|i>>10)),i=56320|1023&i}}e.push(String.fromCharCode(i))}return e.join("")}},function(t,e,n){!function(t){"use strict";var e=function(t){var e,n=new Float64Array(16);if(t)for(e=0;e<t.length;e++)n[e]=t[e];return n},r=function(){throw new Error("no PRNG")},i=new Uint8Array(16),s=new Uint8Array(32);s[0]=9;var o=e(),a=e([1]),h=e([56129,1]),c=e([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),u=e([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),l=e([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),d=e([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),p=e([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function f(t,e,n,r){t[e]=n>>24&255,t[e+1]=n>>16&255,t[e+2]=n>>8&255,t[e+3]=255&n,t[e+4]=r>>24&255,t[e+5]=r>>16&255,t[e+6]=r>>8&255,t[e+7]=255&r}function g(t,e,n,r,i){var s,o=0;for(s=0;s<i;s++)o|=t[e+s]^n[r+s];return(1&o-1>>>8)-1}function b(t,e,n,r){return g(t,e,n,r,16)}function v(t,e,n,r){return g(t,e,n,r,32)}function y(t,e,n,r){!function(t,e,n,r){for(var i,s=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,h=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,c=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,u=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,l=255&e[0]|(255&e[1])<<8|(255&e[2])<<16|(255&e[3])<<24,d=255&e[4]|(255&e[5])<<8|(255&e[6])<<16|(255&e[7])<<24,p=255&e[8]|(255&e[9])<<8|(255&e[10])<<16|(255&e[11])<<24,f=255&e[12]|(255&e[13])<<8|(255&e[14])<<16|(255&e[15])<<24,g=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,b=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,v=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,y=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,m=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,w=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,_=s,S=o,k=a,C=h,T=c,P=u,A=l,E=d,x=p,O=f,L=g,U=b,R=v,M=y,I=m,N=w,D=0;D<20;D+=2)_^=(i=(R^=(i=(x^=(i=(T^=(i=_+R|0)<<7|i>>>25)+_|0)<<9|i>>>23)+T|0)<<13|i>>>19)+x|0)<<18|i>>>14,P^=(i=(S^=(i=(M^=(i=(O^=(i=P+S|0)<<7|i>>>25)+P|0)<<9|i>>>23)+O|0)<<13|i>>>19)+M|0)<<18|i>>>14,L^=(i=(A^=(i=(k^=(i=(I^=(i=L+A|0)<<7|i>>>25)+L|0)<<9|i>>>23)+I|0)<<13|i>>>19)+k|0)<<18|i>>>14,N^=(i=(U^=(i=(E^=(i=(C^=(i=N+U|0)<<7|i>>>25)+N|0)<<9|i>>>23)+C|0)<<13|i>>>19)+E|0)<<18|i>>>14,_^=(i=(C^=(i=(k^=(i=(S^=(i=_+C|0)<<7|i>>>25)+_|0)<<9|i>>>23)+S|0)<<13|i>>>19)+k|0)<<18|i>>>14,P^=(i=(T^=(i=(E^=(i=(A^=(i=P+T|0)<<7|i>>>25)+P|0)<<9|i>>>23)+A|0)<<13|i>>>19)+E|0)<<18|i>>>14,L^=(i=(O^=(i=(x^=(i=(U^=(i=L+O|0)<<7|i>>>25)+L|0)<<9|i>>>23)+U|0)<<13|i>>>19)+x|0)<<18|i>>>14,N^=(i=(I^=(i=(M^=(i=(R^=(i=N+I|0)<<7|i>>>25)+N|0)<<9|i>>>23)+R|0)<<13|i>>>19)+M|0)<<18|i>>>14;_=_+s|0,S=S+o|0,k=k+a|0,C=C+h|0,T=T+c|0,P=P+u|0,A=A+l|0,E=E+d|0,x=x+p|0,O=O+f|0,L=L+g|0,U=U+b|0,R=R+v|0,M=M+y|0,I=I+m|0,N=N+w|0,t[0]=_>>>0&255,t[1]=_>>>8&255,t[2]=_>>>16&255,t[3]=_>>>24&255,t[4]=S>>>0&255,t[5]=S>>>8&255,t[6]=S>>>16&255,t[7]=S>>>24&255,t[8]=k>>>0&255,t[9]=k>>>8&255,t[10]=k>>>16&255,t[11]=k>>>24&255,t[12]=C>>>0&255,t[13]=C>>>8&255,t[14]=C>>>16&255,t[15]=C>>>24&255,t[16]=T>>>0&255,t[17]=T>>>8&255,t[18]=T>>>16&255,t[19]=T>>>24&255,t[20]=P>>>0&255,t[21]=P>>>8&255,t[22]=P>>>16&255,t[23]=P>>>24&255,t[24]=A>>>0&255,t[25]=A>>>8&255,t[26]=A>>>16&255,t[27]=A>>>24&255,t[28]=E>>>0&255,t[29]=E>>>8&255,t[30]=E>>>16&255,t[31]=E>>>24&255,t[32]=x>>>0&255,t[33]=x>>>8&255,t[34]=x>>>16&255,t[35]=x>>>24&255,t[36]=O>>>0&255,t[37]=O>>>8&255,t[38]=O>>>16&255,t[39]=O>>>24&255,t[40]=L>>>0&255,t[41]=L>>>8&255,t[42]=L>>>16&255,t[43]=L>>>24&255,t[44]=U>>>0&255,t[45]=U>>>8&255,t[46]=U>>>16&255,t[47]=U>>>24&255,t[48]=R>>>0&255,t[49]=R>>>8&255,t[50]=R>>>16&255,t[51]=R>>>24&255,t[52]=M>>>0&255,t[53]=M>>>8&255,t[54]=M>>>16&255,t[55]=M>>>24&255,t[56]=I>>>0&255,t[57]=I>>>8&255,t[58]=I>>>16&255,t[59]=I>>>24&255,t[60]=N>>>0&255,t[61]=N>>>8&255,t[62]=N>>>16&255,t[63]=N>>>24&255}(t,e,n,r)}function m(t,e,n,r){!function(t,e,n,r){for(var i,s=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,h=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,c=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,u=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,l=255&e[0]|(255&e[1])<<8|(255&e[2])<<16|(255&e[3])<<24,d=255&e[4]|(255&e[5])<<8|(255&e[6])<<16|(255&e[7])<<24,p=255&e[8]|(255&e[9])<<8|(255&e[10])<<16|(255&e[11])<<24,f=255&e[12]|(255&e[13])<<8|(255&e[14])<<16|(255&e[15])<<24,g=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,b=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,v=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,y=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,m=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,w=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,_=0;_<20;_+=2)s^=(i=(v^=(i=(p^=(i=(c^=(i=s+v|0)<<7|i>>>25)+s|0)<<9|i>>>23)+c|0)<<13|i>>>19)+p|0)<<18|i>>>14,u^=(i=(o^=(i=(y^=(i=(f^=(i=u+o|0)<<7|i>>>25)+u|0)<<9|i>>>23)+f|0)<<13|i>>>19)+y|0)<<18|i>>>14,g^=(i=(l^=(i=(a^=(i=(m^=(i=g+l|0)<<7|i>>>25)+g|0)<<9|i>>>23)+m|0)<<13|i>>>19)+a|0)<<18|i>>>14,w^=(i=(b^=(i=(d^=(i=(h^=(i=w+b|0)<<7|i>>>25)+w|0)<<9|i>>>23)+h|0)<<13|i>>>19)+d|0)<<18|i>>>14,s^=(i=(h^=(i=(a^=(i=(o^=(i=s+h|0)<<7|i>>>25)+s|0)<<9|i>>>23)+o|0)<<13|i>>>19)+a|0)<<18|i>>>14,u^=(i=(c^=(i=(d^=(i=(l^=(i=u+c|0)<<7|i>>>25)+u|0)<<9|i>>>23)+l|0)<<13|i>>>19)+d|0)<<18|i>>>14,g^=(i=(f^=(i=(p^=(i=(b^=(i=g+f|0)<<7|i>>>25)+g|0)<<9|i>>>23)+b|0)<<13|i>>>19)+p|0)<<18|i>>>14,w^=(i=(m^=(i=(y^=(i=(v^=(i=w+m|0)<<7|i>>>25)+w|0)<<9|i>>>23)+v|0)<<13|i>>>19)+y|0)<<18|i>>>14;t[0]=s>>>0&255,t[1]=s>>>8&255,t[2]=s>>>16&255,t[3]=s>>>24&255,t[4]=u>>>0&255,t[5]=u>>>8&255,t[6]=u>>>16&255,t[7]=u>>>24&255,t[8]=g>>>0&255,t[9]=g>>>8&255,t[10]=g>>>16&255,t[11]=g>>>24&255,t[12]=w>>>0&255,t[13]=w>>>8&255,t[14]=w>>>16&255,t[15]=w>>>24&255,t[16]=l>>>0&255,t[17]=l>>>8&255,t[18]=l>>>16&255,t[19]=l>>>24&255,t[20]=d>>>0&255,t[21]=d>>>8&255,t[22]=d>>>16&255,t[23]=d>>>24&255,t[24]=p>>>0&255,t[25]=p>>>8&255,t[26]=p>>>16&255,t[27]=p>>>24&255,t[28]=f>>>0&255,t[29]=f>>>8&255,t[30]=f>>>16&255,t[31]=f>>>24&255}(t,e,n,r)}var w=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function _(t,e,n,r,i,s,o){var a,h,c=new Uint8Array(16),u=new Uint8Array(64);for(h=0;h<16;h++)c[h]=0;for(h=0;h<8;h++)c[h]=s[h];for(;i>=64;){for(y(u,c,o,w),h=0;h<64;h++)t[e+h]=n[r+h]^u[h];for(a=1,h=8;h<16;h++)a=a+(255&c[h])|0,c[h]=255&a,a>>>=8;i-=64,e+=64,r+=64}if(i>0)for(y(u,c,o,w),h=0;h<i;h++)t[e+h]=n[r+h]^u[h];return 0}function S(t,e,n,r,i){var s,o,a=new Uint8Array(16),h=new Uint8Array(64);for(o=0;o<16;o++)a[o]=0;for(o=0;o<8;o++)a[o]=r[o];for(;n>=64;){for(y(h,a,i,w),o=0;o<64;o++)t[e+o]=h[o];for(s=1,o=8;o<16;o++)s=s+(255&a[o])|0,a[o]=255&s,s>>>=8;n-=64,e+=64}if(n>0)for(y(h,a,i,w),o=0;o<n;o++)t[e+o]=h[o];return 0}function k(t,e,n,r,i){var s=new Uint8Array(32);m(s,r,i,w);for(var o=new Uint8Array(8),a=0;a<8;a++)o[a]=r[a+16];return S(t,e,n,o,s)}function C(t,e,n,r,i,s,o){var a=new Uint8Array(32);m(a,s,o,w);for(var h=new Uint8Array(8),c=0;c<8;c++)h[c]=s[c+16];return _(t,e,n,r,i,h,a)}var T=function(t){var e,n,r,i,s,o,a,h;this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0,e=255&t[0]|(255&t[1])<<8,this.r[0]=8191&e,n=255&t[2]|(255&t[3])<<8,this.r[1]=8191&(e>>>13|n<<3),r=255&t[4]|(255&t[5])<<8,this.r[2]=7939&(n>>>10|r<<6),i=255&t[6]|(255&t[7])<<8,this.r[3]=8191&(r>>>7|i<<9),s=255&t[8]|(255&t[9])<<8,this.r[4]=255&(i>>>4|s<<12),this.r[5]=s>>>1&8190,o=255&t[10]|(255&t[11])<<8,this.r[6]=8191&(s>>>14|o<<2),a=255&t[12]|(255&t[13])<<8,this.r[7]=8065&(o>>>11|a<<5),h=255&t[14]|(255&t[15])<<8,this.r[8]=8191&(a>>>8|h<<8),this.r[9]=h>>>5&127,this.pad[0]=255&t[16]|(255&t[17])<<8,this.pad[1]=255&t[18]|(255&t[19])<<8,this.pad[2]=255&t[20]|(255&t[21])<<8,this.pad[3]=255&t[22]|(255&t[23])<<8,this.pad[4]=255&t[24]|(255&t[25])<<8,this.pad[5]=255&t[26]|(255&t[27])<<8,this.pad[6]=255&t[28]|(255&t[29])<<8,this.pad[7]=255&t[30]|(255&t[31])<<8};function P(t,e,n,r,i,s){var o=new T(s);return o.update(n,r,i),o.finish(t,e),0}function A(t,e,n,r,i,s){var o=new Uint8Array(16);return P(o,0,n,r,i,s),b(t,e,o,0)}function E(t,e,n,r,i){var s;if(n<32)return-1;for(C(t,0,e,0,n,r,i),P(t,16,t,32,n-32,t),s=0;s<16;s++)t[s]=0;return 0}function x(t,e,n,r,i){var s,o=new Uint8Array(32);if(n<32)return-1;if(k(o,0,32,r,i),0!==A(e,16,e,32,n-32,o))return-1;for(C(t,0,e,0,n,r,i),s=0;s<32;s++)t[s]=0;return 0}function O(t,e){var n;for(n=0;n<16;n++)t[n]=0|e[n]}function L(t){var e,n,r=1;for(e=0;e<16;e++)n=t[e]+r+65535,r=Math.floor(n/65536),t[e]=n-65536*r;t[0]+=r-1+37*(r-1)}function U(t,e,n){for(var r,i=~(n-1),s=0;s<16;s++)r=i&(t[s]^e[s]),t[s]^=r,e[s]^=r}function R(t,n){var r,i,s,o=e(),a=e();for(r=0;r<16;r++)a[r]=n[r];for(L(a),L(a),L(a),i=0;i<2;i++){for(o[0]=a[0]-65517,r=1;r<15;r++)o[r]=a[r]-65535-(o[r-1]>>16&1),o[r-1]&=65535;o[15]=a[15]-32767-(o[14]>>16&1),s=o[15]>>16&1,o[14]&=65535,U(a,o,1-s)}for(r=0;r<16;r++)t[2*r]=255&a[r],t[2*r+1]=a[r]>>8}function M(t,e){var n=new Uint8Array(32),r=new Uint8Array(32);return R(n,t),R(r,e),v(n,0,r,0)}function I(t){var e=new Uint8Array(32);return R(e,t),1&e[0]}function N(t,e){var n;for(n=0;n<16;n++)t[n]=e[2*n]+(e[2*n+1]<<8);t[15]&=32767}function D(t,e,n){for(var r=0;r<16;r++)t[r]=e[r]+n[r]}function j(t,e,n){for(var r=0;r<16;r++)t[r]=e[r]-n[r]}function z(t,e,n){var r,i,s=0,o=0,a=0,h=0,c=0,u=0,l=0,d=0,p=0,f=0,g=0,b=0,v=0,y=0,m=0,w=0,_=0,S=0,k=0,C=0,T=0,P=0,A=0,E=0,x=0,O=0,L=0,U=0,R=0,M=0,I=0,N=n[0],D=n[1],j=n[2],z=n[3],H=n[4],B=n[5],F=n[6],q=n[7],Y=n[8],K=n[9],$=n[10],J=n[11],W=n[12],X=n[13],G=n[14],V=n[15];s+=(r=e[0])*N,o+=r*D,a+=r*j,h+=r*z,c+=r*H,u+=r*B,l+=r*F,d+=r*q,p+=r*Y,f+=r*K,g+=r*$,b+=r*J,v+=r*W,y+=r*X,m+=r*G,w+=r*V,o+=(r=e[1])*N,a+=r*D,h+=r*j,c+=r*z,u+=r*H,l+=r*B,d+=r*F,p+=r*q,f+=r*Y,g+=r*K,b+=r*$,v+=r*J,y+=r*W,m+=r*X,w+=r*G,_+=r*V,a+=(r=e[2])*N,h+=r*D,c+=r*j,u+=r*z,l+=r*H,d+=r*B,p+=r*F,f+=r*q,g+=r*Y,b+=r*K,v+=r*$,y+=r*J,m+=r*W,w+=r*X,_+=r*G,S+=r*V,h+=(r=e[3])*N,c+=r*D,u+=r*j,l+=r*z,d+=r*H,p+=r*B,f+=r*F,g+=r*q,b+=r*Y,v+=r*K,y+=r*$,m+=r*J,w+=r*W,_+=r*X,S+=r*G,k+=r*V,c+=(r=e[4])*N,u+=r*D,l+=r*j,d+=r*z,p+=r*H,f+=r*B,g+=r*F,b+=r*q,v+=r*Y,y+=r*K,m+=r*$,w+=r*J,_+=r*W,S+=r*X,k+=r*G,C+=r*V,u+=(r=e[5])*N,l+=r*D,d+=r*j,p+=r*z,f+=r*H,g+=r*B,b+=r*F,v+=r*q,y+=r*Y,m+=r*K,w+=r*$,_+=r*J,S+=r*W,k+=r*X,C+=r*G,T+=r*V,l+=(r=e[6])*N,d+=r*D,p+=r*j,f+=r*z,g+=r*H,b+=r*B,v+=r*F,y+=r*q,m+=r*Y,w+=r*K,_+=r*$,S+=r*J,k+=r*W,C+=r*X,T+=r*G,P+=r*V,d+=(r=e[7])*N,p+=r*D,f+=r*j,g+=r*z,b+=r*H,v+=r*B,y+=r*F,m+=r*q,w+=r*Y,_+=r*K,S+=r*$,k+=r*J,C+=r*W,T+=r*X,P+=r*G,A+=r*V,p+=(r=e[8])*N,f+=r*D,g+=r*j,b+=r*z,v+=r*H,y+=r*B,m+=r*F,w+=r*q,_+=r*Y,S+=r*K,k+=r*$,C+=r*J,T+=r*W,P+=r*X,A+=r*G,E+=r*V,f+=(r=e[9])*N,g+=r*D,b+=r*j,v+=r*z,y+=r*H,m+=r*B,w+=r*F,_+=r*q,S+=r*Y,k+=r*K,C+=r*$,T+=r*J,P+=r*W,A+=r*X,E+=r*G,x+=r*V,g+=(r=e[10])*N,b+=r*D,v+=r*j,y+=r*z,m+=r*H,w+=r*B,_+=r*F,S+=r*q,k+=r*Y,C+=r*K,T+=r*$,P+=r*J,A+=r*W,E+=r*X,x+=r*G,O+=r*V,b+=(r=e[11])*N,v+=r*D,y+=r*j,m+=r*z,w+=r*H,_+=r*B,S+=r*F,k+=r*q,C+=r*Y,T+=r*K,P+=r*$,A+=r*J,E+=r*W,x+=r*X,O+=r*G,L+=r*V,v+=(r=e[12])*N,y+=r*D,m+=r*j,w+=r*z,_+=r*H,S+=r*B,k+=r*F,C+=r*q,T+=r*Y,P+=r*K,A+=r*$,E+=r*J,x+=r*W,O+=r*X,L+=r*G,U+=r*V,y+=(r=e[13])*N,m+=r*D,w+=r*j,_+=r*z,S+=r*H,k+=r*B,C+=r*F,T+=r*q,P+=r*Y,A+=r*K,E+=r*$,x+=r*J,O+=r*W,L+=r*X,U+=r*G,R+=r*V,m+=(r=e[14])*N,w+=r*D,_+=r*j,S+=r*z,k+=r*H,C+=r*B,T+=r*F,P+=r*q,A+=r*Y,E+=r*K,x+=r*$,O+=r*J,L+=r*W,U+=r*X,R+=r*G,M+=r*V,w+=(r=e[15])*N,o+=38*(S+=r*j),a+=38*(k+=r*z),h+=38*(C+=r*H),c+=38*(T+=r*B),u+=38*(P+=r*F),l+=38*(A+=r*q),d+=38*(E+=r*Y),p+=38*(x+=r*K),f+=38*(O+=r*$),g+=38*(L+=r*J),b+=38*(U+=r*W),v+=38*(R+=r*X),y+=38*(M+=r*G),m+=38*(I+=r*V),s=(r=(s+=38*(_+=r*D))+(i=1)+65535)-65536*(i=Math.floor(r/65536)),o=(r=o+i+65535)-65536*(i=Math.floor(r/65536)),a=(r=a+i+65535)-65536*(i=Math.floor(r/65536)),h=(r=h+i+65535)-65536*(i=Math.floor(r/65536)),c=(r=c+i+65535)-65536*(i=Math.floor(r/65536)),u=(r=u+i+65535)-65536*(i=Math.floor(r/65536)),l=(r=l+i+65535)-65536*(i=Math.floor(r/65536)),d=(r=d+i+65535)-65536*(i=Math.floor(r/65536)),p=(r=p+i+65535)-65536*(i=Math.floor(r/65536)),f=(r=f+i+65535)-65536*(i=Math.floor(r/65536)),g=(r=g+i+65535)-65536*(i=Math.floor(r/65536)),b=(r=b+i+65535)-65536*(i=Math.floor(r/65536)),v=(r=v+i+65535)-65536*(i=Math.floor(r/65536)),y=(r=y+i+65535)-65536*(i=Math.floor(r/65536)),m=(r=m+i+65535)-65536*(i=Math.floor(r/65536)),w=(r=w+i+65535)-65536*(i=Math.floor(r/65536)),s=(r=(s+=i-1+37*(i-1))+(i=1)+65535)-65536*(i=Math.floor(r/65536)),o=(r=o+i+65535)-65536*(i=Math.floor(r/65536)),a=(r=a+i+65535)-65536*(i=Math.floor(r/65536)),h=(r=h+i+65535)-65536*(i=Math.floor(r/65536)),c=(r=c+i+65535)-65536*(i=Math.floor(r/65536)),u=(r=u+i+65535)-65536*(i=Math.floor(r/65536)),l=(r=l+i+65535)-65536*(i=Math.floor(r/65536)),d=(r=d+i+65535)-65536*(i=Math.floor(r/65536)),p=(r=p+i+65535)-65536*(i=Math.floor(r/65536)),f=(r=f+i+65535)-65536*(i=Math.floor(r/65536)),g=(r=g+i+65535)-65536*(i=Math.floor(r/65536)),b=(r=b+i+65535)-65536*(i=Math.floor(r/65536)),v=(r=v+i+65535)-65536*(i=Math.floor(r/65536)),y=(r=y+i+65535)-65536*(i=Math.floor(r/65536)),m=(r=m+i+65535)-65536*(i=Math.floor(r/65536)),w=(r=w+i+65535)-65536*(i=Math.floor(r/65536)),s+=i-1+37*(i-1),t[0]=s,t[1]=o,t[2]=a,t[3]=h,t[4]=c,t[5]=u,t[6]=l,t[7]=d,t[8]=p,t[9]=f,t[10]=g,t[11]=b,t[12]=v,t[13]=y,t[14]=m,t[15]=w}function H(t,e){z(t,e,e)}function B(t,n){var r,i=e();for(r=0;r<16;r++)i[r]=n[r];for(r=253;r>=0;r--)H(i,i),2!==r&&4!==r&&z(i,i,n);for(r=0;r<16;r++)t[r]=i[r]}function F(t,n){var r,i=e();for(r=0;r<16;r++)i[r]=n[r];for(r=250;r>=0;r--)H(i,i),1!==r&&z(i,i,n);for(r=0;r<16;r++)t[r]=i[r]}function q(t,n,r){var i,s,o=new Uint8Array(32),a=new Float64Array(80),c=e(),u=e(),l=e(),d=e(),p=e(),f=e();for(s=0;s<31;s++)o[s]=n[s];for(o[31]=127&n[31]|64,o[0]&=248,N(a,r),s=0;s<16;s++)u[s]=a[s],d[s]=c[s]=l[s]=0;for(c[0]=d[0]=1,s=254;s>=0;--s)U(c,u,i=o[s>>>3]>>>(7&s)&1),U(l,d,i),D(p,c,l),j(c,c,l),D(l,u,d),j(u,u,d),H(d,p),H(f,c),z(c,l,c),z(l,u,p),D(p,c,l),j(c,c,l),H(u,c),j(l,d,f),z(c,l,h),D(c,c,d),z(l,l,c),z(c,d,f),z(d,u,a),H(u,p),U(c,u,i),U(l,d,i);for(s=0;s<16;s++)a[s+16]=c[s],a[s+32]=l[s],a[s+48]=u[s],a[s+64]=d[s];var g=a.subarray(32),b=a.subarray(16);return B(g,g),z(b,b,g),R(t,b),0}function Y(t,e){return q(t,e,s)}function K(t,e){return r(e,32),Y(t,e)}function $(t,e,n){var r=new Uint8Array(32);return q(r,n,e),m(t,i,r,w)}T.prototype.blocks=function(t,e,n){for(var r,i,s,o,a,h,c,u,l,d,p,f,g,b,v,y,m,w,_,S=this.fin?0:2048,k=this.h[0],C=this.h[1],T=this.h[2],P=this.h[3],A=this.h[4],E=this.h[5],x=this.h[6],O=this.h[7],L=this.h[8],U=this.h[9],R=this.r[0],M=this.r[1],I=this.r[2],N=this.r[3],D=this.r[4],j=this.r[5],z=this.r[6],H=this.r[7],B=this.r[8],F=this.r[9];n>=16;)d=l=0,d+=(k+=8191&(r=255&t[e+0]|(255&t[e+1])<<8))*R,d+=(C+=8191&(r>>>13|(i=255&t[e+2]|(255&t[e+3])<<8)<<3))*(5*F),d+=(T+=8191&(i>>>10|(s=255&t[e+4]|(255&t[e+5])<<8)<<6))*(5*B),d+=(P+=8191&(s>>>7|(o=255&t[e+6]|(255&t[e+7])<<8)<<9))*(5*H),l=(d+=(A+=8191&(o>>>4|(a=255&t[e+8]|(255&t[e+9])<<8)<<12))*(5*z))>>>13,d&=8191,d+=(E+=a>>>1&8191)*(5*j),d+=(x+=8191&(a>>>14|(h=255&t[e+10]|(255&t[e+11])<<8)<<2))*(5*D),d+=(O+=8191&(h>>>11|(c=255&t[e+12]|(255&t[e+13])<<8)<<5))*(5*N),d+=(L+=8191&(c>>>8|(u=255&t[e+14]|(255&t[e+15])<<8)<<8))*(5*I),p=l+=(d+=(U+=u>>>5|S)*(5*M))>>>13,p+=k*M,p+=C*R,p+=T*(5*F),p+=P*(5*B),l=(p+=A*(5*H))>>>13,p&=8191,p+=E*(5*z),p+=x*(5*j),p+=O*(5*D),p+=L*(5*N),l+=(p+=U*(5*I))>>>13,p&=8191,f=l,f+=k*I,f+=C*M,f+=T*R,f+=P*(5*F),l=(f+=A*(5*B))>>>13,f&=8191,f+=E*(5*H),f+=x*(5*z),f+=O*(5*j),f+=L*(5*D),g=l+=(f+=U*(5*N))>>>13,g+=k*N,g+=C*I,g+=T*M,g+=P*R,l=(g+=A*(5*F))>>>13,g&=8191,g+=E*(5*B),g+=x*(5*H),g+=O*(5*z),g+=L*(5*j),b=l+=(g+=U*(5*D))>>>13,b+=k*D,b+=C*N,b+=T*I,b+=P*M,l=(b+=A*R)>>>13,b&=8191,b+=E*(5*F),b+=x*(5*B),b+=O*(5*H),b+=L*(5*z),v=l+=(b+=U*(5*j))>>>13,v+=k*j,v+=C*D,v+=T*N,v+=P*I,l=(v+=A*M)>>>13,v&=8191,v+=E*R,v+=x*(5*F),v+=O*(5*B),v+=L*(5*H),y=l+=(v+=U*(5*z))>>>13,y+=k*z,y+=C*j,y+=T*D,y+=P*N,l=(y+=A*I)>>>13,y&=8191,y+=E*M,y+=x*R,y+=O*(5*F),y+=L*(5*B),m=l+=(y+=U*(5*H))>>>13,m+=k*H,m+=C*z,m+=T*j,m+=P*D,l=(m+=A*N)>>>13,m&=8191,m+=E*I,m+=x*M,m+=O*R,m+=L*(5*F),w=l+=(m+=U*(5*B))>>>13,w+=k*B,w+=C*H,w+=T*z,w+=P*j,l=(w+=A*D)>>>13,w&=8191,w+=E*N,w+=x*I,w+=O*M,w+=L*R,_=l+=(w+=U*(5*F))>>>13,_+=k*F,_+=C*B,_+=T*H,_+=P*z,l=(_+=A*j)>>>13,_&=8191,_+=E*D,_+=x*N,_+=O*I,_+=L*M,k=d=8191&(l=(l=((l+=(_+=U*R)>>>13)<<2)+l|0)+(d&=8191)|0),C=p+=l>>>=13,T=f&=8191,P=g&=8191,A=b&=8191,E=v&=8191,x=y&=8191,O=m&=8191,L=w&=8191,U=_&=8191,e+=16,n-=16;this.h[0]=k,this.h[1]=C,this.h[2]=T,this.h[3]=P,this.h[4]=A,this.h[5]=E,this.h[6]=x,this.h[7]=O,this.h[8]=L,this.h[9]=U},T.prototype.finish=function(t,e){var n,r,i,s,o=new Uint16Array(10);if(this.leftover){for(s=this.leftover,this.buffer[s++]=1;s<16;s++)this.buffer[s]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(n=this.h[1]>>>13,this.h[1]&=8191,s=2;s<10;s++)this.h[s]+=n,n=this.h[s]>>>13,this.h[s]&=8191;for(this.h[0]+=5*n,n=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=n,n=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=n,o[0]=this.h[0]+5,n=o[0]>>>13,o[0]&=8191,s=1;s<10;s++)o[s]=this.h[s]+n,n=o[s]>>>13,o[s]&=8191;for(o[9]-=8192,r=(1^n)-1,s=0;s<10;s++)o[s]&=r;for(r=~r,s=0;s<10;s++)this.h[s]=this.h[s]&r|o[s];for(this.h[0]=65535&(this.h[0]|this.h[1]<<13),this.h[1]=65535&(this.h[1]>>>3|this.h[2]<<10),this.h[2]=65535&(this.h[2]>>>6|this.h[3]<<7),this.h[3]=65535&(this.h[3]>>>9|this.h[4]<<4),this.h[4]=65535&(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14),this.h[5]=65535&(this.h[6]>>>2|this.h[7]<<11),this.h[6]=65535&(this.h[7]>>>5|this.h[8]<<8),this.h[7]=65535&(this.h[8]>>>8|this.h[9]<<5),i=this.h[0]+this.pad[0],this.h[0]=65535&i,s=1;s<8;s++)i=(this.h[s]+this.pad[s]|0)+(i>>>16)|0,this.h[s]=65535&i;t[e+0]=this.h[0]>>>0&255,t[e+1]=this.h[0]>>>8&255,t[e+2]=this.h[1]>>>0&255,t[e+3]=this.h[1]>>>8&255,t[e+4]=this.h[2]>>>0&255,t[e+5]=this.h[2]>>>8&255,t[e+6]=this.h[3]>>>0&255,t[e+7]=this.h[3]>>>8&255,t[e+8]=this.h[4]>>>0&255,t[e+9]=this.h[4]>>>8&255,t[e+10]=this.h[5]>>>0&255,t[e+11]=this.h[5]>>>8&255,t[e+12]=this.h[6]>>>0&255,t[e+13]=this.h[6]>>>8&255,t[e+14]=this.h[7]>>>0&255,t[e+15]=this.h[7]>>>8&255},T.prototype.update=function(t,e,n){var r,i;if(this.leftover){for((i=16-this.leftover)>n&&(i=n),r=0;r<i;r++)this.buffer[this.leftover+r]=t[e+r];if(n-=i,e+=i,this.leftover+=i,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(n>=16&&(i=n-n%16,this.blocks(t,e,i),e+=i,n-=i),n){for(r=0;r<n;r++)this.buffer[this.leftover+r]=t[e+r];this.leftover+=n}};var J=E,W=x;var X=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function G(t,e,n,r){for(var i,s,o,a,h,c,u,l,d,p,f,g,b,v,y,m,w,_,S,k,C,T,P,A,E,x,O=new Int32Array(16),L=new Int32Array(16),U=t[0],R=t[1],M=t[2],I=t[3],N=t[4],D=t[5],j=t[6],z=t[7],H=e[0],B=e[1],F=e[2],q=e[3],Y=e[4],K=e[5],$=e[6],J=e[7],W=0;r>=128;){for(S=0;S<16;S++)k=8*S+W,O[S]=n[k+0]<<24|n[k+1]<<16|n[k+2]<<8|n[k+3],L[S]=n[k+4]<<24|n[k+5]<<16|n[k+6]<<8|n[k+7];for(S=0;S<80;S++)if(i=U,s=R,o=M,a=I,h=N,c=D,u=j,z,d=H,p=B,f=F,g=q,b=Y,v=K,y=$,J,P=65535&(T=J),A=T>>>16,E=65535&(C=z),x=C>>>16,P+=65535&(T=(Y>>>14|N<<18)^(Y>>>18|N<<14)^(N>>>9|Y<<23)),A+=T>>>16,E+=65535&(C=(N>>>14|Y<<18)^(N>>>18|Y<<14)^(Y>>>9|N<<23)),x+=C>>>16,P+=65535&(T=Y&K^~Y&$),A+=T>>>16,E+=65535&(C=N&D^~N&j),x+=C>>>16,P+=65535&(T=X[2*S+1]),A+=T>>>16,E+=65535&(C=X[2*S]),x+=C>>>16,C=O[S%16],A+=(T=L[S%16])>>>16,E+=65535&C,x+=C>>>16,E+=(A+=(P+=65535&T)>>>16)>>>16,P=65535&(T=_=65535&P|A<<16),A=T>>>16,E=65535&(C=w=65535&E|(x+=E>>>16)<<16),x=C>>>16,P+=65535&(T=(H>>>28|U<<4)^(U>>>2|H<<30)^(U>>>7|H<<25)),A+=T>>>16,E+=65535&(C=(U>>>28|H<<4)^(H>>>2|U<<30)^(H>>>7|U<<25)),x+=C>>>16,A+=(T=H&B^H&F^B&F)>>>16,E+=65535&(C=U&R^U&M^R&M),x+=C>>>16,l=65535&(E+=(A+=(P+=65535&T)>>>16)>>>16)|(x+=E>>>16)<<16,m=65535&P|A<<16,P=65535&(T=g),A=T>>>16,E=65535&(C=a),x=C>>>16,A+=(T=_)>>>16,E+=65535&(C=w),x+=C>>>16,R=i,M=s,I=o,N=a=65535&(E+=(A+=(P+=65535&T)>>>16)>>>16)|(x+=E>>>16)<<16,D=h,j=c,z=u,U=l,B=d,F=p,q=f,Y=g=65535&P|A<<16,K=b,$=v,J=y,H=m,S%16==15)for(k=0;k<16;k++)C=O[k],P=65535&(T=L[k]),A=T>>>16,E=65535&C,x=C>>>16,C=O[(k+9)%16],P+=65535&(T=L[(k+9)%16]),A+=T>>>16,E+=65535&C,x+=C>>>16,w=O[(k+1)%16],P+=65535&(T=((_=L[(k+1)%16])>>>1|w<<31)^(_>>>8|w<<24)^(_>>>7|w<<25)),A+=T>>>16,E+=65535&(C=(w>>>1|_<<31)^(w>>>8|_<<24)^w>>>7),x+=C>>>16,w=O[(k+14)%16],A+=(T=((_=L[(k+14)%16])>>>19|w<<13)^(w>>>29|_<<3)^(_>>>6|w<<26))>>>16,E+=65535&(C=(w>>>19|_<<13)^(_>>>29|w<<3)^w>>>6),x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,O[k]=65535&E|x<<16,L[k]=65535&P|A<<16;P=65535&(T=H),A=T>>>16,E=65535&(C=U),x=C>>>16,C=t[0],A+=(T=e[0])>>>16,E+=65535&C,x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,t[0]=U=65535&E|x<<16,e[0]=H=65535&P|A<<16,P=65535&(T=B),A=T>>>16,E=65535&(C=R),x=C>>>16,C=t[1],A+=(T=e[1])>>>16,E+=65535&C,x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,t[1]=R=65535&E|x<<16,e[1]=B=65535&P|A<<16,P=65535&(T=F),A=T>>>16,E=65535&(C=M),x=C>>>16,C=t[2],A+=(T=e[2])>>>16,E+=65535&C,x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,t[2]=M=65535&E|x<<16,e[2]=F=65535&P|A<<16,P=65535&(T=q),A=T>>>16,E=65535&(C=I),x=C>>>16,C=t[3],A+=(T=e[3])>>>16,E+=65535&C,x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,t[3]=I=65535&E|x<<16,e[3]=q=65535&P|A<<16,P=65535&(T=Y),A=T>>>16,E=65535&(C=N),x=C>>>16,C=t[4],A+=(T=e[4])>>>16,E+=65535&C,x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,t[4]=N=65535&E|x<<16,e[4]=Y=65535&P|A<<16,P=65535&(T=K),A=T>>>16,E=65535&(C=D),x=C>>>16,C=t[5],A+=(T=e[5])>>>16,E+=65535&C,x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,t[5]=D=65535&E|x<<16,e[5]=K=65535&P|A<<16,P=65535&(T=$),A=T>>>16,E=65535&(C=j),x=C>>>16,C=t[6],A+=(T=e[6])>>>16,E+=65535&C,x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,t[6]=j=65535&E|x<<16,e[6]=$=65535&P|A<<16,P=65535&(T=J),A=T>>>16,E=65535&(C=z),x=C>>>16,C=t[7],A+=(T=e[7])>>>16,E+=65535&C,x+=C>>>16,x+=(E+=(A+=(P+=65535&T)>>>16)>>>16)>>>16,t[7]=z=65535&E|x<<16,e[7]=J=65535&P|A<<16,W+=128,r-=128}return r}function V(t,e,n){var r,i=new Int32Array(8),s=new Int32Array(8),o=new Uint8Array(256),a=n;for(i[0]=1779033703,i[1]=3144134277,i[2]=1013904242,i[3]=2773480762,i[4]=1359893119,i[5]=2600822924,i[6]=528734635,i[7]=1541459225,s[0]=4089235720,s[1]=2227873595,s[2]=4271175723,s[3]=1595750129,s[4]=2917565137,s[5]=725511199,s[6]=4215389547,s[7]=327033209,G(i,s,e,n),n%=128,r=0;r<n;r++)o[r]=e[a-n+r];for(o[n]=128,o[(n=256-128*(n<112?1:0))-9]=0,f(o,n-8,a/536870912|0,a<<3),G(i,s,o,n),r=0;r<8;r++)f(t,8*r,i[r],s[r]);return 0}function Z(t,n){var r=e(),i=e(),s=e(),o=e(),a=e(),h=e(),c=e(),l=e(),d=e();j(r,t[1],t[0]),j(d,n[1],n[0]),z(r,r,d),D(i,t[0],t[1]),D(d,n[0],n[1]),z(i,i,d),z(s,t[3],n[3]),z(s,s,u),z(o,t[2],n[2]),D(o,o,o),j(a,i,r),j(h,o,s),D(c,o,s),D(l,i,r),z(t[0],a,h),z(t[1],l,c),z(t[2],c,h),z(t[3],a,l)}function Q(t,e,n){var r;for(r=0;r<4;r++)U(t[r],e[r],n)}function tt(t,n){var r=e(),i=e(),s=e();B(s,n[2]),z(r,n[0],s),z(i,n[1],s),R(t,i),t[31]^=I(r)<<7}function et(t,e,n){var r,i;for(O(t[0],o),O(t[1],a),O(t[2],a),O(t[3],o),i=255;i>=0;--i)Q(t,e,r=n[i/8|0]>>(7&i)&1),Z(e,t),Z(t,t),Q(t,e,r)}function nt(t,n){var r=[e(),e(),e(),e()];O(r[0],l),O(r[1],d),O(r[2],a),z(r[3],l,d),et(t,r,n)}function rt(t,n,i){var s,o=new Uint8Array(64),a=[e(),e(),e(),e()];for(i||r(n,32),V(o,n,32),o[0]&=248,o[31]&=127,o[31]|=64,nt(a,o),tt(t,a),s=0;s<32;s++)n[s+32]=t[s];return 0}var it=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function st(t,e){var n,r,i,s;for(r=63;r>=32;--r){for(n=0,i=r-32,s=r-12;i<s;++i)e[i]+=n-16*e[r]*it[i-(r-32)],n=Math.floor((e[i]+128)/256),e[i]-=256*n;e[i]+=n,e[r]=0}for(n=0,i=0;i<32;i++)e[i]+=n-(e[31]>>4)*it[i],n=e[i]>>8,e[i]&=255;for(i=0;i<32;i++)e[i]-=n*it[i];for(r=0;r<32;r++)e[r+1]+=e[r]>>8,t[r]=255&e[r]}function ot(t){var e,n=new Float64Array(64);for(e=0;e<64;e++)n[e]=t[e];for(e=0;e<64;e++)t[e]=0;st(t,n)}function at(t,n,r,i){var s,o,a=new Uint8Array(64),h=new Uint8Array(64),c=new Uint8Array(64),u=new Float64Array(64),l=[e(),e(),e(),e()];V(a,i,32),a[0]&=248,a[31]&=127,a[31]|=64;var d=r+64;for(s=0;s<r;s++)t[64+s]=n[s];for(s=0;s<32;s++)t[32+s]=a[32+s];for(V(c,t.subarray(32),r+32),ot(c),nt(l,c),tt(t,l),s=32;s<64;s++)t[s]=i[s];for(V(h,t,r+64),ot(h),s=0;s<64;s++)u[s]=0;for(s=0;s<32;s++)u[s]=c[s];for(s=0;s<32;s++)for(o=0;o<32;o++)u[s+o]+=h[s]*a[o];return st(t.subarray(32),u),d}function ht(t,n,r,i){var s,h=new Uint8Array(32),u=new Uint8Array(64),l=[e(),e(),e(),e()],d=[e(),e(),e(),e()];if(r<64)return-1;if(function(t,n){var r=e(),i=e(),s=e(),h=e(),u=e(),l=e(),d=e();return O(t[2],a),N(t[1],n),H(s,t[1]),z(h,s,c),j(s,s,t[2]),D(h,t[2],h),H(u,h),H(l,u),z(d,l,u),z(r,d,s),z(r,r,h),F(r,r),z(r,r,s),z(r,r,h),z(r,r,h),z(t[0],r,h),H(i,t[0]),z(i,i,h),M(i,s)&&z(t[0],t[0],p),H(i,t[0]),z(i,i,h),M(i,s)?-1:(I(t[0])===n[31]>>7&&j(t[0],o,t[0]),z(t[3],t[0],t[1]),0)}(d,i))return-1;for(s=0;s<r;s++)t[s]=n[s];for(s=0;s<32;s++)t[s+32]=i[s];if(V(u,t,r),ot(u),et(l,d,u),nt(d,n.subarray(32)),Z(l,d),tt(h,l),r-=64,v(n,0,h,0)){for(s=0;s<r;s++)t[s]=0;return-1}for(s=0;s<r;s++)t[s]=n[s+64];return r}function ct(t,e){if(32!==t.length)throw new Error("bad key size");if(24!==e.length)throw new Error("bad nonce size")}function ut(){for(var t=0;t<arguments.length;t++)if(!(arguments[t]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function lt(t){for(var e=0;e<t.length;e++)t[e]=0}t.lowlevel={crypto_core_hsalsa20:m,crypto_stream_xor:C,crypto_stream:k,crypto_stream_salsa20_xor:_,crypto_stream_salsa20:S,crypto_onetimeauth:P,crypto_onetimeauth_verify:A,crypto_verify_16:b,crypto_verify_32:v,crypto_secretbox:E,crypto_secretbox_open:x,crypto_scalarmult:q,crypto_scalarmult_base:Y,crypto_box_beforenm:$,crypto_box_afternm:J,crypto_box:function(t,e,n,r,i,s){var o=new Uint8Array(32);return $(o,i,s),J(t,e,n,r,o)},crypto_box_open:function(t,e,n,r,i,s){var o=new Uint8Array(32);return $(o,i,s),W(t,e,n,r,o)},crypto_box_keypair:K,crypto_hash:V,crypto_sign:at,crypto_sign_keypair:rt,crypto_sign_open:ht,crypto_secretbox_KEYBYTES:32,crypto_secretbox_NONCEBYTES:24,crypto_secretbox_ZEROBYTES:32,crypto_secretbox_BOXZEROBYTES:16,crypto_scalarmult_BYTES:32,crypto_scalarmult_SCALARBYTES:32,crypto_box_PUBLICKEYBYTES:32,crypto_box_SECRETKEYBYTES:32,crypto_box_BEFORENMBYTES:32,crypto_box_NONCEBYTES:24,crypto_box_ZEROBYTES:32,crypto_box_BOXZEROBYTES:16,crypto_sign_BYTES:64,crypto_sign_PUBLICKEYBYTES:32,crypto_sign_SECRETKEYBYTES:64,crypto_sign_SEEDBYTES:32,crypto_hash_BYTES:64,gf:e,D:c,L:it,pack25519:R,unpack25519:N,M:z,A:D,S:H,Z:j,pow2523:F,add:Z,set25519:O,modL:st,scalarmult:et,scalarbase:nt},t.randomBytes=function(t){var e=new Uint8Array(t);return r(e,t),e},t.secretbox=function(t,e,n){ut(t,e,n),ct(n,e);for(var r=new Uint8Array(32+t.length),i=new Uint8Array(r.length),s=0;s<t.length;s++)r[s+32]=t[s];return E(i,r,r.length,e,n),i.subarray(16)},t.secretbox.open=function(t,e,n){ut(t,e,n),ct(n,e);for(var r=new Uint8Array(16+t.length),i=new Uint8Array(r.length),s=0;s<t.length;s++)r[s+16]=t[s];return r.length<32||0!==x(i,r,r.length,e,n)?null:i.subarray(32)},t.secretbox.keyLength=32,t.secretbox.nonceLength=24,t.secretbox.overheadLength=16,t.scalarMult=function(t,e){if(ut(t,e),32!==t.length)throw new Error("bad n size");if(32!==e.length)throw new Error("bad p size");var n=new Uint8Array(32);return q(n,t,e),n},t.scalarMult.base=function(t){if(ut(t),32!==t.length)throw new Error("bad n size");var e=new Uint8Array(32);return Y(e,t),e},t.scalarMult.scalarLength=32,t.scalarMult.groupElementLength=32,t.box=function(e,n,r,i){var s=t.box.before(r,i);return t.secretbox(e,n,s)},t.box.before=function(t,e){ut(t,e),function(t,e){if(32!==t.length)throw new Error("bad public key size");if(32!==e.length)throw new Error("bad secret key size")}(t,e);var n=new Uint8Array(32);return $(n,t,e),n},t.box.after=t.secretbox,t.box.open=function(e,n,r,i){var s=t.box.before(r,i);return t.secretbox.open(e,n,s)},t.box.open.after=t.secretbox.open,t.box.keyPair=function(){var t=new Uint8Array(32),e=new Uint8Array(32);return K(t,e),{publicKey:t,secretKey:e}},t.box.keyPair.fromSecretKey=function(t){if(ut(t),32!==t.length)throw new Error("bad secret key size");var e=new Uint8Array(32);return Y(e,t),{publicKey:e,secretKey:new Uint8Array(t)}},t.box.publicKeyLength=32,t.box.secretKeyLength=32,t.box.sharedKeyLength=32,t.box.nonceLength=24,t.box.overheadLength=t.secretbox.overheadLength,t.sign=function(t,e){if(ut(t,e),64!==e.length)throw new Error("bad secret key size");var n=new Uint8Array(64+t.length);return at(n,t,t.length,e),n},t.sign.open=function(t,e){if(ut(t,e),32!==e.length)throw new Error("bad public key size");var n=new Uint8Array(t.length),r=ht(n,t,t.length,e);if(r<0)return null;for(var i=new Uint8Array(r),s=0;s<i.length;s++)i[s]=n[s];return i},t.sign.detached=function(e,n){for(var r=t.sign(e,n),i=new Uint8Array(64),s=0;s<i.length;s++)i[s]=r[s];return i},t.sign.detached.verify=function(t,e,n){if(ut(t,e,n),64!==e.length)throw new Error("bad signature size");if(32!==n.length)throw new Error("bad public key size");var r,i=new Uint8Array(64+t.length),s=new Uint8Array(64+t.length);for(r=0;r<64;r++)i[r]=e[r];for(r=0;r<t.length;r++)i[r+64]=t[r];return ht(s,i,i.length,n)>=0},t.sign.keyPair=function(){var t=new Uint8Array(32),e=new Uint8Array(64);return rt(t,e),{publicKey:t,secretKey:e}},t.sign.keyPair.fromSecretKey=function(t){if(ut(t),64!==t.length)throw new Error("bad secret key size");for(var e=new Uint8Array(32),n=0;n<e.length;n++)e[n]=t[32+n];return{publicKey:e,secretKey:new Uint8Array(t)}},t.sign.keyPair.fromSeed=function(t){if(ut(t),32!==t.length)throw new Error("bad seed size");for(var e=new Uint8Array(32),n=new Uint8Array(64),r=0;r<32;r++)n[r]=t[r];return rt(e,n,!0),{publicKey:e,secretKey:n}},t.sign.publicKeyLength=32,t.sign.secretKeyLength=64,t.sign.seedLength=32,t.sign.signatureLength=64,t.hash=function(t){ut(t);var e=new Uint8Array(64);return V(e,t,t.length),e},t.hash.hashLength=64,t.verify=function(t,e){return ut(t,e),0!==t.length&&0!==e.length&&(t.length===e.length&&0===g(t,0,e,0,t.length))},t.setPRNG=function(t){r=t},function(){var e="undefined"!=typeof self?self.crypto||self.msCrypto:null;if(e&&e.getRandomValues){t.setPRNG((function(t,n){var r,i=new Uint8Array(n);for(r=0;r<n;r+=65536)e.getRandomValues(i.subarray(r,r+Math.min(n-r,65536)));for(r=0;r<n;r++)t[r]=i[r];lt(i)}))}else(e=n(4))&&e.randomBytes&&t.setPRNG((function(t,n){var r,i=e.randomBytes(n);for(r=0;r<n;r++)t[r]=i[r];lt(i)}))}()}(t.exports?t.exports:self.nacl=self.nacl||{})},function(t,e,n){t.exports=n(5).default},function(t,e){},function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return Le}));for(var r=String.fromCharCode,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s={},o=0,a=i.length;o<a;o++)s[i.charAt(o)]=o;var h=function(t){var e=t.charCodeAt(0);return e<128?t:e<2048?r(192|e>>>6)+r(128|63&e):r(224|e>>>12&15)+r(128|e>>>6&63)+r(128|63&e)},c=function(t){return t.replace(/[^\x00-\x7F]/g,h)},u=function(t){var e=[0,2,1][t.length%3],n=t.charCodeAt(0)<<16|(t.length>1?t.charCodeAt(1):0)<<8|(t.length>2?t.charCodeAt(2):0);return[i.charAt(n>>>18),i.charAt(n>>>12&63),e>=2?"=":i.charAt(n>>>6&63),e>=1?"=":i.charAt(63&n)].join("")},l=self.btoa||function(t){return t.replace(/[\s\S]{1,3}/g,u)};var d=class{constructor(t,e,n,r){this.clear=e,this.timer=t(()=>{this.timer&&(this.timer=r(this.timer))},n)}isRunning(){return null!==this.timer}ensureAborted(){this.timer&&(this.clear(this.timer),this.timer=null)}};function p(t){self.clearTimeout(t)}function f(t){self.clearInterval(t)}class g extends d{constructor(t,e){super(setTimeout,p,t,(function(t){return e(),null}))}}class b extends d{constructor(t,e){super(setInterval,f,t,(function(t){return e(),t}))}}var v={now:()=>Date.now?Date.now():(new Date).valueOf(),defer:t=>new g(0,t),method(t,...e){var n=Array.prototype.slice.call(arguments,1);return function(e){return e[t].apply(e,n.concat(arguments))}}};function y(t,...e){for(var n=0;n<e.length;n++){var r=e[n];for(var i in r)r[i]&&r[i].constructor&&r[i].constructor===Object?t[i]=y(t[i]||{},r[i]):t[i]=r[i]}return t}function m(){for(var t=["Pusher"],e=0;e<arguments.length;e++)"string"==typeof arguments[e]?t.push(arguments[e]):t.push(O(arguments[e]));return t.join(" : ")}function w(t,e){var n=Array.prototype.indexOf;if(null===t)return-1;if(n&&t.indexOf===n)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r;return-1}function _(t,e){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(t[n],n,t)}function S(t){var e=[];return _(t,(function(t,n){e.push(n)})),e}function k(t,e,n){for(var r=0;r<t.length;r++)e.call(n||self,t[r],r,t)}function C(t,e){for(var n=[],r=0;r<t.length;r++)n.push(e(t[r],r,t,n));return n}function T(t,e){e=e||function(t){return!!t};for(var n=[],r=0;r<t.length;r++)e(t[r],r,t,n)&&n.push(t[r]);return n}function P(t,e){var n={};return _(t,(function(r,i){(e&&e(r,i,t,n)||Boolean(r))&&(n[i]=r)})),n}function A(t,e){for(var n=0;n<t.length;n++)if(e(t[n],n,t))return!0;return!1}function E(t){return e=function(t){return"object"==typeof t&&(t=O(t)),encodeURIComponent((e=t.toString(),l(c(e))));var e},n={},_(t,(function(t,r){n[r]=e(t)})),n;var e,n}function x(t){var e,n,r=P(t,(function(t){return void 0!==t}));return C((e=E(r),n=[],_(e,(function(t,e){n.push([e,t])})),n),v.method("join","=")).join("&")}function O(t){try{return JSON.stringify(t)}catch(r){return JSON.stringify((e=[],n=[],function t(r,i){var s,o,a;switch(typeof r){case"object":if(!r)return null;for(s=0;s<e.length;s+=1)if(e[s]===r)return{$ref:n[s]};if(e.push(r),n.push(i),"[object Array]"===Object.prototype.toString.apply(r))for(a=[],s=0;s<r.length;s+=1)a[s]=t(r[s],i+"["+s+"]");else for(o in a={},r)Object.prototype.hasOwnProperty.call(r,o)&&(a[o]=t(r[o],i+"["+JSON.stringify(o)+"]"));return a;case"number":case"string":case"boolean":return r}}(t,"$")))}var e,n}var L={VERSION:"8.4.0",PROTOCOL:7,wsPort:80,wssPort:443,wsPath:"",httpHost:"sockjs.pusher.com",httpPort:80,httpsPort:443,httpPath:"/pusher",stats_host:"stats.pusher.com",authEndpoint:"/pusher/auth",authTransport:"ajax",activityTimeout:12e4,pongTimeout:3e4,unavailableTimeout:1e4,userAuthentication:{endpoint:"/pusher/user-auth",transport:"ajax"},channelAuthorization:{endpoint:"/pusher/auth",transport:"ajax"},cdn_http:"http://js.pusher.com",cdn_https:"https://js.pusher.com",dependency_suffix:""};function U(t,e,n){return t+(e.useTLS?"s":"")+"://"+(e.useTLS?e.hostTLS:e.hostNonTLS)+n}function R(t,e){return"/app/"+t+("?protocol="+L.PROTOCOL+"&client=js&version="+L.VERSION+(e?"&"+e:""))}var M={getInitial:function(t,e){return U("ws",e,(e.httpPath||"")+R(t,"flash=false"))}},I={getInitial:function(t,e){return U("http",e,(e.httpPath||"/pusher")+R(t))}};class N{constructor(){this._callbacks={}}get(t){return this._callbacks[D(t)]}add(t,e,n){var r=D(t);this._callbacks[r]=this._callbacks[r]||[],this._callbacks[r].push({fn:e,context:n})}remove(t,e,n){if(t||e||n){var r=t?[D(t)]:S(this._callbacks);e||n?this.removeCallback(r,e,n):this.removeAllCallbacks(r)}else this._callbacks={}}removeCallback(t,e,n){k(t,(function(t){this._callbacks[t]=T(this._callbacks[t]||[],(function(t){return e&&e!==t.fn||n&&n!==t.context})),0===this._callbacks[t].length&&delete this._callbacks[t]}),this)}removeAllCallbacks(t){k(t,(function(t){delete this._callbacks[t]}),this)}}function D(t){return"_"+t}class j{constructor(t){this.callbacks=new N,this.global_callbacks=[],this.failThrough=t}bind(t,e,n){return this.callbacks.add(t,e,n),this}bind_global(t){return this.global_callbacks.push(t),this}unbind(t,e,n){return this.callbacks.remove(t,e,n),this}unbind_global(t){return t?(this.global_callbacks=T(this.global_callbacks||[],e=>e!==t),this):(this.global_callbacks=[],this)}unbind_all(){return this.unbind(),this.unbind_global(),this}emit(t,e,n){for(var r=0;r<this.global_callbacks.length;r++)this.global_callbacks[r](t,e);var i=this.callbacks.get(t),s=[];if(n?s.push(e,n):e&&s.push(e),i&&i.length>0)for(r=0;r<i.length;r++)i[r].fn.apply(i[r].context||self,s);else this.failThrough&&this.failThrough(t,e);return this}}var z=new class{constructor(){this.globalLog=t=>{self.console&&self.console.log&&self.console.log(t)}}debug(...t){this.log(this.globalLog,t)}warn(...t){this.log(this.globalLogWarn,t)}error(...t){this.log(this.globalLogError,t)}globalLogWarn(t){self.console&&self.console.warn?self.console.warn(t):this.globalLog(t)}globalLogError(t){self.console&&self.console.error?self.console.error(t):this.globalLogWarn(t)}log(t,...e){var n=m.apply(this,arguments);if(xe.log)xe.log(n);else if(xe.logToConsole){t.bind(this)(n)}}};class H extends j{constructor(t,e,n,r,i){super(),this.initialize=ae.transportConnectionInitializer,this.hooks=t,this.name=e,this.priority=n,this.key=r,this.options=i,this.state="new",this.timeline=i.timeline,this.activityTimeout=i.activityTimeout,this.id=this.timeline.generateUniqueID()}handlesActivityChecks(){return Boolean(this.hooks.handlesActivityChecks)}supportsPing(){return Boolean(this.hooks.supportsPing)}connect(){if(this.socket||"initialized"!==this.state)return!1;var t=this.hooks.urls.getInitial(this.key,this.options);try{this.socket=this.hooks.getSocket(t,this.options)}catch(t){return v.defer(()=>{this.onError(t),this.changeState("closed")}),!1}return this.bindListeners(),z.debug("Connecting",{transport:this.name,url:t}),this.changeState("connecting"),!0}close(){return!!this.socket&&(this.socket.close(),!0)}send(t){return"open"===this.state&&(v.defer(()=>{this.socket&&this.socket.send(t)}),!0)}ping(){"open"===this.state&&this.supportsPing()&&this.socket.ping()}onOpen(){this.hooks.beforeOpen&&this.hooks.beforeOpen(this.socket,this.hooks.urls.getPath(this.key,this.options)),this.changeState("open"),this.socket.onopen=void 0}onError(t){this.emit("error",{type:"WebSocketError",error:t}),this.timeline.error(this.buildTimelineMessage({error:t.toString()}))}onClose(t){t?this.changeState("closed",{code:t.code,reason:t.reason,wasClean:t.wasClean}):this.changeState("closed"),this.unbindListeners(),this.socket=void 0}onMessage(t){this.emit("message",t)}onActivity(){this.emit("activity")}bindListeners(){this.socket.onopen=()=>{this.onOpen()},this.socket.onerror=t=>{this.onError(t)},this.socket.onclose=t=>{this.onClose(t)},this.socket.onmessage=t=>{this.onMessage(t)},this.supportsPing()&&(this.socket.onactivity=()=>{this.onActivity()})}unbindListeners(){this.socket&&(this.socket.onopen=void 0,this.socket.onerror=void 0,this.socket.onclose=void 0,this.socket.onmessage=void 0,this.supportsPing()&&(this.socket.onactivity=void 0))}changeState(t,e){this.state=t,this.timeline.info(this.buildTimelineMessage({state:t,params:e})),this.emit(t,e)}buildTimelineMessage(t){return y({cid:this.id},t)}}class B{constructor(t){this.hooks=t}isSupported(t){return this.hooks.isSupported(t)}createConnection(t,e,n,r){return new H(this.hooks,t,e,n,r)}}var F=new B({urls:M,handlesActivityChecks:!1,supportsPing:!1,isInitialized:function(){return Boolean(ae.getWebSocketAPI())},isSupported:function(){return Boolean(ae.getWebSocketAPI())},getSocket:function(t){return ae.createWebSocket(t)}}),q={urls:I,handlesActivityChecks:!1,supportsPing:!0,isInitialized:function(){return!0}},Y=y({getSocket:function(t){return ae.HTTPFactory.createStreamingSocket(t)}},q),K=y({getSocket:function(t){return ae.HTTPFactory.createPollingSocket(t)}},q),$={isSupported:function(){return ae.isXHRSupported()}},J={ws:F,xhr_streaming:new B(y({},Y,$)),xhr_polling:new B(y({},K,$))};class W{constructor(t,e,n){this.manager=t,this.transport=e,this.minPingDelay=n.minPingDelay,this.maxPingDelay=n.maxPingDelay,this.pingDelay=void 0}createConnection(t,e,n,r){r=y({},r,{activityTimeout:this.pingDelay});var i=this.transport.createConnection(t,e,n,r),s=null,o=function(){i.unbind("open",o),i.bind("closed",a),s=v.now()},a=t=>{if(i.unbind("closed",a),1002===t.code||1003===t.code)this.manager.reportDeath();else if(!t.wasClean&&s){var e=v.now()-s;e<2*this.maxPingDelay&&(this.manager.reportDeath(),this.pingDelay=Math.max(e/2,this.minPingDelay))}};return i.bind("open",o),i}isSupported(t){return this.manager.isAlive()&&this.transport.isSupported(t)}}const X={decodeMessage:function(t){try{var e=JSON.parse(t.data),n=e.data;if("string"==typeof n)try{n=JSON.parse(e.data)}catch(t){}var r={event:e.event,channel:e.channel,data:n};return e.user_id&&(r.user_id=e.user_id),r}catch(e){throw{type:"MessageParseError",error:e,data:t.data}}},encodeMessage:function(t){return JSON.stringify(t)},processHandshake:function(t){var e=X.decodeMessage(t);if("pusher:connection_established"===e.event){if(!e.data.activity_timeout)throw"No activity timeout specified in handshake";return{action:"connected",id:e.data.socket_id,activityTimeout:1e3*e.data.activity_timeout}}if("pusher:error"===e.event)return{action:this.getCloseAction(e.data),error:this.getCloseError(e.data)};throw"Invalid handshake"},getCloseAction:function(t){return t.code<4e3?t.code>=1002&&t.code<=1004?"backoff":null:4e3===t.code?"tls_only":t.code<4100?"refused":t.code<4200?"backoff":t.code<4300?"retry":"refused"},getCloseError:function(t){return 1e3!==t.code&&1001!==t.code?{type:"PusherError",data:{code:t.code,message:t.reason||t.message}}:null}};var G=X;class V extends j{constructor(t,e){super(),this.id=t,this.transport=e,this.activityTimeout=e.activityTimeout,this.bindListeners()}handlesActivityChecks(){return this.transport.handlesActivityChecks()}send(t){return this.transport.send(t)}send_event(t,e,n){var r={event:t,data:e};return n&&(r.channel=n),z.debug("Event sent",r),this.send(G.encodeMessage(r))}ping(){this.transport.supportsPing()?this.transport.ping():this.send_event("pusher:ping",{})}close(){this.transport.close()}bindListeners(){var t={message:t=>{var e;try{e=G.decodeMessage(t)}catch(e){this.emit("error",{type:"MessageParseError",error:e,data:t.data})}if(void 0!==e){switch(z.debug("Event recd",e),e.event){case"pusher:error":this.emit("error",{type:"PusherError",data:e.data});break;case"pusher:ping":this.emit("ping");break;case"pusher:pong":this.emit("pong")}this.emit("message",e)}},activity:()=>{this.emit("activity")},error:t=>{this.emit("error",t)},closed:t=>{e(),t&&t.code&&this.handleCloseEvent(t),this.transport=null,this.emit("closed")}},e=()=>{_(t,(t,e)=>{this.transport.unbind(e,t)})};_(t,(t,e)=>{this.transport.bind(e,t)})}handleCloseEvent(t){var e=G.getCloseAction(t),n=G.getCloseError(t);n&&this.emit("error",n),e&&this.emit(e,{action:e,error:n})}}class Z{constructor(t,e){this.transport=t,this.callback=e,this.bindListeners()}close(){this.unbindListeners(),this.transport.close()}bindListeners(){this.onMessage=t=>{var e;this.unbindListeners();try{e=G.processHandshake(t)}catch(t){return this.finish("error",{error:t}),void this.transport.close()}"connected"===e.action?this.finish("connected",{connection:new V(e.id,this.transport),activityTimeout:e.activityTimeout}):(this.finish(e.action,{error:e.error}),this.transport.close())},this.onClosed=t=>{this.unbindListeners();var e=G.getCloseAction(t)||"backoff",n=G.getCloseError(t);this.finish(e,{error:n})},this.transport.bind("message",this.onMessage),this.transport.bind("closed",this.onClosed)}unbindListeners(){this.transport.unbind("message",this.onMessage),this.transport.unbind("closed",this.onClosed)}finish(t,e){this.callback(y({transport:this.transport,action:t},e))}}class Q{constructor(t,e){this.timeline=t,this.options=e||{}}send(t,e){this.timeline.isEmpty()||this.timeline.send(ae.TimelineTransport.getAgent(this,t),e)}}class tt extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class et extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}Error;class nt extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class rt extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class it extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class st extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class ot extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class at extends Error{constructor(t,e){super(e),this.status=t,Object.setPrototypeOf(this,new.target.prototype)}}const ht={baseUrl:"https://pusher.com",urls:{authenticationEndpoint:{path:"/docs/channels/server_api/authenticating_users"},authorizationEndpoint:{path:"/docs/channels/server_api/authorizing-users/"},javascriptQuickStart:{path:"/docs/javascript_quick_start"},triggeringClientEvents:{path:"/docs/client_api_guide/client_events#trigger-events"},encryptedChannelSupport:{fullUrl:"https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support"}}};var ct=function(t){const e=ht.urls[t];if(!e)return"";let n;return e.fullUrl?n=e.fullUrl:e.path&&(n=ht.baseUrl+e.path),n?"See: "+n:""};class ut extends j{constructor(t,e){super((function(e,n){z.debug("No callbacks on "+t+" for "+e)})),this.name=t,this.pusher=e,this.subscribed=!1,this.subscriptionPending=!1,this.subscriptionCancelled=!1}authorize(t,e){return e(null,{auth:""})}trigger(t,e){if(0!==t.indexOf("client-"))throw new tt("Event '"+t+"' does not start with 'client-'");if(!this.subscribed){var n=ct("triggeringClientEvents");z.warn("Client event triggered before channel 'subscription_succeeded' event . "+n)}return this.pusher.send_event(t,e,this.name)}disconnect(){this.subscribed=!1,this.subscriptionPending=!1}handleEvent(t){var e=t.event,n=t.data;if("pusher_internal:subscription_succeeded"===e)this.handleSubscriptionSucceededEvent(t);else if("pusher_internal:subscription_count"===e)this.handleSubscriptionCountEvent(t);else if(0!==e.indexOf("pusher_internal:")){this.emit(e,n,{})}}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):this.emit("pusher:subscription_succeeded",t.data)}handleSubscriptionCountEvent(t){t.data.subscription_count&&(this.subscriptionCount=t.data.subscription_count),this.emit("pusher:subscription_count",t.data)}subscribe(){this.subscribed||(this.subscriptionPending=!0,this.subscriptionCancelled=!1,this.authorize(this.pusher.connection.socket_id,(t,e)=>{t?(this.subscriptionPending=!1,z.error(t.toString()),this.emit("pusher:subscription_error",Object.assign({},{type:"AuthError",error:t.message},t instanceof at?{status:t.status}:{}))):this.pusher.send_event("pusher:subscribe",{auth:e.auth,channel_data:e.channel_data,channel:this.name})}))}unsubscribe(){this.subscribed=!1,this.pusher.send_event("pusher:unsubscribe",{channel:this.name})}cancelSubscription(){this.subscriptionCancelled=!0}reinstateSubscription(){this.subscriptionCancelled=!1}}class lt extends ut{authorize(t,e){return this.pusher.config.channelAuthorizer({channelName:this.name,socketId:t},e)}}class dt{constructor(){this.reset()}get(t){return Object.prototype.hasOwnProperty.call(this.members,t)?{id:t,info:this.members[t]}:null}each(t){_(this.members,(e,n)=>{t(this.get(n))})}setMyID(t){this.myID=t}onSubscription(t){this.members=t.presence.hash,this.count=t.presence.count,this.me=this.get(this.myID)}addMember(t){return null===this.get(t.user_id)&&this.count++,this.members[t.user_id]=t.user_info,this.get(t.user_id)}removeMember(t){var e=this.get(t.user_id);return e&&(delete this.members[t.user_id],this.count--),e}reset(){this.members={},this.count=0,this.myID=null,this.me=null}}var pt=function(t,e,n,r){return new(n||(n=Promise))((function(i,s){function o(t){try{h(r.next(t))}catch(t){s(t)}}function a(t){try{h(r.throw(t))}catch(t){s(t)}}function h(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(o,a)}h((r=r.apply(t,e||[])).next())}))};class ft extends lt{constructor(t,e){super(t,e),this.members=new dt}authorize(t,e){super.authorize(t,(t,n)=>pt(this,void 0,void 0,(function*(){if(!t)if(null!=(n=n).channel_data){var r=JSON.parse(n.channel_data);this.members.setMyID(r.user_id)}else{if(yield this.pusher.user.signinDonePromise,null==this.pusher.user.user_data){let t=ct("authorizationEndpoint");return z.error(`Invalid auth response for channel '${this.name}', expected 'channel_data' field. ${t}, or the user should be signed in.`),void e("Invalid auth response")}this.members.setMyID(this.pusher.user.user_data.id)}e(t,n)})))}handleEvent(t){var e=t.event;if(0===e.indexOf("pusher_internal:"))this.handleInternalEvent(t);else{var n=t.data,r={};t.user_id&&(r.user_id=t.user_id),this.emit(e,n,r)}}handleInternalEvent(t){var e=t.event,n=t.data;switch(e){case"pusher_internal:subscription_succeeded":this.handleSubscriptionSucceededEvent(t);break;case"pusher_internal:subscription_count":this.handleSubscriptionCountEvent(t);break;case"pusher_internal:member_added":var r=this.members.addMember(n);this.emit("pusher:member_added",r);break;case"pusher_internal:member_removed":var i=this.members.removeMember(n);i&&this.emit("pusher:member_removed",i)}}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):(this.members.onSubscription(t.data),this.emit("pusher:subscription_succeeded",this.members))}disconnect(){this.members.reset(),super.disconnect()}}var gt=n(1),bt=n(0);class vt extends lt{constructor(t,e,n){super(t,e),this.key=null,this.nacl=n}authorize(t,e){super.authorize(t,(t,n)=>{if(t)return void e(t,n);let r=n.shared_secret;r?(this.key=Object(bt.decode)(r),delete n.shared_secret,e(null,n)):e(new Error("No shared_secret key in auth payload for encrypted channel: "+this.name),null)})}trigger(t,e){throw new it("Client events are not currently supported for encrypted channels")}handleEvent(t){var e=t.event,n=t.data;0!==e.indexOf("pusher_internal:")&&0!==e.indexOf("pusher:")?this.handleEncryptedEvent(e,n):super.handleEvent(t)}handleEncryptedEvent(t,e){if(!this.key)return void z.debug("Received encrypted event before key has been retrieved from the authEndpoint");if(!e.ciphertext||!e.nonce)return void z.error("Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: "+e);let n=Object(bt.decode)(e.ciphertext);if(n.length<this.nacl.secretbox.overheadLength)return void z.error(`Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${n.length}`);let r=Object(bt.decode)(e.nonce);if(r.length<this.nacl.secretbox.nonceLength)return void z.error(`Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${r.length}`);let i=this.nacl.secretbox.open(n,r,this.key);if(null===i)return z.debug("Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint..."),void this.authorize(this.pusher.connection.socket_id,(e,s)=>{e?z.error(`Failed to make a request to the authEndpoint: ${s}. Unable to fetch new key, so dropping encrypted event`):(i=this.nacl.secretbox.open(n,r,this.key),null!==i?this.emit(t,this.getDataToEmit(i)):z.error("Failed to decrypt event with new key. Dropping encrypted event"))});this.emit(t,this.getDataToEmit(i))}getDataToEmit(t){let e=Object(gt.decode)(t);try{return JSON.parse(e)}catch(t){return e}}}class yt extends j{constructor(t,e){super(),this.state="initialized",this.connection=null,this.key=t,this.options=e,this.timeline=this.options.timeline,this.usingTLS=this.options.useTLS,this.errorCallbacks=this.buildErrorCallbacks(),this.connectionCallbacks=this.buildConnectionCallbacks(this.errorCallbacks),this.handshakeCallbacks=this.buildHandshakeCallbacks(this.errorCallbacks);var n=ae.getNetwork();n.bind("online",()=>{this.timeline.info({netinfo:"online"}),"connecting"!==this.state&&"unavailable"!==this.state||this.retryIn(0)}),n.bind("offline",()=>{this.timeline.info({netinfo:"offline"}),this.connection&&this.sendActivityCheck()}),this.updateStrategy()}connect(){this.connection||this.runner||(this.strategy.isSupported()?(this.updateState("connecting"),this.startConnecting(),this.setUnavailableTimer()):this.updateState("failed"))}send(t){return!!this.connection&&this.connection.send(t)}send_event(t,e,n){return!!this.connection&&this.connection.send_event(t,e,n)}disconnect(){this.disconnectInternally(),this.updateState("disconnected")}isUsingTLS(){return this.usingTLS}startConnecting(){var t=(e,n)=>{e?this.runner=this.strategy.connect(0,t):"error"===n.action?(this.emit("error",{type:"HandshakeError",error:n.error}),this.timeline.error({handshakeError:n.error})):(this.abortConnecting(),this.handshakeCallbacks[n.action](n))};this.runner=this.strategy.connect(0,t)}abortConnecting(){this.runner&&(this.runner.abort(),this.runner=null)}disconnectInternally(){(this.abortConnecting(),this.clearRetryTimer(),this.clearUnavailableTimer(),this.connection)&&this.abandonConnection().close()}updateStrategy(){this.strategy=this.options.getStrategy({key:this.key,timeline:this.timeline,useTLS:this.usingTLS})}retryIn(t){this.timeline.info({action:"retry",delay:t}),t>0&&this.emit("connecting_in",Math.round(t/1e3)),this.retryTimer=new g(t||0,()=>{this.disconnectInternally(),this.connect()})}clearRetryTimer(){this.retryTimer&&(this.retryTimer.ensureAborted(),this.retryTimer=null)}setUnavailableTimer(){this.unavailableTimer=new g(this.options.unavailableTimeout,()=>{this.updateState("unavailable")})}clearUnavailableTimer(){this.unavailableTimer&&this.unavailableTimer.ensureAborted()}sendActivityCheck(){this.stopActivityCheck(),this.connection.ping(),this.activityTimer=new g(this.options.pongTimeout,()=>{this.timeline.error({pong_timed_out:this.options.pongTimeout}),this.retryIn(0)})}resetActivityCheck(){this.stopActivityCheck(),this.connection&&!this.connection.handlesActivityChecks()&&(this.activityTimer=new g(this.activityTimeout,()=>{this.sendActivityCheck()}))}stopActivityCheck(){this.activityTimer&&this.activityTimer.ensureAborted()}buildConnectionCallbacks(t){return y({},t,{message:t=>{this.resetActivityCheck(),this.emit("message",t)},ping:()=>{this.send_event("pusher:pong",{})},activity:()=>{this.resetActivityCheck()},error:t=>{this.emit("error",t)},closed:()=>{this.abandonConnection(),this.shouldRetry()&&this.retryIn(1e3)}})}buildHandshakeCallbacks(t){return y({},t,{connected:t=>{this.activityTimeout=Math.min(this.options.activityTimeout,t.activityTimeout,t.connection.activityTimeout||1/0),this.clearUnavailableTimer(),this.setConnection(t.connection),this.socket_id=this.connection.id,this.updateState("connected",{socket_id:this.socket_id})}})}buildErrorCallbacks(){let t=t=>e=>{e.error&&this.emit("error",{type:"WebSocketError",error:e.error}),t(e)};return{tls_only:t(()=>{this.usingTLS=!0,this.updateStrategy(),this.retryIn(0)}),refused:t(()=>{this.disconnect()}),backoff:t(()=>{this.retryIn(1e3)}),retry:t(()=>{this.retryIn(0)})}}setConnection(t){for(var e in this.connection=t,this.connectionCallbacks)this.connection.bind(e,this.connectionCallbacks[e]);this.resetActivityCheck()}abandonConnection(){if(this.connection){for(var t in this.stopActivityCheck(),this.connectionCallbacks)this.connection.unbind(t,this.connectionCallbacks[t]);var e=this.connection;return this.connection=null,e}}updateState(t,e){var n=this.state;if(this.state=t,n!==t){var r=t;"connected"===r&&(r+=" with new socket ID "+e.socket_id),z.debug("State changed",n+" -> "+r),this.timeline.info({state:t,params:e}),this.emit("state_change",{previous:n,current:t}),this.emit(t,e)}}shouldRetry(){return"connecting"===this.state||"connected"===this.state}}class mt{constructor(){this.channels={}}add(t,e){return this.channels[t]||(this.channels[t]=function(t,e){if(0===t.indexOf("private-encrypted-")){if(e.config.nacl)return wt.createEncryptedChannel(t,e,e.config.nacl);let n="Tried to subscribe to a private-encrypted- channel but no nacl implementation available",r=ct("encryptedChannelSupport");throw new it(`${n}. ${r}`)}if(0===t.indexOf("private-"))return wt.createPrivateChannel(t,e);if(0===t.indexOf("presence-"))return wt.createPresenceChannel(t,e);if(0===t.indexOf("#"))throw new et('Cannot create a channel with name "'+t+'".');return wt.createChannel(t,e)}(t,e)),this.channels[t]}all(){return function(t){var e=[];return _(t,(function(t){e.push(t)})),e}(this.channels)}find(t){return this.channels[t]}remove(t){var e=this.channels[t];return delete this.channels[t],e}disconnect(){_(this.channels,(function(t){t.disconnect()}))}}var wt={createChannels:()=>new mt,createConnectionManager:(t,e)=>new yt(t,e),createChannel:(t,e)=>new ut(t,e),createPrivateChannel:(t,e)=>new lt(t,e),createPresenceChannel:(t,e)=>new ft(t,e),createEncryptedChannel:(t,e,n)=>new vt(t,e,n),createTimelineSender:(t,e)=>new Q(t,e),createHandshake:(t,e)=>new Z(t,e),createAssistantToTheTransportManager:(t,e,n)=>new W(t,e,n)};class _t{constructor(t){this.options=t||{},this.livesLeft=this.options.lives||1/0}getAssistant(t){return wt.createAssistantToTheTransportManager(this,t,{minPingDelay:this.options.minPingDelay,maxPingDelay:this.options.maxPingDelay})}isAlive(){return this.livesLeft>0}reportDeath(){this.livesLeft-=1}}class St{constructor(t,e){this.strategies=t,this.loop=Boolean(e.loop),this.failFast=Boolean(e.failFast),this.timeout=e.timeout,this.timeoutLimit=e.timeoutLimit}isSupported(){return A(this.strategies,v.method("isSupported"))}connect(t,e){var n=this.strategies,r=0,i=this.timeout,s=null,o=(a,h)=>{h?e(null,h):(r+=1,this.loop&&(r%=n.length),r<n.length?(i&&(i*=2,this.timeoutLimit&&(i=Math.min(i,this.timeoutLimit))),s=this.tryStrategy(n[r],t,{timeout:i,failFast:this.failFast},o)):e(!0))};return s=this.tryStrategy(n[r],t,{timeout:i,failFast:this.failFast},o),{abort:function(){s.abort()},forceMinPriority:function(e){t=e,s&&s.forceMinPriority(e)}}}tryStrategy(t,e,n,r){var i=null,s=null;return n.timeout>0&&(i=new g(n.timeout,(function(){s.abort(),r(!0)}))),s=t.connect(e,(function(t,e){t&&i&&i.isRunning()&&!n.failFast||(i&&i.ensureAborted(),r(t,e))})),{abort:function(){i&&i.ensureAborted(),s.abort()},forceMinPriority:function(t){s.forceMinPriority(t)}}}}class kt{constructor(t){this.strategies=t}isSupported(){return A(this.strategies,v.method("isSupported"))}connect(t,e){return function(t,e,n){var r=C(t,(function(t,r,i,s){return t.connect(e,n(r,s))}));return{abort:function(){k(r,Ct)},forceMinPriority:function(t){k(r,(function(e){e.forceMinPriority(t)}))}}}(this.strategies,t,(function(t,n){return function(r,i){n[t].error=r,r?function(t){return function(t,e){for(var n=0;n<t.length;n++)if(!e(t[n],n,t))return!1;return!0}(t,(function(t){return Boolean(t.error)}))}(n)&&e(!0):(k(n,(function(t){t.forceMinPriority(i.transport.priority)})),e(null,i))}}))}}function Ct(t){t.error||t.aborted||(t.abort(),t.aborted=!0)}class Tt{constructor(t,e,n){this.strategy=t,this.transports=e,this.ttl=n.ttl||18e5,this.usingTLS=n.useTLS,this.timeline=n.timeline}isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.usingTLS,r=function(t){var e=ae.getLocalStorage();if(e)try{var n=e[Pt(t)];if(n)return JSON.parse(n)}catch(e){At(t)}return null}(n),i=r&&r.cacheSkipCount?r.cacheSkipCount:0,s=[this.strategy];if(r&&r.timestamp+this.ttl>=v.now()){var o=this.transports[r.transport];o&&(["ws","wss"].includes(r.transport)||i>3?(this.timeline.info({cached:!0,transport:r.transport,latency:r.latency}),s.push(new St([o],{timeout:2*r.latency+1e3,failFast:!0}))):i++)}var a=v.now(),h=s.pop().connect(t,(function r(o,c){o?(At(n),s.length>0?(a=v.now(),h=s.pop().connect(t,r)):e(o)):(!function(t,e,n,r){var i=ae.getLocalStorage();if(i)try{i[Pt(t)]=O({timestamp:v.now(),transport:e,latency:n,cacheSkipCount:r})}catch(t){}}(n,c.transport.name,v.now()-a,i),e(null,c))}));return{abort:function(){h.abort()},forceMinPriority:function(e){t=e,h&&h.forceMinPriority(e)}}}}function Pt(t){return"pusherTransport"+(t?"TLS":"NonTLS")}function At(t){var e=ae.getLocalStorage();if(e)try{delete e[Pt(t)]}catch(t){}}class Et{constructor(t,{delay:e}){this.strategy=t,this.options={delay:e}}isSupported(){return this.strategy.isSupported()}connect(t,e){var n,r=this.strategy,i=new g(this.options.delay,(function(){n=r.connect(t,e)}));return{abort:function(){i.ensureAborted(),n&&n.abort()},forceMinPriority:function(e){t=e,n&&n.forceMinPriority(e)}}}}class xt{constructor(t,e,n){this.test=t,this.trueBranch=e,this.falseBranch=n}isSupported(){return(this.test()?this.trueBranch:this.falseBranch).isSupported()}connect(t,e){return(this.test()?this.trueBranch:this.falseBranch).connect(t,e)}}class Ot{constructor(t){this.strategy=t}isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.strategy.connect(t,(function(t,r){r&&n.abort(),e(t,r)}));return n}}function Lt(t){return function(){return t.isSupported()}}var Ut=function(t,e,n){var r={};function i(e,i,s,o,a){var h=n(t,e,i,s,o,a);return r[e]=h,h}var s,o=Object.assign({},e,{hostNonTLS:t.wsHost+":"+t.wsPort,hostTLS:t.wsHost+":"+t.wssPort,httpPath:t.wsPath}),a=y({},o,{useTLS:!0}),h=Object.assign({},e,{hostNonTLS:t.httpHost+":"+t.httpPort,hostTLS:t.httpHost+":"+t.httpsPort,httpPath:t.httpPath}),c={loop:!0,timeout:15e3,timeoutLimit:6e4},u=new _t({minPingDelay:1e4,maxPingDelay:t.activityTimeout}),l=new _t({lives:2,minPingDelay:1e4,maxPingDelay:t.activityTimeout}),d=i("ws","ws",3,o,u),p=i("wss","ws",3,a,u),f=i("xhr_streaming","xhr_streaming",1,h,l),g=i("xhr_polling","xhr_polling",1,h),b=new St([d],c),v=new St([p],c),m=new St([f],c),w=new St([g],c),_=new St([new xt(Lt(m),new kt([m,new Et(w,{delay:4e3})]),w)],c);return s=e.useTLS?new kt([b,new Et(_,{delay:2e3})]):new kt([b,new Et(v,{delay:2e3}),new Et(_,{delay:5e3})]),new Tt(new Ot(new xt(Lt(d),s,_)),r,{ttl:18e5,timeline:e.timeline,useTLS:e.useTLS})};class Rt extends j{constructor(t,e,n){super(),this.hooks=t,this.method=e,this.url=n}start(t){this.position=0,this.xhr=this.hooks.getRequest(this),this.unloader=()=>{this.close()},ae.addUnloadListener(this.unloader),this.xhr.open(this.method,this.url,!0),this.xhr.setRequestHeader&&this.xhr.setRequestHeader("Content-Type","application/json"),this.xhr.send(t)}close(){this.unloader&&(ae.removeUnloadListener(this.unloader),this.unloader=null),this.xhr&&(this.hooks.abortRequest(this.xhr),this.xhr=null)}onChunk(t,e){for(;;){var n=this.advanceBuffer(e);if(!n)break;this.emit("chunk",{status:t,data:n})}this.isBufferTooLong(e)&&this.emit("buffer_too_long")}advanceBuffer(t){var e=t.slice(this.position),n=e.indexOf("\n");return-1!==n?(this.position+=n+1,e.slice(0,n)):null}isBufferTooLong(t){return this.position===t.length&&t.length>262144}}var Mt;!function(t){t[t.CONNECTING=0]="CONNECTING",t[t.OPEN=1]="OPEN",t[t.CLOSED=3]="CLOSED"}(Mt||(Mt={}));var It=Mt,Nt=1;function Dt(t){var e=-1===t.indexOf("?")?"?":"&";return t+e+"t="+ +new Date+"&n="+Nt++}function jt(t){return ae.randomInt(t)}var zt=class{constructor(t,e){this.hooks=t,this.session=jt(1e3)+"/"+function(t){for(var e=[],n=0;n<t;n++)e.push(jt(32).toString(32));return e.join("")}(8),this.location=function(t){var e=/([^\?]*)\/*(\??.*)/.exec(t);return{base:e[1],queryString:e[2]}}(e),this.readyState=It.CONNECTING,this.openStream()}send(t){return this.sendRaw(JSON.stringify([t]))}ping(){this.hooks.sendHeartbeat(this)}close(t,e){this.onClose(t,e,!0)}sendRaw(t){if(this.readyState!==It.OPEN)return!1;try{return ae.createSocketRequest("POST",Dt((e=this.location,n=this.session,e.base+"/"+n+"/xhr_send"))).start(t),!0}catch(t){return!1}var e,n}reconnect(){this.closeStream(),this.openStream()}onClose(t,e,n){this.closeStream(),this.readyState=It.CLOSED,this.onclose&&this.onclose({code:t,reason:e,wasClean:n})}onChunk(t){var e;if(200===t.status)switch(this.readyState===It.OPEN&&this.onActivity(),t.data.slice(0,1)){case"o":e=JSON.parse(t.data.slice(1)||"{}"),this.onOpen(e);break;case"a":e=JSON.parse(t.data.slice(1)||"[]");for(var n=0;n<e.length;n++)this.onEvent(e[n]);break;case"m":e=JSON.parse(t.data.slice(1)||"null"),this.onEvent(e);break;case"h":this.hooks.onHeartbeat(this);break;case"c":e=JSON.parse(t.data.slice(1)||"[]"),this.onClose(e[0],e[1],!0)}}onOpen(t){var e,n,r;this.readyState===It.CONNECTING?(t&&t.hostname&&(this.location.base=(e=this.location.base,n=t.hostname,(r=/(https?:\/\/)([^\/:]+)((\/|:)?.*)/.exec(e))[1]+n+r[3])),this.readyState=It.OPEN,this.onopen&&this.onopen()):this.onClose(1006,"Server lost session",!0)}onEvent(t){this.readyState===It.OPEN&&this.onmessage&&this.onmessage({data:t})}onActivity(){this.onactivity&&this.onactivity()}onError(t){this.onerror&&this.onerror(t)}openStream(){this.stream=ae.createSocketRequest("POST",Dt(this.hooks.getReceiveURL(this.location,this.session))),this.stream.bind("chunk",t=>{this.onChunk(t)}),this.stream.bind("finished",t=>{this.hooks.onFinished(this,t)}),this.stream.bind("buffer_too_long",()=>{this.reconnect()});try{this.stream.start()}catch(t){v.defer(()=>{this.onError(t),this.onClose(1006,"Could not start streaming",!1)})}}closeStream(){this.stream&&(this.stream.unbind_all(),this.stream.close(),this.stream=null)}},Ht={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr_streaming"+t.queryString},onHeartbeat:function(t){t.sendRaw("[]")},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){t.onClose(1006,"Connection interrupted ("+e+")",!1)}},Bt={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr"+t.queryString},onHeartbeat:function(){},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){200===e?t.reconnect():t.onClose(1006,"Connection interrupted ("+e+")",!1)}},Ft={getRequest:function(t){var e=new(ae.getXHRAPI());return e.onreadystatechange=e.onprogress=function(){switch(e.readyState){case 3:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText);break;case 4:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText),t.emit("finished",e.status),t.close()}},e},abortRequest:function(t){t.onreadystatechange=null,t.abort()}},qt={getDefaultStrategy:Ut,Transports:J,transportConnectionInitializer:function(){this.timeline.info(this.buildTimelineMessage({transport:this.name+(this.options.useTLS?"s":"")})),this.hooks.isInitialized()?this.changeState("initialized"):this.onClose()},HTTPFactory:{createStreamingSocket(t){return this.createSocket(Ht,t)},createPollingSocket(t){return this.createSocket(Bt,t)},createSocket:(t,e)=>new zt(t,e),createXHR(t,e){return this.createRequest(Ft,t,e)},createRequest:(t,e,n)=>new Rt(t,e,n)},setup(t){t.ready()},getLocalStorage(){},getClientFeatures:()=>S(P({ws:J.ws},(function(t){return t.isSupported({})}))),getProtocol:()=>"http:",isXHRSupported:()=>!0,createSocketRequest(t,e){if(this.isXHRSupported())return this.HTTPFactory.createXHR(t,e);throw"Cross-origin HTTP requests are not supported"},createXHR(){return new(this.getXHRAPI())},createWebSocket(t){return new(this.getWebSocketAPI())(t)},addUnloadListener(t){},removeUnloadListener(t){}};var Yt=new class extends j{isOnline(){return!0}},Kt=function(t,e,n,r,i){var s=new Headers;for(var o in s.set("Content-Type","application/x-www-form-urlencoded"),n.headers)s.set(o,n.headers[o]);if(null!=n.headersProvider){const t=n.headersProvider();for(var o in t)s.set(o,t[o])}var a=e,h=new Request(n.endpoint,{headers:s,body:a,credentials:"same-origin",method:"POST"});return fetch(h).then(t=>{let{status:e}=t;if(200===e)return t.text();throw new at(e,`Could not get ${r.toString()} info from your auth endpoint, status: ${e}`)}).then(t=>{let e;try{e=JSON.parse(t)}catch(e){throw new at(200,`JSON returned from ${r.toString()} endpoint was invalid, yet status code was 200. Data was: ${t}`)}i(null,e)}).catch(t=>{i(t,null)})},$t={name:"xhr",getAgent:function(t,e){return function(n,r){var i="http"+(e?"s":"")+"://"+(t.host||t.options.host)+t.options.path,s=x(n);fetch(i+="/2?"+s).then(t=>{if(200!==t.status)throw`received ${t.status} from stats.pusher.com`;return t.json()}).then(({host:e})=>{e&&(t.host=e)}).catch(t=>{z.debug("TimelineSender Error: ",t)})}}};const{getDefaultStrategy:Jt,Transports:Wt,setup:Xt,getProtocol:Gt,isXHRSupported:Vt,getLocalStorage:Zt,createXHR:Qt,createWebSocket:te,addUnloadListener:ee,removeUnloadListener:ne,transportConnectionInitializer:re,createSocketRequest:ie,HTTPFactory:se}=qt;var oe,ae={getDefaultStrategy:Jt,Transports:Wt,setup:Xt,getProtocol:Gt,isXHRSupported:Vt,getLocalStorage:Zt,createXHR:Qt,createWebSocket:te,addUnloadListener:ee,removeUnloadListener:ne,transportConnectionInitializer:re,createSocketRequest:ie,HTTPFactory:se,TimelineTransport:$t,getAuthorizers:()=>({ajax:Kt}),getWebSocketAPI:()=>WebSocket,getXHRAPI:()=>XMLHttpRequest,getNetwork:()=>Yt,randomInt:t=>Math.floor((globalThis.crypto||globalThis.msCrypto).getRandomValues(new Uint32Array(1))[0]/Math.pow(2,32)*t)};!function(t){t[t.ERROR=3]="ERROR",t[t.INFO=6]="INFO",t[t.DEBUG=7]="DEBUG"}(oe||(oe={}));var he=oe;class ce{constructor(t,e,n){this.key=t,this.session=e,this.events=[],this.options=n||{},this.sent=0,this.uniqueID=0}log(t,e){t<=this.options.level&&(this.events.push(y({},e,{timestamp:v.now()})),this.options.limit&&this.events.length>this.options.limit&&this.events.shift())}error(t){this.log(he.ERROR,t)}info(t){this.log(he.INFO,t)}debug(t){this.log(he.DEBUG,t)}isEmpty(){return 0===this.events.length}send(t,e){var n=y({session:this.session,bundle:this.sent+1,key:this.key,lib:"js",version:this.options.version,cluster:this.options.cluster,features:this.options.features,timeline:this.events},this.options.params);return this.events=[],t(n,(t,n)=>{t||this.sent++,e&&e(t,n)}),!0}generateUniqueID(){return this.uniqueID++,this.uniqueID}}class ue{constructor(t,e,n,r){this.name=t,this.priority=e,this.transport=n,this.options=r||{}}isSupported(){return this.transport.isSupported({useTLS:this.options.useTLS})}connect(t,e){if(!this.isSupported())return le(new ot,e);if(this.priority<t)return le(new nt,e);var n=!1,r=this.transport.createConnection(this.name,this.priority,this.options.key,this.options),i=null,s=function(){r.unbind("initialized",s),r.connect()},o=function(){i=wt.createHandshake(r,(function(t){n=!0,c(),e(null,t)}))},a=function(t){c(),e(t)},h=function(){var t;c(),t=O(r),e(new rt(t))},c=function(){r.unbind("initialized",s),r.unbind("open",o),r.unbind("error",a),r.unbind("closed",h)};return r.bind("initialized",s),r.bind("open",o),r.bind("error",a),r.bind("closed",h),r.initialize(),{abort:()=>{n||(c(),i?i.close():r.close())},forceMinPriority:t=>{n||this.priority<t&&(i?i.close():r.close())}}}}function le(t,e){return v.defer((function(){e(t)})),{abort:function(){},forceMinPriority:function(){}}}const{Transports:de}=ae;var pe,fe=function(t,e,n,r,i,s){var o,a=de[n];if(!a)throw new st(n);return!(t.enabledTransports&&-1===w(t.enabledTransports,e)||t.disabledTransports&&-1!==w(t.disabledTransports,e))?(i=Object.assign({ignoreNullOrigin:t.ignoreNullOrigin},i),o=new ue(e,r,s?s.getAssistant(a):a,i)):o=ge,o},ge={isSupported:function(){return!1},connect:function(t,e){var n=v.defer((function(){e(new ot)}));return{abort:function(){n.ensureAborted()},forceMinPriority:function(){}}}};function be(t){if(null==t)throw"You must pass an options object";if(null==t.cluster)throw"Options object must provide a cluster";"disableStats"in t&&z.warn("The disableStats option is deprecated in favor of enableStats")}!function(t){t.UserAuthentication="user-authentication",t.ChannelAuthorization="channel-authorization"}(pe||(pe={}));var ve=t=>{if(void 0===ae.getAuthorizers()[t.transport])throw`'${t.transport}' is not a recognized auth transport`;return(e,n)=>{const r=((t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var r in e.params)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(e.params[r]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r])}return n})(e,t);ae.getAuthorizers()[t.transport](ae,r,t,pe.UserAuthentication,n)}};var ye=t=>{if(void 0===ae.getAuthorizers()[t.transport])throw`'${t.transport}' is not a recognized auth transport`;return(e,n)=>{const r=((t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var r in n+="&channel_name="+encodeURIComponent(t.channelName),e.params)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(e.params[r]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r])}return n})(e,t);ae.getAuthorizers()[t.transport](ae,r,t,pe.ChannelAuthorization,n)}};function me(t){return t.httpHost?t.httpHost:t.cluster?`sockjs-${t.cluster}.pusher.com`:L.httpHost}function we(t){return t.wsHost?t.wsHost:`ws-${t.cluster}.pusher.com`}function _e(t){return"https:"===ae.getProtocol()||!1!==t.forceTLS}function Se(t){return"enableStats"in t?t.enableStats:"disableStats"in t&&!t.disableStats}function ke(t){const e=Object.assign(Object.assign({},L.userAuthentication),t.userAuthentication);return"customHandler"in e&&null!=e.customHandler?e.customHandler:ve(e)}function Ce(t,e){const n=function(t,e){let n;return"channelAuthorization"in t?n=Object.assign(Object.assign({},L.channelAuthorization),t.channelAuthorization):(n={transport:t.authTransport||L.authTransport,endpoint:t.authEndpoint||L.authEndpoint},"auth"in t&&("params"in t.auth&&(n.params=t.auth.params),"headers"in t.auth&&(n.headers=t.auth.headers)),"authorizer"in t&&(n.customHandler=((t,e,n)=>{const r={authTransport:e.transport,authEndpoint:e.endpoint,auth:{params:e.params,headers:e.headers}};return(e,i)=>{const s=t.channel(e.channelName);n(s,r).authorize(e.socketId,i)}})(e,n,t.authorizer))),n}(t,e);return"customHandler"in n&&null!=n.customHandler?n.customHandler:ye(n)}class Te extends j{constructor(t){super((function(t,e){z.debug("No callbacks on watchlist events for "+t)})),this.pusher=t,this.bindWatchlistInternalEvent()}handleEvent(t){t.data.events.forEach(t=>{this.emit(t.name,t)})}bindWatchlistInternalEvent(){this.pusher.connection.bind("message",t=>{"pusher_internal:watchlist_events"===t.event&&this.handleEvent(t)})}}var Pe=function(){let t,e;return{promise:new Promise((n,r)=>{t=n,e=r}),resolve:t,reject:e}};class Ae extends j{constructor(t){super((function(t,e){z.debug("No callbacks on user for "+t)})),this.signin_requested=!1,this.user_data=null,this.serverToUserChannel=null,this.signinDonePromise=null,this._signinDoneResolve=null,this._onAuthorize=(t,e)=>{if(t)return z.warn("Error during signin: "+t),void this._cleanup();this.pusher.send_event("pusher:signin",{auth:e.auth,user_data:e.user_data})},this.pusher=t,this.pusher.connection.bind("state_change",({previous:t,current:e})=>{"connected"!==t&&"connected"===e&&this._signin(),"connected"===t&&"connected"!==e&&(this._cleanup(),this._newSigninPromiseIfNeeded())}),this.watchlist=new Te(t),this.pusher.connection.bind("message",t=>{"pusher:signin_success"===t.event&&this._onSigninSuccess(t.data),this.serverToUserChannel&&this.serverToUserChannel.name===t.channel&&this.serverToUserChannel.handleEvent(t)})}signin(){this.signin_requested||(this.signin_requested=!0,this._signin())}_signin(){this.signin_requested&&(this._newSigninPromiseIfNeeded(),"connected"===this.pusher.connection.state&&this.pusher.config.userAuthenticator({socketId:this.pusher.connection.socket_id},this._onAuthorize))}_onSigninSuccess(t){try{this.user_data=JSON.parse(t.user_data)}catch(e){return z.error("Failed parsing user data after signin: "+t.user_data),void this._cleanup()}if("string"!=typeof this.user_data.id||""===this.user_data.id)return z.error("user_data doesn't contain an id. user_data: "+this.user_data),void this._cleanup();this._signinDoneResolve(),this._subscribeChannels()}_subscribeChannels(){this.serverToUserChannel=new ut("#server-to-user-"+this.user_data.id,this.pusher),this.serverToUserChannel.bind_global((t,e)=>{0!==t.indexOf("pusher_internal:")&&0!==t.indexOf("pusher:")&&this.emit(t,e)}),(t=>{t.subscriptionPending&&t.subscriptionCancelled?t.reinstateSubscription():t.subscriptionPending||"connected"!==this.pusher.connection.state||t.subscribe()})(this.serverToUserChannel)}_cleanup(){this.user_data=null,this.serverToUserChannel&&(this.serverToUserChannel.unbind_all(),this.serverToUserChannel.disconnect(),this.serverToUserChannel=null),this.signin_requested&&this._signinDoneResolve()}_newSigninPromiseIfNeeded(){if(!this.signin_requested)return;if(this.signinDonePromise&&!this.signinDonePromise.done)return;const{promise:t,resolve:e,reject:n}=Pe();t.done=!1;const r=()=>{t.done=!0};t.then(r).catch(r),this.signinDonePromise=t,this._signinDoneResolve=e}}class Ee{static ready(){Ee.isReady=!0;for(var t=0,e=Ee.instances.length;t<e;t++)Ee.instances[t].connect()}static getClientFeatures(){return S(P({ws:ae.Transports.ws},(function(t){return t.isSupported({})})))}constructor(t,e){!function(t){if(null==t)throw"You must pass your app key when you instantiate Pusher."}(t),be(e),this.key=t,this.config=function(t,e){let n={activityTimeout:t.activityTimeout||L.activityTimeout,cluster:t.cluster,httpPath:t.httpPath||L.httpPath,httpPort:t.httpPort||L.httpPort,httpsPort:t.httpsPort||L.httpsPort,pongTimeout:t.pongTimeout||L.pongTimeout,statsHost:t.statsHost||L.stats_host,unavailableTimeout:t.unavailableTimeout||L.unavailableTimeout,wsPath:t.wsPath||L.wsPath,wsPort:t.wsPort||L.wsPort,wssPort:t.wssPort||L.wssPort,enableStats:Se(t),httpHost:me(t),useTLS:_e(t),wsHost:we(t),userAuthenticator:ke(t),channelAuthorizer:Ce(t,e)};return"disabledTransports"in t&&(n.disabledTransports=t.disabledTransports),"enabledTransports"in t&&(n.enabledTransports=t.enabledTransports),"ignoreNullOrigin"in t&&(n.ignoreNullOrigin=t.ignoreNullOrigin),"timelineParams"in t&&(n.timelineParams=t.timelineParams),"nacl"in t&&(n.nacl=t.nacl),n}(e,this),this.channels=wt.createChannels(),this.global_emitter=new j,this.sessionID=ae.randomInt(1e9),this.timeline=new ce(this.key,this.sessionID,{cluster:this.config.cluster,features:Ee.getClientFeatures(),params:this.config.timelineParams||{},limit:50,level:he.INFO,version:L.VERSION}),this.config.enableStats&&(this.timelineSender=wt.createTimelineSender(this.timeline,{host:this.config.statsHost,path:"/timeline/v2/"+ae.TimelineTransport.name}));this.connection=wt.createConnectionManager(this.key,{getStrategy:t=>ae.getDefaultStrategy(this.config,t,fe),timeline:this.timeline,activityTimeout:this.config.activityTimeout,pongTimeout:this.config.pongTimeout,unavailableTimeout:this.config.unavailableTimeout,useTLS:Boolean(this.config.useTLS)}),this.connection.bind("connected",()=>{this.subscribeAll(),this.timelineSender&&this.timelineSender.send(this.connection.isUsingTLS())}),this.connection.bind("message",t=>{var e=0===t.event.indexOf("pusher_internal:");if(t.channel){var n=this.channel(t.channel);n&&n.handleEvent(t)}e||this.global_emitter.emit(t.event,t.data)}),this.connection.bind("connecting",()=>{this.channels.disconnect()}),this.connection.bind("disconnected",()=>{this.channels.disconnect()}),this.connection.bind("error",t=>{z.warn(t)}),Ee.instances.push(this),this.timeline.info({instances:Ee.instances.length}),this.user=new Ae(this),Ee.isReady&&this.connect()}channel(t){return this.channels.find(t)}allChannels(){return this.channels.all()}connect(){if(this.connection.connect(),this.timelineSender&&!this.timelineSenderTimer){var t=this.connection.isUsingTLS(),e=this.timelineSender;this.timelineSenderTimer=new b(6e4,(function(){e.send(t)}))}}disconnect(){this.connection.disconnect(),this.timelineSenderTimer&&(this.timelineSenderTimer.ensureAborted(),this.timelineSenderTimer=null)}bind(t,e,n){return this.global_emitter.bind(t,e,n),this}unbind(t,e,n){return this.global_emitter.unbind(t,e,n),this}bind_global(t){return this.global_emitter.bind_global(t),this}unbind_global(t){return this.global_emitter.unbind_global(t),this}unbind_all(t){return this.global_emitter.unbind_all(),this}subscribeAll(){var t;for(t in this.channels.channels)this.channels.channels.hasOwnProperty(t)&&this.subscribe(t)}subscribe(t){var e=this.channels.add(t,this);return e.subscriptionPending&&e.subscriptionCancelled?e.reinstateSubscription():e.subscriptionPending||"connected"!==this.connection.state||e.subscribe(),e}unsubscribe(t){var e=this.channels.find(t);e&&e.subscriptionPending?e.cancelSubscription():(e=this.channels.remove(t))&&e.subscribed&&e.unsubscribe()}send_event(t,e,n){return this.connection.send_event(t,e,n)}shouldUseTLS(){return this.config.useTLS}signin(){this.user.signin()}}Ee.instances=[],Ee.isReady=!1,Ee.logToConsole=!1,Ee.Runtime=ae,Ee.ScriptReceivers=ae.ScriptReceivers,Ee.DependenciesReceivers=ae.DependenciesReceivers,Ee.auth_callbacks=ae.auth_callbacks;var xe=Ee;ae.setup(Ee);var Oe=n(2);class Le extends xe{constructor(t,e){xe.logToConsole=Le.logToConsole,xe.log=Le.log,be(e),e.nacl=Oe,super(t,e)}}}])}));
//# sourceMappingURL=pusher-with-encryption.worker.min.js.map