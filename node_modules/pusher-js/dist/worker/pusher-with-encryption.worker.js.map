{"version": 3, "sources": ["webpack://Pusher/webpack/universalModuleDefinition", "webpack://Pusher/webpack/bootstrap", "webpack://Pusher/./node_modules/@stablelib/base64/base64.ts", "webpack://Pusher/./node_modules/@stablelib/utf8/utf8.ts", "webpack://Pusher/./node_modules/tweetnacl/nacl-fast.js", "webpack://Pusher/./src/core/pusher-with-encryption.js", "webpack://Pusher/crypto (ignored)", "webpack://Pusher/./src/core/base64.ts", "webpack://Pusher/./src/core/utils/timers/abstract_timer.ts", "webpack://Pusher/./src/core/utils/timers/index.ts", "webpack://Pusher/./src/core/util.ts", "webpack://Pusher/./src/core/utils/collections.ts", "webpack://Pusher/./src/core/defaults.ts", "webpack://Pusher/./src/core/transports/url_schemes.ts", "webpack://Pusher/./src/core/events/callback_registry.ts", "webpack://Pusher/./src/core/events/dispatcher.ts", "webpack://Pusher/./src/core/logger.ts", "webpack://Pusher/./src/core/transports/transport_connection.ts", "webpack://Pusher/./src/core/transports/transport.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transports.ts", "webpack://Pusher/./src/core/transports/assistant_to_the_transport_manager.ts", "webpack://Pusher/./src/core/connection/protocol/protocol.ts", "webpack://Pusher/./src/core/connection/connection.ts", "webpack://Pusher/./src/core/connection/handshake/index.ts", "webpack://Pusher/./src/core/timeline/timeline_sender.ts", "webpack://Pusher/./src/core/errors.ts", "webpack://Pusher/./src/core/utils/url_store.ts", "webpack://Pusher/./src/core/channels/channel.ts", "webpack://Pusher/./src/core/channels/private_channel.ts", "webpack://Pusher/./src/core/channels/members.ts", "webpack://Pusher/./src/core/channels/presence_channel.ts", "webpack://Pusher/./src/core/channels/encrypted_channel.ts", "webpack://Pusher/./src/core/connection/connection_manager.ts", "webpack://Pusher/./src/core/channels/channels.ts", "webpack://Pusher/./src/core/utils/factory.ts", "webpack://Pusher/./src/core/transports/transport_manager.ts", "webpack://Pusher/./src/core/strategies/sequential_strategy.ts", "webpack://Pusher/./src/core/strategies/best_connected_ever_strategy.ts", "webpack://Pusher/./src/core/strategies/websocket_prioritized_cached_strategy.ts", "webpack://Pusher/./src/core/strategies/delayed_strategy.ts", "webpack://Pusher/./src/core/strategies/if_strategy.ts", "webpack://Pusher/./src/core/strategies/first_connected_strategy.ts", "webpack://Pusher/./src/runtimes/isomorphic/default_strategy.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transport_connection_initializer.ts", "webpack://Pusher/./src/core/http/http_request.ts", "webpack://Pusher/./src/core/http/state.ts", "webpack://Pusher/./src/core/http/http_socket.ts", "webpack://Pusher/./src/core/http/http_streaming_socket.ts", "webpack://Pusher/./src/core/http/http_polling_socket.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http_xhr_request.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http.ts", "webpack://Pusher/./src/runtimes/isomorphic/runtime.ts", "webpack://Pusher/./src/runtimes/worker/net_info.ts", "webpack://Pusher/./src/runtimes/worker/auth/fetch_auth.ts", "webpack://Pusher/./src/runtimes/worker/timeline/fetch_timeline.ts", "webpack://Pusher/./src/runtimes/worker/runtime.ts", "webpack://Pusher/./src/core/timeline/level.ts", "webpack://Pusher/./src/core/timeline/timeline.ts", "webpack://Pusher/./src/core/strategies/transport_strategy.ts", "webpack://Pusher/./src/core/strategies/strategy_builder.ts", "webpack://Pusher/./src/core/options.ts", "webpack://Pusher/./src/core/auth/options.ts", "webpack://Pusher/./src/core/auth/user_authenticator.ts", "webpack://Pusher/./src/core/auth/channel_authorizer.ts", "webpack://Pusher/./src/core/auth/deprecated_channel_authorizer.ts", "webpack://Pusher/./src/core/config.ts", "webpack://Pusher/./src/core/watchlist.ts", "webpack://Pusher/./src/core/utils/flat_promise.ts", "webpack://Pusher/./src/core/user.ts", "webpack://Pusher/./src/core/pusher.ts", "webpack://Pusher/./src/core/pusher-with-encryption.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;AClFA,sCAAsC;AACtC,6CAA6C;;;;;;;;;;;;;;;AAE7C;;GAEG;AAEH,iDAAiD;AACjD,kDAAkD;AAClD,kCAAkC;AAClC,IAAM,YAAY,GAAG,GAAG,CAAC;AAEzB;;;;GAIG;AACH;IACI,kDAAkD;IAElD,eAAoB,iBAAuB;QAAvB,2DAAuB;QAAvB,sBAAiB,GAAjB,iBAAiB,CAAM;IAAI,CAAC;IAEhD,6BAAa,GAAb,UAAc,MAAc;QACxB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACnC;QACD,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED,sBAAM,GAAN,UAAO,IAAgB;QACnB,IAAI,GAAG,GAAG,EAAE,CAAC;QAEb,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7D,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5C,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5C,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5C,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;SAC/C;QAED,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7B,IAAI,IAAI,GAAG,CAAC,EAAE;YACV,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5C,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5C,IAAI,IAAI,KAAK,CAAC,EAAE;gBACZ,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;aAC/C;iBAAM;gBACH,GAAG,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAC;aACvC;YACD,GAAG,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAC;SACvC;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAED,gCAAgB,GAAhB,UAAiB,MAAc;QAC3B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACnC;QACD,OAAO,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,6BAAa,GAAb,UAAc,CAAS;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,sBAAM,GAAN,UAAO,CAAS;QACZ,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAChB,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;SAC5B;QACD,IAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAChD,IAAM,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,aAAa,CAAC;QACxC,IAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1D,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YAC3B,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YACnC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YACnC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;YAC3B,OAAO,IAAI,EAAE,GAAG,YAAY,CAAC;YAC7B,OAAO,IAAI,EAAE,GAAG,YAAY,CAAC;YAC7B,OAAO,IAAI,EAAE,GAAG,YAAY,CAAC;YAC7B,OAAO,IAAI,EAAE,GAAG,YAAY,CAAC;SAChC;QACD,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YACnC,OAAO,IAAI,EAAE,GAAG,YAAY,CAAC;YAC7B,OAAO,IAAI,EAAE,GAAG,YAAY,CAAC;SAChC;QACD,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YACnC,OAAO,IAAI,EAAE,GAAG,YAAY,CAAC;SAChC;QACD,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;YAC3B,OAAO,IAAI,EAAE,GAAG,YAAY,CAAC;SAChC;QACD,IAAI,OAAO,KAAK,CAAC,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACrE;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED,+DAA+D;IAC/D,oCAAoC;IACpC,EAAE;IACF,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,EAAE;IAEF,2CAA2C;IACjC,2BAAW,GAArB,UAAsB,CAAS;QAC3B,qDAAqD;QACrD,EAAE;QACF,wDAAwD;QACxD,qDAAqD;QACrD,uCAAuC;QACvC,EAAE;QACF,0DAA0D;QAC1D,uCAAuC;QACvC,uCAAuC;QACvC,EAAE;QACF,kEAAkE;QAClE,+DAA+D;QAC/D,EAAE;QACF,gEAAgE;QAChE,gEAAgE;QAChE,8BAA8B;QAC9B,EAAE;QACF,2CAA2C;QAC3C,wCAAwC;QACxC,EAAE;QACF,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,SAAS;QACT,MAAM,IAAI,EAAE,CAAC;QACb,SAAS;QACT,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAClD,SAAS;QACT,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACnD,SAAS;QACT,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACnD,SAAS;QACT,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAEnD,OAAO,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAED,uCAAuC;IACvC,yDAAyD;IAC/C,2BAAW,GAArB,UAAsB,CAAS;QAC3B,gEAAgE;QAChE,qEAAqE;QACrE,4DAA4D;QAC5D,aAAa;QACb,EAAE;QACF,yDAAyD;QACzD,gEAAgE;QAChE,4DAA4D;QAC5D,6BAA6B;QAC7B,IAAI,MAAM,GAAG,YAAY,CAAC,CAAC,+BAA+B;QAE1D,8BAA8B;QAC9B,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACxE,8BAA8B;QAC9B,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACxE,oBAAoB;QACpB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACxE,oBAAoB;QACpB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACvE,qBAAqB;QACrB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAEzE,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,iCAAiB,GAAzB,UAA0B,CAAS;QAC/B,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,iBAAiB,EAAE;oBACjC,MAAM;iBACT;gBACD,aAAa,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;aACrD;SACJ;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAEL,YAAC;AAAD,CAAC;AA3LY,sBAAK;AA6LlB,IAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;AAE7B,SAAgB,MAAM,CAAC,IAAgB;IACnC,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC;AAFD,wBAEC;AAED,SAAgB,MAAM,CAAC,CAAS;IAC5B,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC;AAFD,wBAEC;AAED;;;;;GAKG;AACH;IAAkC,gCAAK;IAAvC;;IAwCA,CAAC;IAvCG,+DAA+D;IAC/D,EAAE;IACF,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,EAAE;IAEQ,kCAAW,GAArB,UAAsB,CAAS;QAC3B,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,SAAS;QACT,MAAM,IAAI,EAAE,CAAC;QACb,SAAS;QACT,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAClD,SAAS;QACT,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACnD,SAAS;QACT,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACnD,SAAS;QACT,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAEnD,OAAO,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAES,kCAAW,GAArB,UAAsB,CAAS;QAC3B,IAAI,MAAM,GAAG,YAAY,CAAC;QAE1B,8BAA8B;QAC9B,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACxE,8BAA8B;QAC9B,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACxE,oBAAoB;QACpB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACxE,oBAAoB;QACpB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACvE,qBAAqB;QACrB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAEzE,OAAO,MAAM,CAAC;IAClB,CAAC;IACL,mBAAC;AAAD,CAAC,CAxCiC,KAAK,GAwCtC;AAxCY,oCAAY;AA0CzB,IAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAExC,SAAgB,aAAa,CAAC,IAAgB;IAC1C,OAAO,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrC,CAAC;AAFD,sCAEC;AAED,SAAgB,aAAa,CAAC,CAAS;IACnC,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,CAAC;AAFD,sCAEC;AAGY,qBAAa,GAAG,UAAC,MAAc;IACxC,eAAQ,CAAC,aAAa,CAAC,MAAM,CAAC;AAA9B,CAA8B,CAAC;AAEtB,wBAAgB,GAAG,UAAC,MAAc;IAC3C,eAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAAjC,CAAiC,CAAC;AAEzB,qBAAa,GAAG,UAAC,CAAS;IACnC,eAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;AAAzB,CAAyB,CAAC;;;;;;;;;AC1R9B,sCAAsC;AACtC,6CAA6C;;AAE7C;;GAEG;AAEH,IAAM,aAAa,GAAG,sBAAsB,CAAC;AAC7C,IAAM,YAAY,GAAG,+BAA+B,CAAC;AAErD;;;GAGG;AACH,SAAgB,MAAM,CAAC,CAAS;IAC5B,qDAAqD;IACrD,2DAA2D;IAC3D,2CAA2C;IAC3C,IAAM,GAAG,GAAG,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAE7C,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,IAAI,EAAE;YACV,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;SAClB;aAAM,IAAI,CAAC,GAAG,KAAK,EAAE;YAClB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;YAC3B,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;SAChC;aAAM,IAAI,CAAC,GAAG,MAAM,EAAE;YACnB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YAC5B,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YACpC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;SAChC;aAAM;YACH,CAAC,EAAE,CAAC,CAAC,yBAAyB;YAC9B,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YACtB,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YAC7B,CAAC,IAAI,OAAO,CAAC;YAEb,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YAC5B,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;YACrC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YACpC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;SAChC;KACJ;IACD,OAAO,GAAG,CAAC;AACf,CAAC;AA/BD,wBA+BC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAAC,CAAS;IACnC,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,IAAM,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,GAAG,IAAI,EAAE;YACV,MAAM,IAAI,CAAC,CAAC;SACf;aAAM,IAAI,CAAC,GAAG,KAAK,EAAE;YAClB,MAAM,IAAI,CAAC,CAAC;SACf;aAAM,IAAI,CAAC,GAAG,MAAM,EAAE;YACnB,MAAM,IAAI,CAAC,CAAC;SACf;aAAM,IAAI,CAAC,IAAI,MAAM,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;aAClC;YACD,CAAC,EAAE,CAAC,CAAC,uBAAuB;YAC5B,MAAM,IAAI,CAAC,CAAC;SACf;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;SAClC;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AArBD,sCAqBC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,GAAe;IAClC,IAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAEf,IAAI,CAAC,GAAG,IAAI,EAAE;YACV,IAAI,GAAG,UAAC;YACR,IAAI,CAAC,GAAG,IAAI,EAAE;gBACV,oBAAoB;gBACpB,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;oBACjB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;iBACjC;gBACD,IAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;oBACtB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;iBACjC;gBACD,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;gBAClC,GAAG,GAAG,IAAI,CAAC;aACd;iBAAM,IAAI,CAAC,GAAG,IAAI,EAAE;gBACjB,qBAAqB;gBACrB,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;iBACjC;gBACD,IAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpB,IAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;oBAC9C,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;iBACjC;gBACD,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;gBACtD,GAAG,GAAG,KAAK,CAAC;aACf;iBAAM,IAAI,CAAC,GAAG,IAAI,EAAE;gBACjB,qBAAqB;gBACrB,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;iBACjC;gBACD,IAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpB,IAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpB,IAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;oBACtE,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;iBACjC;gBACD,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC1E,GAAG,GAAG,OAAO,CAAC;aACjB;iBAAM;gBACH,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;aACjC;YAED,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;gBACzC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;aACjC;YAED,IAAI,CAAC,IAAI,OAAO,EAAE;gBACd,kBAAkB;gBAClB,IAAI,CAAC,GAAG,QAAQ,EAAE;oBACd,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;iBACjC;gBACD,CAAC,IAAI,OAAO,CAAC;gBACb,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;aAC5B;SACJ;QAED,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1B,CAAC;AAjED,wBAiEC;;;;;;;AC/ID;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,uBAAuB,iBAAiB;AACxC;AACA;;AAEA;AACA,wCAAwC,4BAA4B;;AAEpE;AACA,4BAA4B;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,OAAO;AACpB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,OAAO;AACpB;AACA;AACA,eAAe,QAAQ;AACvB;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,OAAO;AACtB;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,OAAO;AACpB;AACA;AACA,eAAe,QAAQ;AACvB;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,OAAO;AACtB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB,OAAO;AACxB;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB,OAAO;AACxB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,8CAA8C;AAC9C,8CAA8C;AAC9C,8CAA8C;AAC9C,8CAA8C;AAC9C,8CAA8C;AAC9C;AACA,8CAA8C;AAC9C,8CAA8C;AAC9C,8CAA8C;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,sDAAsD;AACtD,sDAAsD;AACtD,sDAAsD;AACtD,sDAAsD;AACtD,sDAAsD;AACtD;AACA,sDAAsD;AACtD,sDAAsD;AACtD,sDAAsD;AACtD;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU,QAAQ;AAClB;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,QAAQ;AACrB;AACA,aAAa,QAAQ;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,eAAe,WAAW;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA,aAAa,OAAO;AACpB;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;;AAEA;AACA,iBAAiB,QAAQ;AACzB;;AAEA;AACA,iBAAiB,QAAQ;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD;;AAEA;AACA;AACA,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD,sBAAsB,2BAA2B;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,QAAQ;AACvB;AACA;AACA;AACA,aAAa,QAAQ;AACrB;;AAEA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,QAAQ;AACvB;AACA;AACA;AACA,aAAa,QAAQ;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA,aAAa,MAAM;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB;AACrB,qBAAqB;;AAErB;AACA;AACA;;AAEA,sBAAsB;AACtB,sBAAsB;;AAEtB;AACA;AACA;;AAEA,sBAAsB;AACtB,sBAAsB;;AAEtB;AACA;AACA;;AAEA,sBAAsB;AACtB,sBAAsB;;AAEtB;AACA;AACA;;AAEA,sBAAsB;AACtB,sBAAsB;;AAEtB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB;AACrB,qBAAqB;;AAErB;AACA;AACA;;AAEA,sBAAsB;AACtB,sBAAsB;;AAEtB;AACA;AACA;;AAEA,sBAAsB;AACtB,sBAAsB;;AAEtB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB;AACrB,qBAAqB;;AAErB;AACA;;AAEA,sBAAsB;AACtB,sBAAsB;;AAEtB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB,QAAQ;AAC3B;AACA;AACA;;AAEA,yBAAyB;AACzB,yBAAyB;;AAEzB;AACA;;AAEA,0BAA0B;AAC1B,0BAA0B;;AAE1B;AACA;AACA;AACA;AACA;;AAEA,0BAA0B;AAC1B,0BAA0B;;AAE1B;AACA;AACA;AACA;AACA;;AAEA,0BAA0B;AAC1B,0BAA0B;;AAE1B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,mBAAmB;AACnB,mBAAmB;;AAEnB;AACA;;AAEA,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,mBAAmB;AACnB,mBAAmB;;AAEnB;AACA;;AAEA,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,mBAAmB;AACnB,mBAAmB;;AAEnB;AACA;;AAEA,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,mBAAmB;AACnB,mBAAmB;;AAEnB;AACA;;AAEA,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,mBAAmB;AACnB,mBAAmB;;AAEnB;AACA;;AAEA,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,mBAAmB;AACnB,mBAAmB;;AAEnB;AACA;;AAEA,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,mBAAmB;AACnB,mBAAmB;;AAEnB;AACA;;AAEA,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,mBAAmB;AACnB,mBAAmB;;AAEnB;AACA;;AAEA,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,aAAa,OAAO;AACpB;;AAEA;AACA;AACA;AACA;;AAEA,aAAa,OAAO;;AAEpB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,aAAa,QAAQ;AACrB;AACA;;AAEA;;AAEA;AACA;AACA,cAAc,SAAS;AACvB;AACA,gCAAgC,OAAO;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,aAAa,OAAO;AACpB,aAAa,QAAQ;;AAErB;AACA;AACA;AACA;;AAEA,cAAc,QAAQ;AACtB;AACA;;AAEA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,QAAQ;AACvB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA,aAAa,OAAO;AACpB,aAAa,QAAQ;AACrB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,eAAe,OAAO;AACtB;AACA;;AAEA,aAAa,OAAO;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,gBAAgB;AACjC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,cAAc;AAC/B;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,uBAAuB;AACpC,aAAa,gBAAgB;AAC7B;AACA;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,eAAe;AAChC,UAAU;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA,UAAU;AACV;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,OAAO;AACxB;AACA;AACA,iBAAiB,OAAO;AACxB;AACA,KAAK;AACL,GAAG,UAAU,IAA8B;AAC3C;AACA,aAAa,mBAAO,CAAC,CAAQ;AAC7B;AACA;AACA;AACA,mBAAmB,OAAO;AAC1B;AACA,OAAO;AACP;AACA;AACA,CAAC;;AAED,CAAC,EAAE,KAA6B,kEAAkE;;;;;;;ACt1ElG,iBAAiB,mBAAO,CAAC,CAA0B;;;;;;;ACAnD,e;;;;;;;;;;;;;;ACAe,SAAS,MAAM,CAAC,CAAM;IACnC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;AAED,IAAI,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AAEvC,IAAI,QAAQ,GACV,kEAAkE,CAAC;AACrE,IAAI,MAAM,GAAG,EAAE,CAAC;AAEhB,KAAK,IAAI,QAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,QAAC,GAAG,CAAC,EAAE,QAAC,EAAE,EAAE;IAC/C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAC,CAAC,CAAC,GAAG,QAAC,CAAC;CAChC;AAED,IAAI,OAAO,GAAG,UAAU,CAAC;IACvB,IAAI,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACzB,OAAO,EAAE,GAAG,IAAI;QACd,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,GAAG,KAAK;YACV,CAAC,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;YACpE,CAAC,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;gBACzC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;gBACxC,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC;AAEF,IAAI,IAAI,GAAG,UAAU,CAAC;IACpB,OAAO,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF,IAAI,SAAS,GAAG,UAAU,GAAG;IAC3B,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,GAAG,GACL,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,KAAK,GAAG;QACV,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,EAAE,CAAC;QAC3B,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;QAClC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;QACrD,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC;KAC9C,CAAC;IACF,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,IAAI,IAAI,GACN,IAAM,CAAC,IAAI;IACX,UAAU,CAAC;QACT,OAAO,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC,CAAC;;;AC7CJ,MAAe,KAAK;IAIlB,YACE,GAAc,EACd,KAAgB,EAChB,KAAY,EACZ,QAAuB;QAEvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE;YACpB,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACnC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAMD,SAAS;QACP,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC;IAC7B,CAAC;IAGD,aAAa;QACX,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACnB;IACH,CAAC;CACF;AAEc,wDAAK,EAAC;;;ACtCgB;AAKrC,SAAS,mBAAY,CAAC,KAAK;IACzB,IAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AACD,SAAS,oBAAa,CAAC,KAAK;IAC1B,IAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAOM,MAAM,kBAAY,SAAQ,cAAK;IACpC,YAAY,KAAY,EAAE,QAAuB;QAC/C,KAAK,CAAC,UAAU,EAAE,mBAAY,EAAE,KAAK,EAAE,UAAU,KAAK;YACpD,QAAQ,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAOM,MAAM,oBAAc,SAAQ,cAAK;IACtC,YAAY,KAAY,EAAE,QAAuB;QAC/C,KAAK,CAAC,WAAW,EAAE,oBAAa,EAAE,KAAK,EAAE,UAAU,KAAK;YACtD,QAAQ,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;CACF;;;ACpC2D;AAE5D,IAAI,IAAI,GAAG;IACT,GAAG;QACD,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;SACnB;aAAM;YACL,OAAO,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;SAC7B;IACH,CAAC;IAED,KAAK,CAAC,QAAuB;QAC3B,OAAO,IAAI,kBAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAUD,MAAM,CAAC,IAAY,EAAE,GAAG,IAAW;QACjC,IAAI,cAAc,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAC9D,OAAO,UAAU,MAAM;YACrB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC;IACJ,CAAC;CACF,CAAC;AAEa,6CAAI,EAAC;;;ACjCiB;AACV;AAgBpB,SAAS,MAAM,CAAI,MAAW,EAAE,GAAG,OAAc;IACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,IAAI,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5B,KAAK,IAAI,QAAQ,IAAI,UAAU,EAAE;YAC/B,IACE,UAAU,CAAC,QAAQ,CAAC;gBACpB,UAAU,CAAC,QAAQ,CAAC,CAAC,WAAW;gBAChC,UAAU,CAAC,QAAQ,CAAC,CAAC,WAAW,KAAK,MAAM,EAC3C;gBACA,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;aACzE;iBAAM;gBACL,MAAM,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;aACzC;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAEM,SAAS,SAAS;IACvB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACzC,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACpC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACtB;aAAM;YACL,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACzC;KACF;IACD,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AAEM,SAAS,YAAY,CAAC,KAAY,EAAE,IAAS;IAElD,IAAI,aAAa,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC;IAC5C,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,CAAC,CAAC,CAAC;KACX;IACD,IAAI,aAAa,IAAI,KAAK,CAAC,OAAO,KAAK,aAAa,EAAE;QACpD,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAC5B;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC5C,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACrB,OAAO,CAAC,CAAC;SACV;KACF;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAYM,SAAS,WAAW,CAAC,MAAW,EAAE,CAAW;IAClD,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;QACtB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YACrD,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;SAC7B;KACF;AACH,CAAC;AAOM,SAAS,IAAI,CAAC,MAAW;IAC9B,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG;QAClC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;IACH,OAAO,IAAI,CAAC;AACd,CAAC;AAOM,SAAS,MAAM,CAAC,MAAW;IAChC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,WAAW,CAAC,MAAM,EAAE,UAAU,KAAK;QACjC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAYM,SAAS,KAAK,CAAC,KAAY,EAAE,CAAW,EAAE,OAAa;IAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;KAC/C;AACH,CAAC;AAaM,SAAS,GAAG,CAAC,KAAY,EAAE,CAAW;IAC3C,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;KAC5C;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAaM,SAAS,SAAS,CAAC,MAAW,EAAE,CAAW;IAChD,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,WAAW,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE,GAAG;QACtC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAaM,SAAS,MAAM,CAAC,KAAY,EAAE,IAAc;IACjD,IAAI;QACF,IAAI;YACJ,UAAU,KAAK;gBACb,OAAO,CAAC,CAAC,KAAK,CAAC;YACjB,CAAC,CAAC;IAEJ,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACvB;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAaM,SAAS,YAAY,CAAC,MAAc,EAAE,IAAc;IACzD,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,WAAW,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE,GAAG;QACtC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;YAChE,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACrB;IACH,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAOM,SAAS,OAAO,CAAC,MAAc;IACpC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,WAAW,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE,GAAG;QACtC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAYM,SAAS,GAAG,CAAC,KAAY,EAAE,IAAc;IAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAYM,SAAS,eAAG,CAAC,KAAY,EAAE,IAAc;IAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,SAAS,kBAAkB,CAAC,IAAI;IACrC,OAAO,SAAS,CAAC,IAAI,EAAE,UAAU,KAAK;QACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;SAClC;QACD,OAAO,kBAAkB,CAAC,MAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;AACL,CAAC;AAEM,SAAS,gBAAgB,CAAC,IAAS;IACxC,IAAI,MAAM,GAAG,YAAY,CAAC,IAAI,EAAE,UAAU,KAAK;QAC7C,OAAO,KAAK,KAAK,SAAS,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,IAAI,KAAK,GAAG,GAAG,CACb,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,EACnC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CACzB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,OAAO,KAAK,CAAC;AACf,CAAC;AAWM,SAAS,aAAa,CAAC,MAAW;IACvC,IAAI,OAAO,GAAG,EAAE,EACd,KAAK,GAAG,EAAE,CAAC;IAEb,OAAO,CAAC,SAAS,KAAK,CAAC,KAAK,EAAE,IAAI;QAChC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;QAEhB,QAAQ,OAAO,KAAK,EAAE;YACpB,KAAK,QAAQ;gBACX,IAAI,CAAC,KAAK,EAAE;oBACV,OAAO,IAAI,CAAC;iBACb;gBACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;oBACtC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;wBACxB,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;qBAC3B;iBACF;gBAED,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEjB,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,gBAAgB,EAAE;oBAC/D,EAAE,GAAG,EAAE,CAAC;oBACR,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;wBACpC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;qBAC/C;iBACF;qBAAM;oBACL,EAAE,GAAG,EAAE,CAAC;oBACR,KAAK,IAAI,IAAI,KAAK,EAAE;wBAClB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;4BACrD,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,CACd,KAAK,CAAC,IAAI,CAAC,EACX,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,CACxC,CAAC;yBACH;qBACF;iBACF;gBACD,OAAO,EAAE,CAAC;YACZ,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ,CAAC;YACd,KAAK,SAAS;gBACZ,OAAO,KAAK,CAAC;SAChB;IACH,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAClB,CAAC;AAUM,SAAS,iBAAiB,CAAC,MAAW;IAC3C,IAAI;QACF,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KAC/B;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KAC9C;AACH,CAAC;;;AClUD,IAAI,QAAQ,GAAkB;IAC5B,OAAO,EAAE,OAAO;IAChB,QAAQ,EAAE,CAAC;IAEX,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,EAAE;IAEV,QAAQ,EAAE,mBAAmB;IAC7B,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,GAAG;IACd,QAAQ,EAAE,SAAS;IAEnB,UAAU,EAAE,kBAAkB;IAE9B,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE,MAAM;IACvB,WAAW,EAAE,KAAK;IAClB,kBAAkB,EAAE,KAAK;IACzB,kBAAkB,EAAE;QAClB,QAAQ,EAAE,mBAAmB;QAC7B,SAAS,EAAE,MAAM;KAClB;IACD,oBAAoB,EAAE;QACpB,QAAQ,EAAE,cAAc;QACxB,SAAS,EAAE,MAAM;KAClB;IAGD,QAAQ,EAAE,sBAAQ;IAClB,SAAS,EAAE,uBAAS;IACpB,iBAAiB,EAAE,EAAiB;CACrC,CAAC;AAEa,qDAAQ,EAAC;;;ACjEW;AAGnC,SAAS,aAAa,CACpB,UAAkB,EAClB,MAAuB,EACvB,IAAY;IAEZ,IAAI,MAAM,GAAG,UAAU,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrD,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;IAC9D,OAAO,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;AACtC,CAAC;AAED,SAAS,cAAc,CAAC,GAAW,EAAE,WAAoB;IACvD,IAAI,IAAI,GAAG,OAAO,GAAG,GAAG,CAAC;IACzB,IAAI,KAAK,GACP,YAAY;QACZ,QAAQ,CAAC,QAAQ;QACjB,YAAY;QACZ,WAAW;QACX,QAAQ,CAAC,OAAO;QAChB,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzC,OAAO,IAAI,GAAG,KAAK,CAAC;AACtB,CAAC;AAEM,IAAI,EAAE,GAAc;IACzB,UAAU,EAAE,UAAU,GAAW,EAAE,MAAuB;QACxD,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,cAAc,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QACxE,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;CACF,CAAC;AAEK,IAAI,IAAI,GAAc;IAC3B,UAAU,EAAE,UAAU,GAAW,EAAE,MAAuB;QACxD,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,SAAS,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;CACF,CAAC;AAEK,IAAI,MAAM,GAAc;IAC7B,UAAU,EAAE,UAAU,GAAW,EAAE,MAAuB;QACxD,OAAO,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,QAAQ,IAAI,SAAS,CAAC,CAAC;IACrE,CAAC;IACD,OAAO,EAAE,UAAU,GAAW,EAAE,MAAuB;QACrD,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;CACF,CAAC;;;AC7CkD;AAGrC,MAAM,kCAAgB;IAGnC;QACE,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAED,GAAG,CAAC,IAAY;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACvC,CAAC;IAED,GAAG,CAAC,IAAY,EAAE,QAAkB,EAAE,OAAY;QAChD,IAAI,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;YAChC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;YACtC,EAAE,EAAE,QAAQ;YACZ,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,IAAa,EAAE,QAAmB,EAAE,OAAa;QACtD,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,EAAE;YAClC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,OAAO;SACR;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtE,IAAI,QAAQ,IAAI,OAAO,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;SAC/C;aAAM;YACL,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAChC;IACH,CAAC;IAEO,cAAc,CAAC,KAAe,EAAE,QAAkB,EAAE,OAAY;QACtE,KAAiB,CACf,KAAK,EACL,UAAU,IAAI;YACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,MAAkB,CACxC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,EAC3B,UAAU,OAAO;gBACf,OAAO,CACL,CAAC,QAAQ,IAAI,QAAQ,KAAK,OAAO,CAAC,EAAE,CAAC;oBACrC,CAAC,OAAO,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,CACzC,CAAC;YACJ,CAAC,CACF,CAAC;YACF,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aAC9B;QACH,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,KAAe;QACxC,KAAiB,CACf,KAAK,EACL,UAAU,IAAI;YACZ,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;CACF;AAED,SAAS,MAAM,CAAC,IAAY;IAC1B,OAAO,GAAG,GAAG,IAAI,CAAC;AACpB,CAAC;;;AC1EmD;AAGD;AAMpC,MAAM,qBAAU;IAK7B,YAAY,WAAsB;QAChC,IAAI,CAAC,SAAS,GAAG,IAAI,kCAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,IAAI,CAAC,SAAiB,EAAE,QAAkB,EAAE,OAAa;QACvD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,QAAkB;QAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,SAAkB,EAAE,QAAmB,EAAE,OAAa;QAC3D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,aAAa,CAAC,QAAmB;QAC/B,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,gBAAgB,GAAG,MAAkB,CACxC,IAAI,CAAC,gBAAgB,IAAI,EAAE,EAC3B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,QAAQ,CACtB,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU;QACR,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAAC,SAAiB,EAAE,IAAU,EAAE,QAAmB;QACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SAC3C;QAED,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,IAAI,QAAQ,EAAE;YAGZ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC3B;aAAM,IAAI,IAAI,EAAE;YAGf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjB;QAED,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,IAAM,EAAE,IAAI,CAAC,CAAC;aAC7D;SACF;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SACnC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;;;ACnF+C;AAClB;AAE9B,MAAM,aAAM;IAAZ;QAaU,cAAS,GAAG,CAAC,OAAe,EAAE,EAAE;YACtC,IAAI,IAAM,CAAC,OAAO,IAAI,IAAM,CAAC,OAAO,CAAC,GAAG,EAAE;gBACxC,IAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aAC7B;QACH,CAAC,CAAC;IA8BJ,CAAC;IA9CC,KAAK,CAAC,GAAG,IAAW;QAClB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,IAAI,CAAC,GAAG,IAAW;QACjB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,GAAG,IAAW;QAClB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAQO,aAAa,CAAC,OAAe;QACnC,IAAI,IAAM,CAAC,OAAO,IAAI,IAAM,CAAC,OAAO,CAAC,IAAI,EAAE;YACzC,IAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC9B;aAAM;YACL,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SACzB;IACH,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,IAAI,IAAM,CAAC,OAAO,IAAI,IAAM,CAAC,OAAO,CAAC,KAAK,EAAE;YAC1C,IAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC/B;aAAM;YACL,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC;IAEO,GAAG,CACT,sBAAiD,EACjD,GAAG,IAAW;QAEd,IAAI,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC/C,IAAI,WAAM,CAAC,GAAG,EAAE;YACd,WAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SACrB;aAAM,IAAI,WAAM,CAAC,YAAY,EAAE;YAC9B,MAAM,GAAG,GAAG,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,GAAG,CAAC,OAAO,CAAC,CAAC;SACd;IACH,CAAC;CACF;AAEc,+CAAI,aAAM,EAAE,EAAC;;;ACpDD;AACyB;AACe;AACpC;AAGD;AAgCf,MAAM,wCAAoB,SAAQ,qBAAgB;IAc/D,YACE,KAAqB,EACrB,IAAY,EACZ,QAAgB,EAChB,GAAW,EACX,OAAmC;QAEnC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,GAAG,cAAO,CAAC,8BAA8B,CAAC;QACzD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;IAC7C,CAAC;IAMD,qBAAqB;QACnB,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACnD,CAAC;IAMD,YAAY;QACV,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC;IAMD,OAAO;QACL,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;QAED,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7D,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACvD;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAChB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAMD,KAAK;QACH,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;SACb;aAAM;YACL,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAOD,IAAI,CAAC,IAAS;QACZ,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YAEzB,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;gBACd,IAAI,IAAI,CAAC,MAAM,EAAE;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACxB;YACH,CAAC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;SACb;aAAM;YACL,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAGD,IAAI;QACF,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SACpB;IACH,CAAC;IAEO,MAAM;QACZ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CACnB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAChD,CAAC;SACH;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;IACjC,CAAC;IAEO,OAAO,CAAC,KAAK;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEO,OAAO,CAAC,UAAgB;QAC9B,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;gBACzB,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;aAC9B,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAEO,SAAS,CAAC,OAAO;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAChC,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACxB,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;YACxB,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,UAAU,EAAE,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,OAAO,EAAE,EAAE;YAClC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,EAAE;gBAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC,CAAC;SACH;IACH,CAAC;IAEO,eAAe;QACrB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;YAClC,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;gBACvB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,SAAS,CAAC;aACpC;SACF;IACH,CAAC;IAEO,WAAW,CAAC,KAAa,EAAE,MAAY;QAC7C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAChB,IAAI,CAAC,oBAAoB,CAAC;YACxB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,MAAM;SACf,CAAC,CACH,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC3B,CAAC;IAED,oBAAoB,CAAC,OAAO;QAC1B,OAAO,MAAkB,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;CACF;;;AC9OwD;AAmB1C,MAAM,mBAAS;IAG5B,YAAY,KAAqB;QAC/B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAOD,WAAW,CAAC,WAAgB;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;IAUD,gBAAgB,CACd,IAAY,EACZ,QAAgB,EAChB,GAAW,EACX,OAAY;QAEZ,OAAO,IAAI,wCAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IAC3E,CAAC;CACF;;;ACrDyD;AAER;AAEI;AAGxB;AAO9B,IAAI,WAAW,GAAG,IAAI,mBAAS,CAAiB;IAC9C,IAAI,EAAE,EAAa;IACnB,qBAAqB,EAAE,KAAK;IAC5B,YAAY,EAAE,KAAK;IAEnB,aAAa,EAAE;QACb,OAAO,OAAO,CAAC,cAAO,CAAC,eAAe,EAAE,CAAC,CAAC;IAC5C,CAAC;IACD,WAAW,EAAE;QACX,OAAO,OAAO,CAAC,cAAO,CAAC,eAAe,EAAE,CAAC,CAAC;IAC5C,CAAC;IACD,SAAS,EAAE,UAAU,GAAG;QACtB,OAAO,cAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;CACF,CAAC,CAAC;AAEH,IAAI,iBAAiB,GAAG;IACtB,IAAI,EAAE,IAAe;IACrB,qBAAqB,EAAE,KAAK;IAC5B,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE;QACb,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAC;AAEK,IAAI,sBAAsB,GAAG,MAAkB,CACpD;IACE,SAAS,EAAE,UAAU,GAAG;QACtB,OAAO,cAAO,CAAC,WAAW,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;IACxD,CAAC;CACF,EACD,iBAAiB,CAClB,CAAC;AACK,IAAI,oBAAoB,GAAG,MAAkB,CAClD;IACE,SAAS,EAAE,UAAU,GAAG;QACtB,OAAO,cAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACtD,CAAC;CACF,EACD,iBAAiB,CAClB,CAAC;AAEF,IAAI,gBAAgB,GAAG;IACrB,WAAW,EAAE;QACX,OAAO,cAAO,CAAC,cAAc,EAAE,CAAC;IAClC,CAAC;CACF,CAAC;AAGF,IAAI,qBAAqB,GAAG,IAAI,mBAAS,CACvB,CACd,MAAkB,CAAC,EAAE,EAAE,sBAAsB,EAAE,gBAAgB,CAAC,CACjE,CACF,CAAC;AAGF,IAAI,mBAAmB,GAAG,IAAI,mBAAS,CACrB,CACd,MAAkB,CAAC,EAAE,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,CAC/D,CACF,CAAC;AAEF,IAAI,UAAU,GAAoB;IAChC,EAAE,EAAE,WAAW;IACf,aAAa,EAAE,qBAAqB;IACpC,WAAW,EAAE,mBAAmB;CACjC,CAAC;AAEa,yDAAU,EAAC;;;AClFC;AACyB;AAmBrC,MAAM,iEAA8B;IAOjD,YACE,OAAyB,EACzB,SAAoB,EACpB,OAAyB;QAEzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAYD,gBAAgB,CACd,IAAY,EACZ,QAAgB,EAChB,GAAW,EACX,OAAe;QAEf,OAAO,GAAG,MAAkB,CAAC,EAAE,EAAE,OAAO,EAAE;YACxC,eAAe,EAAE,IAAI,CAAC,SAAS;SAChC,CAAC,CAAC;QACH,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAC9C,IAAI,EACJ,QAAQ,EACR,GAAG,EACH,OAAO,CACR,CAAC;QAEF,IAAI,aAAa,GAAG,IAAI,CAAC;QAEzB,IAAI,MAAM,GAAG;YACX,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAClC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACpC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,CAAC,CAAC;QACF,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,EAAE;YAC5B,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEtC,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI,EAAE;gBAExD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;aAC5B;iBAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,aAAa,EAAE;gBAEhD,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa,CAAC;gBAC1C,IAAI,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;oBAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC5D;aACF;QACH,CAAC,CAAC;QAEF,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAChC,OAAO,UAAU,CAAC;IACpB,CAAC;IAUD,WAAW,CAAC,WAAmB;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAC3E,CAAC;CACF;;;ACjGD,MAAM,QAAQ,GAAG;IAgBf,aAAa,EAAE,UAAU,YAA0B;QACjD,IAAI;YACF,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC;YACvC,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;gBACvC,IAAI;oBACF,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBAChD;gBAAC,OAAO,CAAC,EAAE,GAAE;aACf;YACD,IAAI,WAAW,GAAgB;gBAC7B,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,IAAI,EAAE,eAAe;aACtB,CAAC;YACF,IAAI,WAAW,CAAC,OAAO,EAAE;gBACvB,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;aAC3C;YACD,OAAO,WAAW,CAAC;SACpB;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,EAAE,IAAI,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC;SACxE;IACH,CAAC;IAQD,aAAa,EAAE,UAAU,KAAkB;QACzC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAgBD,gBAAgB,EAAE,UAAU,YAA0B;QACpD,IAAI,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAEnD,IAAI,OAAO,CAAC,KAAK,KAAK,+BAA+B,EAAE;YACrD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAClC,MAAM,4CAA4C,CAAC;aACpD;YACD,OAAO;gBACL,MAAM,EAAE,WAAW;gBACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,SAAS;gBAC1B,eAAe,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI;aACtD,CAAC;SACH;aAAM,IAAI,OAAO,CAAC,KAAK,KAAK,cAAc,EAAE;YAG3C,OAAO;gBACL,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC;gBACzC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;aACxC,CAAC;SACH;aAAM;YACL,MAAM,mBAAmB,CAAC;SAC3B;IACH,CAAC;IAYD,cAAc,EAAE,UAAU,UAAU;QAClC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,EAAE;YAM1B,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,EAAE;gBACtD,OAAO,SAAS,CAAC;aAClB;iBAAM;gBACL,OAAO,IAAI,CAAC;aACb;SACF;aAAM,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI,EAAE;YACnC,OAAO,UAAU,CAAC;SACnB;aAAM,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,EAAE;YACjC,OAAO,SAAS,CAAC;SAClB;aAAM,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,EAAE;YACjC,OAAO,SAAS,CAAC;SAClB;aAAM,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,EAAE;YACjC,OAAO,OAAO,CAAC;SAChB;aAAM;YAEL,OAAO,SAAS,CAAC;SAClB;IACH,CAAC;IAWD,aAAa,EAAE,UAAU,UAAU;QACjC,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI,EAAE;YACxD,OAAO;gBACL,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,OAAO,EAAE,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,OAAO;iBACjD;aACF,CAAC;SACH;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;CACF,CAAC;AAEa,qDAAQ,EAAC;;;ACzJ4B;AACe;AACxB;AAEZ;AAmBhB,MAAM,qBAAW,SAAQ,qBAAgB;IAKtD,YAAY,EAAU,EAAE,SAA8B;QACpD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC;QACjD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAMD,qBAAqB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;IAChD,CAAC;IAMD,IAAI,CAAC,IAAS;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IASD,UAAU,CAAC,IAAY,EAAE,IAAS,EAAE,OAAgB;QAClD,IAAI,KAAK,GAAgB,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACrD,IAAI,OAAO,EAAE;YACX,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;SACzB;QACD,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IAClD,CAAC;IAOD,IAAI;QACF,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE;YACjC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;SACvB;aAAM;YACL,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;SACpC;IACH,CAAC;IAGD,KAAK;QACH,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAEO,aAAa;QACnB,IAAI,SAAS,GAAG;YACd,OAAO,EAAE,CAAC,YAA0B,EAAE,EAAE;gBACtC,IAAI,WAAW,CAAC;gBAChB,IAAI;oBACF,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;iBACpD;gBAAC,OAAO,CAAC,EAAE;oBACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACjB,IAAI,EAAE,mBAAmB;wBACzB,KAAK,EAAE,CAAC;wBACR,IAAI,EAAE,YAAY,CAAC,IAAI;qBACxB,CAAC,CAAC;iBACJ;gBAED,IAAI,WAAW,KAAK,SAAS,EAAE;oBAC7B,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;oBAExC,QAAQ,WAAW,CAAC,KAAK,EAAE;wBACzB,KAAK,cAAc;4BACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gCACjB,IAAI,EAAE,aAAa;gCACnB,IAAI,EAAE,WAAW,CAAC,IAAI;6BACvB,CAAC,CAAC;4BACH,MAAM;wBACR,KAAK,aAAa;4BAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BAClB,MAAM;wBACR,KAAK,aAAa;4BAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BAClB,MAAM;qBACT;oBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;iBACnC;YACH,CAAC;YACD,QAAQ,EAAE,GAAG,EAAE;gBACb,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxB,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5B,CAAC;YACD,MAAM,EAAE,CAAC,UAAU,EAAE,EAAE;gBACrB,eAAe,EAAE,CAAC;gBAElB,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE;oBACjC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;iBACnC;gBAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC;SACF,CAAC;QAEF,IAAI,eAAe,GAAG,GAAG,EAAE;YACzB,WAAuB,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACrD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,WAAuB,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,UAAe;QACtC,IAAI,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACjD,IAAI,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC3B;QACD,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;SACrD;IACH,CAAC;CACF;;;AC9JsD;AACX;AACL;AAoBxB,MAAM,mBAAS;IAM5B,YACE,SAA8B,EAC9B,QAAoC;QAEpC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE;YACrB,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,IAAI,MAAM,CAAC;YACX,IAAI;gBACF,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;aACvC;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;gBACnC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;gBACvB,OAAO;aACR;YAED,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE;gBACjC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;oBACvB,UAAU,EAAE,IAAI,qBAAU,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC;oBACrD,eAAe,EAAE,MAAM,CAAC,eAAe;iBACxC,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;gBACpD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;aACxB;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,EAAE,EAAE;YAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,IAAI,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC;YAC9D,IAAI,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAEO,MAAM,CAAC,MAAc,EAAE,MAAW;QACxC,IAAI,CAAC,QAAQ,CACX,MAAkB,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,CAC1E,CAAC;IACJ,CAAC;CACF;;;ACrF6B;AAQf,MAAM,8BAAc;IAKjC,YAAY,QAAkB,EAAE,OAA8B;QAC5D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,MAAe,EAAE,QAAmB;QACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE;YAC3B,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAChB,cAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAChD,QAAQ,CACT,CAAC;IACJ,CAAC;CACF;;;AC9BM,MAAM,YAAa,SAAQ,KAAK;IACrC,YAAY,GAAY;QACtB,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;AAEM,MAAM,cAAe,SAAQ,KAAK;IACvC,YAAY,GAAY;QACtB,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;AAEM,MAAM,eAAgB,SAAQ,KAAK;IACxC,YAAY,GAAY;QACtB,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;AACM,MAAM,uBAAwB,SAAQ,KAAK;IAChD,YAAY,GAAY;QACtB,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;AACM,MAAM,eAAgB,SAAQ,KAAK;IACxC,YAAY,GAAY;QACtB,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;AACM,MAAM,kBAAmB,SAAQ,KAAK;IAC3C,YAAY,GAAY;QACtB,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;AACM,MAAM,oBAAqB,SAAQ,KAAK;IAC7C,YAAY,GAAY;QACtB,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;AACM,MAAM,mBAAoB,SAAQ,KAAK;IAC5C,YAAY,GAAY;QACtB,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;AACM,MAAM,aAAc,SAAQ,KAAK;IAEtC,YAAY,MAAc,EAAE,GAAY;QACtC,KAAK,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACF;;;AChED,MAAM,QAAQ,GAAG;IACf,OAAO,EAAE,oBAAoB;IAC7B,IAAI,EAAE;QACJ,sBAAsB,EAAE;YACtB,IAAI,EAAE,gDAAgD;SACvD;QACD,qBAAqB,EAAE;YACrB,IAAI,EAAE,8CAA8C;SACrD;QACD,oBAAoB,EAAE;YACpB,IAAI,EAAE,8BAA8B;SACrC;QACD,sBAAsB,EAAE;YACtB,IAAI,EAAE,qDAAqD;SAC5D;QACD,uBAAuB,EAAE;YACvB,OAAO,EACL,6GAA6G;SAChH;KACF;CACF,CAAC;AAOF,MAAM,cAAc,GAAG,UAAU,GAAW;IAC1C,MAAM,SAAS,GAAG,MAAM,CAAC;IACzB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClC,IAAI,CAAC,MAAM;QAAE,OAAO,EAAE,CAAC;IAEvB,IAAI,GAAG,CAAC;IACR,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC;KACtB;SAAM,IAAI,MAAM,CAAC,IAAI,EAAE;QACtB,GAAG,GAAG,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC;KACtC;IAED,IAAI,CAAC,GAAG;QAAE,OAAO,EAAE,CAAC;IACpB,OAAO,GAAG,SAAS,IAAI,GAAG,EAAE,CAAC;AAC/B,CAAC,CAAC;AAEa,gDAAE,cAAc,EAAE,EAAC;;;AC/CiC;AAC/B;AACL;AAIW;AAKA;AAW3B,MAAM,eAAQ,SAAQ,qBAAgB;IAQnD,YAAY,IAAY,EAAE,MAAc;QACtC,KAAK,CAAC,UAAU,KAAK,EAAE,IAAI;YACzB,MAAM,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACrC,CAAC;IAMD,SAAS,CAAC,QAAgB,EAAE,QAAsC;QAChE,OAAO,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;IACtC,CAAC;IAGD,OAAO,CAAC,KAAa,EAAE,IAAS;QAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAClC,MAAM,IAAI,YAAmB,CAC3B,SAAS,GAAG,KAAK,GAAG,iCAAiC,CACtD,CAAC;SACH;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,MAAM,GAAG,SAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;YAC/D,MAAM,CAAC,IAAI,CACT,0EAA0E,MAAM,EAAE,CACnF,CAAC;SACH;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAGD,UAAU;QACR,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACnC,CAAC;IAMD,WAAW,CAAC,KAAkB;QAC5B,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;QAC5B,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACtB,IAAI,SAAS,KAAK,wCAAwC,EAAE;YAC1D,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;SAC9C;aAAM,IAAI,SAAS,KAAK,oCAAoC,EAAE;YAC7D,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;SAC1C;aAAM,IAAI,SAAS,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;YACtD,IAAI,QAAQ,GAAa,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;SACtC;IACH,CAAC;IAED,gCAAgC,CAAC,KAAkB;QACjD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpC;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SACxD;IACH,CAAC;IAED,4BAA4B,CAAC,KAAkB;QAC7C,IAAI,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACjC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC;SACxD;QAED,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAGD,SAAS;QACP,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,OAAO;SACR;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,SAAS,CACZ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,EAChC,CAAC,KAAmB,EAAE,IAA8B,EAAE,EAAE;YACtD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;gBAIjC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC/B,IAAI,CAAC,IAAI,CACP,2BAA2B,EAC3B,MAAM,CAAC,MAAM,CACX,EAAE,EACF;oBACE,IAAI,EAAE,WAAW;oBACjB,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,EACD,KAAK,YAAY,aAAa,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAC/D,CACF,CAAC;aACH;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE;oBACzC,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,OAAO,EAAE,IAAI,CAAC,IAAI;iBACnB,CAAC,CAAC;aACJ;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAGD,WAAW;QACT,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE;YAC3C,OAAO,EAAE,IAAI,CAAC,IAAI;SACnB,CAAC,CAAC;IACL,CAAC;IAGD,kBAAkB;QAChB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACpC,CAAC;IAGD,qBAAqB;QACnB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACrC,CAAC;CACF;;;ACjK+B;AAQjB,MAAM,8BAAe,SAAQ,eAAO;IAMjD,SAAS,CAAC,QAAgB,EAAE,QAAsC;QAChE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CACzC;YACE,WAAW,EAAE,IAAI,CAAC,IAAI;YACtB,QAAQ,EAAE,QAAQ;SACnB,EACD,QAAQ,CACT,CAAC;IACJ,CAAC;CACF;;;ACxBmD;AAGrC,MAAM,eAAO;IAM1B;QACE,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IASD,GAAG,CAAC,EAAU;QACZ,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE;YAC1D,OAAO;gBACL,EAAE,EAAE,EAAE;gBACN,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;aACvB,CAAC;SACH;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAMD,IAAI,CAAC,QAAkB;QACrB,WAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;YACnD,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAGD,OAAO,CAAC,EAAU;QAChB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;IACjB,CAAC;IAGD,cAAc,CAAC,gBAAqB;QAClC,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC9C,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC7C,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAGD,SAAS,CAAC,UAAe;QACvB,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YACzC,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;QACD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC;QACxD,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAGD,YAAY,CAAC,UAAe;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,MAAM,EAAE;YACV,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAGD,KAAK;QACH,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;IACjB,CAAC;CACF;;;;;;;;;;;;AC/E8C;AAChB;AACC;AAEY;AAK7B,MAAM,gCAAgB,SAAQ,8BAAc;IAQzD,YAAY,IAAY,EAAE,MAAc;QACtC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,IAAI,eAAO,EAAE,CAAC;IAC/B,CAAC;IAOD,SAAS,CAAC,QAAgB,EAAE,QAAkB;QAC5C,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAO,KAAK,EAAE,QAAQ,EAAE,EAAE;YAClD,IAAI,CAAC,KAAK,EAAE;gBACV,QAAQ,GAAG,QAAoC,CAAC;gBAChD,IAAI,QAAQ,CAAC,YAAY,IAAI,IAAI,EAAE;oBACjC,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;oBACpD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;iBAC3C;qBAAM;oBACL,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC;oBACzC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;wBAGtC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;qBACrD;yBAAM;wBACL,IAAI,MAAM,GAAG,SAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;wBAC9D,MAAM,CAAC,KAAK,CACV,sCAAsC,IAAI,CAAC,IAAI,KAAK;4BAClD,kCAAkC,MAAM,IAAI;4BAC5C,kCAAkC,CACrC,CAAC;wBACF,QAAQ,CAAC,uBAAuB,CAAC,CAAC;wBAClC,OAAO;qBACR;iBACF;aACF;YACD,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC5B,CAAC,EAAC,CAAC;IACL,CAAC;IAMD,WAAW,CAAC,KAAkB;QAC5B,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;QAC5B,IAAI,SAAS,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;YAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SACjC;aAAM;YACL,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACtB,IAAI,QAAQ,GAAa,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,OAAO,EAAE;gBACjB,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;aAClC;YACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;SACtC;IACH,CAAC;IACD,mBAAmB,CAAC,KAAkB;QACpC,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;QAC5B,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACtB,QAAQ,SAAS,EAAE;YACjB,KAAK,wCAAwC;gBAC3C,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,oCAAoC;gBACvC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;gBACzC,MAAM;YACR,KAAK,8BAA8B;gBACjC,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;gBAC9C,MAAM;YACR,KAAK,gCAAgC;gBACnC,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACpD,IAAI,aAAa,EAAE;oBACjB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;iBACnD;gBACD,MAAM;SACT;IACH,CAAC;IAED,gCAAgC,CAAC,KAAkB;QACjD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpC;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC1D;IACH,CAAC;IAGD,UAAU;QACR,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,KAAK,CAAC,UAAU,EAAE,CAAC;IACrB,CAAC;CACF;;;;;;;;;AChH8C;AACX;AACL;AAEwB;AACI;AAc5C,MAAM,kCAAiB,SAAQ,8BAAc;IAI1D,YAAY,IAAY,EAAE,MAAc,EAAE,IAAU;QAClD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAJtB,QAAG,GAAe,IAAI,CAAC;QAKrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAOD,SAAS,CAAC,QAAgB,EAAE,QAAsC;QAChE,KAAK,CAAC,SAAS,CACb,QAAQ,EACR,CAAC,KAAmB,EAAE,QAAkC,EAAE,EAAE;YAC1D,IAAI,KAAK,EAAE;gBACT,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAC1B,OAAO;aACR;YACD,IAAI,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC;YAC7C,IAAI,CAAC,YAAY,EAAE;gBACjB,QAAQ,CACN,IAAI,KAAK,CACP,+DAA+D,IAAI,CAAC,IAAI,EAAE,CAC3E,EACD,IAAI,CACL,CAAC;gBACF,OAAO;aACR;YACD,IAAI,CAAC,GAAG,GAAG,wBAAY,CAAC,YAAY,CAAC,CAAC;YACtC,OAAO,QAAQ,CAAC,eAAe,CAAC,CAAC;YACjC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC3B,CAAC,CACF,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,KAAa,EAAE,IAAS;QAC9B,MAAM,IAAI,kBAAyB,CACjC,kEAAkE,CACnE,CAAC;IACJ,CAAC;IAMD,WAAW,CAAC,KAAkB;QAC5B,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;QAC5B,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACtB,IACE,SAAS,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC3C,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAClC;YACA,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACzB,OAAO;SACR;QACD,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEO,oBAAoB,CAAC,KAAa,EAAE,IAAS;QACnD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACb,MAAM,CAAC,KAAK,CACV,8EAA8E,CAC/E,CAAC;YACF,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACnC,MAAM,CAAC,KAAK,CACV,oGAAoG;gBAClG,IAAI,CACP,CAAC;YACF,OAAO;SACR;QACD,IAAI,UAAU,GAAG,wBAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;YAC1D,MAAM,CAAC,KAAK,CACV,oDAAoD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,UAAU,UAAU,CAAC,MAAM,EAAE,CACpH,CAAC;YACF,OAAO;SACR;QACD,IAAI,KAAK,GAAG,wBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAClD,MAAM,CAAC,KAAK,CACV,+CAA+C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,UAAU,KAAK,CAAC,MAAM,EAAE,CACvG,CAAC;YACF,OAAO;SACR;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAClE,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,MAAM,CAAC,KAAK,CACV,iIAAiI,CAClI,CAAC;YAGF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACnE,IAAI,KAAK,EAAE;oBACT,MAAM,CAAC,KAAK,CACV,iDAAiD,QAAQ,wDAAwD,CAClH,CAAC;oBACF,OAAO;iBACR;gBACD,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9D,IAAI,KAAK,KAAK,IAAI,EAAE;oBAClB,MAAM,CAAC,KAAK,CACV,gEAAgE,CACjE,CAAC;oBACF,OAAO;iBACR;gBACD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC5C,OAAO;YACT,CAAC,CAAC,CAAC;YACH,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9C,CAAC;IAID,aAAa,CAAC,KAAiB;QAC7B,IAAI,GAAG,GAAG,sBAAU,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI;YACF,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxB;QAAC,WAAM;YACN,OAAO,GAAG,CAAC;SACZ;IACH,CAAC;CACF;;;ACrJkE;AACZ;AAExB;AAKqB;AAGtB;AAmCf,MAAM,oCAAkB,SAAQ,qBAAgB;IAkB7D,YAAY,GAAW,EAAE,OAAiC;QACxD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,CACtD,IAAI,CAAC,cAAc,CACpB,CAAC;QACF,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE5E,IAAI,OAAO,GAAG,cAAO,CAAC,UAAU,EAAE,CAAC;QAEnC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa,EAAE;gBAC/D,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACjB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;YAC3C,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;aAC1B;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAOD,OAAO;QACL,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE;YAClC,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE;YAChC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC3B,OAAO;SACR;QACD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAMD,IAAI,CAAC,IAAI;QACP,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACnC;aAAM;YACL,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IASD,UAAU,CAAC,IAAY,EAAE,IAAS,EAAE,OAAgB;QAClD,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SACxD;aAAM;YACL,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAGD,UAAU;QACR,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;IACnC,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEO,eAAe;QACrB,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;YAClC,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;aAClD;iBAAM;gBACL,IAAI,SAAS,CAAC,MAAM,KAAK,OAAO,EAAE;oBAChC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACjB,IAAI,EAAE,gBAAgB;wBACtB,KAAK,EAAE,SAAS,CAAC,KAAK;qBACvB,CAAC,CAAC;oBACH,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;iBAC1D;qBAAM;oBACL,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;iBACtD;aACF;QACH,CAAC,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;IAEO,eAAe;QACrB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpB;IACH,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,UAAU,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1C,UAAU,CAAC,KAAK,EAAE,CAAC;SACpB;IACH,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;YACvC,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,QAAQ;SACtB,CAAC,CAAC;IACL,CAAC;IAEO,OAAO,CAAC,KAAK;QACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QACtD,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;SACtD;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAK,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,EAAE;YAC3C,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe;QACrB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YAChC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,kBAAK,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,GAAG,EAAE;YACtE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB;QAC3B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;SACvC;IACH,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QAEvB,IAAI,CAAC,aAAa,GAAG,IAAI,kBAAK,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,EAAE;YAC5D,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;YAClE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,EAAE;YAC/D,IAAI,CAAC,aAAa,GAAG,IAAI,kBAAK,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE;gBACxD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAEO,iBAAiB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;SACpC;IACH,CAAC;IAEO,wBAAwB,CAC9B,cAA8B;QAE9B,OAAO,MAAkB,CAAsB,EAAE,EAAE,cAAc,EAAE;YACjE,OAAO,EAAE,CAAC,OAAO,EAAE,EAAE;gBAEnB,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAChC,CAAC;YACD,IAAI,EAAE,GAAG,EAAE;gBACT,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC;YACD,QAAQ,EAAE,GAAG,EAAE;gBACb,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBAEf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5B,CAAC;YACD,MAAM,EAAE,GAAG,EAAE;gBACX,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;oBACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBACpB;YACH,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAC7B,cAA8B;QAE9B,OAAO,MAAkB,CAAqB,EAAE,EAAE,cAAc,EAAE;YAChE,SAAS,EAAE,CAAC,SAA2B,EAAE,EAAE;gBACzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAC7B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,SAAS,CAAC,eAAe,EACzB,SAAS,CAAC,UAAU,CAAC,eAAe,IAAI,QAAQ,CACjD,CAAC;gBACF,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YAC/D,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB;QACzB,IAAI,gBAAgB,GAAG,CAAC,QAAQ,EAAE,EAAE;YAClC,OAAO,CAAC,MAAiC,EAAE,EAAE;gBAC3C,IAAI,MAAM,CAAC,KAAK,EAAE;oBAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;iBACrE;gBACD,QAAQ,CAAC,MAAM,CAAC,CAAC;YACnB,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,QAAQ,EAAE,gBAAgB,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC;YACF,OAAO,EAAE,gBAAgB,CAAC,GAAG,EAAE;gBAC7B,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC,CAAC;YACF,OAAO,EAAE,gBAAgB,CAAC,GAAG,EAAE;gBAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC,CAAC;YACF,KAAK,EAAE,gBAAgB,CAAC,GAAG,EAAE;gBAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,UAAU;QAC9B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;SAC9D;QACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,OAAO;SACR;QACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;SAChE;QACD,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,WAAW,CAAC,QAAgB,EAAE,IAAU;QAC9C,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACtB,IAAI,aAAa,KAAK,QAAQ,EAAE;YAC9B,IAAI,mBAAmB,GAAG,QAAQ,CAAC;YACnC,IAAI,mBAAmB,KAAK,WAAW,EAAE;gBACvC,mBAAmB,IAAI,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC;aAChE;YACD,MAAM,CAAC,KAAK,CACV,eAAe,EACf,aAAa,GAAG,MAAM,GAAG,mBAAmB,CAC7C,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC3B;IACH,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC;IACnE,CAAC;CACF;;;ACjXmD;AAEb;AAGH;AACM;AAG3B,MAAM,iBAAQ;IAG3B;QACE,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAQD,GAAG,CAAC,IAAY,EAAE,MAAc;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACnD;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAMD,GAAG;QACD,OAAO,MAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAOD,IAAI,CAAC,IAAY;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAMD,MAAM,CAAC,IAAY;QACjB,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3B,OAAO,OAAO,CAAC;IACjB,CAAC;IAGD,UAAU;QACR,WAAuB,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,OAAO;YACtD,OAAO,CAAC,UAAU,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,SAAS,aAAa,CAAC,IAAY,EAAE,MAAc;IACjD,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;QAC5C,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;YACtB,OAAO,OAAO,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACzE;QACD,IAAI,MAAM,GACR,yFAAyF,CAAC;QAC5F,IAAI,MAAM,GAAG,SAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;QAChE,MAAM,IAAI,kBAAyB,CAAC,GAAG,MAAM,KAAK,MAAM,EAAE,CAAC,CAAC;KAC7D;SAAM,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;QACzC,OAAO,OAAO,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACnD;SAAM,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;QAC1C,OAAO,OAAO,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACpD;SAAM,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,cAAqB,CAC7B,qCAAqC,GAAG,IAAI,GAAG,IAAI,CACpD,CAAC;KACH;SAAM;QACL,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC5C;AACH,CAAC;;;ACrF6F;AAI9C;AASX;AACsB;AACF;AACI;AACnB;AACuB;AAGrB;AAK5C,IAAI,OAAO,GAAG;IACZ,cAAc;QACZ,OAAO,IAAI,iBAAQ,EAAE,CAAC;IACxB,CAAC;IAED,uBAAuB,CACrB,GAAW,EACX,OAAiC;QAEjC,OAAO,IAAI,oCAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,aAAa,CAAC,IAAY,EAAE,MAAc;QACxC,OAAO,IAAI,eAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,oBAAoB,CAAC,IAAY,EAAE,MAAc;QAC/C,OAAO,IAAI,8BAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC1C,CAAC;IAED,qBAAqB,CAAC,IAAY,EAAE,MAAc;QAChD,OAAO,IAAI,gCAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,sBAAsB,CACpB,IAAY,EACZ,MAAc,EACd,IAAU;QAEV,OAAO,IAAI,kCAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAED,oBAAoB,CAAC,QAAkB,EAAE,OAA8B;QACrE,OAAO,IAAI,8BAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,eAAe,CACb,SAA8B,EAC9B,QAAoC;QAEpC,OAAO,IAAI,mBAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED,oCAAoC,CAClC,OAAyB,EACzB,SAAoB,EACpB,OAAyB;QAEzB,OAAO,IAAI,iEAA8B,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;CACF,CAAC;AAEa,mDAAO,EAAC;;;AC3EgB;AAexB,MAAM,kCAAgB;IAInC,YAAY,OAAgC;QAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,QAAQ,CAAC;IAClD,CAAC;IAOD,YAAY,CAAC,SAAoB;QAC/B,OAAO,OAAO,CAAC,oCAAoC,CAAC,IAAI,EAAE,SAAS,EAAE;YACnE,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;YACvC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;SACxC,CAAC,CAAC;IACL,CAAC;IAMD,OAAO;QACL,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IAC5B,CAAC;IAGD,WAAW;QACT,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;IACtB,CAAC;CACF;;;ACnDmD;AACzB;AAC4B;AAcxC,MAAM,sCAAkB;IAOrC,YAAY,UAAsB,EAAE,OAAwB;QAC1D,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,CAAC;IAED,WAAW;QACT,OAAO,GAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,CAAC,WAAmB,EAAE,QAAkB;QAC7C,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,MAAM,GAAG,IAAI,CAAC;QAElB,IAAI,eAAe,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;YACzC,IAAI,SAAS,EAAE;gBACb,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aAC3B;iBAAM;gBACL,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;gBACtB,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,OAAO,GAAG,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;iBACvC;gBAED,IAAI,OAAO,GAAG,UAAU,CAAC,MAAM,EAAE;oBAC/B,IAAI,OAAO,EAAE;wBACX,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;wBACtB,IAAI,IAAI,CAAC,YAAY,EAAE;4BACrB,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;yBAChD;qBACF;oBACD,MAAM,GAAG,IAAI,CAAC,WAAW,CACvB,UAAU,CAAC,OAAO,CAAC,EACnB,WAAW,EACX,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EACpC,eAAe,CAChB,CAAC;iBACH;qBAAM;oBACL,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAChB;aACF;QACH,CAAC,CAAC;QAEF,MAAM,GAAG,IAAI,CAAC,WAAW,CACvB,UAAU,CAAC,OAAO,CAAC,EACnB,WAAW,EACX,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAC7C,eAAe,CAChB,CAAC;QAEF,OAAO;YACL,KAAK,EAAE;gBACL,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;YACD,gBAAgB,EAAE,UAAU,CAAC;gBAC3B,WAAW,GAAG,CAAC,CAAC;gBAChB,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;iBAC5B;YACH,CAAC;SACF,CAAC;IACJ,CAAC;IAEO,WAAW,CACjB,QAAkB,EAClB,WAAmB,EACnB,OAAwB,EACxB,QAAkB;QAElB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,MAAM,GAAG,IAAI,CAAC;QAElB,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE;YACvB,KAAK,GAAG,IAAI,kBAAK,CAAC,OAAO,CAAC,OAAO,EAAE;gBACjC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,QAAQ,CAAC,IAAI,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC;SACJ;QAED,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,UAAU,KAAK,EAAE,SAAS;YAC/D,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBAE5D,OAAO;aACR;YACD,IAAI,KAAK,EAAE;gBACT,KAAK,CAAC,aAAa,EAAE,CAAC;aACvB;YACD,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,KAAK,EAAE;gBACL,IAAI,KAAK,EAAE;oBACT,KAAK,CAAC,aAAa,EAAE,CAAC;iBACvB;gBACD,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;YACD,gBAAgB,EAAE,UAAU,CAAC;gBAC3B,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;SACF,CAAC;IACJ,CAAC;CACF;;;AChImD;AACzB;AAOZ,MAAM,sDAAyB;IAG5C,YAAY,UAAsB;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,WAAW;QACT,OAAO,GAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,CAAC,WAAmB,EAAE,QAAkB;QAC7C,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,EAAE,OAAO;YAC/D,OAAO,UAAU,KAAK,EAAE,SAAS;gBAC/B,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;gBACzB,IAAI,KAAK,EAAE;oBACT,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE;wBAC7B,QAAQ,CAAC,IAAI,CAAC,CAAC;qBAChB;oBACD,OAAO;iBACR;gBACD,KAAiB,CAAC,OAAO,EAAE,UAAU,MAAM;oBACzC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACxD,CAAC,CAAC,CAAC;gBACH,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC5B,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAaD,SAAS,OAAO,CACd,UAAsB,EACtB,WAAmB,EACnB,eAAyB;IAEzB,IAAI,OAAO,GAAG,GAAe,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACpE,OAAO,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IACH,OAAO;QACL,KAAK,EAAE;YACL,KAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAC1C,CAAC;QACD,gBAAgB,EAAE,UAAU,CAAC;YAC3B,KAAiB,CAAC,OAAO,EAAE,UAAU,MAAM;gBACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACL,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAO;IAC/B,OAAO,eAAe,CAAC,OAAO,EAAE,UAAU,MAAM;QAC9C,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,WAAW,CAAC,MAAM;IACzB,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;QACpC,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;AACH,CAAC;;;AChF0B;AACG;AAEyB;AAIH;AAarC,MAAM,wEAAkC;IAOrD,YACE,QAAkB,EAClB,UAAuC,EACvC,OAAwB;QAExB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAED,OAAO,CAAC,WAAmB,EAAE,QAAkB;QAC7C,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7B,IAAI,IAAI,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,cAAc,GAAG,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3E,IAAI,UAAU,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YACnD,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,cAAc,GAAG,CAAC,EAAE;oBAChE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;wBACjB,MAAM,EAAE,IAAI;wBACZ,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;qBACtB,CAAC,CAAC;oBACH,UAAU,CAAC,IAAI,CACb,IAAI,sCAAkB,CAAC,CAAC,SAAS,CAAC,EAAE;wBAClC,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,IAAI;wBAChC,QAAQ,EAAE,IAAI;qBACf,CAAC,CACH,CAAC;iBACH;qBAAM;oBACL,cAAc,EAAE,CAAC;iBAClB;aACF;SACF;QAED,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,MAAM,GAAG,UAAU;aACpB,GAAG,EAAE;aACL,OAAO,CAAC,WAAW,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,SAAS;YAChD,IAAI,KAAK,EAAE;gBACT,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAC9B,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzB,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAC5B,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;iBACpD;qBAAM;oBACL,QAAQ,CAAC,KAAK,CAAC,CAAC;iBACjB;aACF;iBAAM;gBACL,mBAAmB,CACjB,QAAQ,EACR,SAAS,CAAC,SAAS,CAAC,IAAI,EACxB,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,EAC3B,cAAc,CACf,CAAC;gBACF,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aAC3B;QACH,CAAC,CAAC,CAAC;QAEL,OAAO;YACL,KAAK,EAAE;gBACL,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;YACD,gBAAgB,EAAE,UAAU,CAAC;gBAC3B,WAAW,GAAG,CAAC,CAAC;gBAChB,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;iBAC5B;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF;AAED,SAAS,oBAAoB,CAAC,QAAiB;IAC7C,OAAO,iBAAiB,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAiB;IAC5C,IAAI,OAAO,GAAG,cAAO,CAAC,eAAe,EAAE,CAAC;IACxC,IAAI,OAAO,EAAE;QACX,IAAI;YACF,IAAI,eAAe,GAAG,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC9D,IAAI,eAAe,EAAE;gBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;aACpC;SACF;QAAC,OAAO,CAAC,EAAE;YACV,mBAAmB,CAAC,QAAQ,CAAC,CAAC;SAC/B;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,mBAAmB,CAC1B,QAAiB,EACjB,SAA4B,EAC5B,OAAe,EACf,cAAsB;IAEtB,IAAI,OAAO,GAAG,cAAO,CAAC,eAAe,EAAE,CAAC;IACxC,IAAI,OAAO,EAAE;QACX,IAAI;YACF,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,GAAG,iBAA6B,CAAC;gBACtE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS,EAAE,SAAS;gBACpB,OAAO,EAAE,OAAO;gBAChB,cAAc,EAAE,cAAc;aAC/B,CAAC,CAAC;SACJ;QAAC,OAAO,CAAC,EAAE;SAEX;KACF;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAiB;IAC5C,IAAI,OAAO,GAAG,cAAO,CAAC,eAAe,EAAE,CAAC;IACxC,IAAI,OAAO,EAAE;QACX,IAAI;YACF,OAAO,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;SAChD;QAAC,OAAO,CAAC,EAAE;SAEX;KACF;AACH,CAAC;;;AC5JsD;AAYxC,MAAM,gCAAe;IAIlC,YAAY,QAAkB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;QAC/C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAED,OAAO,CAAC,WAAmB,EAAE,QAAkB;QAC7C,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7B,IAAI,MAAM,CAAC;QACX,IAAI,KAAK,GAAG,IAAI,kBAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACxC,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,KAAK,EAAE;gBACL,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtB,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,KAAK,EAAE,CAAC;iBAChB;YACH,CAAC;YACD,gBAAgB,EAAE,UAAU,CAAC;gBAC3B,WAAW,GAAG,CAAC,CAAC;gBAChB,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;iBAC5B;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF;;;ACtCc,MAAM,UAAU;IAK7B,YACE,IAAmB,EACnB,UAAoB,EACpB,WAAqB;QAErB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,WAAW;QACT,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QAC9D,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC;IAED,OAAO,CAAC,WAAmB,EAAE,QAAkB;QAC7C,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QAC9D,OAAO,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC/C,CAAC;CACF;;;AC1Bc,MAAM,sBAAsB;IAGzC,YAAY,QAAkB;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAED,OAAO,CAAC,WAAmB,EAAE,QAAkB;QAC7C,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAChC,WAAW,EACX,UAAU,KAAK,EAAE,SAAS;YACxB,IAAI,SAAS,EAAE;gBACb,MAAM,CAAC,KAAK,EAAE,CAAC;aAChB;YACD,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC7B,CAAC,CACF,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;;;AC9BqD;AACW;AAEI;AACgB;AAGtB;AACA;AACV;AACyB;AAI9E,SAAS,oBAAoB,CAAC,QAAkB;IAC9C,OAAO;QACL,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC;AACJ,CAAC;AAED,IAAI,kBAAkB,GAAG,UACvB,MAAc,EACd,WAA4B,EAC5B,eAAyB;IAEzB,IAAI,iBAAiB,GAAgC,EAAE,CAAC;IAExD,SAAS,uBAAuB,CAC9B,IAAY,EACZ,IAAY,EACZ,QAAgB,EAChB,OAAwB,EACxB,OAA0B;QAE1B,IAAI,SAAS,GAAG,eAAe,CAC7B,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,OAAO,CACR,CAAC;QAEF,iBAAiB,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;QAEpC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,UAAU,GAAoB,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,EAAE;QAC/D,UAAU,EAAE,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM;QAC/C,OAAO,EAAE,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO;QAC7C,QAAQ,EAAE,MAAM,CAAC,MAAM;KACxB,CAAC,CAAC;IACH,IAAI,WAAW,GAAoB,MAAkB,CAAC,EAAE,EAAE,UAAU,EAAE;QACpE,MAAM,EAAE,IAAI;KACb,CAAC,CAAC;IACH,IAAI,YAAY,GAAoB,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,EAAE;QACjE,UAAU,EAAE,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC,QAAQ;QACnD,OAAO,EAAE,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS;QACjD,QAAQ,EAAE,MAAM,CAAC,QAAQ;KAC1B,CAAC,CAAC;IACH,IAAI,QAAQ,GAAG;QACb,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,KAAK;QACd,YAAY,EAAE,KAAK;KACpB,CAAC;IAEF,IAAI,UAAU,GAAG,IAAI,kCAAgB,CAAC;QACpC,YAAY,EAAE,KAAK;QACnB,YAAY,EAAE,MAAM,CAAC,eAAe;KACrC,CAAC,CAAC;IACH,IAAI,iBAAiB,GAAG,IAAI,kCAAgB,CAAC;QAC3C,KAAK,EAAE,CAAC;QACR,YAAY,EAAE,KAAK;QACnB,YAAY,EAAE,MAAM,CAAC,eAAe;KACrC,CAAC,CAAC;IAEH,IAAI,YAAY,GAAG,uBAAuB,CACxC,IAAI,EACJ,IAAI,EACJ,CAAC,EACD,UAAU,EACV,UAAU,CACX,CAAC;IACF,IAAI,aAAa,GAAG,uBAAuB,CACzC,KAAK,EACL,IAAI,EACJ,CAAC,EACD,WAAW,EACX,UAAU,CACX,CAAC;IACF,IAAI,uBAAuB,GAAG,uBAAuB,CACnD,eAAe,EACf,eAAe,EACf,CAAC,EACD,YAAY,EACZ,iBAAiB,CAClB,CAAC;IACF,IAAI,qBAAqB,GAAG,uBAAuB,CACjD,aAAa,EACb,aAAa,EACb,CAAC,EACD,YAAY,CACb,CAAC;IAEF,IAAI,OAAO,GAAG,IAAI,sCAAkB,CAAC,CAAC,YAAY,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC/D,IAAI,QAAQ,GAAG,IAAI,sCAAkB,CAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,CAAC;IACjE,IAAI,cAAc,GAAG,IAAI,sCAAkB,CACzC,CAAC,uBAAuB,CAAC,EACzB,QAAQ,CACT,CAAC;IACF,IAAI,YAAY,GAAG,IAAI,sCAAkB,CAAC,CAAC,qBAAqB,CAAC,EAAE,QAAQ,CAAC,CAAC;IAE7E,IAAI,SAAS,GAAG,IAAI,sCAAkB,CACpC;QACE,IAAI,UAAU,CACZ,oBAAoB,CAAC,cAAc,CAAC,EACpC,IAAI,sDAAyB,CAAC;YAC5B,cAAc;YACd,IAAI,gCAAe,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;SACnD,CAAC,EACF,YAAY,CACb;KACF,EACD,QAAQ,CACT,CAAC;IAEF,IAAI,UAAU,CAAC;IACf,IAAI,WAAW,CAAC,MAAM,EAAE;QACtB,UAAU,GAAG,IAAI,sDAAyB,CAAC;YACzC,OAAO;YACP,IAAI,gCAAe,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;SAChD,CAAC,CAAC;KACJ;SAAM;QACL,UAAU,GAAG,IAAI,sDAAyB,CAAC;YACzC,OAAO;YACP,IAAI,gCAAe,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC9C,IAAI,gCAAe,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;SAChD,CAAC,CAAC;KACJ;IAED,OAAO,IAAI,wEAAkC,CAC3C,IAAI,sBAAsB,CACxB,IAAI,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,CAC1E,EACD,iBAAiB,EACjB;QACE,GAAG,EAAE,OAAO;QACZ,QAAQ,EAAE,WAAW,CAAC,QAAQ;QAC9B,MAAM,EAAE,WAAW,CAAC,MAAM;KAC3B,CACF,CAAC;AACJ,CAAC,CAAC;AAEa,uEAAkB,EAAC;;;ACtJnB;IACb,IAAI,IAAI,GAAG,IAAI,CAAC;IAEhB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAChB,IAAI,CAAC,oBAAoB,CAAC;QACxB,SAAS,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;KACxD,CAAC,CACH,CAAC;IAEF,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE;QAC9B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;KACjC;SAAM;QACL,IAAI,CAAC,OAAO,EAAE,CAAC;KAChB;AACH,CAAC;;;AClB6B;AAGqC;AAEnE,MAAM,iBAAiB,GAAG,GAAG,GAAG,IAAI,CAAC;AAEtB,MAAM,wBAAY,SAAQ,qBAAgB;IAQvD,YAAY,KAAmB,EAAE,MAAc,EAAE,GAAW;QAC1D,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,OAAa;QACjB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,QAAQ,GAAG,GAAG,EAAE;YACnB,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC,CAAC;QACF,cAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEzC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE;YAC7B,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,cAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;QACD,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;SACjB;IACH,CAAC;IAED,OAAO,CAAC,MAAc,EAAE,IAAS;QAC/B,OAAO,IAAI,EAAE;YACX,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;aACrD;iBAAM;gBACL,MAAM;aACP;SACF;QACD,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC9B;IACH,CAAC;IAEO,aAAa,CAAC,MAAa;QACjC,IAAI,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE;YAC5B,IAAI,CAAC,QAAQ,IAAI,iBAAiB,GAAG,CAAC,CAAC;YACvC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;SAC/C;aAAM;YAEL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEO,eAAe,CAAC,MAAW;QACjC,OAAO,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,iBAAiB,CAAC;IAC9E,CAAC;CACF;;;AChFD,IAAK,KAIJ;AAJD,WAAK,KAAK;IACR,6CAAc;IACd,iCAAQ;IACR,qCAAU;AACZ,CAAC,EAJI,KAAK,KAAL,KAAK,QAIT;AAEc,+CAAK,EAAC;;;ACLO;AAGD;AAGG;AAE9B,IAAI,aAAa,GAAG,CAAC,CAAC;AAEtB,MAAM,sBAAU;IAad,YAAY,KAAkB,EAAE,GAAW;QACzC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACnC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,IAAI,CAAC,OAAY;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,IAAI;QACF,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,IAAS,EAAE,MAAW;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAGD,OAAO,CAAC,OAAY;QAClB,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,EAAE;YAClC,IAAI;gBACF,cAAO,CAAC,mBAAmB,CACzB,MAAM,EACN,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CACtD,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACjB,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,KAAK,CAAC;aACd;SACF;aAAM;YACL,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAGD,SAAS;QACP,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAGD,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC;gBACX,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;SACJ;IACH,CAAC;IAEO,OAAO,CAAC,KAAK;QACnB,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE;YACxB,OAAO;SACR;QACD,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,EAAE;YAClC,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;QAED,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,QAAQ,IAAI,EAAE;YACZ,KAAK,GAAG;gBACN,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;gBAClD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACrB,MAAM;YACR,KAAK,GAAG;gBACN,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;gBAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACvC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC1B;gBACD,MAAM;YACR,KAAK,GAAG;gBACN,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;gBACpD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACtB,MAAM;YACR,KAAK,GAAG;gBACN,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAC7B,MAAM;YACR,KAAK,GAAG;gBACN,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;gBAClD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC3C,MAAM;SACT;IACH,CAAC;IAEO,MAAM,CAAC,OAAO;QACpB,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,EAAE;YACxC,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;aACxE;YACD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC;YAE7B,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,EAAE,CAAC;aACf;SACF;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;SACjD;IACH,CAAC;IAEO,OAAO,CAAC,KAAK;QACnB,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACpD,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;SACjC;IACH,CAAC;IAEO,UAAU;QAChB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;IACH,CAAC;IAEO,OAAO,CAAC,KAAK;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACrB;IACH,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC,MAAM,GAAG,cAAO,CAAC,mBAAmB,CACvC,MAAM,EACN,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CACpE,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YAClC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,EAAE;YACtC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE;YACvC,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;SACrB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAEO,WAAW;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpB;IACH,CAAC;CACF;AAED,SAAS,WAAW,CAAC,GAAG;IACtB,IAAI,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3C,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACd,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;KACtB,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,GAAgB,EAAE,OAAe;IACnD,OAAO,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,OAAO,GAAG,WAAW,CAAC;AAChD,CAAC;AAED,SAAS,YAAY,CAAC,GAAW;IAC/B,IAAI,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACpD,OAAO,GAAG,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,GAAG,aAAa,EAAE,CAAC;AACxE,CAAC;AAED,SAAS,WAAW,CAAC,GAAW,EAAE,QAAgB;IAChD,IAAI,QAAQ,GAAG,mCAAmC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7D,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,YAAY,CAAC,GAAW;IAC/B,OAAO,cAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,YAAY,CAAC,MAAc;IAClC,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;KAC5C;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,CAAC;AAEc,sEAAU,EAAC;;;ACxN1B,IAAI,2BAAK,GAAgB;IACvB,aAAa,EAAE,UAAU,GAAG,EAAE,OAAO;QACnC,OAAO,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,OAAO,GAAG,gBAAgB,GAAG,GAAG,CAAC,WAAW,CAAC;IACvE,CAAC;IACD,WAAW,EAAE,UAAU,MAAM;QAC3B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,aAAa,EAAE,UAAU,MAAM;QAC7B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,UAAU,EAAE,UAAU,MAAM,EAAE,MAAM;QAClC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,0BAA0B,GAAG,MAAM,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;IACzE,CAAC;CACF,CAAC;AAEa,qFAAK,EAAC;;;ACdrB,IAAI,yBAAK,GAAgB;IACvB,aAAa,EAAE,UAAU,GAAgB,EAAE,OAAe;QACxD,OAAO,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,OAAO,GAAG,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC;IAC7D,CAAC;IACD,WAAW,EAAE;IAEb,CAAC;IACD,aAAa,EAAE,UAAU,MAAM;QAC7B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,UAAU,EAAE,UAAU,MAAM,EAAE,MAAM;QAClC,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,MAAM,CAAC,SAAS,EAAE,CAAC;SACpB;aAAM;YACL,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,0BAA0B,GAAG,MAAM,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;SACxE;IACH,CAAC;CACF,CAAC;AAEa,iFAAK,EAAC;;;ACpBS;AAE9B,IAAI,sBAAK,GAAiB;IACxB,UAAU,EAAE,UAAU,MAAmB;QACvC,IAAI,WAAW,GAAG,cAAO,CAAC,SAAS,EAAE,CAAC;QACtC,IAAI,GAAG,GAAG,IAAI,WAAW,EAAE,CAAC;QAC5B,GAAG,CAAC,kBAAkB,GAAG,GAAG,CAAC,UAAU,GAAG;YACxC,QAAQ,GAAG,CAAC,UAAU,EAAE;gBACtB,KAAK,CAAC;oBACJ,IAAI,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;wBACnD,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;qBAC9C;oBACD,MAAM;gBACR,KAAK,CAAC;oBAEJ,IAAI,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;wBACnD,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;qBAC9C;oBACD,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpC,MAAM,CAAC,KAAK,EAAE,CAAC;oBACf,MAAM;aACT;QACH,CAAC,CAAC;QACF,OAAO,GAAG,CAAC;IACb,CAAC;IACD,YAAY,EAAE,UAAU,GAAS;QAC/B,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC9B,GAAG,CAAC,KAAK,EAAE,CAAC;IACd,CAAC;CACF,CAAC;AAEa,2EAAK,EAAC;;;AClC4B;AACF;AAGc;AACJ;AACf;AAG1C,IAAI,IAAI,GAAgB;IACtB,qBAAqB,CAAC,GAAW;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,qBAAc,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,mBAAmB,CAAC,GAAW;QAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAY,EAAE,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED,YAAY,CAAC,KAAkB,EAAE,GAAW;QAC1C,OAAO,IAAI,WAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACpC,CAAC;IAED,SAAS,CAAC,MAAc,EAAE,GAAW;QACnC,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;IACnD,CAAC;IAED,aAAa,CAAC,KAAmB,EAAE,MAAc,EAAE,GAAW;QAC5D,OAAO,IAAI,wBAAW,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7C,CAAC;CACF,CAAC;AAEa,kDAAI,EAAC;;;AC/BkC;AACI;AAGN;AAEuC;AACrD;AAEtC,IAAI,UAAU,GAAQ;IACpB,oCAAkB;IAClB,UAAU,EAAmB,UAAU;IACvC,gEAA8B;IAC9B,sBAAW;IAEX,KAAK,CAAC,WAAW;QACf,WAAW,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED,eAAe;QACb,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,iBAAiB;QACf,OAAO,IAAgB,CACrB,YAAwB,CAAC,EAAE,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,UAAU,CAAC;YACzD,OAAO,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,WAAW;QACT,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,CAAC,MAAc,EAAE,GAAW;QAC7C,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;YACzB,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAChD;aAAM;YACL,MAAM,8CAA8C,CAAC;SACtD;IACH,CAAC;IAED,SAAS;QACP,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,IAAI,WAAW,EAAE,CAAC;IAC3B,CAAC;IAED,eAAe,CAAC,GAAW;QACzB,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACzC,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,iBAAiB,CAAC,QAAa,IAAG,CAAC;IACnC,oBAAoB,CAAC,QAAa,IAAG,CAAC;CACvC,CAAC;AAEa,sDAAU,EAAC;;;AC7D2C;AAG9D,MAAM,gBAAQ,SAAQ,qBAAgB;IAC3C,QAAQ;QACN,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAEM,IAAI,gBAAO,GAAG,IAAI,gBAAO,EAAE,CAAC;;;ACFS;AAE5C,IAAI,SAAS,GAAkB,UAC7B,OAAwB,EACxB,KAAa,EACb,WAAgC,EAChC,eAAgC,EAChC,QAA+B;IAE/B,IAAI,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,mCAAmC,CAAC,CAAC;IAEjE,KAAK,IAAI,UAAU,IAAI,WAAW,CAAC,OAAO,EAAE;QAC1C,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;KAC1D;IAED,IAAI,WAAW,CAAC,eAAe,IAAI,IAAI,EAAE;QACvC,MAAM,cAAc,GAAG,WAAW,CAAC,eAAe,EAAE,CAAC;QACrD,KAAK,IAAI,UAAU,IAAI,cAAc,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;SACrD;KACF;IAED,IAAI,IAAI,GAAG,KAAK,CAAC;IACjB,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE;QAC9C,OAAO;QACP,IAAI;QACJ,WAAW,EAAE,aAAa;QAC1B,MAAM,EAAE,MAAM;KACf,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,OAAO,CAAC;SAClB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;QACjB,IAAI,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;QAC1B,IAAI,MAAM,KAAK,GAAG,EAAE;YAGlB,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;SACxB;QACD,MAAM,IAAI,aAAa,CACrB,MAAM,EACN,iBAAiB,eAAe,CAAC,QAAQ,EAAE,0CAA0C,MAAM,EAAE,CAC9F,CAAC;IACJ,CAAC,CAAC;SACD,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;QACb,IAAI,UAAU,CAAC;QACf,IAAI;YACF,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC/B;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,aAAa,CACrB,GAAG,EACH,sBAAsB,eAAe,CAAC,QAAQ,EAAE,6DAA6D,IAAI,EAAE,CACpH,CAAC;SACH;QACD,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC7B,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;QACb,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEa,wDAAS,EAAC;;;ACpEQ;AAEqB;AAKtD,IAAI,QAAQ,GAAG,UAAU,MAAsB,EAAE,MAAe;IAC9D,OAAO,UAAU,IAAS,EAAE,QAAkB;QAC5C,IAAI,MAAM,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;QAClD,IAAI,GAAG,GACL,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;QACtE,IAAI,KAAK,GAAG,gBAA4B,CAAC,IAAI,CAAC,CAAC;QAC/C,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;QAE7B,KAAK,CAAC,GAAG,CAAC;aACP,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;gBAC3B,MAAM,YAAY,QAAQ,CAAC,MAAM,wBAAwB,CAAC;aAC3D;YACD,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;YACjB,IAAI,IAAI,EAAE;gBACR,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;aACpB;QACH,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,IAAI,aAAa,GAAG;IAClB,IAAI,EAAE,KAAK;IACX,QAAQ;CACT,CAAC;AAEa,gEAAa,EAAC;;;ACtCe;AAEP;AACK;AAEY;AAKtD,MAAM,EACJ,kBAAkB,8BAClB,UAAU,sBACV,KAAK,EACL,WAAW,EACX,cAAc,EACd,eAAe,EACf,SAAS,EACT,eAAe,EACf,iBAAiB,EACjB,oBAAoB,EACpB,8BAA8B,EAC9B,mBAAmB,EACnB,WAAW,GACZ,GAAG,OAAU,CAAC;AAEf,MAAM,MAAM,GAAY;IACtB,kBAAkB;IAClB,UAAU;IACV,KAAK;IACL,WAAW;IACX,cAAc;IACd,eAAe;IACf,SAAS;IACT,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,8BAA8B;IAC9B,mBAAmB;IACnB,WAAW;IAEX,iBAAiB,EAAE,cAAa;IAEhC,cAAc;QACZ,OAAO,EAAE,IAAI,EAAE,UAAS,EAAE,CAAC;IAC7B,CAAC;IAED,eAAe;QACb,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,SAAS;QACP,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,UAAU;QACR,OAAO,gBAAO,CAAC;IACjB,CAAC;IAED,SAAS,CAAC,GAAW;QAInB,MAAM,MAAM,GAAG;YACb,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7D,OAAO,MAAM,GAAG,UAAC,EAAI,EAAE,EAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;IACpC,CAAC;CACF,CAAC;AAEa,yDAAM,EAAC;;;AC1EtB,IAAK,aAIJ;AAJD,WAAK,aAAa;IAChB,mDAAS;IACT,iDAAQ;IACR,mDAAS;AACX,CAAC,EAJI,aAAa,KAAb,aAAa,QAIjB;AAEc,gEAAa,EAAC;;;ACNuB;AACzB;AACgB;AAW5B,MAAM,iBAAQ;IAQ3B,YAAY,GAAW,EAAE,OAAe,EAAE,OAAwB;QAChE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,GAAG,CAAC,KAAK,EAAE,KAAK;QACd,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,MAAkB,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CACzD,CAAC;YACF,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;gBACjE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;aACrB;SACF;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,GAAG,CAAC,cAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,KAAK;QACR,IAAI,CAAC,GAAG,CAAC,cAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,GAAG,CAAC,cAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,QAAQ;QACnB,IAAI,IAAI,GAAG,MAAkB,CAC3B;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;YACrB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,IAAI;YACT,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;YAC7B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;YAC7B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC/B,QAAQ,EAAE,IAAI,CAAC,MAAM;SACtB,EACD,IAAI,CAAC,OAAO,CAAC,MAAM,CACpB,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAC7B,IAAI,CAAC,KAAK,EAAE;gBACV,IAAI,CAAC,IAAI,EAAE,CAAC;aACb;YACD,IAAI,QAAQ,EAAE;gBACZ,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;aACzB;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gBAAgB;QACd,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CACF;;;ACzFsC;AACZ;AACS;AACgB;AAarC,MAAM,oCAAiB;IAMpC,YACE,IAAY,EACZ,QAAgB,EAChB,SAAoB,EACpB,OAAwB;QAExB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;IAC/B,CAAC;IAMD,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;YAChC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;SAC5B,CAAC,CAAC;IACL,CAAC;IAOD,OAAO,CAAC,WAAmB,EAAE,QAAkB;QAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,OAAO,WAAW,CAAC,IAAI,mBAA0B,EAAE,EAAE,QAAQ,CAAC,CAAC;SAChE;aAAM,IAAI,IAAI,CAAC,QAAQ,GAAG,WAAW,EAAE;YACtC,OAAO,WAAW,CAAC,IAAI,uBAA8B,EAAE,EAAE,QAAQ,CAAC,CAAC;SACpE;QAED,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,CAAC,GAAG,EAChB,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,IAAI,aAAa,GAAG;YAClB,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAC/C,SAAS,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC;QACF,IAAI,MAAM,GAAG;YACX,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,SAAS,EAAE,UAAU,MAAM;gBAC7D,SAAS,GAAG,IAAI,CAAC;gBACjB,eAAe,EAAE,CAAC;gBAClB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QACF,IAAI,OAAO,GAAG,UAAU,KAAK;YAC3B,eAAe,EAAE,CAAC;YAClB,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC,CAAC;QACF,IAAI,QAAQ,GAAG;YACb,eAAe,EAAE,CAAC;YAClB,IAAI,mBAAmB,CAAC;YAMxB,mBAAmB,GAAG,iBAA6B,CAAC,SAAS,CAAC,CAAC;YAC/D,QAAQ,CAAC,IAAI,eAAsB,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC;QAEF,IAAI,eAAe,GAAG;YACpB,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAC/C,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACjC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACnC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QAC7C,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/B,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACjC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAGnC,SAAS,CAAC,UAAU,EAAE,CAAC;QAEvB,OAAO;YACL,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,SAAS,EAAE;oBACb,OAAO;iBACR;gBACD,eAAe,EAAE,CAAC;gBAClB,IAAI,SAAS,EAAE;oBACb,SAAS,CAAC,KAAK,EAAE,CAAC;iBACnB;qBAAM;oBACL,SAAS,CAAC,KAAK,EAAE,CAAC;iBACnB;YACH,CAAC;YACD,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE;gBACtB,IAAI,SAAS,EAAE;oBACb,OAAO;iBACR;gBACD,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;oBACrB,IAAI,SAAS,EAAE;wBACb,SAAS,CAAC,KAAK,EAAE,CAAC;qBACnB;yBAAM;wBACL,SAAS,CAAC,KAAK,EAAE,CAAC;qBACnB;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF;AAED,SAAS,WAAW,CAAC,KAAY,EAAE,QAAkB;IACnD,IAAI,CAAC,KAAK,CAAC;QACT,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IACH,OAAO;QACL,KAAK,EAAE,cAAa,CAAC;QACrB,gBAAgB,EAAE,cAAa,CAAC;KACjC,CAAC;AACJ,CAAC;;;AC/ImD;AACzB;AAES;AAEiB;AAGvB;AAE9B,MAAM,EAAE,UAAU,+BAAE,GAAG,cAAO,CAAC;AAExB,IAAI,gCAAe,GAAG,UAC3B,MAAc,EACd,IAAY,EACZ,IAAY,EACZ,QAAgB,EAChB,OAAwB,EACxB,OAA0B;IAE1B,IAAI,cAAc,GAAG,2BAAU,CAAC,IAAI,CAAC,CAAC;IACtC,IAAI,CAAC,cAAc,EAAE;QACnB,MAAM,IAAI,oBAA2B,CAAC,IAAI,CAAC,CAAC;KAC7C;IAED,IAAI,OAAO,GACT,CAAC,CAAC,MAAM,CAAC,iBAAiB;QACxB,YAAwB,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,MAAM,CAAC,kBAAkB;YACzB,YAAwB,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEtE,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,EAAE;QACX,OAAO,GAAG,MAAM,CAAC,MAAM,CACrB,EAAE,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,EAAE,EAC7C,OAAO,CACR,CAAC;QAEF,SAAS,GAAG,IAAI,oCAAiB,CAC/B,IAAI,EACJ,QAAQ,EACR,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,EAC/D,OAAO,CACR,CAAC;KACH;SAAM;QACL,SAAS,GAAG,oCAAmB,CAAC;KACjC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,IAAI,oCAAmB,GAAa;IAClC,WAAW,EAAE;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,EAAE,UAAU,CAAC,EAAE,QAAQ;QAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;YACxB,QAAQ,CAAC,IAAI,mBAA0B,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO;YACL,KAAK,EAAE;gBACL,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC3B,CAAC;YACD,gBAAgB,EAAE,cAAa,CAAC;SACjC,CAAC;IACJ,CAAC;CACF,CAAC;;;ACvD4B;AAmCvB,SAAS,eAAe,CAAC,OAAO;IACrC,IAAI,OAAO,IAAI,IAAI,EAAE;QACnB,MAAM,iCAAiC,CAAC;KACzC;IACD,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE;QAC3B,MAAM,uCAAuC,CAAC;KAC/C;IACD,IAAI,cAAc,IAAI,OAAO,EAAE;QAC7B,MAAM,CAAC,IAAI,CACT,+DAA+D,CAChE,CAAC;KACH;AACH,CAAC;;;AC1DD,IAAY,eAGX;AAHD,WAAY,eAAe;IACzB,6DAA0C;IAC1C,iEAA8C;AAChD,CAAC,EAHW,eAAe,KAAf,eAAe,QAG1B;;;ACGkB;AAEW;AAE9B,MAAM,mBAAmB,GAAG,CAC1B,MAAuC,EACvC,WAAgC,EAChC,EAAE;IACF,IAAI,KAAK,GAAG,YAAY,GAAG,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAE/D,KAAK,IAAI,GAAG,IAAI,WAAW,CAAC,MAAM,EAAE;QAClC,KAAK;YACH,GAAG;gBACH,kBAAkB,CAAC,GAAG,CAAC;gBACvB,GAAG;gBACH,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KAC/C;IAED,IAAI,WAAW,CAAC,cAAc,IAAI,IAAI,EAAE;QACtC,IAAI,aAAa,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;QACjD,KAAK,IAAI,GAAG,IAAI,aAAa,EAAE;YAC7B,KAAK;gBACH,GAAG;oBACH,kBAAkB,CAAC,GAAG,CAAC;oBACvB,GAAG;oBACH,kBAAkB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1C;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CACxB,WAAgC,EACL,EAAE;IAC7B,IAAI,OAAO,cAAO,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,WAAW,EAAE;QAC1E,MAAM,IAAI,WAAW,CAAC,SAAS,sCAAsC,CAAC;KACvE;IAED,OAAO,CACL,MAAuC,EACvC,QAAoC,EACpC,EAAE;QACF,MAAM,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAEvD,cAAO,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAC7C,cAAO,EACP,KAAK,EACL,WAAW,EACX,eAAe,CAAC,kBAAkB,EAClC,QAAQ,CACT,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEa,wEAAiB,EAAC;;;ACvDd;AAEW;AAE9B,MAAM,sCAAmB,GAAG,CAC1B,MAAyC,EACzC,WAAgC,EAChC,EAAE;IACF,IAAI,KAAK,GAAG,YAAY,GAAG,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAE/D,KAAK,IAAI,gBAAgB,GAAG,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAEnE,KAAK,IAAI,GAAG,IAAI,WAAW,CAAC,MAAM,EAAE;QAClC,KAAK;YACH,GAAG;gBACH,kBAAkB,CAAC,GAAG,CAAC;gBACvB,GAAG;gBACH,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KAC/C;IAED,IAAI,WAAW,CAAC,cAAc,IAAI,IAAI,EAAE;QACtC,IAAI,aAAa,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;QACjD,KAAK,IAAI,GAAG,IAAI,aAAa,EAAE;YAC7B,KAAK;gBACH,GAAG;oBACH,kBAAkB,CAAC,GAAG,CAAC;oBACvB,GAAG;oBACH,kBAAkB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1C;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CACxB,WAAgC,EACH,EAAE;IAC/B,IAAI,OAAO,cAAO,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,WAAW,EAAE;QAC1E,MAAM,IAAI,WAAW,CAAC,SAAS,sCAAsC,CAAC;KACvE;IAED,OAAO,CACL,MAAyC,EACzC,QAAsC,EACtC,EAAE;QACF,MAAM,KAAK,GAAG,sCAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAEvD,cAAO,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAC7C,cAAO,EACP,KAAK,EACL,WAAW,EACX,eAAe,CAAC,oBAAoB,EACpC,QAAQ,CACT,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEa,wEAAiB,EAAC;;;ACjC1B,MAAM,sBAAsB,GAAG,CACpC,MAAM,EACN,WAAgC,EAChC,0BAAsD,EACzB,EAAE;IAC/B,MAAM,2BAA2B,GAAgC;QAC/D,aAAa,EAAE,WAAW,CAAC,SAAS;QACpC,YAAY,EAAE,WAAW,CAAC,QAAQ;QAClC,IAAI,EAAE;YACJ,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,OAAO,EAAE,WAAW,CAAC,OAAO;SAC7B;KACF,CAAC;IACF,OAAO,CACL,MAAyC,EACzC,QAAsC,EACtC,EAAE;QACF,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAInD,MAAM,iBAAiB,GACrB,0BAA0B,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC;QACnE,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACzD,CAAC,CAAC;AACJ,CAAC,CAAC;;;ACtDgC;AAMwB;AACA;AACoB;AAChD;AA2CvB,SAAS,SAAS,CAAC,IAAa,EAAE,MAAM;IAC7C,IAAI,MAAM,GAAW;QACnB,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,QAAQ,CAAC,eAAe;QACjE,OAAO,EAAE,IAAI,CAAC,OAAO;QACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;QAC5C,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;QAC5C,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS;QAC/C,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW;QACrD,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU;QAChD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,IAAI,QAAQ,CAAC,kBAAkB;QAC1E,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM;QACtC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM;QACtC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO;QAEzC,WAAW,EAAE,oBAAoB,CAAC,IAAI,CAAC;QACvC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC;QAC3B,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC;QAC1B,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC;QAE9B,iBAAiB,EAAE,sBAAsB,CAAC,IAAI,CAAC;QAC/C,iBAAiB,EAAE,sBAAsB,CAAC,IAAI,EAAE,MAAM,CAAC;KACxD,CAAC;IAEF,IAAI,oBAAoB,IAAI,IAAI;QAC9B,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;IACtD,IAAI,mBAAmB,IAAI,IAAI;QAC7B,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;IACpD,IAAI,kBAAkB,IAAI,IAAI;QAC5B,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;IAClD,IAAI,gBAAgB,IAAI,IAAI;QAAE,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;IAC1E,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;KACzB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,WAAW,CAAC,IAAa;IAChC,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,OAAO,UAAU,IAAI,CAAC,OAAO,aAAa,CAAC;KAC5C;IACD,OAAO,QAAQ,CAAC,QAAQ,CAAC;AAC3B,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAa;IACrC,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,OAAO,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,2BAA2B,CAAC,OAAe;IAClD,OAAO,MAAM,OAAO,aAAa,CAAC;AACpC,CAAC;AAED,SAAS,YAAY,CAAC,IAAa;IACjC,IAAI,cAAO,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;QACtC,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;QAClC,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAKD,SAAS,oBAAoB,CAAC,IAAa;IACzC,IAAI,aAAa,IAAI,IAAI,EAAE;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,cAAc,IAAI,IAAI,EAAE;QAC1B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;KAC3B;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAa;IAC3C,MAAM,kBAAkB,mCACnB,QAAQ,CAAC,kBAAkB,GAC3B,IAAI,CAAC,kBAAkB,CAC3B,CAAC;IACF,IACE,eAAe,IAAI,kBAAkB;QACrC,kBAAkB,CAAC,eAAe,CAAC,IAAI,IAAI,EAC3C;QACA,OAAO,kBAAkB,CAAC,eAAe,CAAC,CAAC;KAC5C;IAED,OAAO,kBAAiB,CAAC,kBAAkB,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAa,EAAE,MAAM;IAC7C,IAAI,oBAAiD,CAAC;IACtD,IAAI,sBAAsB,IAAI,IAAI,EAAE;QAClC,oBAAoB,mCACf,QAAQ,CAAC,oBAAoB,GAC7B,IAAI,CAAC,oBAAoB,CAC7B,CAAC;KACH;SAAM;QACL,oBAAoB,GAAG;YACrB,SAAS,EAAE,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa;YACvD,QAAQ,EAAE,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,YAAY;SACrD,CAAC;QACF,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI;gBAAE,oBAAoB,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAC1E,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI;gBACxB,oBAAoB,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;SACpD;QACD,IAAI,YAAY,IAAI,IAAI;YACtB,oBAAoB,CAAC,aAAa,GAAG,sBAAsB,CACzD,MAAM,EACN,oBAAoB,EACpB,IAAI,CAAC,UAAU,CAChB,CAAC;KACL;IACD,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED,SAAS,sBAAsB,CAC7B,IAAa,EACb,MAAM;IAEN,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC5D,IACE,eAAe,IAAI,oBAAoB;QACvC,oBAAoB,CAAC,eAAe,CAAC,IAAI,IAAI,EAC7C;QACA,OAAO,oBAAoB,CAAC,eAAe,CAAC,CAAC;KAC9C;IAED,OAAO,kBAAiB,CAAC,oBAAoB,CAAC,CAAC;AACjD,CAAC;;;AC5L6B;AAEqB;AAEpC,MAAM,yBAAgB,SAAQ,qBAAgB;IAG3D,YAAmB,MAAc;QAC/B,KAAK,CAAC,UAAU,SAAS,EAAE,IAAI;YAC7B,MAAM,CAAC,KAAK,CAAC,wCAAwC,SAAS,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACpC,CAAC;IAED,WAAW,CAAC,WAAW;QACrB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACjD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,0BAA0B;QAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,WAAW,EAAE,EAAE;YACrD,IAAI,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;YAClC,IAAI,SAAS,KAAK,kCAAkC,EAAE;gBACpD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;aAC/B;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;;;AC9BD,SAAS,WAAW;IAClB,IAAI,OAAO,EAAE,MAAM,CAAC;IACpB,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACvC,OAAO,GAAG,GAAG,CAAC;QACd,MAAM,GAAG,GAAG,CAAC;IACf,CAAC,CAAC,CAAC;IACH,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACtC,CAAC;AAEc,4DAAW,EAAC;;;ACRG;AAKW;AACC;AACS;AACJ;AAEhC,MAAM,eAAW,SAAQ,qBAAgB;IAStD,YAAmB,MAAc;QAC/B,KAAK,CAAC,UAAU,SAAS,EAAE,IAAI;YAC7B,MAAM,CAAC,KAAK,CAAC,2BAA2B,GAAG,SAAS,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAVL,qBAAgB,GAAY,KAAK,CAAC;QAClC,cAAS,GAAQ,IAAI,CAAC;QACtB,wBAAmB,GAAY,IAAI,CAAC;QACpC,sBAAiB,GAAiB,IAAI,CAAC;QAE/B,uBAAkB,GAAa,IAAI,CAAC;QA8DpC,iBAAY,GAA+B,CACjD,GAAG,EACH,QAAgC,EAChC,EAAE;YACF,IAAI,GAAG,EAAE;gBACP,MAAM,CAAC,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC,CAAC;gBAC3C,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,OAAO;aACR;YAED,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE;gBACtC,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC,CAAC;QAGL,CAAC,CAAC;QAxEA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE;YACpE,IAAI,QAAQ,KAAK,WAAW,IAAI,OAAO,KAAK,WAAW,EAAE;gBACvD,IAAI,CAAC,OAAO,EAAE,CAAC;aAChB;YACD,IAAI,QAAQ,KAAK,WAAW,IAAI,OAAO,KAAK,WAAW,EAAE;gBACvD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,IAAI,CAAC,yBAAyB,EAAE,CAAC;aAClC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,IAAI,yBAAe,CAAC,MAAM,CAAC,CAAC;QAE7C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAC/C,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;YAC5B,IAAI,SAAS,KAAK,uBAAuB,EAAE;gBACzC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aACnC;YACD,IACE,IAAI,CAAC,mBAAmB;gBACxB,IAAI,CAAC,mBAAmB,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAC/C;gBACA,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAC7C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,MAAM;QACX,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,OAAO;SACR;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAEO,OAAO;QACb,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,OAAO;SACR;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,KAAK,WAAW,EAAE;YAEhD,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAClC;YACE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS;SAC3C,EACD,IAAI,CAAC,YAAY,CAClB,CAAC;IACJ,CAAC;IAoBO,gBAAgB,CAAC,IAAS;QAChC,IAAI;YACF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC7C;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,0CAA0C,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YACzE,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO;SACR;QAED,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,EAAE;YACrE,MAAM,CAAC,KAAK,CACV,+CAA+C,IAAI,CAAC,SAAS,EAAE,CAChE,CAAC;YACF,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO;SACR;QAGD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,EAAE;YACpC,IAAI,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,qBAAqB,EAAE;gBAChE,OAAO,CAAC,qBAAqB,EAAE,CAAC;aACjC;iBAAM,IACL,CAAC,OAAO,CAAC,mBAAmB;gBAC5B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,KAAK,WAAW,EAC5C;gBACA,OAAO,CAAC,SAAS,EAAE,CAAC;aACrB;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,mBAAmB,GAAG,IAAI,eAAO,CACpC,mBAAmB,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,EACtC,IAAI,CAAC,MAAM,CACZ,CAAC;QACF,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE;YACvD,IACE,SAAS,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC;gBAC3C,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAClC;gBAEA,OAAO;aACR;YACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QACH,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC9C,CAAC;IAEO,QAAQ;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC;YACtC,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC;YACtC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAGzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC3B;IACH,CAAC;IAEO,yBAAyB;QAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,OAAO;SACR;QAGD,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAE,IAAI,CAAC,iBAAyB,CAAC,IAAI,EAAE;YACnE,OAAO;SACR;QAID,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,YAAW,EAAE,CAAC;QACrD,OAAe,CAAC,IAAI,GAAG,KAAK,CAAC;QAC9B,MAAM,OAAO,GAAG,GAAG,EAAE;YAClB,OAAe,CAAC,IAAI,GAAG,IAAI,CAAC;QAC/B,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;IACpC,CAAC;CACF;;;ACxL6B;AAEqB;AAGe;AACvB;AAEE;AACmB;AAGjB;AACb;AAEJ;AACQ;AAEe;AACR;AAEb;AAEhC,MAAqB,aAAM;IAYzB,MAAM,CAAC,KAAK;QACV,aAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACvD,aAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;SAC/B;IACH,CAAC;IAIO,MAAM,CAAC,iBAAiB;QAC9B,OAAO,IAAgB,CACrB,YAAwB,CAAC,EAAE,EAAE,EAAE,cAAO,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,UAAU,CAAC;YACjE,OAAO,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAaD,YAAY,OAAe,EAAE,OAAgB;QAC3C,WAAW,CAAC,OAAO,CAAC,CAAC;QACrB,eAAe,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,IAAI,qBAAgB,EAAE,CAAC;QAC7C,IAAI,CAAC,SAAS,GAAG,cAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAE/C,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE;YACrD,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,QAAQ,EAAE,aAAM,CAAC,iBAAiB,EAAE;YACpC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,EAAE;YACxC,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,cAAa,CAAC,IAAI;YACzB,OAAO,EAAE,QAAQ,CAAC,OAAO;SAC1B,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;gBAC3B,IAAI,EAAE,eAAe,GAAG,cAAO,CAAC,iBAAiB,CAAC,IAAI;aACvD,CAAC,CAAC;SACJ;QAED,IAAI,WAAW,GAAG,CAAC,OAAwB,EAAE,EAAE;YAC7C,OAAO,cAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,gCAAe,CAAC,CAAC;QAC3E,CAAC,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,GAAG,EAAE;YAC1D,WAAW,EAAE,WAAW;YACxB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe;YAC5C,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAClD,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;SACpC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;YACrC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;aACxD;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YACxC,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;YAC5B,IAAI,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC3D,IAAI,KAAK,CAAC,OAAO,EAAE;gBACjB,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC1C,IAAI,OAAO,EAAE;oBACX,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;iBAC5B;aACF;YAED,IAAI,CAAC,QAAQ,EAAE;gBACb,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;aACnD;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE;YACxC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,aAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,aAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAE3D,IAAI,CAAC,IAAI,GAAG,IAAI,eAAU,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,aAAM,CAAC,OAAO,EAAE;YAClB,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;IACH,CAAC;IAED,OAAO,CAAC,IAAY;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED,OAAO;QACL,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC7B,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC5C,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;gBACzC,IAAI,CAAC,mBAAmB,GAAG,IAAI,oBAAa,CAAC,KAAK,EAAE;oBAClD,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAChC,CAAC,CAAC,CAAC;aACJ;SACF;IACH,CAAC;IAED,UAAU;QACR,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;YACzC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;IACH,CAAC;IAED,IAAI,CAAC,UAAkB,EAAE,QAAkB,EAAE,OAAa;QACxD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,UAAmB,EAAE,QAAmB,EAAE,OAAa;QAC5D,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,QAAkB;QAC5B,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,aAAa,CAAC,QAAmB;QAC/B,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU,CAAC,QAAmB;QAC5B,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY;QACV,IAAI,WAAW,CAAC;QAChB,KAAK,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAC1C,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;gBACtD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;aAC7B;SACF;IACH,CAAC;IAED,SAAS,CAAC,YAAoB;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACpD,IAAI,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,qBAAqB,EAAE;YAChE,OAAO,CAAC,qBAAqB,EAAE,CAAC;SACjC;aAAM,IACL,CAAC,OAAO,CAAC,mBAAmB;YAC5B,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,WAAW,EACrC;YACA,OAAO,CAAC,SAAS,EAAE,CAAC;SACrB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,WAAW,CAAC,YAAoB;QAC9B,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/C,IAAI,OAAO,IAAI,OAAO,CAAC,mBAAmB,EAAE;YAC1C,OAAO,CAAC,kBAAkB,EAAE,CAAC;SAC9B;aAAM;YACL,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC7C,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE;gBACjC,OAAO,CAAC,WAAW,EAAE,CAAC;aACvB;SACF;IACH,CAAC;IAED,UAAU,CAAC,UAAkB,EAAE,IAAS,EAAE,OAAgB;QACxD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;IACrB,CAAC;;AAzNM,uBAAS,GAAa,EAAE,CAAC;AACzB,qBAAO,GAAY,KAAK,CAAC;AACzB,0BAAY,GAAY,KAAK,CAAC;AAG9B,qBAAO,GAAoB,cAAO,CAAC;AACnC,6BAAe,GAAc,cAAQ,CAAC,eAAe,CAAC;AACtD,mCAAqB,GAAc,cAAQ,CAAC,qBAAqB,CAAC;AAClE,4BAAc,GAAc,cAAQ,CAAC,cAAc,CAAC;AAVxC,6DAAM;AA8N3B,SAAS,WAAW,CAAC,GAAG;IACtB,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;QACrC,MAAM,yDAAyD,CAAC;KACjE;AACH,CAAC;AAED,cAAO,CAAC,KAAK,CAAC,aAAM,CAAC,CAAC;;;;;;AC5PQ;AACuB;AACnB;AAEnB,MAAM,2CAAqB,SAAQ,WAAM;IACtD,YAAY,OAAe,EAAE,OAAgB;QAC3C,WAAM,CAAC,YAAY,GAAG,2CAAoB,CAAC,YAAY,CAAC;QACxD,WAAM,CAAC,GAAG,GAAG,2CAAoB,CAAC,GAAG,CAAC;QAEtC,eAAe,CAAC,OAAO,CAAC,CAAC;QACzB,OAAO,CAAC,IAAI,GAAG,SAAI,CAAC;QACpB,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1B,CAAC;CACF", "file": "pusher-with-encryption.worker.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Pusher\"] = factory();\n\telse\n\t\troot[\"Pusher\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 3);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package base64 implements Base64 encoding and decoding.\n */\n\n// Invalid character used in decoding to indicate\n// that the character to decode is out of range of\n// alphabet and cannot be decoded.\nconst INVALID_BYTE = 256;\n\n/**\n * Implements standard Base64 encoding.\n *\n * Operates in constant time.\n */\nexport class Coder {\n    // TODO(dchest): methods to encode chunk-by-chunk.\n\n    constructor(private _padding<PERSON>haracter = \"=\") { }\n\n    encodedLength(length: number): number {\n        if (!this._padding<PERSON>haracter) {\n            return (length * 8 + 5) / 6 | 0;\n        }\n        return (length + 2) / 3 * 4 | 0;\n    }\n\n    encode(data: Uint8Array): string {\n        let out = \"\";\n\n        let i = 0;\n        for (; i < data.length - 2; i += 3) {\n            let c = (data[i] << 16) | (data[i + 1] << 8) | (data[i + 2]);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            out += this._encodeByte((c >>> 1 * 6) & 63);\n            out += this._encodeByte((c >>> 0 * 6) & 63);\n        }\n\n        const left = data.length - i;\n        if (left > 0) {\n            let c = (data[i] << 16) | (left === 2 ? data[i + 1] << 8 : 0);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            if (left === 2) {\n                out += this._encodeByte((c >>> 1 * 6) & 63);\n            } else {\n                out += this._paddingCharacter || \"\";\n            }\n            out += this._paddingCharacter || \"\";\n        }\n\n        return out;\n    }\n\n    maxDecodedLength(length: number): number {\n        if (!this._paddingCharacter) {\n            return (length * 6 + 7) / 8 | 0;\n        }\n        return length / 4 * 3 | 0;\n    }\n\n    decodedLength(s: string): number {\n        return this.maxDecodedLength(s.length - this._getPaddingLength(s));\n    }\n\n    decode(s: string): Uint8Array {\n        if (s.length === 0) {\n            return new Uint8Array(0);\n        }\n        const paddingLength = this._getPaddingLength(s);\n        const length = s.length - paddingLength;\n        const out = new Uint8Array(this.maxDecodedLength(length));\n        let op = 0;\n        let i = 0;\n        let haveBad = 0;\n        let v0 = 0, v1 = 0, v2 = 0, v3 = 0;\n        for (; i < length - 4; i += 4) {\n            v0 = this._decodeChar(s.charCodeAt(i + 0));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n            haveBad |= v2 & INVALID_BYTE;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (i < length - 1) {\n            v0 = this._decodeChar(s.charCodeAt(i));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n        }\n        if (i < length - 2) {\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            haveBad |= v2 & INVALID_BYTE;\n        }\n        if (i < length - 3) {\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (haveBad !== 0) {\n            throw new Error(\"Base64Coder: incorrect characters for decoding\");\n        }\n        return out;\n    }\n\n    // Standard encoding have the following encoded/decoded ranges,\n    // which we need to convert between.\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  +   /\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   43  47\n    //\n\n    // Encode 6 bits in b into a new character.\n    protected _encodeByte(b: number): string {\n        // Encoding uses constant time operations as follows:\n        //\n        // 1. Define comparison of A with B using (A - B) >>> 8:\n        //          if A > B, then result is positive integer\n        //          if A <= B, then result is 0\n        //\n        // 2. Define selection of C or 0 using bitwise AND: X & C:\n        //          if X == 0, then result is 0\n        //          if X != 0, then result is C\n        //\n        // 3. Start with the smallest comparison (b >= 0), which is always\n        //    true, so set the result to the starting ASCII value (65).\n        //\n        // 4. Continue comparing b to higher ASCII values, and selecting\n        //    zero if comparison isn't true, otherwise selecting a value\n        //    to add to result, which:\n        //\n        //          a) undoes the previous addition\n        //          b) provides new value to add\n        //\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 43);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 43) - 63 + 47);\n\n        return String.fromCharCode(result);\n    }\n\n    // Decode a character code into a byte.\n    // Must return 256 if character is out of alphabet range.\n    protected _decodeChar(c: number): number {\n        // Decoding works similar to encoding: using the same comparison\n        // function, but now it works on ranges: result is always incremented\n        // by value, but this value becomes zero if the range is not\n        // satisfied.\n        //\n        // Decoding starts with invalid value, 256, which is then\n        // subtracted when the range is satisfied. If none of the ranges\n        // apply, the function returns 256, which is then checked by\n        // the caller to throw error.\n        let result = INVALID_BYTE; // start with invalid character\n\n        // c == 43 (c > 42 and c < 44)\n        result += (((42 - c) & (c - 44)) >>> 8) & (-INVALID_BYTE + c - 43 + 62);\n        // c == 47 (c > 46 and c < 48)\n        result += (((46 - c) & (c - 48)) >>> 8) & (-INVALID_BYTE + c - 47 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n\n    private _getPaddingLength(s: string): number {\n        let paddingLength = 0;\n        if (this._paddingCharacter) {\n            for (let i = s.length - 1; i >= 0; i--) {\n                if (s[i] !== this._paddingCharacter) {\n                    break;\n                }\n                paddingLength++;\n            }\n            if (s.length < 4 || paddingLength > 2) {\n                throw new Error(\"Base64Coder: incorrect padding\");\n            }\n        }\n        return paddingLength;\n    }\n\n}\n\nconst stdCoder = new Coder();\n\nexport function encode(data: Uint8Array): string {\n    return stdCoder.encode(data);\n}\n\nexport function decode(s: string): Uint8Array {\n    return stdCoder.decode(s);\n}\n\n/**\n * Implements URL-safe Base64 encoding.\n * (Same as Base64, but '+' is replaced with '-', and '/' with '_').\n *\n * Operates in constant time.\n */\nexport class URLSafeCoder extends Coder {\n    // URL-safe encoding have the following encoded/decoded ranges:\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  -   _\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   45  95\n    //\n\n    protected _encodeByte(b: number): string {\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 45);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 45) - 63 + 95);\n\n        return String.fromCharCode(result);\n    }\n\n    protected _decodeChar(c: number): number {\n        let result = INVALID_BYTE;\n\n        // c == 45 (c > 44 and c < 46)\n        result += (((44 - c) & (c - 46)) >>> 8) & (-INVALID_BYTE + c - 45 + 62);\n        // c == 95 (c > 94 and c < 96)\n        result += (((94 - c) & (c - 96)) >>> 8) & (-INVALID_BYTE + c - 95 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n}\n\nconst urlSafeCoder = new URLSafeCoder();\n\nexport function encodeURLSafe(data: Uint8Array): string {\n    return urlSafeCoder.encode(data);\n}\n\nexport function decodeURLSafe(s: string): Uint8Array {\n    return urlSafeCoder.decode(s);\n}\n\n\nexport const encodedLength = (length: number) =>\n    stdCoder.encodedLength(length);\n\nexport const maxDecodedLength = (length: number) =>\n    stdCoder.maxDecodedLength(length);\n\nexport const decodedLength = (s: string) =>\n    stdCoder.decodedLength(s);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package utf8 implements UTF-8 encoding and decoding.\n */\n\nconst INVALID_UTF16 = \"utf8: invalid string\";\nconst INVALID_UTF8 = \"utf8: invalid source encoding\";\n\n/**\n * Encodes the given string into UTF-8 byte array.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encode(s: string): Uint8Array {\n    // Calculate result length and allocate output array.\n    // encodedLength() also validates string and throws errors,\n    // so we don't need repeat validation here.\n    const arr = new Uint8Array(encodedLength(s));\n\n    let pos = 0;\n    for (let i = 0; i < s.length; i++) {\n        let c = s.charCodeAt(i);\n        if (c < 0x80) {\n            arr[pos++] = c;\n        } else if (c < 0x800) {\n            arr[pos++] = 0xc0 | c >> 6;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else if (c < 0xd800) {\n            arr[pos++] = 0xe0 | c >> 12;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else {\n            i++; // get one more character\n            c = (c & 0x3ff) << 10;\n            c |= s.charCodeAt(i) & 0x3ff;\n            c += 0x10000;\n\n            arr[pos++] = 0xf0 | c >> 18;\n            arr[pos++] = 0x80 | (c >> 12) & 0x3f;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        }\n    }\n    return arr;\n}\n\n/**\n * Returns the number of bytes required to encode the given string into UTF-8.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encodedLength(s: string): number {\n    let result = 0;\n    for (let i = 0; i < s.length; i++) {\n        const c = s.charCodeAt(i);\n        if (c < 0x80) {\n            result += 1;\n        } else if (c < 0x800) {\n            result += 2;\n        } else if (c < 0xd800) {\n            result += 3;\n        } else if (c <= 0xdfff) {\n            if (i >= s.length - 1) {\n                throw new Error(INVALID_UTF16);\n            }\n            i++; // \"eat\" next character\n            result += 4;\n        } else {\n            throw new Error(INVALID_UTF16);\n        }\n    }\n    return result;\n}\n\n/**\n * Decodes the given byte array from UTF-8 into a string.\n * Throws if encoding is invalid.\n */\nexport function decode(arr: Uint8Array): string {\n    const chars: string[] = [];\n    for (let i = 0; i < arr.length; i++) {\n        let b = arr[i];\n\n        if (b & 0x80) {\n            let min;\n            if (b < 0xe0) {\n                // Need 1 more byte.\n                if (i >= arr.length) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x1f) << 6 | (n1 & 0x3f);\n                min = 0x80;\n            } else if (b < 0xf0) {\n                // Need 2 more bytes.\n                if (i >= arr.length - 1) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 12 | (n1 & 0x3f) << 6 | (n2 & 0x3f);\n                min = 0x800;\n            } else if (b < 0xf8) {\n                // Need 3 more bytes.\n                if (i >= arr.length - 2) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                const n3 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80 || (n3 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 18 | (n1 & 0x3f) << 12 | (n2 & 0x3f) << 6 | (n3 & 0x3f);\n                min = 0x10000;\n            } else {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b < min || (b >= 0xd800 && b <= 0xdfff)) {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b >= 0x10000) {\n                // Surrogate pair.\n                if (b > 0x10ffff) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b -= 0x10000;\n                chars.push(String.fromCharCode(0xd800 | (b >> 10)));\n                b = 0xdc00 | (b & 0x3ff);\n            }\n        }\n\n        chars.push(String.fromCharCode(b));\n    }\n    return chars.join(\"\");\n}\n", "(function(nacl) {\n'use strict';\n\n// Ported in 2014 by <PERSON> and <PERSON>.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (typeof require !== 'undefined') {\n    // Node.js.\n    crypto = require('crypto');\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})(typeof module !== 'undefined' && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n", "module.exports = require('./pusher-with-encryption').default;\n", "/* (ignored) */", "export default function encode(s: any): string {\n  return btoa(utob(s));\n}\n\nvar fromCharCode = String.fromCharCode;\n\nvar b64chars =\n  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nvar b64tab = {};\n\nfor (var i = 0, l = b64chars.length; i < l; i++) {\n  b64tab[b64chars.charAt(i)] = i;\n}\n\nvar cb_utob = function (c) {\n  var cc = c.charCodeAt(0);\n  return cc < 0x80\n    ? c\n    : cc < 0x800\n      ? fromCharCode(0xc0 | (cc >>> 6)) + fromCharCode(0x80 | (cc & 0x3f))\n      : fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +\n        fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +\n        fromCharCode(0x80 | (cc & 0x3f));\n};\n\nvar utob = function (u) {\n  return u.replace(/[^\\x00-\\x7F]/g, cb_utob);\n};\n\nvar cb_encode = function (ccc) {\n  var padlen = [0, 2, 1][ccc.length % 3];\n  var ord =\n    (ccc.charCodeAt(0) << 16) |\n    ((ccc.length > 1 ? ccc.charCodeAt(1) : 0) << 8) |\n    (ccc.length > 2 ? ccc.charCodeAt(2) : 0);\n  var chars = [\n    b64chars.charAt(ord >>> 18),\n    b64chars.charAt((ord >>> 12) & 63),\n    padlen >= 2 ? '=' : b64chars.charAt((ord >>> 6) & 63),\n    padlen >= 1 ? '=' : b64chars.charAt(ord & 63),\n  ];\n  return chars.join('');\n};\n\nvar btoa =\n  global.btoa ||\n  function (b) {\n    return b.replace(/[\\s\\S]{1,3}/g, cb_encode);\n  };\n", "import TimedCallback from './timed_callback';\nimport { Delay, Scheduler, Canceller } from './scheduling';\n\nabstract class Timer {\n  protected clear: Canceller;\n  protected timer: number | void;\n\n  constructor(\n    set: Scheduler,\n    clear: Canceller,\n    delay: Delay,\n    callback: TimedCallback,\n  ) {\n    this.clear = clear;\n    this.timer = set(() => {\n      if (this.timer) {\n        this.timer = callback(this.timer);\n      }\n    }, delay);\n  }\n\n  /** Returns whether the timer is still running.\n   *\n   * @return {Boolean}\n   */\n  isRunning(): boolean {\n    return this.timer !== null;\n  }\n\n  /** Aborts a timer when it's running. */\n  ensureAborted() {\n    if (this.timer) {\n      this.clear(this.timer);\n      this.timer = null;\n    }\n  }\n}\n\nexport default Timer;\n", "import Timer from './abstract_timer';\nimport TimedCallback from './timed_callback';\nimport { Delay } from './scheduling';\n\n// We need to bind clear functions this way to avoid exceptions on IE8\nfunction clearTimeout(timer) {\n  global.clearTimeout(timer);\n}\nfunction clearInterval(timer) {\n  global.clearInterval(timer);\n}\n\n/** Cross-browser compatible one-off timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class OneOffTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setTimeout, clearTimeout, delay, function (timer) {\n      callback();\n      return null;\n    });\n  }\n}\n\n/** Cross-browser compatible periodic timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class PeriodicTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setInterval, clearInterval, delay, function (timer) {\n      callback();\n      return timer;\n    });\n  }\n}\n", "import * as Collections from './utils/collections';\nimport TimedCallback from './utils/timers/timed_callback';\nimport { OneOffTimer, PeriodicTimer } from './utils/timers';\n\nvar Util = {\n  now(): number {\n    if (Date.now) {\n      return Date.now();\n    } else {\n      return new Date().valueOf();\n    }\n  },\n\n  defer(callback: TimedCallback): OneOffTimer {\n    return new OneOffTimer(0, callback);\n  },\n\n  /** Builds a function that will proxy a method call to its first argument.\n   *\n   * Allows partial application of arguments, so additional arguments are\n   * prepended to the argument list.\n   *\n   * @param  {String} name method name\n   * @return {Function} proxy function\n   */\n  method(name: string, ...args: any[]): Function {\n    var boundArguments = Array.prototype.slice.call(arguments, 1);\n    return function (object) {\n      return object[name].apply(object, boundArguments.concat(arguments));\n    };\n  },\n};\n\nexport default Util;\n", "import base64encode from '../base64';\nimport Util from '../util';\n\n/** Merges multiple objects into the target argument.\n *\n * For properties that are plain Objects, performs a deep-merge. For the\n * rest it just copies the value of the property.\n *\n * To extend prototypes use it as following:\n *   Pusher.Util.extend(Target.prototype, Base.prototype)\n *\n * You can also use it to merge objects without altering them:\n *   Pusher.Util.extend({}, object1, object2)\n *\n * @param  {Object} target\n * @return {Object} the target argument\n */\nexport function extend<T>(target: any, ...sources: any[]): T {\n  for (var i = 0; i < sources.length; i++) {\n    var extensions = sources[i];\n    for (var property in extensions) {\n      if (\n        extensions[property] &&\n        extensions[property].constructor &&\n        extensions[property].constructor === Object\n      ) {\n        target[property] = extend(target[property] || {}, extensions[property]);\n      } else {\n        target[property] = extensions[property];\n      }\n    }\n  }\n  return target;\n}\n\nexport function stringify(): string {\n  var m = ['Pusher'];\n  for (var i = 0; i < arguments.length; i++) {\n    if (typeof arguments[i] === 'string') {\n      m.push(arguments[i]);\n    } else {\n      m.push(safeJSONStringify(arguments[i]));\n    }\n  }\n  return m.join(' : ');\n}\n\nexport function arrayIndexOf(array: any[], item: any): number {\n  // MSIE doesn't have array.indexOf\n  var nativeIndexOf = Array.prototype.indexOf;\n  if (array === null) {\n    return -1;\n  }\n  if (nativeIndexOf && array.indexOf === nativeIndexOf) {\n    return array.indexOf(item);\n  }\n  for (var i = 0, l = array.length; i < l; i++) {\n    if (array[i] === item) {\n      return i;\n    }\n  }\n  return -1;\n}\n\n/** Applies a function f to all properties of an object.\n *\n * Function f gets 3 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function objectApply(object: any, f: Function) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      f(object[key], key, object);\n    }\n  }\n}\n\n/** Return a list of objects own proerty keys\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function keys(object: any): string[] {\n  var keys = [];\n  objectApply(object, function (_, key) {\n    keys.push(key);\n  });\n  return keys;\n}\n\n/** Return a list of object's own property values\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function values(object: any): any[] {\n  var values = [];\n  objectApply(object, function (value) {\n    values.push(value);\n  });\n  return values;\n}\n\n/** Applies a function f to all elements of an array.\n *\n * Function f gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function apply(array: any[], f: Function, context?: any) {\n  for (var i = 0; i < array.length; i++) {\n    f.call(context || global, array[i], i, array);\n  }\n}\n\n/** Maps all elements of the array and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function map(array: any[], f: Function): any[] {\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    result.push(f(array[i], i, array, result));\n  }\n  return result;\n}\n\n/** Maps all elements of the object and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function mapObject(object: any, f: Function): any {\n  var result = {};\n  objectApply(object, function (value, key) {\n    result[key] = f(value);\n  });\n  return result;\n}\n\n/** Filters elements of the array using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function filter(array: any[], test: Function): any[] {\n  test =\n    test ||\n    function (value) {\n      return !!value;\n    };\n\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array, result)) {\n      result.push(array[i]);\n    }\n  }\n  return result;\n}\n\n/** Filters properties of the object using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function filterObject(object: Object, test: Function) {\n  var result = {};\n  objectApply(object, function (value, key) {\n    if ((test && test(value, key, object, result)) || Boolean(value)) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\n\n/** Flattens an object into a two-dimensional array.\n *\n * @param  {Object} object\n * @return {Array} resulting array of [key, value] pairs\n */\nexport function flatten(object: Object): any[] {\n  var result = [];\n  objectApply(object, function (value, key) {\n    result.push([key, value]);\n  });\n  return result;\n}\n\n/** Checks whether any element of the array passes the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function any(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/** Checks whether all elements of the array pass the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function all(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (!test(array[i], i, array)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function encodeParamsObject(data): string {\n  return mapObject(data, function (value) {\n    if (typeof value === 'object') {\n      value = safeJSONStringify(value);\n    }\n    return encodeURIComponent(base64encode(value.toString()));\n  });\n}\n\nexport function buildQueryString(data: any): string {\n  var params = filterObject(data, function (value) {\n    return value !== undefined;\n  });\n\n  var query = map(\n    flatten(encodeParamsObject(params)),\n    Util.method('join', '='),\n  ).join('&');\n\n  return query;\n}\n\n/**\n * See https://github.com/douglascrockford/JSON-js/blob/master/cycle.js\n *\n * Remove circular references from an object. Required for JSON.stringify in\n * React Native, which tends to blow up a lot.\n *\n * @param  {any} object\n * @return {any}        Decycled object\n */\nexport function decycleObject(object: any): any {\n  var objects = [],\n    paths = [];\n\n  return (function derez(value, path) {\n    var i, name, nu;\n\n    switch (typeof value) {\n      case 'object':\n        if (!value) {\n          return null;\n        }\n        for (i = 0; i < objects.length; i += 1) {\n          if (objects[i] === value) {\n            return { $ref: paths[i] };\n          }\n        }\n\n        objects.push(value);\n        paths.push(path);\n\n        if (Object.prototype.toString.apply(value) === '[object Array]') {\n          nu = [];\n          for (i = 0; i < value.length; i += 1) {\n            nu[i] = derez(value[i], path + '[' + i + ']');\n          }\n        } else {\n          nu = {};\n          for (name in value) {\n            if (Object.prototype.hasOwnProperty.call(value, name)) {\n              nu[name] = derez(\n                value[name],\n                path + '[' + JSON.stringify(name) + ']',\n              );\n            }\n          }\n        }\n        return nu;\n      case 'number':\n      case 'string':\n      case 'boolean':\n        return value;\n    }\n  })(object, '$');\n}\n\n/**\n * Provides a cross-browser and cross-platform way to safely stringify objects\n * into JSON. This is particularly necessary for ReactNative, where circular JSON\n * structures throw an exception.\n *\n * @param  {any}    source The object to stringify\n * @return {string}        The serialized output.\n */\nexport function safeJSONStringify(source: any): string {\n  try {\n    return JSON.stringify(source);\n  } catch (e) {\n    return JSON.stringify(decycleObject(source));\n  }\n}\n", "import {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions,\n} from './auth/options';\nimport { AuthTransport } from './config';\n\nexport interface DefaultConfig {\n  VERSION: string;\n  PROTOCOL: number;\n  wsPort: number;\n  wssPort: number;\n  wsPath: string;\n  httpHost: string;\n  httpPort: number;\n  httpsPort: number;\n  httpPath: string;\n  stats_host: string;\n  authEndpoint: string;\n  authTransport: AuthTransport;\n  activityTimeout: number;\n  pongTimeout: number;\n  unavailableTimeout: number;\n  userAuthentication: UserAuthenticationOptions;\n  channelAuthorization: ChannelAuthorizationOptions;\n\n  cdn_http?: string;\n  cdn_https?: string;\n  dependency_suffix?: string;\n}\n\nvar Defaults: DefaultConfig = {\n  VERSION: VERSION,\n  PROTOCOL: 7,\n\n  wsPort: 80,\n  wssPort: 443,\n  wsPath: '',\n  // DEPRECATED: SockJS fallback parameters\n  httpHost: 'sockjs.pusher.com',\n  httpPort: 80,\n  httpsPort: 443,\n  httpPath: '/pusher',\n  // DEPRECATED: Stats\n  stats_host: 'stats.pusher.com',\n  // DEPRECATED: Other settings\n  authEndpoint: '/pusher/auth',\n  authTransport: 'ajax',\n  activityTimeout: 120000,\n  pongTimeout: 30000,\n  unavailableTimeout: 10000,\n  userAuthentication: {\n    endpoint: '/pusher/user-auth',\n    transport: 'ajax',\n  },\n  channelAuthorization: {\n    endpoint: '/pusher/auth',\n    transport: 'ajax',\n  },\n\n  // CDN configuration\n  cdn_http: CDN_HTTP,\n  cdn_https: CDN_HTTPS,\n  dependency_suffix: DEPENDENCY_SUFFIX,\n};\n\nexport default Defaults;\n", "import Defaults from '../defaults';\nimport { default as URLScheme, URLSchemeParams } from './url_scheme';\n\nfunction getGenericURL(\n  baseScheme: string,\n  params: URLSchemeParams,\n  path: string,\n): string {\n  var scheme = baseScheme + (params.useTLS ? 's' : '');\n  var host = params.useTLS ? params.hostTLS : params.hostNonTLS;\n  return scheme + '://' + host + path;\n}\n\nfunction getGenericPath(key: string, queryString?: string): string {\n  var path = '/app/' + key;\n  var query =\n    '?protocol=' +\n    Defaults.PROTOCOL +\n    '&client=js' +\n    '&version=' +\n    Defaults.VERSION +\n    (queryString ? '&' + queryString : '');\n  return path + query;\n}\n\nexport var ws: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '') + getGenericPath(key, 'flash=false');\n    return getGenericURL('ws', params, path);\n  },\n};\n\nexport var http: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '/pusher') + getGenericPath(key);\n    return getGenericURL('http', params, path);\n  },\n};\n\nexport var sockjs: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    return getGenericURL('http', params, params.httpPath || '/pusher');\n  },\n  getPath: function (key: string, params: URLSchemeParams): string {\n    return getGenericPath(key);\n  },\n};\n", "import Callback from './callback';\nimport * as Collections from '../utils/collections';\nimport CallbackTable from './callback_table';\n\nexport default class CallbackRegistry {\n  _callbacks: CallbackTable;\n\n  constructor() {\n    this._callbacks = {};\n  }\n\n  get(name: string): Callback[] {\n    return this._callbacks[prefix(name)];\n  }\n\n  add(name: string, callback: Function, context: any) {\n    var prefixedEventName = prefix(name);\n    this._callbacks[prefixedEventName] =\n      this._callbacks[prefixedEventName] || [];\n    this._callbacks[prefixedEventName].push({\n      fn: callback,\n      context: context,\n    });\n  }\n\n  remove(name?: string, callback?: Function, context?: any) {\n    if (!name && !callback && !context) {\n      this._callbacks = {};\n      return;\n    }\n\n    var names = name ? [prefix(name)] : Collections.keys(this._callbacks);\n\n    if (callback || context) {\n      this.removeCallback(names, callback, context);\n    } else {\n      this.removeAllCallbacks(names);\n    }\n  }\n\n  private removeCallback(names: string[], callback: Function, context: any) {\n    Collections.apply(\n      names,\n      function (name) {\n        this._callbacks[name] = Collections.filter(\n          this._callbacks[name] || [],\n          function (binding) {\n            return (\n              (callback && callback !== binding.fn) ||\n              (context && context !== binding.context)\n            );\n          },\n        );\n        if (this._callbacks[name].length === 0) {\n          delete this._callbacks[name];\n        }\n      },\n      this,\n    );\n  }\n\n  private removeAllCallbacks(names: string[]) {\n    Collections.apply(\n      names,\n      function (name) {\n        delete this._callbacks[name];\n      },\n      this,\n    );\n  }\n}\n\nfunction prefix(name: string): string {\n  return '_' + name;\n}\n", "import * as Collections from '../utils/collections';\nimport Callback from './callback';\nimport Metadata from '../channels/metadata';\nimport CallbackRegistry from './callback_registry';\n\n/** Manages callback bindings and event emitting.\n *\n * @param Function failThrough called when no listeners are bound to an event\n */\nexport default class Dispatcher {\n  callbacks: CallbackRegistry;\n  global_callbacks: Function[];\n  failThrough: Function;\n\n  constructor(failThrough?: Function) {\n    this.callbacks = new CallbackRegistry();\n    this.global_callbacks = [];\n    this.failThrough = failThrough;\n  }\n\n  bind(eventName: string, callback: Function, context?: any) {\n    this.callbacks.add(eventName, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function) {\n    this.global_callbacks.push(callback);\n    return this;\n  }\n\n  unbind(eventName?: string, callback?: Function, context?: any) {\n    this.callbacks.remove(eventName, callback, context);\n    return this;\n  }\n\n  unbind_global(callback?: Function) {\n    if (!callback) {\n      this.global_callbacks = [];\n      return this;\n    }\n\n    this.global_callbacks = Collections.filter(\n      this.global_callbacks || [],\n      (c) => c !== callback,\n    );\n\n    return this;\n  }\n\n  unbind_all() {\n    this.unbind();\n    this.unbind_global();\n    return this;\n  }\n\n  emit(eventName: string, data?: any, metadata?: Metadata): Dispatcher {\n    for (var i = 0; i < this.global_callbacks.length; i++) {\n      this.global_callbacks[i](eventName, data);\n    }\n\n    var callbacks = this.callbacks.get(eventName);\n    var args = [];\n\n    if (metadata) {\n      // if there's a metadata argument, we need to call the callback with both\n      // data and metadata regardless of whether data is undefined\n      args.push(data, metadata);\n    } else if (data) {\n      // metadata is undefined, so we only need to call the callback with data\n      // if data exists\n      args.push(data);\n    }\n\n    if (callbacks && callbacks.length > 0) {\n      for (var i = 0; i < callbacks.length; i++) {\n        callbacks[i].fn.apply(callbacks[i].context || global, args);\n      }\n    } else if (this.failThrough) {\n      this.failThrough(eventName, data);\n    }\n\n    return this;\n  }\n}\n", "import { stringify } from './utils/collections';\nimport Pusher from './pusher';\n\nclass Logger {\n  debug(...args: any[]) {\n    this.log(this.globalLog, args);\n  }\n\n  warn(...args: any[]) {\n    this.log(this.globalLogWarn, args);\n  }\n\n  error(...args: any[]) {\n    this.log(this.globalLogError, args);\n  }\n\n  private globalLog = (message: string) => {\n    if (global.console && global.console.log) {\n      global.console.log(message);\n    }\n  };\n\n  private globalLogWarn(message: string) {\n    if (global.console && global.console.warn) {\n      global.console.warn(message);\n    } else {\n      this.globalLog(message);\n    }\n  }\n\n  private globalLogError(message: string) {\n    if (global.console && global.console.error) {\n      global.console.error(message);\n    } else {\n      this.globalLogWarn(message);\n    }\n  }\n\n  private log(\n    defaultLoggingFunction: (message: string) => void,\n    ...args: any[]\n  ) {\n    var message = stringify.apply(this, arguments);\n    if (Pusher.log) {\n      Pusher.log(message);\n    } else if (Pusher.logToConsole) {\n      const log = defaultLoggingFunction.bind(this);\n      log(message);\n    }\n  }\n}\n\nexport default new Logger();\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Logger from '../logger';\nimport TransportHooks from './transport_hooks';\nimport Socket from '../socket';\nimport Runtime from 'runtime';\nimport Timeline from '../timeline/timeline';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides universal API for transport connections.\n *\n * Transport connection is a low-level object that wraps a connection method\n * and exposes a simple evented interface for the connection state and\n * messaging. It does not implement Pusher-specific WebSocket protocol.\n *\n * Additionally, it fetches resources needed for transport to work and exposes\n * an interface for querying transport features.\n *\n * States:\n * - new - initial state after constructing the object\n * - initializing - during initialization phase, usually fetching resources\n * - intialized - ready to establish a connection\n * - connection - when connection is being established\n * - open - when connection ready to be used\n * - closed - after connection was closed be either side\n *\n * Emits:\n * - error - after the connection raised an error\n *\n * Options:\n * - useTLS - whether connection should be over TLS\n * - hostTLS - host to connect to when connection is over TLS\n * - hostNonTLS - host to connect to when connection is over TLS\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class TransportConnection extends EventsDispatcher {\n  hooks: TransportHooks;\n  name: string;\n  priority: number;\n  key: string;\n  options: TransportConnectionOptions;\n  state: string;\n  timeline: Timeline;\n  activityTimeout: number;\n  id: number;\n  socket: Socket;\n  beforeOpen: Function;\n  initialize: Function;\n\n  constructor(\n    hooks: TransportHooks,\n    name: string,\n    priority: number,\n    key: string,\n    options: TransportConnectionOptions,\n  ) {\n    super();\n    this.initialize = Runtime.transportConnectionInitializer;\n    this.hooks = hooks;\n    this.name = name;\n    this.priority = priority;\n    this.key = key;\n    this.options = options;\n\n    this.state = 'new';\n    this.timeline = options.timeline;\n    this.activityTimeout = options.activityTimeout;\n    this.id = this.timeline.generateUniqueID();\n  }\n\n  /** Checks whether the transport handles activity checks by itself.\n   *\n   * @return {Boolean}\n   */\n  handlesActivityChecks(): boolean {\n    return Boolean(this.hooks.handlesActivityChecks);\n  }\n\n  /** Checks whether the transport supports the ping/pong API.\n   *\n   * @return {Boolean}\n   */\n  supportsPing(): boolean {\n    return Boolean(this.hooks.supportsPing);\n  }\n\n  /** Tries to establish a connection.\n   *\n   * @returns {Boolean} false if transport is in invalid state\n   */\n  connect(): boolean {\n    if (this.socket || this.state !== 'initialized') {\n      return false;\n    }\n\n    var url = this.hooks.urls.getInitial(this.key, this.options);\n    try {\n      this.socket = this.hooks.getSocket(url, this.options);\n    } catch (e) {\n      Util.defer(() => {\n        this.onError(e);\n        this.changeState('closed');\n      });\n      return false;\n    }\n\n    this.bindListeners();\n\n    Logger.debug('Connecting', { transport: this.name, url });\n    this.changeState('connecting');\n    return true;\n  }\n\n  /** Closes the connection.\n   *\n   * @return {Boolean} true if there was a connection to close\n   */\n  close(): boolean {\n    if (this.socket) {\n      this.socket.close();\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends data over the open connection.\n   *\n   * @param {String} data\n   * @return {Boolean} true only when in the \"open\" state\n   */\n  send(data: any): boolean {\n    if (this.state === 'open') {\n      // Workaround for MobileSafari bug (see https://gist.github.com/2052006)\n      Util.defer(() => {\n        if (this.socket) {\n          this.socket.send(data);\n        }\n      });\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends a ping if the connection is open and transport supports it. */\n  ping() {\n    if (this.state === 'open' && this.supportsPing()) {\n      this.socket.ping();\n    }\n  }\n\n  private onOpen() {\n    if (this.hooks.beforeOpen) {\n      this.hooks.beforeOpen(\n        this.socket,\n        this.hooks.urls.getPath(this.key, this.options),\n      );\n    }\n    this.changeState('open');\n    this.socket.onopen = undefined;\n  }\n\n  private onError(error) {\n    this.emit('error', { type: 'WebSocketError', error: error });\n    this.timeline.error(this.buildTimelineMessage({ error: error.toString() }));\n  }\n\n  private onClose(closeEvent?: any) {\n    if (closeEvent) {\n      this.changeState('closed', {\n        code: closeEvent.code,\n        reason: closeEvent.reason,\n        wasClean: closeEvent.wasClean,\n      });\n    } else {\n      this.changeState('closed');\n    }\n    this.unbindListeners();\n    this.socket = undefined;\n  }\n\n  private onMessage(message) {\n    this.emit('message', message);\n  }\n\n  private onActivity() {\n    this.emit('activity');\n  }\n\n  private bindListeners() {\n    this.socket.onopen = () => {\n      this.onOpen();\n    };\n    this.socket.onerror = (error) => {\n      this.onError(error);\n    };\n    this.socket.onclose = (closeEvent) => {\n      this.onClose(closeEvent);\n    };\n    this.socket.onmessage = (message) => {\n      this.onMessage(message);\n    };\n\n    if (this.supportsPing()) {\n      this.socket.onactivity = () => {\n        this.onActivity();\n      };\n    }\n  }\n\n  private unbindListeners() {\n    if (this.socket) {\n      this.socket.onopen = undefined;\n      this.socket.onerror = undefined;\n      this.socket.onclose = undefined;\n      this.socket.onmessage = undefined;\n      if (this.supportsPing()) {\n        this.socket.onactivity = undefined;\n      }\n    }\n  }\n\n  private changeState(state: string, params?: any) {\n    this.state = state;\n    this.timeline.info(\n      this.buildTimelineMessage({\n        state: state,\n        params: params,\n      }),\n    );\n    this.emit(state, params);\n  }\n\n  buildTimelineMessage(message): any {\n    return Collections.extend({ cid: this.id }, message);\n  }\n}\n", "import Factory from '../utils/factory';\nimport TransportHooks from './transport_hooks';\nimport TransportConnection from './transport_connection';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides interface for transport connection instantiation.\n *\n * Takes transport-specific hooks as the only argument, which allow checking\n * for transport support and creating its connections.\n *\n * Supported hooks: * - file - the name of the file to be fetched during initialization\n * - urls - URL scheme to be used by transport\n * - handlesActivityCheck - true when the transport handles activity checks\n * - supportsPing - true when the transport has a ping/activity API\n * - isSupported - tells whether the transport is supported in the environment\n * - getSocket - creates a WebSocket-compatible transport socket\n *\n * See transports.js for specific implementations.\n *\n * @param {Object} hooks object containing all needed transport hooks\n */\nexport default class Transport {\n  hooks: TransportHooks;\n\n  constructor(hooks: TransportHooks) {\n    this.hooks = hooks;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * @param {Object} envronment te environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: any): boolean {\n    return this.hooks.isSupported(environment);\n  }\n\n  /** Creates a transport connection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: any,\n  ): TransportConnection {\n    return new TransportConnection(this.hooks, name, priority, key, options);\n  }\n}\n", "import * as URLSchemes from 'core/transports/url_schemes';\nimport URLScheme from 'core/transports/url_scheme';\nimport Transport from 'core/transports/transport';\nimport Util from 'core/util';\nimport * as Collections from 'core/utils/collections';\nimport TransportHooks from 'core/transports/transport_hooks';\nimport TransportsTable from 'core/transports/transports_table';\nimport Runtime from 'runtime';\n\n/** WebSocket transport.\n *\n * Uses native WebSocket implementation, including MozWebSocket supported by\n * earlier Firefox versions.\n */\nvar WSTransport = new Transport(<TransportHooks>{\n  urls: URLSchemes.ws,\n  handlesActivityChecks: false,\n  supportsPing: false,\n\n  isInitialized: function () {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  isSupported: function (): boolean {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  getSocket: function (url) {\n    return Runtime.createWebSocket(url);\n  },\n});\n\nvar httpConfiguration = {\n  urls: URLSchemes.http,\n  handlesActivityChecks: false,\n  supportsPing: true,\n  isInitialized: function () {\n    return true;\n  },\n};\n\nexport var streamingConfiguration = Collections.extend(\n  {\n    getSocket: function (url) {\n      return Runtime.HTTPFactory.createStreamingSocket(url);\n    },\n  },\n  httpConfiguration,\n);\nexport var pollingConfiguration = Collections.extend(\n  {\n    getSocket: function (url) {\n      return Runtime.HTTPFactory.createPollingSocket(url);\n    },\n  },\n  httpConfiguration,\n);\n\nvar xhrConfiguration = {\n  isSupported: function (): boolean {\n    return Runtime.isXHRSupported();\n  },\n};\n\n/** HTTP streaming transport using CORS-enabled XMLHttpRequest. */\nvar XHRStreamingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, streamingConfiguration, xhrConfiguration)\n  ),\n);\n\n/** HTTP long-polling transport using CORS-enabled XMLHttpRequest. */\nvar XHRPollingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, pollingConfiguration, xhrConfiguration)\n  ),\n);\n\nvar Transports: TransportsTable = {\n  ws: WSTransport,\n  xhr_streaming: XHRStreamingTransport,\n  xhr_polling: XHRPollingTransport,\n};\n\nexport default Transports;\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport TransportManager from './transport_manager';\nimport TransportConnection from './transport_connection';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\n\n/** Creates transport connections monitored by a transport manager.\n *\n * When a transport is closed, it might mean the environment does not support\n * it. It's possible that messages get stuck in an intermediate buffer or\n * proxies terminate inactive connections. To combat these problems,\n * assistants monitor the connection lifetime, report unclean exits and\n * adjust ping timeouts to keep the connection active. The decision to disable\n * a transport is the manager's responsibility.\n *\n * @param {TransportManager} manager\n * @param {TransportConnection} transport\n * @param {Object} options\n */\nexport default class AssistantToTheTransportManager {\n  manager: TransportManager;\n  transport: Transport;\n  minPingDelay: number;\n  maxPingDelay: number;\n  pingDelay: number;\n\n  constructor(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions,\n  ) {\n    this.manager = manager;\n    this.transport = transport;\n    this.minPingDelay = options.minPingDelay;\n    this.maxPingDelay = options.maxPingDelay;\n    this.pingDelay = undefined;\n  }\n\n  /** Creates a transport connection.\n   *\n   * This function has the same API as Transport#createConnection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: Object,\n  ): TransportConnection {\n    options = Collections.extend({}, options, {\n      activityTimeout: this.pingDelay,\n    });\n    var connection = this.transport.createConnection(\n      name,\n      priority,\n      key,\n      options,\n    );\n\n    var openTimestamp = null;\n\n    var onOpen = function () {\n      connection.unbind('open', onOpen);\n      connection.bind('closed', onClosed);\n      openTimestamp = Util.now();\n    };\n    var onClosed = (closeEvent) => {\n      connection.unbind('closed', onClosed);\n\n      if (closeEvent.code === 1002 || closeEvent.code === 1003) {\n        // we don't want to use transports not obeying the protocol\n        this.manager.reportDeath();\n      } else if (!closeEvent.wasClean && openTimestamp) {\n        // report deaths only for short-living transport\n        var lifespan = Util.now() - openTimestamp;\n        if (lifespan < 2 * this.maxPingDelay) {\n          this.manager.reportDeath();\n          this.pingDelay = Math.max(lifespan / 2, this.minPingDelay);\n        }\n      }\n    };\n\n    connection.bind('open', onOpen);\n    return connection;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * This function has the same API as Transport#isSupported. Might return false\n   * when the manager decides to kill the transport.\n   *\n   * @param {Object} environment the environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: string): boolean {\n    return this.manager.isAlive() && this.transport.isSupported(environment);\n  }\n}\n", "import Action from './action';\nimport { PusherEvent } from './message-types';\n/**\n * Provides functions for handling Pusher protocol-specific messages.\n */\n\nconst Protocol = {\n  /**\n   * Decodes a message in a Pusher format.\n   *\n   * The MessageEvent we receive from the transport should contain a pusher event\n   * (https://pusher.com/docs/pusher_protocol#events) serialized as JSO<PERSON> in the\n   * data field\n   *\n   * The pusher event may contain a data field too, and it may also be\n   * serialised as JSON\n   *\n   * Throws errors when messages are not parse'able.\n   *\n   * @param  {MessageEvent} messageEvent\n   * @return {PusherEvent}\n   */\n  decodeMessage: function (messageEvent: MessageEvent): PusherEvent {\n    try {\n      var messageData = JSON.parse(messageEvent.data);\n      var pusherEventData = messageData.data;\n      if (typeof pusherEventData === 'string') {\n        try {\n          pusherEventData = JSON.parse(messageData.data);\n        } catch (e) {}\n      }\n      var pusherEvent: PusherEvent = {\n        event: messageData.event,\n        channel: messageData.channel,\n        data: pusherEventData,\n      };\n      if (messageData.user_id) {\n        pusherEvent.user_id = messageData.user_id;\n      }\n      return pusherEvent;\n    } catch (e) {\n      throw { type: 'MessageParseError', error: e, data: messageEvent.data };\n    }\n  },\n\n  /**\n   * Encodes a message to be sent.\n   *\n   * @param  {PusherEvent} event\n   * @return {String}\n   */\n  encodeMessage: function (event: PusherEvent): string {\n    return JSON.stringify(event);\n  },\n\n  /**\n   * Processes a handshake message and returns appropriate actions.\n   *\n   * Returns an object with an 'action' and other action-specific properties.\n   *\n   * There are three outcomes when calling this function. First is a successful\n   * connection attempt, when pusher:connection_established is received, which\n   * results in a 'connected' action with an 'id' property. When passed a\n   * pusher:error event, it returns a result with action appropriate to the\n   * close code and an error. Otherwise, it raises an exception.\n   *\n   * @param {String} message\n   * @result Object\n   */\n  processHandshake: function (messageEvent: MessageEvent): Action {\n    var message = Protocol.decodeMessage(messageEvent);\n\n    if (message.event === 'pusher:connection_established') {\n      if (!message.data.activity_timeout) {\n        throw 'No activity timeout specified in handshake';\n      }\n      return {\n        action: 'connected',\n        id: message.data.socket_id,\n        activityTimeout: message.data.activity_timeout * 1000,\n      };\n    } else if (message.event === 'pusher:error') {\n      // From protocol 6 close codes are sent only once, so this only\n      // happens when connection does not support close codes\n      return {\n        action: this.getCloseAction(message.data),\n        error: this.getCloseError(message.data),\n      };\n    } else {\n      throw 'Invalid handshake';\n    }\n  },\n\n  /**\n   * Dispatches the close event and returns an appropriate action name.\n   *\n   * See:\n   * 1. https://developer.mozilla.org/en-US/docs/WebSockets/WebSockets_reference/CloseEvent\n   * 2. http://pusher.com/docs/pusher_protocol\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {String} close action name\n   */\n  getCloseAction: function (closeEvent): string {\n    if (closeEvent.code < 4000) {\n      // ignore 1000 CLOSE_NORMAL, 1001 CLOSE_GOING_AWAY,\n      //        1005 CLOSE_NO_STATUS, 1006 CLOSE_ABNORMAL\n      // ignore 1007...3999\n      // handle 1002 CLOSE_PROTOCOL_ERROR, 1003 CLOSE_UNSUPPORTED,\n      //        1004 CLOSE_TOO_LARGE\n      if (closeEvent.code >= 1002 && closeEvent.code <= 1004) {\n        return 'backoff';\n      } else {\n        return null;\n      }\n    } else if (closeEvent.code === 4000) {\n      return 'tls_only';\n    } else if (closeEvent.code < 4100) {\n      return 'refused';\n    } else if (closeEvent.code < 4200) {\n      return 'backoff';\n    } else if (closeEvent.code < 4300) {\n      return 'retry';\n    } else {\n      // unknown error\n      return 'refused';\n    }\n  },\n\n  /**\n   * Returns an error or null basing on the close event.\n   *\n   * Null is returned when connection was closed cleanly. Otherwise, an object\n   * with error details is returned.\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {Object} error object\n   */\n  getCloseError: function (closeEvent): any {\n    if (closeEvent.code !== 1000 && closeEvent.code !== 1001) {\n      return {\n        type: 'PusherError',\n        data: {\n          code: closeEvent.code,\n          message: closeEvent.reason || closeEvent.message,\n        },\n      };\n    } else {\n      return null;\n    }\n  },\n};\n\nexport default Protocol;\n", "import * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Protocol from './protocol/protocol';\nimport { PusherEvent } from './protocol/message-types';\nimport Logger from '../logger';\nimport TransportConnection from '../transports/transport_connection';\nimport Socket from '../socket';\n/**\n * Provides Pusher protocol interface for transports.\n *\n * Emits following events:\n * - message - on received messages\n * - ping - on ping requests\n * - pong - on pong responses\n * - error - when the transport emits an error\n * - closed - after closing the transport\n *\n * It also emits more events when connection closes with a code.\n * See Protocol.getCloseAction to get more details.\n *\n * @param {Number} id\n * @param {AbstractTransport} transport\n */\nexport default class Connection extends EventsDispatcher implements Socket {\n  id: string;\n  transport: TransportConnection;\n  activityTimeout: number;\n\n  constructor(id: string, transport: TransportConnection) {\n    super();\n    this.id = id;\n    this.transport = transport;\n    this.activityTimeout = transport.activityTimeout;\n    this.bindListeners();\n  }\n\n  /** Returns whether used transport handles activity checks by itself\n   *\n   * @returns {Boolean} true if activity checks are handled by the transport\n   */\n  handlesActivityChecks() {\n    return this.transport.handlesActivityChecks();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data: any): boolean {\n    return this.transport.send(data);\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string): boolean {\n    var event: PusherEvent = { event: name, data: data };\n    if (channel) {\n      event.channel = channel;\n    }\n    Logger.debug('Event sent', event);\n    return this.send(Protocol.encodeMessage(event));\n  }\n\n  /** Sends a ping message to the server.\n   *\n   * Basing on the underlying transport, it might send either transport's\n   * protocol-specific ping or pusher:ping event.\n   */\n  ping() {\n    if (this.transport.supportsPing()) {\n      this.transport.ping();\n    } else {\n      this.send_event('pusher:ping', {});\n    }\n  }\n\n  /** Closes the connection. */\n  close() {\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    var listeners = {\n      message: (messageEvent: MessageEvent) => {\n        var pusherEvent;\n        try {\n          pusherEvent = Protocol.decodeMessage(messageEvent);\n        } catch (e) {\n          this.emit('error', {\n            type: 'MessageParseError',\n            error: e,\n            data: messageEvent.data,\n          });\n        }\n\n        if (pusherEvent !== undefined) {\n          Logger.debug('Event recd', pusherEvent);\n\n          switch (pusherEvent.event) {\n            case 'pusher:error':\n              this.emit('error', {\n                type: 'PusherError',\n                data: pusherEvent.data,\n              });\n              break;\n            case 'pusher:ping':\n              this.emit('ping');\n              break;\n            case 'pusher:pong':\n              this.emit('pong');\n              break;\n          }\n          this.emit('message', pusherEvent);\n        }\n      },\n      activity: () => {\n        this.emit('activity');\n      },\n      error: (error) => {\n        this.emit('error', error);\n      },\n      closed: (closeEvent) => {\n        unbindListeners();\n\n        if (closeEvent && closeEvent.code) {\n          this.handleCloseEvent(closeEvent);\n        }\n\n        this.transport = null;\n        this.emit('closed');\n      },\n    };\n\n    var unbindListeners = () => {\n      Collections.objectApply(listeners, (listener, event) => {\n        this.transport.unbind(event, listener);\n      });\n    };\n\n    Collections.objectApply(listeners, (listener, event) => {\n      this.transport.bind(event, listener);\n    });\n  }\n\n  private handleCloseEvent(closeEvent: any) {\n    var action = Protocol.getCloseAction(closeEvent);\n    var error = Protocol.getCloseError(closeEvent);\n    if (error) {\n      this.emit('error', error);\n    }\n    if (action) {\n      this.emit(action, { action: action, error: error });\n    }\n  }\n}\n", "import Util from '../../util';\nimport * as Collections from '../../utils/collections';\nimport Protocol from '../protocol/protocol';\nimport Connection from '../connection';\nimport TransportConnection from '../../transports/transport_connection';\nimport HandshakePayload from './handshake_payload';\n\n/**\n * Handles Pusher protocol handshakes for transports.\n *\n * Calls back with a result object after handshake is completed. Results\n * always have two fields:\n * - action - string describing action to be taken after the handshake\n * - transport - the transport object passed to the constructor\n *\n * Different actions can set different additional properties on the result.\n * In the case of 'connected' action, there will be a 'connection' property\n * containing a Connection object for the transport. Other actions should\n * carry an 'error' property.\n *\n * @param {AbstractTransport} transport\n * @param {Function} callback\n */\nexport default class Handshake {\n  transport: TransportConnection;\n  callback: (HandshakePayload) => void;\n  onMessage: Function;\n  onClosed: Function;\n\n  constructor(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void,\n  ) {\n    this.transport = transport;\n    this.callback = callback;\n    this.bindListeners();\n  }\n\n  close() {\n    this.unbindListeners();\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    this.onMessage = (m) => {\n      this.unbindListeners();\n\n      var result;\n      try {\n        result = Protocol.processHandshake(m);\n      } catch (e) {\n        this.finish('error', { error: e });\n        this.transport.close();\n        return;\n      }\n\n      if (result.action === 'connected') {\n        this.finish('connected', {\n          connection: new Connection(result.id, this.transport),\n          activityTimeout: result.activityTimeout,\n        });\n      } else {\n        this.finish(result.action, { error: result.error });\n        this.transport.close();\n      }\n    };\n\n    this.onClosed = (closeEvent) => {\n      this.unbindListeners();\n\n      var action = Protocol.getCloseAction(closeEvent) || 'backoff';\n      var error = Protocol.getCloseError(closeEvent);\n      this.finish(action, { error: error });\n    };\n\n    this.transport.bind('message', this.onMessage);\n    this.transport.bind('closed', this.onClosed);\n  }\n\n  private unbindListeners() {\n    this.transport.unbind('message', this.onMessage);\n    this.transport.unbind('closed', this.onClosed);\n  }\n\n  private finish(action: string, params: any) {\n    this.callback(\n      Collections.extend({ transport: this.transport, action: action }, params),\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport base64encode from '../base64';\nimport Timeline from './timeline';\nimport Runtime from 'runtime';\n\nexport interface TimelineSenderOptions {\n  host?: string;\n  port?: number;\n  path?: string;\n}\n\nexport default class TimelineSender {\n  timeline: Timeline;\n  options: TimelineSenderOptions;\n  host: string;\n\n  constructor(timeline: Timeline, options: TimelineSenderOptions) {\n    this.timeline = timeline;\n    this.options = options || {};\n  }\n\n  send(useTLS: boolean, callback?: Function) {\n    if (this.timeline.isEmpty()) {\n      return;\n    }\n\n    this.timeline.send(\n      Runtime.TimelineTransport.getAgent(this, useTLS),\n      callback,\n    );\n  }\n}\n", "/** Error classes used throughout the library. */\n// https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\nexport class BadEventName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class BadChannelName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class RequestTimedOut extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportPriorityTooLow extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportClosed extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedFeature extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedTransport extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedStrategy extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class HTTPAuthError extends Error {\n  status: number;\n  constructor(status: number, msg?: string) {\n    super(msg);\n    this.status = status;\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n", "/**\n * A place to store help URLs for error messages etc\n */\n\nconst urlStore = {\n  baseUrl: 'https://pusher.com',\n  urls: {\n    authenticationEndpoint: {\n      path: '/docs/channels/server_api/authenticating_users',\n    },\n    authorizationEndpoint: {\n      path: '/docs/channels/server_api/authorizing-users/',\n    },\n    javascriptQuickStart: {\n      path: '/docs/javascript_quick_start',\n    },\n    triggeringClientEvents: {\n      path: '/docs/client_api_guide/client_events#trigger-events',\n    },\n    encryptedChannelSupport: {\n      fullUrl:\n        'https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support',\n    },\n  },\n};\n\n/** Builds a consistent string with links to pusher documentation\n *\n * @param {string} key - relevant key in the url_store.urls object\n * @return {string} suffix string to append to log message\n */\nconst buildLogSuffix = function (key: string): string {\n  const urlPrefix = 'See:';\n  const urlObj = urlStore.urls[key];\n  if (!urlObj) return '';\n\n  let url;\n  if (urlObj.fullUrl) {\n    url = urlObj.fullUrl;\n  } else if (urlObj.path) {\n    url = urlStore.baseUrl + urlObj.path;\n  }\n\n  if (!url) return '';\n  return `${urlPrefix} ${url}`;\n};\n\nexport default { buildLogSuffix };\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metada<PERSON> from './metadata';\nimport UrlStore from '../utils/url_store';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback,\n} from '../auth/options';\nimport { HTTPAuthError } from '../errors';\n\n/** Provides base public channel interface with an event emitter.\n *\n * Emits:\n * - pusher:subscription_succeeded - after subscribing successfully\n * - other non-internal events\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class Channel extends EventsDispatcher {\n  name: string;\n  pusher: Pusher;\n  subscribed: boolean;\n  subscriptionPending: boolean;\n  subscriptionCancelled: boolean;\n  subscriptionCount: null;\n\n  constructor(name: string, pusher: Pusher) {\n    super(function (event, data) {\n      Logger.debug('No callbacks on ' + name + ' for ' + event);\n    });\n\n    this.name = name;\n    this.pusher = pusher;\n    this.subscribed = false;\n    this.subscriptionPending = false;\n    this.subscriptionCancelled = false;\n  }\n\n  /** Skips authorization, since public channels don't require it.\n   *\n   * @param {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return callback(null, { auth: '' });\n  }\n\n  /** Triggers an event */\n  trigger(event: string, data: any) {\n    if (event.indexOf('client-') !== 0) {\n      throw new Errors.BadEventName(\n        \"Event '\" + event + \"' does not start with 'client-'\",\n      );\n    }\n    if (!this.subscribed) {\n      var suffix = UrlStore.buildLogSuffix('triggeringClientEvents');\n      Logger.warn(\n        `Client event triggered before channel 'subscription_succeeded' event . ${suffix}`,\n      );\n    }\n    return this.pusher.send_event(event, data, this.name);\n  }\n\n  /** Signals disconnection to the channel. For internal use only. */\n  disconnect() {\n    this.subscribed = false;\n    this.subscriptionPending = false;\n  }\n\n  /** Handles a PusherEvent. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (eventName === 'pusher_internal:subscription_succeeded') {\n      this.handleSubscriptionSucceededEvent(event);\n    } else if (eventName === 'pusher_internal:subscription_count') {\n      this.handleSubscriptionCountEvent(event);\n    } else if (eventName.indexOf('pusher_internal:') !== 0) {\n      var metadata: Metadata = {};\n      this.emit(eventName, data, metadata);\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.emit('pusher:subscription_succeeded', event.data);\n    }\n  }\n\n  handleSubscriptionCountEvent(event: PusherEvent) {\n    if (event.data.subscription_count) {\n      this.subscriptionCount = event.data.subscription_count;\n    }\n\n    this.emit('pusher:subscription_count', event.data);\n  }\n\n  /** Sends a subscription request. For internal use only. */\n  subscribe() {\n    if (this.subscribed) {\n      return;\n    }\n    this.subscriptionPending = true;\n    this.subscriptionCancelled = false;\n    this.authorize(\n      this.pusher.connection.socket_id,\n      (error: Error | null, data: ChannelAuthorizationData) => {\n        if (error) {\n          this.subscriptionPending = false;\n          // Why not bind to 'pusher:subscription_error' a level up, and log there?\n          // Binding to this event would cause the warning about no callbacks being\n          // bound (see constructor) to be suppressed, that's not what we want.\n          Logger.error(error.toString());\n          this.emit(\n            'pusher:subscription_error',\n            Object.assign(\n              {},\n              {\n                type: 'AuthError',\n                error: error.message,\n              },\n              error instanceof HTTPAuthError ? { status: error.status } : {},\n            ),\n          );\n        } else {\n          this.pusher.send_event('pusher:subscribe', {\n            auth: data.auth,\n            channel_data: data.channel_data,\n            channel: this.name,\n          });\n        }\n      },\n    );\n  }\n\n  /** Sends an unsubscription request. For internal use only. */\n  unsubscribe() {\n    this.subscribed = false;\n    this.pusher.send_event('pusher:unsubscribe', {\n      channel: this.name,\n    });\n  }\n\n  /** Cancels an in progress subscription. For internal use only. */\n  cancelSubscription() {\n    this.subscriptionCancelled = true;\n  }\n\n  /** Reinstates an in progress subscripiton. For internal use only. */\n  reinstateSubscription() {\n    this.subscriptionCancelled = false;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Channel from './channel';\nimport { ChannelAuthorizationCallback } from '../auth/options';\n\n/** Extends public channels to provide private channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class PrivateChannel extends Channel {\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return this.pusher.config.channelAuthorizer(\n      {\n        channelName: this.name,\n        socketId: socketId,\n      },\n      callback,\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\n\n/** Represents a collection of members of a presence channel. */\nexport default class Members {\n  members: any;\n  count: number;\n  myID: any;\n  me: any;\n\n  constructor() {\n    this.reset();\n  }\n\n  /** Returns member's info for given id.\n   *\n   * Resulting object containts two fields - id and info.\n   *\n   * @param {Number} id\n   * @return {Object} member's info or null\n   */\n  get(id: string): any {\n    if (Object.prototype.hasOwnProperty.call(this.members, id)) {\n      return {\n        id: id,\n        info: this.members[id],\n      };\n    } else {\n      return null;\n    }\n  }\n\n  /** Calls back for each member in unspecified order.\n   *\n   * @param  {Function} callback\n   */\n  each(callback: Function) {\n    Collections.objectApply(this.members, (member, id) => {\n      callback(this.get(id));\n    });\n  }\n\n  /** Updates the id for connected member. For internal use only. */\n  setMyID(id: string) {\n    this.myID = id;\n  }\n\n  /** Handles subscription data. For internal use only. */\n  onSubscription(subscriptionData: any) {\n    this.members = subscriptionData.presence.hash;\n    this.count = subscriptionData.presence.count;\n    this.me = this.get(this.myID);\n  }\n\n  /** Adds a new member to the collection. For internal use only. */\n  addMember(memberData: any) {\n    if (this.get(memberData.user_id) === null) {\n      this.count++;\n    }\n    this.members[memberData.user_id] = memberData.user_info;\n    return this.get(memberData.user_id);\n  }\n\n  /** Adds a member from the collection. For internal use only. */\n  removeMember(memberData: any) {\n    var member = this.get(memberData.user_id);\n    if (member) {\n      delete this.members[memberData.user_id];\n      this.count--;\n    }\n    return member;\n  }\n\n  /** Resets the collection to the initial state. For internal use only. */\n  reset() {\n    this.members = {};\n    this.count = 0;\n    this.myID = null;\n    this.me = null;\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport Logger from '../logger';\nimport Members from './members';\nimport Pusher from '../pusher';\nimport UrlStore from 'core/utils/url_store';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metadata from './metadata';\nimport { ChannelAuthorizationData } from '../auth/options';\n\nexport default class PresenceChannel extends PrivateChannel {\n  members: Members;\n\n  /** Adds presence channel functionality to private channels.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   */\n  constructor(name: string, pusher: Pusher) {\n    super(name, pusher);\n    this.members = new Members();\n  }\n\n  /** Authorizes the connection as a member of the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: Function) {\n    super.authorize(socketId, async (error, authData) => {\n      if (!error) {\n        authData = authData as ChannelAuthorizationData;\n        if (authData.channel_data != null) {\n          var channelData = JSON.parse(authData.channel_data);\n          this.members.setMyID(channelData.user_id);\n        } else {\n          await this.pusher.user.signinDonePromise;\n          if (this.pusher.user.user_data != null) {\n            // If the user is signed in, get the id of the authenticated user\n            // and allow the presence authorization to continue.\n            this.members.setMyID(this.pusher.user.user_data.id);\n          } else {\n            let suffix = UrlStore.buildLogSuffix('authorizationEndpoint');\n            Logger.error(\n              `Invalid auth response for channel '${this.name}', ` +\n                `expected 'channel_data' field. ${suffix}, ` +\n                `or the user should be signed in.`,\n            );\n            callback('Invalid auth response');\n            return;\n          }\n        }\n      }\n      callback(error, authData);\n    });\n  }\n\n  /** Handles presence and subscription events. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    if (eventName.indexOf('pusher_internal:') === 0) {\n      this.handleInternalEvent(event);\n    } else {\n      var data = event.data;\n      var metadata: Metadata = {};\n      if (event.user_id) {\n        metadata.user_id = event.user_id;\n      }\n      this.emit(eventName, data, metadata);\n    }\n  }\n  handleInternalEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    switch (eventName) {\n      case 'pusher_internal:subscription_succeeded':\n        this.handleSubscriptionSucceededEvent(event);\n        break;\n      case 'pusher_internal:subscription_count':\n        this.handleSubscriptionCountEvent(event);\n        break;\n      case 'pusher_internal:member_added':\n        var addedMember = this.members.addMember(data);\n        this.emit('pusher:member_added', addedMember);\n        break;\n      case 'pusher_internal:member_removed':\n        var removedMember = this.members.removeMember(data);\n        if (removedMember) {\n          this.emit('pusher:member_removed', removedMember);\n        }\n        break;\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.members.onSubscription(event.data);\n      this.emit('pusher:subscription_succeeded', this.members);\n    }\n  }\n\n  /** Resets the channel state, including members map. For internal use only. */\n  disconnect() {\n    this.members.reset();\n    super.disconnect();\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { decode as encodeUTF8 } from '@stablelib/utf8';\nimport { decode as decodeBase64 } from '@stablelib/base64';\nimport Dispatcher from '../events/dispatcher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback,\n} from '../auth/options';\nimport * as nacl from 'tweetnacl';\n\n/** Extends private channels to provide encrypted channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class EncryptedChannel extends PrivateChannel {\n  key: Uint8Array = null;\n  nacl: nacl;\n\n  constructor(name: string, pusher: Pusher, nacl: nacl) {\n    super(name, pusher);\n    this.nacl = nacl;\n  }\n\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    super.authorize(\n      socketId,\n      (error: Error | null, authData: ChannelAuthorizationData) => {\n        if (error) {\n          callback(error, authData);\n          return;\n        }\n        let sharedSecret = authData['shared_secret'];\n        if (!sharedSecret) {\n          callback(\n            new Error(\n              `No shared_secret key in auth payload for encrypted channel: ${this.name}`,\n            ),\n            null,\n          );\n          return;\n        }\n        this.key = decodeBase64(sharedSecret);\n        delete authData['shared_secret'];\n        callback(null, authData);\n      },\n    );\n  }\n\n  trigger(event: string, data: any): boolean {\n    throw new Errors.UnsupportedFeature(\n      'Client events are not currently supported for encrypted channels',\n    );\n  }\n\n  /** Handles an event. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (\n      eventName.indexOf('pusher_internal:') === 0 ||\n      eventName.indexOf('pusher:') === 0\n    ) {\n      super.handleEvent(event);\n      return;\n    }\n    this.handleEncryptedEvent(eventName, data);\n  }\n\n  private handleEncryptedEvent(event: string, data: any): void {\n    if (!this.key) {\n      Logger.debug(\n        'Received encrypted event before key has been retrieved from the authEndpoint',\n      );\n      return;\n    }\n    if (!data.ciphertext || !data.nonce) {\n      Logger.error(\n        'Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: ' +\n          data,\n      );\n      return;\n    }\n    let cipherText = decodeBase64(data.ciphertext);\n    if (cipherText.length < this.nacl.secretbox.overheadLength) {\n      Logger.error(\n        `Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${cipherText.length}`,\n      );\n      return;\n    }\n    let nonce = decodeBase64(data.nonce);\n    if (nonce.length < this.nacl.secretbox.nonceLength) {\n      Logger.error(\n        `Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${nonce.length}`,\n      );\n      return;\n    }\n\n    let bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n    if (bytes === null) {\n      Logger.debug(\n        'Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint...',\n      );\n      // Try a single time to retrieve a new auth key and decrypt the event with it\n      // If this fails, a new key will be requested when a new message is received\n      this.authorize(this.pusher.connection.socket_id, (error, authData) => {\n        if (error) {\n          Logger.error(\n            `Failed to make a request to the authEndpoint: ${authData}. Unable to fetch new key, so dropping encrypted event`,\n          );\n          return;\n        }\n        bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n        if (bytes === null) {\n          Logger.error(\n            `Failed to decrypt event with new key. Dropping encrypted event`,\n          );\n          return;\n        }\n        this.emit(event, this.getDataToEmit(bytes));\n        return;\n      });\n      return;\n    }\n    this.emit(event, this.getDataToEmit(bytes));\n  }\n\n  // Try and parse the decrypted bytes as JSON. If we can't parse it, just\n  // return the utf-8 string\n  getDataToEmit(bytes: Uint8Array): string {\n    let raw = encodeUTF8(bytes);\n    try {\n      return JSON.parse(raw);\n    } catch {\n      return raw;\n    }\n  }\n}\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport { Config } from '../config';\nimport Logger from '../logger';\nimport HandshakePayload from './handshake/handshake_payload';\nimport Connection from './connection';\nimport Strategy from '../strategies/strategy';\nimport StrategyRunner from '../strategies/strategy_runner';\nimport * as Collections from '../utils/collections';\nimport Timeline from '../timeline/timeline';\nimport ConnectionManagerOptions from './connection_manager_options';\nimport Runtime from 'runtime';\n\nimport {\n  ErrorCallbacks,\n  HandshakeCallbacks,\n  ConnectionCallbacks,\n} from './callbacks';\nimport Action from './protocol/action';\n\n/** Manages connection to Pusher.\n *\n * Uses a strategy (currently only default), timers and network availability\n * info to establish a connection and export its state. In case of failures,\n * manages reconnection attempts.\n *\n * Exports state changes as following events:\n * - \"state_change\", { previous: p, current: state }\n * - state\n *\n * States:\n * - initialized - initial state, never transitioned to\n * - connecting - connection is being established\n * - connected - connection has been fully established\n * - disconnected - on requested disconnection\n * - unavailable - after connection timeout or when there's no network\n * - failed - when the connection strategy is not supported\n *\n * Options:\n * - unavailableTimeout - time to transition to unavailable state\n * - activityTimeout - time after which ping message should be sent\n * - pongTimeout - time for Pusher to respond with pong before reconnecting\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class ConnectionManager extends EventsDispatcher {\n  key: string;\n  options: ConnectionManagerOptions;\n  state: string;\n  connection: Connection;\n  usingTLS: boolean;\n  timeline: Timeline;\n  socket_id: string;\n  unavailableTimer: Timer;\n  activityTimer: Timer;\n  retryTimer: Timer;\n  activityTimeout: number;\n  strategy: Strategy;\n  runner: StrategyRunner;\n  errorCallbacks: ErrorCallbacks;\n  handshakeCallbacks: HandshakeCallbacks;\n  connectionCallbacks: ConnectionCallbacks;\n\n  constructor(key: string, options: ConnectionManagerOptions) {\n    super();\n    this.state = 'initialized';\n    this.connection = null;\n\n    this.key = key;\n    this.options = options;\n    this.timeline = this.options.timeline;\n    this.usingTLS = this.options.useTLS;\n\n    this.errorCallbacks = this.buildErrorCallbacks();\n    this.connectionCallbacks = this.buildConnectionCallbacks(\n      this.errorCallbacks,\n    );\n    this.handshakeCallbacks = this.buildHandshakeCallbacks(this.errorCallbacks);\n\n    var Network = Runtime.getNetwork();\n\n    Network.bind('online', () => {\n      this.timeline.info({ netinfo: 'online' });\n      if (this.state === 'connecting' || this.state === 'unavailable') {\n        this.retryIn(0);\n      }\n    });\n    Network.bind('offline', () => {\n      this.timeline.info({ netinfo: 'offline' });\n      if (this.connection) {\n        this.sendActivityCheck();\n      }\n    });\n\n    this.updateStrategy();\n  }\n\n  /** Establishes a connection to Pusher.\n   *\n   * Does nothing when connection is already established. See top-level doc\n   * to find events emitted on connection attempts.\n   */\n  connect() {\n    if (this.connection || this.runner) {\n      return;\n    }\n    if (!this.strategy.isSupported()) {\n      this.updateState('failed');\n      return;\n    }\n    this.updateState('connecting');\n    this.startConnecting();\n    this.setUnavailableTimer();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data) {\n    if (this.connection) {\n      return this.connection.send(data);\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string) {\n    if (this.connection) {\n      return this.connection.send_event(name, data, channel);\n    } else {\n      return false;\n    }\n  }\n\n  /** Closes the connection. */\n  disconnect() {\n    this.disconnectInternally();\n    this.updateState('disconnected');\n  }\n\n  isUsingTLS() {\n    return this.usingTLS;\n  }\n\n  private startConnecting() {\n    var callback = (error, handshake) => {\n      if (error) {\n        this.runner = this.strategy.connect(0, callback);\n      } else {\n        if (handshake.action === 'error') {\n          this.emit('error', {\n            type: 'HandshakeError',\n            error: handshake.error,\n          });\n          this.timeline.error({ handshakeError: handshake.error });\n        } else {\n          this.abortConnecting(); // we don't support switching connections yet\n          this.handshakeCallbacks[handshake.action](handshake);\n        }\n      }\n    };\n    this.runner = this.strategy.connect(0, callback);\n  }\n\n  private abortConnecting() {\n    if (this.runner) {\n      this.runner.abort();\n      this.runner = null;\n    }\n  }\n\n  private disconnectInternally() {\n    this.abortConnecting();\n    this.clearRetryTimer();\n    this.clearUnavailableTimer();\n    if (this.connection) {\n      var connection = this.abandonConnection();\n      connection.close();\n    }\n  }\n\n  private updateStrategy() {\n    this.strategy = this.options.getStrategy({\n      key: this.key,\n      timeline: this.timeline,\n      useTLS: this.usingTLS,\n    });\n  }\n\n  private retryIn(delay) {\n    this.timeline.info({ action: 'retry', delay: delay });\n    if (delay > 0) {\n      this.emit('connecting_in', Math.round(delay / 1000));\n    }\n    this.retryTimer = new Timer(delay || 0, () => {\n      this.disconnectInternally();\n      this.connect();\n    });\n  }\n\n  private clearRetryTimer() {\n    if (this.retryTimer) {\n      this.retryTimer.ensureAborted();\n      this.retryTimer = null;\n    }\n  }\n\n  private setUnavailableTimer() {\n    this.unavailableTimer = new Timer(this.options.unavailableTimeout, () => {\n      this.updateState('unavailable');\n    });\n  }\n\n  private clearUnavailableTimer() {\n    if (this.unavailableTimer) {\n      this.unavailableTimer.ensureAborted();\n    }\n  }\n\n  private sendActivityCheck() {\n    this.stopActivityCheck();\n    this.connection.ping();\n    // wait for pong response\n    this.activityTimer = new Timer(this.options.pongTimeout, () => {\n      this.timeline.error({ pong_timed_out: this.options.pongTimeout });\n      this.retryIn(0);\n    });\n  }\n\n  private resetActivityCheck() {\n    this.stopActivityCheck();\n    // send ping after inactivity\n    if (this.connection && !this.connection.handlesActivityChecks()) {\n      this.activityTimer = new Timer(this.activityTimeout, () => {\n        this.sendActivityCheck();\n      });\n    }\n  }\n\n  private stopActivityCheck() {\n    if (this.activityTimer) {\n      this.activityTimer.ensureAborted();\n    }\n  }\n\n  private buildConnectionCallbacks(\n    errorCallbacks: ErrorCallbacks,\n  ): ConnectionCallbacks {\n    return Collections.extend<ConnectionCallbacks>({}, errorCallbacks, {\n      message: (message) => {\n        // includes pong messages from server\n        this.resetActivityCheck();\n        this.emit('message', message);\n      },\n      ping: () => {\n        this.send_event('pusher:pong', {});\n      },\n      activity: () => {\n        this.resetActivityCheck();\n      },\n      error: (error) => {\n        // just emit error to user - socket will already be closed by browser\n        this.emit('error', error);\n      },\n      closed: () => {\n        this.abandonConnection();\n        if (this.shouldRetry()) {\n          this.retryIn(1000);\n        }\n      },\n    });\n  }\n\n  private buildHandshakeCallbacks(\n    errorCallbacks: ErrorCallbacks,\n  ): HandshakeCallbacks {\n    return Collections.extend<HandshakeCallbacks>({}, errorCallbacks, {\n      connected: (handshake: HandshakePayload) => {\n        this.activityTimeout = Math.min(\n          this.options.activityTimeout,\n          handshake.activityTimeout,\n          handshake.connection.activityTimeout || Infinity,\n        );\n        this.clearUnavailableTimer();\n        this.setConnection(handshake.connection);\n        this.socket_id = this.connection.id;\n        this.updateState('connected', { socket_id: this.socket_id });\n      },\n    });\n  }\n\n  private buildErrorCallbacks(): ErrorCallbacks {\n    let withErrorEmitted = (callback) => {\n      return (result: Action | HandshakePayload) => {\n        if (result.error) {\n          this.emit('error', { type: 'WebSocketError', error: result.error });\n        }\n        callback(result);\n      };\n    };\n\n    return {\n      tls_only: withErrorEmitted(() => {\n        this.usingTLS = true;\n        this.updateStrategy();\n        this.retryIn(0);\n      }),\n      refused: withErrorEmitted(() => {\n        this.disconnect();\n      }),\n      backoff: withErrorEmitted(() => {\n        this.retryIn(1000);\n      }),\n      retry: withErrorEmitted(() => {\n        this.retryIn(0);\n      }),\n    };\n  }\n\n  private setConnection(connection) {\n    this.connection = connection;\n    for (var event in this.connectionCallbacks) {\n      this.connection.bind(event, this.connectionCallbacks[event]);\n    }\n    this.resetActivityCheck();\n  }\n\n  private abandonConnection() {\n    if (!this.connection) {\n      return;\n    }\n    this.stopActivityCheck();\n    for (var event in this.connectionCallbacks) {\n      this.connection.unbind(event, this.connectionCallbacks[event]);\n    }\n    var connection = this.connection;\n    this.connection = null;\n    return connection;\n  }\n\n  private updateState(newState: string, data?: any) {\n    var previousState = this.state;\n    this.state = newState;\n    if (previousState !== newState) {\n      var newStateDescription = newState;\n      if (newStateDescription === 'connected') {\n        newStateDescription += ' with new socket ID ' + data.socket_id;\n      }\n      Logger.debug(\n        'State changed',\n        previousState + ' -> ' + newStateDescription,\n      );\n      this.timeline.info({ state: newState, params: data });\n      this.emit('state_change', { previous: previousState, current: newState });\n      this.emit(newState, data);\n    }\n  }\n\n  private shouldRetry(): boolean {\n    return this.state === 'connecting' || this.state === 'connected';\n  }\n}\n", "import Channel from './channel';\nimport * as Collections from '../utils/collections';\nimport ChannelTable from './channel_table';\nimport Factory from '../utils/factory';\nimport Pusher from '../pusher';\nimport Logger from '../logger';\nimport * as Errors from '../errors';\nimport urlStore from '../utils/url_store';\n\n/** Handles a channel map. */\nexport default class Channels {\n  channels: ChannelTable;\n\n  constructor() {\n    this.channels = {};\n  }\n\n  /** Creates or retrieves an existing channel by its name.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   * @return {Channel}\n   */\n  add(name: string, pusher: Pusher) {\n    if (!this.channels[name]) {\n      this.channels[name] = createChannel(name, pusher);\n    }\n    return this.channels[name];\n  }\n\n  /** Returns a list of all channels\n   *\n   * @return {Array}\n   */\n  all(): Channel[] {\n    return Collections.values(this.channels);\n  }\n\n  /** Finds a channel by its name.\n   *\n   * @param {String} name\n   * @return {Channel} channel or null if it doesn't exist\n   */\n  find(name: string) {\n    return this.channels[name];\n  }\n\n  /** Removes a channel from the map.\n   *\n   * @param {String} name\n   */\n  remove(name: string) {\n    var channel = this.channels[name];\n    delete this.channels[name];\n    return channel;\n  }\n\n  /** Proxies disconnection signal to all channels. */\n  disconnect() {\n    Collections.objectApply(this.channels, function (channel) {\n      channel.disconnect();\n    });\n  }\n}\n\nfunction createChannel(name: string, pusher: Pusher): Channel {\n  if (name.indexOf('private-encrypted-') === 0) {\n    if (pusher.config.nacl) {\n      return Factory.createEncryptedChannel(name, pusher, pusher.config.nacl);\n    }\n    let errMsg =\n      'Tried to subscribe to a private-encrypted- channel but no nacl implementation available';\n    let suffix = urlStore.buildLogSuffix('encryptedChannelSupport');\n    throw new Errors.UnsupportedFeature(`${errMsg}. ${suffix}`);\n  } else if (name.indexOf('private-') === 0) {\n    return Factory.createPrivateChannel(name, pusher);\n  } else if (name.indexOf('presence-') === 0) {\n    return Factory.createPresenceChannel(name, pusher);\n  } else if (name.indexOf('#') === 0) {\n    throw new Errors.BadChannelName(\n      'Cannot create a channel with name \"' + name + '\".',\n    );\n  } else {\n    return Factory.createChannel(name, pusher);\n  }\n}\n", "import AssistantToTheTransportManager from '../transports/assistant_to_the_transport_manager';\nimport PingDelayOptions from '../transports/ping_delay_options';\nimport Transport from '../transports/transport';\nimport TransportManager from '../transports/transport_manager';\nimport Handshake from '../connection/handshake';\nimport TransportConnection from '../transports/transport_connection';\nimport SocketHooks from '../http/socket_hooks';\nimport HTTPSocket from '../http/http_socket';\n\nimport Timeline from '../timeline/timeline';\nimport {\n  default as TimelineSender,\n  TimelineSenderOptions,\n} from '../timeline/timeline_sender';\nimport PresenceChannel from '../channels/presence_channel';\nimport PrivateChannel from '../channels/private_channel';\nimport EncryptedChannel from '../channels/encrypted_channel';\nimport Channel from '../channels/channel';\nimport ConnectionManager from '../connection/connection_manager';\nimport ConnectionManagerOptions from '../connection/connection_manager_options';\nimport Ajax from '../http/ajax';\nimport Channels from '../channels/channels';\nimport Pusher from '../pusher';\nimport { Config } from '../config';\nimport * as nacl from 'tweetnacl';\n\nvar Factory = {\n  createChannels(): Channels {\n    return new Channels();\n  },\n\n  createConnectionManager(\n    key: string,\n    options: ConnectionManagerOptions,\n  ): ConnectionManager {\n    return new ConnectionManager(key, options);\n  },\n\n  createChannel(name: string, pusher: Pusher): Channel {\n    return new Channel(name, pusher);\n  },\n\n  createPrivateChannel(name: string, pusher: Pusher): PrivateChannel {\n    return new PrivateChannel(name, pusher);\n  },\n\n  createPresenceChannel(name: string, pusher: Pusher): PresenceChannel {\n    return new PresenceChannel(name, pusher);\n  },\n\n  createEncryptedChannel(\n    name: string,\n    pusher: Pusher,\n    nacl: nacl,\n  ): EncryptedChannel {\n    return new EncryptedChannel(name, pusher, nacl);\n  },\n\n  createTimelineSender(timeline: Timeline, options: TimelineSenderOptions) {\n    return new TimelineSender(timeline, options);\n  },\n\n  createHandshake(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void,\n  ): Handshake {\n    return new Handshake(transport, callback);\n  },\n\n  createAssistantToTheTransportManager(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions,\n  ): AssistantToTheTransportManager {\n    return new AssistantToTheTransportManager(manager, transport, options);\n  },\n};\n\nexport default Factory;\n", "import AssistantToTheTransportManager from './assistant_to_the_transport_manager';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\nimport Factory from '../utils/factory';\n\nexport interface TransportManagerOptions extends PingDelayOptions {\n  lives?: number;\n}\n\n/** Keeps track of the number of lives left for a transport.\n *\n * In the beginning of a session, transports may be assigned a number of\n * lives. When an AssistantToTheTransportManager instance reports a transport\n * connection closed uncleanly, the transport loses a life. When the number\n * of lives drops to zero, the transport gets disabled by its manager.\n *\n * @param {Object} options\n */\nexport default class TransportManager {\n  options: TransportManagerOptions;\n  livesLeft: number;\n\n  constructor(options: TransportManagerOptions) {\n    this.options = options || {};\n    this.livesLeft = this.options.lives || Infinity;\n  }\n\n  /** Creates a assistant for the transport.\n   *\n   * @param {Transport} transport\n   * @returns {AssistantToTheTransportManager}\n   */\n  getAssistant(transport: Transport): AssistantToTheTransportManager {\n    return Factory.createAssistantToTheTransportManager(this, transport, {\n      minPingDelay: this.options.minPingDelay,\n      maxPingDelay: this.options.maxPingDelay,\n    });\n  }\n\n  /** Returns whether the transport has any lives left.\n   *\n   * @returns {Boolean}\n   */\n  isAlive(): boolean {\n    return this.livesLeft > 0;\n  }\n\n  /** Takes one life from the transport. */\n  reportDeath() {\n    this.livesLeft -= 1;\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Loops through strategies with optional timeouts.\n *\n * Options:\n * - loop - whether it should loop through the substrategy list\n * - timeout - initial timeout for a single substrategy\n * - timeoutLimit - maximum timeout\n *\n * @param {Strategy[]} strategies\n * @param {Object} options\n */\nexport default class SequentialStrategy implements Strategy {\n  strategies: Strategy[];\n  loop: boolean;\n  failFast: boolean;\n  timeout: number;\n  timeoutLimit: number;\n\n  constructor(strategies: Strategy[], options: StrategyOptions) {\n    this.strategies = strategies;\n    this.loop = Boolean(options.loop);\n    this.failFast = Boolean(options.failFast);\n    this.timeout = options.timeout;\n    this.timeoutLimit = options.timeoutLimit;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategies = this.strategies;\n    var current = 0;\n    var timeout = this.timeout;\n    var runner = null;\n\n    var tryNextStrategy = (error, handshake) => {\n      if (handshake) {\n        callback(null, handshake);\n      } else {\n        current = current + 1;\n        if (this.loop) {\n          current = current % strategies.length;\n        }\n\n        if (current < strategies.length) {\n          if (timeout) {\n            timeout = timeout * 2;\n            if (this.timeoutLimit) {\n              timeout = Math.min(timeout, this.timeoutLimit);\n            }\n          }\n          runner = this.tryStrategy(\n            strategies[current],\n            minPriority,\n            { timeout, failFast: this.failFast },\n            tryNextStrategy,\n          );\n        } else {\n          callback(true);\n        }\n      }\n    };\n\n    runner = this.tryStrategy(\n      strategies[current],\n      minPriority,\n      { timeout: timeout, failFast: this.failFast },\n      tryNextStrategy,\n    );\n\n    return {\n      abort: function () {\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n\n  private tryStrategy(\n    strategy: Strategy,\n    minPriority: number,\n    options: StrategyOptions,\n    callback: Function,\n  ) {\n    var timer = null;\n    var runner = null;\n\n    if (options.timeout > 0) {\n      timer = new Timer(options.timeout, function () {\n        runner.abort();\n        callback(true);\n      });\n    }\n\n    runner = strategy.connect(minPriority, function (error, handshake) {\n      if (error && timer && timer.isRunning() && !options.failFast) {\n        // advance to the next strategy after the timeout\n        return;\n      }\n      if (timer) {\n        timer.ensureAborted();\n      }\n      callback(error, handshake);\n    });\n\n    return {\n      abort: function () {\n        if (timer) {\n          timer.ensureAborted();\n        }\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        runner.forceMinPriority(p);\n      },\n    };\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport Strategy from './strategy';\n\n/** Launches all substrategies and emits prioritized connected transports.\n *\n * @param {Array} strategies\n */\nexport default class BestConnectedEverStrategy implements Strategy {\n  strategies: Strategy[];\n\n  constructor(strategies: Strategy[]) {\n    this.strategies = strategies;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    return connect(this.strategies, minPriority, function (i, runners) {\n      return function (error, handshake) {\n        runners[i].error = error;\n        if (error) {\n          if (allRunnersFailed(runners)) {\n            callback(true);\n          }\n          return;\n        }\n        Collections.apply(runners, function (runner) {\n          runner.forceMinPriority(handshake.transport.priority);\n        });\n        callback(null, handshake);\n      };\n    });\n  }\n}\n\n/** Connects to all strategies in parallel.\n *\n * Callback builder should be a function that takes two arguments: index\n * and a list of runners. It should return another function that will be\n * passed to the substrategy with given index. Runners can be aborted using\n * abortRunner(s) functions from this class.\n *\n * @param  {Array} strategies\n * @param  {Function} callbackBuilder\n * @return {Object} strategy runner\n */\nfunction connect(\n  strategies: Strategy[],\n  minPriority: number,\n  callbackBuilder: Function,\n) {\n  var runners = Collections.map(strategies, function (strategy, i, _, rs) {\n    return strategy.connect(minPriority, callbackBuilder(i, rs));\n  });\n  return {\n    abort: function () {\n      Collections.apply(runners, abortRunner);\n    },\n    forceMinPriority: function (p) {\n      Collections.apply(runners, function (runner) {\n        runner.forceMinPriority(p);\n      });\n    },\n  };\n}\n\nfunction allRunnersFailed(runners): boolean {\n  return Collections.all(runners, function (runner) {\n    return Boolean(runner.error);\n  });\n}\n\nfunction abortRunner(runner) {\n  if (!runner.error && !runner.aborted) {\n    runner.abort();\n    runner.aborted = true;\n  }\n}\n", "import Util from '../util';\nimport Runtime from 'runtime';\nimport Strategy from './strategy';\nimport SequentialStrategy from './sequential_strategy';\nimport StrategyOptions from './strategy_options';\nimport TransportStrategy from './transport_strategy';\nimport Timeline from '../timeline/timeline';\nimport * as Collections from '../utils/collections';\n\nexport interface TransportStrategyDictionary {\n  [key: string]: TransportStrategy;\n}\n\n/** Caches the last successful transport and, after the first few attempts,\n *  uses the cached transport for subsequent attempts.\n *\n * @param {Strategy} strategy\n * @param {Object} transports\n * @param {Object} options\n */\nexport default class WebSocketPrioritizedCachedStrategy implements Strategy {\n  strategy: Strategy;\n  transports: TransportStrategyDictionary;\n  ttl: number;\n  usingTLS: boolean;\n  timeline: Timeline;\n\n  constructor(\n    strategy: Strategy,\n    transports: TransportStrategyDictionary,\n    options: StrategyOptions,\n  ) {\n    this.strategy = strategy;\n    this.transports = transports;\n    this.ttl = options.ttl || 1800 * 1000;\n    this.usingTLS = options.useTLS;\n    this.timeline = options.timeline;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var usingTLS = this.usingTLS;\n    var info = fetchTransportCache(usingTLS);\n    var cacheSkipCount = info && info.cacheSkipCount ? info.cacheSkipCount : 0;\n\n    var strategies = [this.strategy];\n    if (info && info.timestamp + this.ttl >= Util.now()) {\n      var transport = this.transports[info.transport];\n      if (transport) {\n        if (['ws', 'wss'].includes(info.transport) || cacheSkipCount > 3) {\n          this.timeline.info({\n            cached: true,\n            transport: info.transport,\n            latency: info.latency,\n          });\n          strategies.push(\n            new SequentialStrategy([transport], {\n              timeout: info.latency * 2 + 1000,\n              failFast: true,\n            }),\n          );\n        } else {\n          cacheSkipCount++;\n        }\n      }\n    }\n\n    var startTimestamp = Util.now();\n    var runner = strategies\n      .pop()\n      .connect(minPriority, function cb(error, handshake) {\n        if (error) {\n          flushTransportCache(usingTLS);\n          if (strategies.length > 0) {\n            startTimestamp = Util.now();\n            runner = strategies.pop().connect(minPriority, cb);\n          } else {\n            callback(error);\n          }\n        } else {\n          storeTransportCache(\n            usingTLS,\n            handshake.transport.name,\n            Util.now() - startTimestamp,\n            cacheSkipCount,\n          );\n          callback(null, handshake);\n        }\n      });\n\n    return {\n      abort: function () {\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n}\n\nfunction getTransportCacheKey(usingTLS: boolean): string {\n  return 'pusherTransport' + (usingTLS ? 'TLS' : 'NonTLS');\n}\n\nfunction fetchTransportCache(usingTLS: boolean): any {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      var serializedCache = storage[getTransportCacheKey(usingTLS)];\n      if (serializedCache) {\n        return JSON.parse(serializedCache);\n      }\n    } catch (e) {\n      flushTransportCache(usingTLS);\n    }\n  }\n  return null;\n}\n\nfunction storeTransportCache(\n  usingTLS: boolean,\n  transport: TransportStrategy,\n  latency: number,\n  cacheSkipCount: number,\n) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      storage[getTransportCacheKey(usingTLS)] = Collections.safeJSONStringify({\n        timestamp: Util.now(),\n        transport: transport,\n        latency: latency,\n        cacheSkipCount: cacheSkipCount,\n      });\n    } catch (e) {\n      // catch over quota exceptions raised by localStorage\n    }\n  }\n}\n\nfunction flushTransportCache(usingTLS: boolean) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      delete storage[getTransportCacheKey(usingTLS)];\n    } catch (e) {\n      // catch exceptions raised by localStorage\n    }\n  }\n}\n", "import { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Runs substrategy after specified delay.\n *\n * Options:\n * - delay - time in miliseconds to delay the substrategy attempt\n *\n * @param {Strategy} strategy\n * @param {Object} options\n */\nexport default class DelayedStrategy implements Strategy {\n  strategy: Strategy;\n  options: { delay: number };\n\n  constructor(strategy: Strategy, { delay: number }) {\n    this.strategy = strategy;\n    this.options = { delay: number };\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategy = this.strategy;\n    var runner;\n    var timer = new Timer(this.options.delay, function () {\n      runner = strategy.connect(minPriority, callback);\n    });\n\n    return {\n      abort: function () {\n        timer.ensureAborted();\n        if (runner) {\n          runner.abort();\n        }\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Proxies method calls to one of substrategies basing on the test function.\n *\n * @param {Function} test\n * @param {Strategy} trueBranch strategy used when test returns true\n * @param {Strategy} falseBranch strategy used when test returns false\n */\nexport default class IfStrategy implements Strategy {\n  test: () => boolean;\n  trueBranch: Strategy;\n  falseBranch: Strategy;\n\n  constructor(\n    test: () => boolean,\n    trueBranch: Strategy,\n    falseBranch: Strategy,\n  ) {\n    this.test = test;\n    this.trueBranch = trueBranch;\n    this.falseBranch = falseBranch;\n  }\n\n  isSupported(): boolean {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.connect(minPriority, callback);\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Launches the substrategy and terminates on the first open connection.\n *\n * @param {Strategy} strategy\n */\nexport default class FirstConnectedStrategy implements Strategy {\n  strategy: Strategy;\n\n  constructor(strategy: Strategy) {\n    this.strategy = strategy;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var runner = this.strategy.connect(\n      minPriority,\n      function (error, handshake) {\n        if (handshake) {\n          runner.abort();\n        }\n        callback(error, handshake);\n      },\n    );\n    return runner;\n  }\n}\n", "import * as Collections from 'core/utils/collections';\nimport TransportManager from 'core/transports/transport_manager';\nimport Strategy from 'core/strategies/strategy';\nimport SequentialStrategy from 'core/strategies/sequential_strategy';\nimport BestConnectedEverStrategy from 'core/strategies/best_connected_ever_strategy';\nimport WebSocketPrioritizedCachedStrategy, {\n  TransportStrategyDictionary,\n} from 'core/strategies/websocket_prioritized_cached_strategy';\nimport DelayedStrategy from 'core/strategies/delayed_strategy';\nimport IfStrategy from 'core/strategies/if_strategy';\nimport FirstConnectedStrategy from 'core/strategies/first_connected_strategy';\nimport { Config } from 'core/config';\nimport StrategyOptions from 'core/strategies/strategy_options';\n\nfunction testSupportsStrategy(strategy: Strategy) {\n  return function () {\n    return strategy.isSupported();\n  };\n}\n\nvar getDefaultStrategy = function (\n  config: Config,\n  baseOptions: StrategyOptions,\n  defineTransport: Function,\n): Strategy {\n  var definedTransports = <TransportStrategyDictionary>{};\n\n  function defineTransportStrategy(\n    name: string,\n    type: string,\n    priority: number,\n    options: StrategyOptions,\n    manager?: TransportManager,\n  ) {\n    var transport = defineTransport(\n      config,\n      name,\n      type,\n      priority,\n      options,\n      manager,\n    );\n\n    definedTransports[name] = transport;\n\n    return transport;\n  }\n\n  var ws_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.wsHost + ':' + config.wsPort,\n    hostTLS: config.wsHost + ':' + config.wssPort,\n    httpPath: config.wsPath,\n  });\n  var wss_options: StrategyOptions = Collections.extend({}, ws_options, {\n    useTLS: true,\n  });\n  var http_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.httpHost + ':' + config.httpPort,\n    hostTLS: config.httpHost + ':' + config.httpsPort,\n    httpPath: config.httpPath,\n  });\n  var timeouts = {\n    loop: true,\n    timeout: 15000,\n    timeoutLimit: 60000,\n  };\n\n  var ws_manager = new TransportManager({\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout,\n  });\n  var streaming_manager = new TransportManager({\n    lives: 2,\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout,\n  });\n\n  var ws_transport = defineTransportStrategy(\n    'ws',\n    'ws',\n    3,\n    ws_options,\n    ws_manager,\n  );\n  var wss_transport = defineTransportStrategy(\n    'wss',\n    'ws',\n    3,\n    wss_options,\n    ws_manager,\n  );\n  var xhr_streaming_transport = defineTransportStrategy(\n    'xhr_streaming',\n    'xhr_streaming',\n    1,\n    http_options,\n    streaming_manager,\n  );\n  var xhr_polling_transport = defineTransportStrategy(\n    'xhr_polling',\n    'xhr_polling',\n    1,\n    http_options,\n  );\n\n  var ws_loop = new SequentialStrategy([ws_transport], timeouts);\n  var wss_loop = new SequentialStrategy([wss_transport], timeouts);\n  var streaming_loop = new SequentialStrategy(\n    [xhr_streaming_transport],\n    timeouts,\n  );\n  var polling_loop = new SequentialStrategy([xhr_polling_transport], timeouts);\n\n  var http_loop = new SequentialStrategy(\n    [\n      new IfStrategy(\n        testSupportsStrategy(streaming_loop),\n        new BestConnectedEverStrategy([\n          streaming_loop,\n          new DelayedStrategy(polling_loop, { delay: 4000 }),\n        ]),\n        polling_loop,\n      ),\n    ],\n    timeouts,\n  );\n\n  var wsStrategy;\n  if (baseOptions.useTLS) {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(http_loop, { delay: 2000 }),\n    ]);\n  } else {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(wss_loop, { delay: 2000 }),\n      new DelayedStrategy(http_loop, { delay: 5000 }),\n    ]);\n  }\n\n  return new WebSocketPrioritizedCachedStrategy(\n    new FirstConnectedStrategy(\n      new IfStrategy(testSupportsStrategy(ws_transport), wsStrategy, http_loop),\n    ),\n    definedTransports,\n    {\n      ttl: 1800000,\n      timeline: baseOptions.timeline,\n      useTLS: baseOptions.useTLS,\n    },\n  );\n};\n\nexport default getDefaultStrategy;\n", "/** Initializes the transport.\n *\n * Fetches resources if needed and then transitions to initialized.\n */\nexport default function () {\n  var self = this;\n\n  self.timeline.info(\n    self.buildTimelineMessage({\n      transport: self.name + (self.options.useTLS ? 's' : ''),\n    }),\n  );\n\n  if (self.hooks.isInitialized()) {\n    self.changeState('initialized');\n  } else {\n    self.onClose();\n  }\n}\n", "import Runtime from 'runtime';\nimport RequestHooks from './request_hooks';\nimport <PERSON> from './ajax';\nimport { default as EventsDispatcher } from '../events/dispatcher';\n\nconst MAX_BUFFER_LENGTH = 256 * 1024;\n\nexport default class HTTPRequest extends EventsDispatcher {\n  hooks: RequestHooks;\n  method: string;\n  url: string;\n  position: number;\n  xhr: Ajax;\n  unloader: Function;\n\n  constructor(hooks: RequestHooks, method: string, url: string) {\n    super();\n    this.hooks = hooks;\n    this.method = method;\n    this.url = url;\n  }\n\n  start(payload?: any) {\n    this.position = 0;\n    this.xhr = this.hooks.getRequest(this);\n\n    this.unloader = () => {\n      this.close();\n    };\n    Runtime.addUnloadListener(this.unloader);\n\n    this.xhr.open(this.method, this.url, true);\n\n    if (this.xhr.setRequestHeader) {\n      this.xhr.setRequestHeader('Content-Type', 'application/json'); // ReactNative doesn't set this header by default.\n    }\n    this.xhr.send(payload);\n  }\n\n  close() {\n    if (this.unloader) {\n      Runtime.removeUnloadListener(this.unloader);\n      this.unloader = null;\n    }\n    if (this.xhr) {\n      this.hooks.abortRequest(this.xhr);\n      this.xhr = null;\n    }\n  }\n\n  onChunk(status: number, data: any) {\n    while (true) {\n      var chunk = this.advanceBuffer(data);\n      if (chunk) {\n        this.emit('chunk', { status: status, data: chunk });\n      } else {\n        break;\n      }\n    }\n    if (this.isBufferTooLong(data)) {\n      this.emit('buffer_too_long');\n    }\n  }\n\n  private advanceBuffer(buffer: any[]): any {\n    var unreadData = buffer.slice(this.position);\n    var endOfLinePosition = unreadData.indexOf('\\n');\n\n    if (endOfLinePosition !== -1) {\n      this.position += endOfLinePosition + 1;\n      return unreadData.slice(0, endOfLinePosition);\n    } else {\n      // chunk is not finished yet, don't move the buffer pointer\n      return null;\n    }\n  }\n\n  private isBufferTooLong(buffer: any): boolean {\n    return this.position === buffer.length && buffer.length > MAX_BUFFER_LENGTH;\n  }\n}\n", "enum State {\n  CONNECTING = 0,\n  OPEN = 1,\n  CLOSED = 3,\n}\n\nexport default State;\n", "import URLLocation from './url_location';\nimport State from './state';\nimport Socket from '../socket';\nimport SocketHooks from './socket_hooks';\nimport Util from '../util';\nimport Ajax from './ajax';\nimport HTTPRequest from './http_request';\nimport Runtime from 'runtime';\n\nvar autoIncrement = 1;\n\nclass HTTPSocket implements Socket {\n  hooks: SocketHooks;\n  session: string;\n  location: URLLocation;\n  readyState: State;\n  stream: HTTPRequest;\n\n  onopen: () => void;\n  onerror: (error: any) => void;\n  onclose: (closeEvent: any) => void;\n  onmessage: (message: any) => void;\n  onactivity: () => void;\n\n  constructor(hooks: SocketHooks, url: string) {\n    this.hooks = hooks;\n    this.session = randomNumber(1000) + '/' + randomString(8);\n    this.location = getLocation(url);\n    this.readyState = State.CONNECTING;\n    this.openStream();\n  }\n\n  send(payload: any) {\n    return this.sendRaw(JSON.stringify([payload]));\n  }\n\n  ping() {\n    this.hooks.sendHeartbeat(this);\n  }\n\n  close(code: any, reason: any) {\n    this.onClose(code, reason, true);\n  }\n\n  /** For internal use only */\n  sendRaw(payload: any): boolean {\n    if (this.readyState === State.OPEN) {\n      try {\n        Runtime.createSocketRequest(\n          'POST',\n          getUniqueURL(getSendURL(this.location, this.session)),\n        ).start(payload);\n        return true;\n      } catch (e) {\n        return false;\n      }\n    } else {\n      return false;\n    }\n  }\n\n  /** For internal use only */\n  reconnect() {\n    this.closeStream();\n    this.openStream();\n  }\n\n  /** For internal use only */\n  onClose(code, reason, wasClean) {\n    this.closeStream();\n    this.readyState = State.CLOSED;\n    if (this.onclose) {\n      this.onclose({\n        code: code,\n        reason: reason,\n        wasClean: wasClean,\n      });\n    }\n  }\n\n  private onChunk(chunk) {\n    if (chunk.status !== 200) {\n      return;\n    }\n    if (this.readyState === State.OPEN) {\n      this.onActivity();\n    }\n\n    var payload;\n    var type = chunk.data.slice(0, 1);\n    switch (type) {\n      case 'o':\n        payload = JSON.parse(chunk.data.slice(1) || '{}');\n        this.onOpen(payload);\n        break;\n      case 'a':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        for (var i = 0; i < payload.length; i++) {\n          this.onEvent(payload[i]);\n        }\n        break;\n      case 'm':\n        payload = JSON.parse(chunk.data.slice(1) || 'null');\n        this.onEvent(payload);\n        break;\n      case 'h':\n        this.hooks.onHeartbeat(this);\n        break;\n      case 'c':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        this.onClose(payload[0], payload[1], true);\n        break;\n    }\n  }\n\n  private onOpen(options) {\n    if (this.readyState === State.CONNECTING) {\n      if (options && options.hostname) {\n        this.location.base = replaceHost(this.location.base, options.hostname);\n      }\n      this.readyState = State.OPEN;\n\n      if (this.onopen) {\n        this.onopen();\n      }\n    } else {\n      this.onClose(1006, 'Server lost session', true);\n    }\n  }\n\n  private onEvent(event) {\n    if (this.readyState === State.OPEN && this.onmessage) {\n      this.onmessage({ data: event });\n    }\n  }\n\n  private onActivity() {\n    if (this.onactivity) {\n      this.onactivity();\n    }\n  }\n\n  private onError(error) {\n    if (this.onerror) {\n      this.onerror(error);\n    }\n  }\n\n  private openStream() {\n    this.stream = Runtime.createSocketRequest(\n      'POST',\n      getUniqueURL(this.hooks.getReceiveURL(this.location, this.session)),\n    );\n\n    this.stream.bind('chunk', (chunk) => {\n      this.onChunk(chunk);\n    });\n    this.stream.bind('finished', (status) => {\n      this.hooks.onFinished(this, status);\n    });\n    this.stream.bind('buffer_too_long', () => {\n      this.reconnect();\n    });\n\n    try {\n      this.stream.start();\n    } catch (error) {\n      Util.defer(() => {\n        this.onError(error);\n        this.onClose(1006, 'Could not start streaming', false);\n      });\n    }\n  }\n\n  private closeStream() {\n    if (this.stream) {\n      this.stream.unbind_all();\n      this.stream.close();\n      this.stream = null;\n    }\n  }\n}\n\nfunction getLocation(url): URLLocation {\n  var parts = /([^\\?]*)\\/*(\\??.*)/.exec(url);\n  return {\n    base: parts[1],\n    queryString: parts[2],\n  };\n}\n\nfunction getSendURL(url: URLLocation, session: string): string {\n  return url.base + '/' + session + '/xhr_send';\n}\n\nfunction getUniqueURL(url: string): string {\n  var separator = url.indexOf('?') === -1 ? '?' : '&';\n  return url + separator + 't=' + +new Date() + '&n=' + autoIncrement++;\n}\n\nfunction replaceHost(url: string, hostname: string): string {\n  var urlParts = /(https?:\\/\\/)([^\\/:]+)((\\/|:)?.*)/.exec(url);\n  return urlParts[1] + hostname + urlParts[3];\n}\n\nfunction randomNumber(max: number): number {\n  return Runtime.randomInt(max);\n}\n\nfunction randomString(length: number): string {\n  var result = [];\n\n  for (var i = 0; i < length; i++) {\n    result.push(randomNumber(32).toString(32));\n  }\n\n  return result.join('');\n}\n\nexport default HTTPSocket;\n", "import SocketHooks from './socket_hooks';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function (url, session) {\n    return url.base + '/' + session + '/xhr_streaming' + url.queryString;\n  },\n  onHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  sendHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function (socket, status) {\n    socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n  },\n};\n\nexport default hooks;\n", "import SocketHooks from './socket_hooks';\nimport URLLocation from './url_location';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function (url: URLLocation, session: string): string {\n    return url.base + '/' + session + '/xhr' + url.queryString;\n  },\n  onHeartbeat: function () {\n    // next HTTP request will reset server's activity timer\n  },\n  sendHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function (socket, status) {\n    if (status === 200) {\n      socket.reconnect();\n    } else {\n      socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n    }\n  },\n};\n\nexport default hooks;\n", "import HTTPRequest from 'core/http/http_request';\nimport <PERSON>questHooks from 'core/http/request_hooks';\nimport Ajax from 'core/http/ajax';\nimport Runtime from 'runtime';\n\nvar hooks: RequestHooks = {\n  getRequest: function (socket: HTTPRequest): Ajax {\n    var Constructor = Runtime.getXHRAPI();\n    var xhr = new Constructor();\n    xhr.onreadystatechange = xhr.onprogress = function () {\n      switch (xhr.readyState) {\n        case 3:\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          break;\n        case 4:\n          // this happens only on errors, never after calling close\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          socket.emit('finished', xhr.status);\n          socket.close();\n          break;\n      }\n    };\n    return xhr;\n  },\n  abortRequest: function (xhr: Ajax) {\n    xhr.onreadystatechange = null;\n    xhr.abort();\n  },\n};\n\nexport default hooks;\n", "import HTTPRequest from 'core/http/http_request';\nimport HTTPSocket from 'core/http/http_socket';\nimport SocketHooks from 'core/http/socket_hooks';\nimport RequestHooks from 'core/http/request_hooks';\nimport streamingHooks from 'core/http/http_streaming_socket';\nimport pollingHooks from 'core/http/http_polling_socket';\nimport xhrHooks from './http_xhr_request';\nimport HTTPFactory from 'core/http/http_factory';\n\nvar HTTP: HTTPFactory = {\n  createStreamingSocket(url: string): HTTPSocket {\n    return this.createSocket(streamingHooks, url);\n  },\n\n  createPollingSocket(url: string): HTTPSocket {\n    return this.createSocket(pollingHooks, url);\n  },\n\n  createSocket(hooks: SocketHooks, url: string): HTTPSocket {\n    return new HTTPSocket(hooks, url);\n  },\n\n  createXHR(method: string, url: string): HTTPRequest {\n    return this.createRequest(xhrHooks, method, url);\n  },\n\n  createRequest(hooks: RequestHooks, method: string, url: string): HTTPRequest {\n    return new HTTPRequest(hooks, method, url);\n  },\n};\n\nexport default HTTP;\n", "import * as Collections from 'core/utils/collections';\nimport Transports from 'isomorphic/transports/transports';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport Ajax from 'core/http/ajax';\nimport getDefaultStrategy from './default_strategy';\nimport TransportsTable from 'core/transports/transports_table';\nimport transportConnectionInitializer from './transports/transport_connection_initializer';\nimport HTTPFactory from './http/http';\n\nvar Isomorphic: any = {\n  getDefaultStrategy,\n  Transports: <TransportsTable>Transports,\n  transportConnectionInitializer,\n  HTTPFactory,\n\n  setup(PusherClass): void {\n    PusherClass.ready();\n  },\n\n  getLocalStorage(): any {\n    return undefined;\n  },\n\n  getClientFeatures(): any[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Transports.ws }, function (t) {\n        return t.isSupported({});\n      }),\n    );\n  },\n\n  getProtocol(): string {\n    return 'http:';\n  },\n\n  isXHRSupported(): boolean {\n    return true;\n  },\n\n  createSocketRequest(method: string, url: string) {\n    if (this.isXHRSupported()) {\n      return this.HTTPFactory.createXHR(method, url);\n    } else {\n      throw 'Cross-origin HTTP requests are not supported';\n    }\n  },\n\n  createXHR(): Ajax {\n    var Constructor = this.getXHRAPI();\n    return new Constructor();\n  },\n\n  createWebSocket(url: string): any {\n    var Constructor = this.getWebSocketAPI();\n    return new Constructor(url);\n  },\n\n  addUnloadListener(listener: any) {},\n  removeUnloadListener(listener: any) {},\n};\n\nexport default Isomorphic;\n", "import { default as EventsDispatcher } from 'core/events/dispatcher';\nimport Reachability from 'core/reachability';\n\nexport class NetInfo extends EventsDispatcher implements Reachability {\n  isOnline(): boolean {\n    return true;\n  }\n}\n\nexport var Network = new NetInfo();\n", "import AbstractRuntime from 'runtimes/interface';\nimport { AuthTransport } from 'core/auth/auth_transports';\nimport {\n  AuthRequestType,\n  AuthTransportCallback,\n  InternalAuthOptions,\n} from 'core/auth/options';\nimport { HTTPAuthError } from 'core/errors';\n\nvar fetchAuth: AuthTransport = function (\n  context: AbstractRuntime,\n  query: string,\n  authOptions: InternalAuthOptions,\n  authRequestType: AuthRequestType,\n  callback: AuthTransportCallback,\n) {\n  var headers = new Headers();\n  headers.set('Content-Type', 'application/x-www-form-urlencoded');\n\n  for (var headerName in authOptions.headers) {\n    headers.set(headerName, authOptions.headers[headerName]);\n  }\n\n  if (authOptions.headersProvider != null) {\n    const dynamicHeaders = authOptions.headersProvider();\n    for (var headerName in dynamicHeaders) {\n      headers.set(headerName, dynamicHeaders[headerName]);\n    }\n  }\n\n  var body = query;\n  var request = new Request(authOptions.endpoint, {\n    headers,\n    body,\n    credentials: 'same-origin',\n    method: 'POST',\n  });\n\n  return fetch(request)\n    .then((response) => {\n      let { status } = response;\n      if (status === 200) {\n        // manually parse the json so we can provide a more helpful error in\n        // failure case\n        return response.text();\n      }\n      throw new HTTPAuthError(\n        status,\n        `Could not get ${authRequestType.toString()} info from your auth endpoint, status: ${status}`,\n      );\n    })\n    .then((data) => {\n      let parsedData;\n      try {\n        parsedData = JSON.parse(data);\n      } catch (e) {\n        throw new HTTPAuthError(\n          200,\n          `JSON returned from ${authRequestType.toString()} endpoint was invalid, yet status code was 200. Data was: ${data}`,\n        );\n      }\n      callback(null, parsedData);\n    })\n    .catch((err) => {\n      callback(err, null);\n    });\n};\n\nexport default fetchAuth;\n", "import Logger from 'core/logger';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport * as Collections from 'core/utils/collections';\nimport Util from 'core/util';\nimport Runtime from 'runtime';\nimport TimelineTransport from 'core/timeline/timeline_transport';\n\nvar getAgent = function (sender: TimelineSender, useTLS: boolean) {\n  return function (data: any, callback: Function) {\n    var scheme = 'http' + (useTLS ? 's' : '') + '://';\n    var url =\n      scheme + (sender.host || sender.options.host) + sender.options.path;\n    var query = Collections.buildQueryString(data);\n    url += '/' + 2 + '?' + query;\n\n    fetch(url)\n      .then((response) => {\n        if (response.status !== 200) {\n          throw `received ${response.status} from stats.pusher.com`;\n        }\n        return response.json();\n      })\n      .then(({ host }) => {\n        if (host) {\n          sender.host = host;\n        }\n      })\n      .catch((err) => {\n        Logger.debug('TimelineSender Error: ', err);\n      });\n  };\n};\n\nvar fetchTimeline = {\n  name: 'xhr',\n  getAgent,\n};\n\nexport default fetchTimeline;\n", "import Isomorphic from 'isomorphic/runtime';\nimport Runtime from '../interface';\nimport { Network } from './net_info';\nimport fetchAuth from './auth/fetch_auth';\nimport { AuthTransports } from 'core/auth/auth_transports';\nimport fetchTimeline from './timeline/fetch_timeline';\n\n// Very verbose but until unavoidable until\n// TypeScript 2.1, when spread attributes will be\n// supported\nconst {\n  getDefaultStrategy,\n  Transports,\n  setup,\n  getProtocol,\n  isXHRSupported,\n  getLocalStorage,\n  createXHR,\n  createWebSocket,\n  addUnloadListener,\n  removeUnloadListener,\n  transportConnectionInitializer,\n  createSocketRequest,\n  HTTPFactory,\n} = Isomorphic;\n\nconst Worker: Runtime = {\n  getDefaultStrategy,\n  Transports,\n  setup,\n  getProtocol,\n  isXHRSupported,\n  getLocalStorage,\n  createXHR,\n  createWebSocket,\n  addUnloadListener,\n  removeUnloadListener,\n  transportConnectionInitializer,\n  createSocketRequest,\n  HTTPFactory,\n\n  TimelineTransport: fetchTimeline,\n\n  getAuthorizers(): AuthTransports {\n    return { ajax: fetchAuth };\n  },\n\n  getWebSocketAPI() {\n    return WebSocket;\n  },\n\n  getXHRAPI() {\n    return XMLHttpRequest;\n  },\n\n  getNetwork() {\n    return Network;\n  },\n\n  randomInt(max: number): number {\n    /**\n     * Return values in the range of [0, 1[\n     */\n    const random = function () {\n      const crypto = globalThis.crypto || globalThis['msCrypto'];\n      const random = crypto.getRandomValues(new Uint32Array(1))[0];\n\n      return random / 2 ** 32;\n    };\n\n    return Math.floor(random() * max);\n  },\n};\n\nexport default Worker;\n", "enum TimelineLevel {\n  ERROR = 3,\n  INFO = 6,\n  DEBUG = 7,\n}\n\nexport default TimelineLevel;\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { default as Level } from './level';\n\nexport interface TimelineOptions {\n  level?: Level;\n  limit?: number;\n  version?: string;\n  cluster?: string;\n  features?: string[];\n  params?: any;\n}\n\nexport default class Timeline {\n  key: string;\n  session: number;\n  events: any[];\n  options: TimelineOptions;\n  sent: number;\n  uniqueID: number;\n\n  constructor(key: string, session: number, options: TimelineOptions) {\n    this.key = key;\n    this.session = session;\n    this.events = [];\n    this.options = options || {};\n    this.sent = 0;\n    this.uniqueID = 0;\n  }\n\n  log(level, event) {\n    if (level <= this.options.level) {\n      this.events.push(\n        Collections.extend({}, event, { timestamp: Util.now() }),\n      );\n      if (this.options.limit && this.events.length > this.options.limit) {\n        this.events.shift();\n      }\n    }\n  }\n\n  error(event) {\n    this.log(Level.ERROR, event);\n  }\n\n  info(event) {\n    this.log(Level.INFO, event);\n  }\n\n  debug(event) {\n    this.log(Level.DEBUG, event);\n  }\n\n  isEmpty() {\n    return this.events.length === 0;\n  }\n\n  send(sendfn, callback) {\n    var data = Collections.extend(\n      {\n        session: this.session,\n        bundle: this.sent + 1,\n        key: this.key,\n        lib: 'js',\n        version: this.options.version,\n        cluster: this.options.cluster,\n        features: this.options.features,\n        timeline: this.events,\n      },\n      this.options.params,\n    );\n\n    this.events = [];\n    sendfn(data, (error, result) => {\n      if (!error) {\n        this.sent++;\n      }\n      if (callback) {\n        callback(error, result);\n      }\n    });\n\n    return true;\n  }\n\n  generateUniqueID(): number {\n    this.uniqueID++;\n    return this.uniqueID;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Util from '../util';\nimport * as Errors from '../errors';\nimport * as Collections from '../utils/collections';\nimport Strategy from './strategy';\nimport Transport from '../transports/transport';\nimport StrategyOptions from './strategy_options';\nimport Handshake from '../connection/handshake';\n\n/** Provides a strategy interface for transports.\n *\n * @param {String} name\n * @param {Number} priority\n * @param {Class} transport\n * @param {Object} options\n */\nexport default class TransportStrategy implements Strategy {\n  name: string;\n  priority: number;\n  transport: Transport;\n  options: StrategyOptions;\n\n  constructor(\n    name: string,\n    priority: number,\n    transport: Transport,\n    options: StrategyOptions,\n  ) {\n    this.name = name;\n    this.priority = priority;\n    this.transport = transport;\n    this.options = options || {};\n  }\n\n  /** Returns whether the transport is supported in the browser.\n   *\n   * @returns {Boolean}\n   */\n  isSupported(): boolean {\n    return this.transport.isSupported({\n      useTLS: this.options.useTLS,\n    });\n  }\n\n  /** Launches a connection attempt and returns a strategy runner.\n   *\n   * @param  {Function} callback\n   * @return {Object} strategy runner\n   */\n  connect(minPriority: number, callback: Function) {\n    if (!this.isSupported()) {\n      return failAttempt(new Errors.UnsupportedStrategy(), callback);\n    } else if (this.priority < minPriority) {\n      return failAttempt(new Errors.TransportPriorityTooLow(), callback);\n    }\n\n    var connected = false;\n    var transport = this.transport.createConnection(\n      this.name,\n      this.priority,\n      this.options.key,\n      this.options,\n    );\n    var handshake = null;\n\n    var onInitialized = function () {\n      transport.unbind('initialized', onInitialized);\n      transport.connect();\n    };\n    var onOpen = function () {\n      handshake = Factory.createHandshake(transport, function (result) {\n        connected = true;\n        unbindListeners();\n        callback(null, result);\n      });\n    };\n    var onError = function (error) {\n      unbindListeners();\n      callback(error);\n    };\n    var onClosed = function () {\n      unbindListeners();\n      var serializedTransport;\n\n      // The reason for this try/catch block is that on React Native\n      // the WebSocket object is circular. Therefore transport.socket will\n      // throw errors upon stringification. Collections.safeJSONStringify\n      // discards circular references when serializing.\n      serializedTransport = Collections.safeJSONStringify(transport);\n      callback(new Errors.TransportClosed(serializedTransport));\n    };\n\n    var unbindListeners = function () {\n      transport.unbind('initialized', onInitialized);\n      transport.unbind('open', onOpen);\n      transport.unbind('error', onError);\n      transport.unbind('closed', onClosed);\n    };\n\n    transport.bind('initialized', onInitialized);\n    transport.bind('open', onOpen);\n    transport.bind('error', onError);\n    transport.bind('closed', onClosed);\n\n    // connect will be called automatically after initialization\n    transport.initialize();\n\n    return {\n      abort: () => {\n        if (connected) {\n          return;\n        }\n        unbindListeners();\n        if (handshake) {\n          handshake.close();\n        } else {\n          transport.close();\n        }\n      },\n      forceMinPriority: (p) => {\n        if (connected) {\n          return;\n        }\n        if (this.priority < p) {\n          if (handshake) {\n            handshake.close();\n          } else {\n            transport.close();\n          }\n        }\n      },\n    };\n  }\n}\n\nfunction failAttempt(error: Error, callback: Function) {\n  Util.defer(function () {\n    callback(error);\n  });\n  return {\n    abort: function () {},\n    forceMinPriority: function () {},\n  };\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport TransportManager from '../transports/transport_manager';\nimport * as Errors from '../errors';\nimport Strategy from './strategy';\nimport TransportStrategy from './transport_strategy';\nimport StrategyOptions from '../strategies/strategy_options';\nimport { Config } from '../config';\nimport Runtime from 'runtime';\n\nconst { Transports } = Runtime;\n\nexport var defineTransport = function (\n  config: Config,\n  name: string,\n  type: string,\n  priority: number,\n  options: StrategyOptions,\n  manager?: TransportManager,\n): Strategy {\n  var transportClass = Transports[type];\n  if (!transportClass) {\n    throw new Errors.UnsupportedTransport(type);\n  }\n\n  var enabled =\n    (!config.enabledTransports ||\n      Collections.arrayIndexOf(config.enabledTransports, name) !== -1) &&\n    (!config.disabledTransports ||\n      Collections.arrayIndexOf(config.disabledTransports, name) === -1);\n\n  var transport;\n  if (enabled) {\n    options = Object.assign(\n      { ignoreNullOrigin: config.ignoreNullOrigin },\n      options,\n    );\n\n    transport = new TransportStrategy(\n      name,\n      priority,\n      manager ? manager.getAssistant(transportClass) : transportClass,\n      options,\n    );\n  } else {\n    transport = UnsupportedStrategy;\n  }\n\n  return transport;\n};\n\nvar UnsupportedStrategy: Strategy = {\n  isSupported: function () {\n    return false;\n  },\n  connect: function (_, callback) {\n    var deferred = Util.defer(function () {\n      callback(new Errors.UnsupportedStrategy());\n    });\n    return {\n      abort: function () {\n        deferred.ensureAborted();\n      },\n      forceMinPriority: function () {},\n    };\n  },\n};\n", "import ConnectionManager from './connection/connection_manager';\nimport {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions,\n} from './auth/options';\nimport {\n  ChannelAuthorizerGenerator,\n  DeprecatedAuthOptions,\n} from './auth/deprecated_channel_authorizer';\nimport { AuthTransport, Transport } from './config';\nimport * as nacl from 'tweetnacl';\nimport Logger from './logger';\n\nexport interface Options {\n  activityTimeout?: number;\n\n  auth?: DeprecatedAuthOptions; // DEPRECATED use channelAuthorization instead\n  authEndpoint?: string; // DEPRECATED use channelAuthorization instead\n  authTransport?: AuthTransport; // DEPRECATED use channelAuthorization instead\n  authorizer?: ChannelAuthorizerGenerator; // DEPRECATED use channelAuthorization instead\n\n  channelAuthorization?: ChannelAuthorizationOptions;\n  userAuthentication?: UserAuthenticationOptions;\n\n  cluster: string;\n  enableStats?: boolean;\n  disableStats?: boolean;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  forceTLS?: boolean;\n  httpHost?: string;\n  httpPath?: string;\n  httpPort?: number;\n  httpsPort?: number;\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  pongTimeout?: number;\n  statsHost?: string;\n  timelineParams?: any;\n  unavailableTimeout?: number;\n  wsHost?: string;\n  wsPath?: string;\n  wsPort?: number;\n  wssPort?: number;\n}\n\nexport function validateOptions(options) {\n  if (options == null) {\n    throw 'You must pass an options object';\n  }\n  if (options.cluster == null) {\n    throw 'Options object must provide a cluster';\n  }\n  if ('disableStats' in options) {\n    Logger.warn(\n      'The disableStats option is deprecated in favor of enableStats',\n    );\n  }\n}\n", "export enum AuthRequestType {\n  UserAuthentication = 'user-authentication',\n  ChannelAuthorization = 'channel-authorization',\n}\n\nexport interface ChannelAuthorizationData {\n  auth: string;\n  channel_data?: string;\n  shared_secret?: string;\n}\n\nexport type ChannelAuthorizationCallback = (\n  error: Error | null,\n  authData: ChannelAuthorizationData | null,\n) => void;\n\nexport interface ChannelAuthorizationRequestParams {\n  socketId: string;\n  channelName: string;\n}\n\nexport interface ChannelAuthorizationHandler {\n  (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ): void;\n}\n\nexport interface UserAuthenticationData {\n  auth: string;\n  user_data: string;\n}\n\nexport type UserAuthenticationCallback = (\n  error: Error | null,\n  authData: UserAuthenticationData | null,\n) => void;\n\nexport interface UserAuthenticationRequestParams {\n  socketId: string;\n}\n\nexport interface UserAuthenticationHandler {\n  (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback,\n  ): void;\n}\n\nexport type AuthTransportCallback =\n  | ChannelAuthorizationCallback\n  | UserAuthenticationCallback;\n\nexport interface AuthOptionsT<AuthHandler> {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n  customHandler?: AuthHandler;\n}\n\nexport declare type UserAuthenticationOptions =\n  AuthOptionsT<UserAuthenticationHandler>;\nexport declare type ChannelAuthorizationOptions =\n  AuthOptionsT<ChannelAuthorizationHandler>;\n\nexport interface InternalAuthOptions {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n}\n", "import {\n  UserAuthenticationCallback,\n  InternalAuthOptions,\n  UserAuthenticationHandler,\n  UserAuthenticationRequestParams,\n  AuthRequestType,\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: UserAuthenticationRequestParams,\n  authOptions: InternalAuthOptions,\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst UserAuthenticator = (\n  authOptions: InternalAuthOptions,\n): UserAuthenticationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback,\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.UserAuthentication,\n      callback,\n    );\n  };\n};\n\nexport default UserAuthenticator;\n", "import {\n  AuthRequestType,\n  InternalAuthOptions,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  ChannelAuthorizationCallback,\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: ChannelAuthorizationRequestParams,\n  authOptions: InternalAuthOptions,\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  query += '&channel_name=' + encodeURIComponent(params.channelName);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst ChannelAuthorizer = (\n  authOptions: InternalAuthOptions,\n): ChannelAuthorizationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.ChannelAuthorization,\n      callback,\n    );\n  };\n};\n\nexport default ChannelAuthorizer;\n", "import Channel from '../channels/channel';\nimport {\n  ChannelAuthorizationCallback,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  InternalAuthOptions,\n} from './options';\n\nexport interface DeprecatedChannelAuthorizer {\n  authorize(socketId: string, callback: ChannelAuthorizationCallback): void;\n}\n\nexport interface ChannelAuthorizerGenerator {\n  (\n    channel: Channel,\n    options: DeprecatedAuthorizerOptions,\n  ): DeprecatedChannelAuthorizer;\n}\n\nexport interface DeprecatedAuthOptions {\n  params?: any;\n  headers?: any;\n}\n\nexport interface DeprecatedAuthorizerOptions {\n  authTransport: 'ajax' | 'jsonp';\n  authEndpoint: string;\n  auth?: DeprecatedAuthOptions;\n}\n\nexport const ChannelAuthorizerProxy = (\n  pusher,\n  authOptions: InternalAuthOptions,\n  channelAuthorizerGenerator: ChannelAuthorizerGenerator,\n): ChannelAuthorizationHandler => {\n  const deprecatedAuthorizerOptions: DeprecatedAuthorizerOptions = {\n    authTransport: authOptions.transport,\n    authEndpoint: authOptions.endpoint,\n    auth: {\n      params: authOptions.params,\n      headers: authOptions.headers,\n    },\n  };\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ) => {\n    const channel = pusher.channel(params.channelName);\n    // This line creates a new channel authorizer every time.\n    // In the past, this was only done once per channel and reused.\n    // We can do that again if we want to keep this behavior intact.\n    const channelAuthorizer: DeprecatedChannelAuthorizer =\n      channelAuthorizerGenerator(channel, deprecatedAuthorizerOptions);\n    channelAuthorizer.authorize(params.socketId, callback);\n  };\n};\n", "import { Options } from './options';\nimport Defaults from './defaults';\nimport {\n  Channel<PERSON>uth<PERSON>zation<PERSON><PERSON><PERSON>,\n  UserAuthenticationHandler,\n  ChannelAuthorizationOptions,\n} from './auth/options';\nimport UserAuthenticator from './auth/user_authenticator';\nimport ChannelAuthorizer from './auth/channel_authorizer';\nimport { ChannelAuthorizerProxy } from './auth/deprecated_channel_authorizer';\nimport Runtime from 'runtime';\nimport * as nacl from 'tweetnacl';\n\nexport type AuthTransport = 'ajax' | 'jsonp';\nexport type Transport =\n  | 'ws'\n  | 'wss'\n  | 'xhr_streaming'\n  | 'xhr_polling'\n  | 'sockjs';\n\nexport interface Config {\n  // these are all 'required' config parameters, it's not necessary for the user\n  // to set them, but they have configured defaults.\n  activityTimeout: number;\n  enableStats: boolean;\n  httpHost: string;\n  httpPath: string;\n  httpPort: number;\n  httpsPort: number;\n  pongTimeout: number;\n  statsHost: string;\n  unavailableTimeout: number;\n  useTLS: boolean;\n  wsHost: string;\n  wsPath: string;\n  wsPort: number;\n  wssPort: number;\n  userAuthenticator: UserAuthenticationHandler;\n  channelAuthorizer: ChannelAuthorizationHandler;\n\n  // these are all optional parameters or overrrides. The customer can set these\n  // but it's not strictly necessary\n  forceTLS?: boolean;\n  cluster?: string;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  timelineParams?: any;\n}\n\n// getConfig mainly sets the defaults for the options that are not provided\nexport function getConfig(opts: Options, pusher): Config {\n  let config: Config = {\n    activityTimeout: opts.activityTimeout || Defaults.activityTimeout,\n    cluster: opts.cluster,\n    httpPath: opts.httpPath || Defaults.httpPath,\n    httpPort: opts.httpPort || Defaults.httpPort,\n    httpsPort: opts.httpsPort || Defaults.httpsPort,\n    pongTimeout: opts.pongTimeout || Defaults.pongTimeout,\n    statsHost: opts.statsHost || Defaults.stats_host,\n    unavailableTimeout: opts.unavailableTimeout || Defaults.unavailableTimeout,\n    wsPath: opts.wsPath || Defaults.wsPath,\n    wsPort: opts.wsPort || Defaults.wsPort,\n    wssPort: opts.wssPort || Defaults.wssPort,\n\n    enableStats: getEnableStatsConfig(opts),\n    httpHost: getHttpHost(opts),\n    useTLS: shouldUseTLS(opts),\n    wsHost: getWebsocketHost(opts),\n\n    userAuthenticator: buildUserAuthenticator(opts),\n    channelAuthorizer: buildChannelAuthorizer(opts, pusher),\n  };\n\n  if ('disabledTransports' in opts)\n    config.disabledTransports = opts.disabledTransports;\n  if ('enabledTransports' in opts)\n    config.enabledTransports = opts.enabledTransports;\n  if ('ignoreNullOrigin' in opts)\n    config.ignoreNullOrigin = opts.ignoreNullOrigin;\n  if ('timelineParams' in opts) config.timelineParams = opts.timelineParams;\n  if ('nacl' in opts) {\n    config.nacl = opts.nacl;\n  }\n\n  return config;\n}\n\nfunction getHttpHost(opts: Options): string {\n  if (opts.httpHost) {\n    return opts.httpHost;\n  }\n  if (opts.cluster) {\n    return `sockjs-${opts.cluster}.pusher.com`;\n  }\n  return Defaults.httpHost;\n}\n\nfunction getWebsocketHost(opts: Options): string {\n  if (opts.wsHost) {\n    return opts.wsHost;\n  }\n  return getWebsocketHostFromCluster(opts.cluster);\n}\n\nfunction getWebsocketHostFromCluster(cluster: string): string {\n  return `ws-${cluster}.pusher.com`;\n}\n\nfunction shouldUseTLS(opts: Options): boolean {\n  if (Runtime.getProtocol() === 'https:') {\n    return true;\n  } else if (opts.forceTLS === false) {\n    return false;\n  }\n  return true;\n}\n\n// if enableStats is set take the value\n// if disableStats is set take the inverse\n// otherwise default to false\nfunction getEnableStatsConfig(opts: Options): boolean {\n  if ('enableStats' in opts) {\n    return opts.enableStats;\n  }\n  if ('disableStats' in opts) {\n    return !opts.disableStats;\n  }\n  return false;\n}\n\nfunction buildUserAuthenticator(opts: Options): UserAuthenticationHandler {\n  const userAuthentication = {\n    ...Defaults.userAuthentication,\n    ...opts.userAuthentication,\n  };\n  if (\n    'customHandler' in userAuthentication &&\n    userAuthentication['customHandler'] != null\n  ) {\n    return userAuthentication['customHandler'];\n  }\n\n  return UserAuthenticator(userAuthentication);\n}\n\nfunction buildChannelAuth(opts: Options, pusher): ChannelAuthorizationOptions {\n  let channelAuthorization: ChannelAuthorizationOptions;\n  if ('channelAuthorization' in opts) {\n    channelAuthorization = {\n      ...Defaults.channelAuthorization,\n      ...opts.channelAuthorization,\n    };\n  } else {\n    channelAuthorization = {\n      transport: opts.authTransport || Defaults.authTransport,\n      endpoint: opts.authEndpoint || Defaults.authEndpoint,\n    };\n    if ('auth' in opts) {\n      if ('params' in opts.auth) channelAuthorization.params = opts.auth.params;\n      if ('headers' in opts.auth)\n        channelAuthorization.headers = opts.auth.headers;\n    }\n    if ('authorizer' in opts)\n      channelAuthorization.customHandler = ChannelAuthorizerProxy(\n        pusher,\n        channelAuthorization,\n        opts.authorizer,\n      );\n  }\n  return channelAuthorization;\n}\n\nfunction buildChannelAuthorizer(\n  opts: Options,\n  pusher,\n): ChannelAuthorizationHandler {\n  const channelAuthorization = buildChannelAuth(opts, pusher);\n  if (\n    'customHandler' in channelAuthorization &&\n    channelAuthorization['customHandler'] != null\n  ) {\n    return channelAuthorization['customHandler'];\n  }\n\n  return ChannelAuthorizer(channelAuthorization);\n}\n", "import Logger from './logger';\nimport Pusher from './pusher';\nimport EventsDispatcher from './events/dispatcher';\n\nexport default class WatchlistFacade extends EventsDispatcher {\n  private pusher: Pusher;\n\n  public constructor(pusher: Pusher) {\n    super(function (eventName, data) {\n      Logger.debug(`No callbacks on watchlist events for ${eventName}`);\n    });\n\n    this.pusher = pusher;\n    this.bindWatchlistInternalEvent();\n  }\n\n  handleEvent(pusherEvent) {\n    pusherEvent.data.events.forEach((watchlistEvent) => {\n      this.emit(watchlistEvent.name, watchlistEvent);\n    });\n  }\n\n  private bindWatchlistInternalEvent() {\n    this.pusher.connection.bind('message', (pusherEvent) => {\n      var eventName = pusherEvent.event;\n      if (eventName === 'pusher_internal:watchlist_events') {\n        this.handleEvent(pusherEvent);\n      }\n    });\n  }\n}\n", "function flatPromise() {\n  let resolve, reject;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n}\n\nexport default flatPromise;\n", "import Pusher from './pusher';\nimport Logger from './logger';\nimport {\n  UserAuthenticationData,\n  UserAuthenticationCallback,\n} from './auth/options';\nimport Channel from './channels/channel';\nimport WatchlistFacade from './watchlist';\nimport EventsDispatcher from './events/dispatcher';\nimport flatPromise from './utils/flat_promise';\n\nexport default class UserFacade extends EventsDispatcher {\n  pusher: Pusher;\n  signin_requested: boolean = false;\n  user_data: any = null;\n  serverToUserChannel: Channel = null;\n  signinDonePromise: Promise<any> = null;\n  watchlist: WatchlistFacade;\n  private _signinDoneResolve: Function = null;\n\n  public constructor(pusher: Pusher) {\n    super(function (eventName, data) {\n      Logger.debug('No callbacks on user for ' + eventName);\n    });\n    this.pusher = pusher;\n    this.pusher.connection.bind('state_change', ({ previous, current }) => {\n      if (previous !== 'connected' && current === 'connected') {\n        this._signin();\n      }\n      if (previous === 'connected' && current !== 'connected') {\n        this._cleanup();\n        this._newSigninPromiseIfNeeded();\n      }\n    });\n\n    this.watchlist = new WatchlistFacade(pusher);\n\n    this.pusher.connection.bind('message', (event) => {\n      var eventName = event.event;\n      if (eventName === 'pusher:signin_success') {\n        this._onSigninSuccess(event.data);\n      }\n      if (\n        this.serverToUserChannel &&\n        this.serverToUserChannel.name === event.channel\n      ) {\n        this.serverToUserChannel.handleEvent(event);\n      }\n    });\n  }\n\n  public signin() {\n    if (this.signin_requested) {\n      return;\n    }\n\n    this.signin_requested = true;\n    this._signin();\n  }\n\n  private _signin() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    this._newSigninPromiseIfNeeded();\n\n    if (this.pusher.connection.state !== 'connected') {\n      // Signin will be attempted when the connection is connected\n      return;\n    }\n\n    this.pusher.config.userAuthenticator(\n      {\n        socketId: this.pusher.connection.socket_id,\n      },\n      this._onAuthorize,\n    );\n  }\n\n  private _onAuthorize: UserAuthenticationCallback = (\n    err,\n    authData: UserAuthenticationData,\n  ) => {\n    if (err) {\n      Logger.warn(`Error during signin: ${err}`);\n      this._cleanup();\n      return;\n    }\n\n    this.pusher.send_event('pusher:signin', {\n      auth: authData.auth,\n      user_data: authData.user_data,\n    });\n\n    // Later when we get pusher:singin_success event, the user will be marked as signed in\n  };\n\n  private _onSigninSuccess(data: any) {\n    try {\n      this.user_data = JSON.parse(data.user_data);\n    } catch (e) {\n      Logger.error(`Failed parsing user data after signin: ${data.user_data}`);\n      this._cleanup();\n      return;\n    }\n\n    if (typeof this.user_data.id !== 'string' || this.user_data.id === '') {\n      Logger.error(\n        `user_data doesn't contain an id. user_data: ${this.user_data}`,\n      );\n      this._cleanup();\n      return;\n    }\n\n    // Signin succeeded\n    this._signinDoneResolve();\n    this._subscribeChannels();\n  }\n\n  private _subscribeChannels() {\n    const ensure_subscribed = (channel) => {\n      if (channel.subscriptionPending && channel.subscriptionCancelled) {\n        channel.reinstateSubscription();\n      } else if (\n        !channel.subscriptionPending &&\n        this.pusher.connection.state === 'connected'\n      ) {\n        channel.subscribe();\n      }\n    };\n\n    this.serverToUserChannel = new Channel(\n      `#server-to-user-${this.user_data.id}`,\n      this.pusher,\n    );\n    this.serverToUserChannel.bind_global((eventName, data) => {\n      if (\n        eventName.indexOf('pusher_internal:') === 0 ||\n        eventName.indexOf('pusher:') === 0\n      ) {\n        // ignore internal events\n        return;\n      }\n      this.emit(eventName, data);\n    });\n    ensure_subscribed(this.serverToUserChannel);\n  }\n\n  private _cleanup() {\n    this.user_data = null;\n    if (this.serverToUserChannel) {\n      this.serverToUserChannel.unbind_all();\n      this.serverToUserChannel.disconnect();\n      this.serverToUserChannel = null;\n    }\n\n    if (this.signin_requested) {\n      // If signin is in progress and cleanup is called,\n      // Mark the current signin process as done.\n      this._signinDoneResolve();\n    }\n  }\n\n  private _newSigninPromiseIfNeeded() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    // If there is a promise and it is not resolved, return without creating a new one.\n    if (this.signinDonePromise && !(this.signinDonePromise as any).done) {\n      return;\n    }\n\n    // This promise is never rejected.\n    // It gets resolved when the signin process is done whether it failed or succeeded\n    const { promise, resolve, reject: _ } = flatPromise();\n    (promise as any).done = false;\n    const setDone = () => {\n      (promise as any).done = true;\n    };\n    promise.then(setDone).catch(setDone);\n    this.signinDonePromise = promise;\n    this._signinDoneResolve = resolve;\n  }\n}\n", "import AbstractRuntime from '../runtimes/interface';\nimport Runtime from 'runtime';\nimport Util from './util';\nimport * as Collections from './utils/collections';\nimport Channels from './channels/channels';\nimport Channel from './channels/channel';\nimport { default as EventsDispatcher } from './events/dispatcher';\nimport Timeline from './timeline/timeline';\nimport TimelineSender from './timeline/timeline_sender';\nimport TimelineLevel from './timeline/level';\nimport { defineTransport } from './strategies/strategy_builder';\nimport ConnectionManager from './connection/connection_manager';\nimport ConnectionManagerOptions from './connection/connection_manager_options';\nimport { PeriodicTimer } from './utils/timers';\nimport Defaults from './defaults';\nimport * as DefaultConfig from './config';\nimport Logger from './logger';\nimport Factory from './utils/factory';\nimport UrlStore from 'core/utils/url_store';\nimport { Options, validateOptions } from './options';\nimport { Config, getConfig } from './config';\nimport StrategyOptions from './strategies/strategy_options';\nimport UserFacade from './user';\n\nexport default class Pusher {\n  /*  STATIC PROPERTIES */\n  static instances: Pusher[] = [];\n  static isReady: boolean = false;\n  static logToConsole: boolean = false;\n\n  // for jsonp\n  static Runtime: AbstractRuntime = Runtime;\n  static ScriptReceivers: any = (<any>Runtime).ScriptReceivers;\n  static DependenciesReceivers: any = (<any>Runtime).DependenciesReceivers;\n  static auth_callbacks: any = (<any>Runtime).auth_callbacks;\n\n  static ready() {\n    Pusher.isReady = true;\n    for (var i = 0, l = Pusher.instances.length; i < l; i++) {\n      Pusher.instances[i].connect();\n    }\n  }\n\n  static log: (message: any) => void;\n\n  private static getClientFeatures(): string[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Runtime.Transports.ws }, function (t) {\n        return t.isSupported({});\n      }),\n    );\n  }\n\n  /* INSTANCE PROPERTIES */\n  key: string;\n  config: Config;\n  channels: Channels;\n  global_emitter: EventsDispatcher;\n  sessionID: number;\n  timeline: Timeline;\n  timelineSender: TimelineSender;\n  connection: ConnectionManager;\n  timelineSenderTimer: PeriodicTimer;\n  user: UserFacade;\n  constructor(app_key: string, options: Options) {\n    checkAppKey(app_key);\n    validateOptions(options);\n    this.key = app_key;\n    this.config = getConfig(options, this);\n\n    this.channels = Factory.createChannels();\n    this.global_emitter = new EventsDispatcher();\n    this.sessionID = Runtime.randomInt(1000000000);\n\n    this.timeline = new Timeline(this.key, this.sessionID, {\n      cluster: this.config.cluster,\n      features: Pusher.getClientFeatures(),\n      params: this.config.timelineParams || {},\n      limit: 50,\n      level: TimelineLevel.INFO,\n      version: Defaults.VERSION,\n    });\n    if (this.config.enableStats) {\n      this.timelineSender = Factory.createTimelineSender(this.timeline, {\n        host: this.config.statsHost,\n        path: '/timeline/v2/' + Runtime.TimelineTransport.name,\n      });\n    }\n\n    var getStrategy = (options: StrategyOptions) => {\n      return Runtime.getDefaultStrategy(this.config, options, defineTransport);\n    };\n\n    this.connection = Factory.createConnectionManager(this.key, {\n      getStrategy: getStrategy,\n      timeline: this.timeline,\n      activityTimeout: this.config.activityTimeout,\n      pongTimeout: this.config.pongTimeout,\n      unavailableTimeout: this.config.unavailableTimeout,\n      useTLS: Boolean(this.config.useTLS),\n    });\n\n    this.connection.bind('connected', () => {\n      this.subscribeAll();\n      if (this.timelineSender) {\n        this.timelineSender.send(this.connection.isUsingTLS());\n      }\n    });\n\n    this.connection.bind('message', (event) => {\n      var eventName = event.event;\n      var internal = eventName.indexOf('pusher_internal:') === 0;\n      if (event.channel) {\n        var channel = this.channel(event.channel);\n        if (channel) {\n          channel.handleEvent(event);\n        }\n      }\n      // Emit globally [deprecated]\n      if (!internal) {\n        this.global_emitter.emit(event.event, event.data);\n      }\n    });\n    this.connection.bind('connecting', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('disconnected', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('error', (err) => {\n      Logger.warn(err);\n    });\n\n    Pusher.instances.push(this);\n    this.timeline.info({ instances: Pusher.instances.length });\n\n    this.user = new UserFacade(this);\n\n    if (Pusher.isReady) {\n      this.connect();\n    }\n  }\n\n  channel(name: string): Channel {\n    return this.channels.find(name);\n  }\n\n  allChannels(): Channel[] {\n    return this.channels.all();\n  }\n\n  connect() {\n    this.connection.connect();\n\n    if (this.timelineSender) {\n      if (!this.timelineSenderTimer) {\n        var usingTLS = this.connection.isUsingTLS();\n        var timelineSender = this.timelineSender;\n        this.timelineSenderTimer = new PeriodicTimer(60000, function () {\n          timelineSender.send(usingTLS);\n        });\n      }\n    }\n  }\n\n  disconnect() {\n    this.connection.disconnect();\n\n    if (this.timelineSenderTimer) {\n      this.timelineSenderTimer.ensureAborted();\n      this.timelineSenderTimer = null;\n    }\n  }\n\n  bind(event_name: string, callback: Function, context?: any): Pusher {\n    this.global_emitter.bind(event_name, callback, context);\n    return this;\n  }\n\n  unbind(event_name?: string, callback?: Function, context?: any): Pusher {\n    this.global_emitter.unbind(event_name, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function): Pusher {\n    this.global_emitter.bind_global(callback);\n    return this;\n  }\n\n  unbind_global(callback?: Function): Pusher {\n    this.global_emitter.unbind_global(callback);\n    return this;\n  }\n\n  unbind_all(callback?: Function): Pusher {\n    this.global_emitter.unbind_all();\n    return this;\n  }\n\n  subscribeAll() {\n    var channelName;\n    for (channelName in this.channels.channels) {\n      if (this.channels.channels.hasOwnProperty(channelName)) {\n        this.subscribe(channelName);\n      }\n    }\n  }\n\n  subscribe(channel_name: string) {\n    var channel = this.channels.add(channel_name, this);\n    if (channel.subscriptionPending && channel.subscriptionCancelled) {\n      channel.reinstateSubscription();\n    } else if (\n      !channel.subscriptionPending &&\n      this.connection.state === 'connected'\n    ) {\n      channel.subscribe();\n    }\n    return channel;\n  }\n\n  unsubscribe(channel_name: string) {\n    var channel = this.channels.find(channel_name);\n    if (channel && channel.subscriptionPending) {\n      channel.cancelSubscription();\n    } else {\n      channel = this.channels.remove(channel_name);\n      if (channel && channel.subscribed) {\n        channel.unsubscribe();\n      }\n    }\n  }\n\n  send_event(event_name: string, data: any, channel?: string) {\n    return this.connection.send_event(event_name, data, channel);\n  }\n\n  shouldUseTLS(): boolean {\n    return this.config.useTLS;\n  }\n\n  signin() {\n    this.user.signin();\n  }\n}\n\nfunction checkAppKey(key) {\n  if (key === null || key === undefined) {\n    throw 'You must pass your app key when you instantiate Pusher.';\n  }\n}\n\nRuntime.setup(Pusher);\n", "import Pusher from './pusher';\nimport { Options, validateOptions } from './options';\nimport * as nacl from 'tweetnacl';\n\nexport default class PusherWithEncryption extends Pusher {\n  constructor(app_key: string, options: Options) {\n    Pusher.logToConsole = PusherWithEncryption.logToConsole;\n    Pusher.log = PusherWithEncryption.log;\n\n    validateOptions(options);\n    options.nacl = nacl;\n    super(app_key, options);\n  }\n}\n"], "sourceRoot": ""}