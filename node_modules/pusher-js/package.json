{"name": "pusher-js", "version": "8.4.0", "description": "Pusher Channels JavaScript library for browsers, React Native, NodeJS and web workers", "main": "dist/node/pusher.js", "browser": "dist/web/pusher.js", "react-native": "dist/react-native/pusher.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "format": "prettier --config .prettierrc --write 'src/**/*.ts' 'webpack/**/*.js'", "check-format": "prettier --config .prettierrc --check 'src/**/*.ts' 'webpack/**/*.js'"}, "repository": {"type": "git", "url": "https://github.com/pusher/pusher-js.git"}, "keywords": ["pusher", "client", "websocket", "http", "fallback", "isomorphic", "events", "pubsub"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/pusher/pusher-js"}, "homepage": "https://github.com/pusher/pusher-js", "devDependencies": {"@react-native-community/netinfo": "^5.9.7", "@stablelib/base64": "^1.0.0", "@stablelib/utf8": "^1.0.0", "buffer": "^5.6.0", "faye-websocket": "^0.11.3", "fetch-mock": "git+https://**************/jpatel531/fetch-mock.git", "isomorphic-fetch": "^3.0.0", "jasmine": "^4.5.0", "jasmine-spec-reporter": "^7.0.0", "karma": "^6.4.4", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.3", "karma-jasmine": "^4.0.1", "karma-jasmine-web-worker": "git+https://**************/pusher/karma-jasmine-web-worker.git#jasmine_3", "karma-opera-launcher": "^1.0.0", "karma-safari-launcher": "^1.0.0", "karma-spec-reporter": "0.0.32", "karma-verbose-reporter": "0.0.6", "karma-webpack": "^4.0.2", "prettier": "^3.4.2", "source-map-loader": "^1.1.3", "ts-loader": "^6.0.4", "typescript": "^5.1.3", "uglify-js": "^2.6.2", "webpack": "^4.46.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^4.11.1", "webpack-merge": "^5.8.0", "xmlhttprequest": "^1.8.0", "@types/node": "^20.3.0", "@types/express-serve-static-core": "4.17.28"}, "dependencies": {"tweetnacl": "^1.0.3"}}