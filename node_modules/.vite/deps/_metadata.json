{"hash": "8f6f2108", "configHash": "263d4380", "lockfileHash": "1d61948a", "browserHash": "94683e11", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "81bd9303", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "cfac884e", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "cc25daa2", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "8f34582b", "needsInterop": true}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "6570625d", "needsInterop": false}, "@inertiajs/react": {"src": "../../@inertiajs/react/dist/index.esm.js", "file": "@inertiajs_react.js", "fileHash": "fe398dc9", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "c01098cd", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "a6cca790", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "d24642a6", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "9df61e36", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "5be5ac6b", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "6dcd0c97", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "1fc39ad6", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "8a55011e", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "4c4c62b2", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "7a1b6d59", "needsInterop": false}, "laravel-vite-plugin/inertia-helpers": {"src": "../../laravel-vite-plugin/inertia-helpers/index.js", "file": "laravel-vite-plugin_inertia-helpers.js", "fileHash": "d681c7d0", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b27fb163", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "e77afef9", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "fefc7d94", "needsInterop": false}}, "chunks": {"chunk-JIXRZ3LX": {"file": "chunk-JIXRZ3LX.js"}, "chunk-TU66YK52": {"file": "chunk-TU66YK52.js"}, "chunk-VHXKHRYN": {"file": "chunk-VHXKHRYN.js"}, "chunk-C2BKLZBK": {"file": "chunk-C2BKLZBK.js"}, "chunk-XU4QX5LS": {"file": "chunk-XU4QX5LS.js"}, "chunk-OERCM43O": {"file": "chunk-OERCM43O.js"}, "chunk-6W3ABJTF": {"file": "chunk-6W3ABJTF.js"}, "chunk-XTICEB36": {"file": "chunk-XTICEB36.js"}, "chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-S4X2W4AT": {"file": "chunk-S4X2W4AT.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}