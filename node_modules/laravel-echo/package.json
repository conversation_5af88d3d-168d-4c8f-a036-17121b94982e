{"name": "laravel-echo", "version": "2.1.6", "description": "Laravel Echo library for beautiful Pusher and Socket.IO integration", "keywords": ["laravel", "pusher", "ably"], "homepage": "https://github.com/laravel/echo/tree/2.x/packages/laravel-echo", "repository": {"type": "git", "url": "https://github.com/laravel/echo"}, "license": "MIT", "author": {"name": "<PERSON>"}, "type": "module", "main": "dist/echo.common.js", "module": "dist/echo.js", "types": "dist/echo.d.ts", "devDependencies": {"@babel/core": "^7.26.7", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-proposal-function-sent": "^7.25.9", "@babel/plugin-proposal-throw-expressions": "^7.25.9", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-numeric-separator": "^7.25.9", "@babel/plugin-transform-object-assign": "^7.25.9", "@babel/preset-env": "^7.26.7", "@types/jquery": "^3.5.32", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "axios": "^1.9.0", "eslint": "^9.0.0", "prettier": "^3.5.3", "pusher-js": "^8.0", "socket.io-client": "^4.0", "tslib": "^2.8.1", "typescript": "^5.7.0", "vite": "^5.0.0", "vite-plugin-dts": "^3.0.0", "vitest": "^3.1.2"}, "peerDependencies": {"pusher-js": "*", "socket.io-client": "*"}, "typesVersions": {"*": {"socket.io-client": [], "pusher-js": []}}, "engines": {"node": ">=20"}, "exports": {".": {"types": "./dist/echo.d.ts", "import": "./dist/echo.js", "require": "./dist/echo.common.js"}, "./iife": "./dist/echo.iife.js"}, "overrides": {"glob": "^9.0.0"}, "scripts": {"build": "vite build && FORMAT=iife vite build", "lint": "eslint --config eslint.config.mjs \"src/**/*.ts\"", "prepublish": "pnpm run build", "release": "vitest --run && git push --follow-tags && pnpm publish", "test": "vitest", "format": "prettier --write ."}}