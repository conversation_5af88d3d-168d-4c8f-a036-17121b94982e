{"version": 3, "file": "echo.iife.js", "sources": ["../src/channel/channel.ts", "../src/util/event-formatter.ts", "../src/util/index.ts", "../src/channel/pusher-channel.ts", "../src/channel/pusher-private-channel.ts", "../src/channel/pusher-encrypted-private-channel.ts", "../src/channel/pusher-presence-channel.ts", "../src/channel/socketio-channel.ts", "../src/channel/socketio-private-channel.ts", "../src/channel/socketio-presence-channel.ts", "../src/channel/null-channel.ts", "../src/channel/null-private-channel.ts", "../src/channel/null-encrypted-private-channel.ts", "../src/channel/null-presence-channel.ts", "../src/connector/connector.ts", "../src/connector/pusher-connector.ts", "../src/connector/socketio-connector.ts", "../src/connector/null-connector.ts", "../src/echo.ts"], "sourcesContent": ["import type { EchoOptionsWithDefaults } from \"../connector\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a basic channel.\n */\nexport abstract class Channel {\n    /**\n     * The Echo options.\n     */\n    options: EchoOptionsWithDefaults<BroadcastDriver>;\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    abstract listen(event: string, callback: CallableFunction): this;\n\n    /**\n     * Listen for a whisper event on the channel instance.\n     */\n    listenForWhisper(event: string, callback: CallableFunction): this {\n        return this.listen(\".client-\" + event, callback);\n    }\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    notification(callback: CallableFunction): this {\n        return this.listen(\n            \".Illuminate\\\\Notifications\\\\Events\\\\BroadcastNotificationCreated\",\n            callback,\n        );\n    }\n\n    /**\n     * Stop listening to an event on the channel instance.\n     */\n    abstract stopListening(event: string, callback?: CallableFunction): this;\n\n    /**\n     * Stop listening for a whisper event on the channel instance.\n     */\n    stopListeningForWhisper(event: string, callback?: CallableFunction): this {\n        return this.stopListening(\".client-\" + event, callback);\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription succeeds.\n     */\n    abstract subscribed(callback: CallableFunction): this;\n\n    /**\n     * Register a callback to be called anytime an error occurs.\n     */\n    abstract error(callback: CallableFunction): this;\n}\n", "/**\n * Event name formatter\n */\nexport class EventFormatter {\n    /**\n     * Create a new class instance.\n     */\n    constructor(private namespace: string | boolean | undefined) {\n        //\n    }\n\n    /**\n     * Format the given event name.\n     */\n    format(event: string): string {\n        if ([\".\", \"\\\\\"].includes(event.charAt(0))) {\n            return event.substring(1);\n        } else if (this.namespace) {\n            event = this.namespace + \".\" + event;\n        }\n\n        return event.replace(/\\./g, \"\\\\\");\n    }\n\n    /**\n     * Set the event namespace.\n     */\n    setNamespace(value: string | boolean): void {\n        this.namespace = value;\n    }\n}\n", "function isConstructor(obj: unknown): obj is new (...args: any[]) => any {\n    try {\n        new (obj as new (...args: any[]) => any)();\n    } catch (err) {\n        if (\n            err instanceof Error &&\n            err.message.includes(\"is not a constructor\")\n        ) {\n            return false;\n        }\n    }\n\n    return true;\n}\n\nexport { isConstructor };\nexport * from \"./event-formatter\";\n", "import { EventFormatter } from \"../util\";\nimport { Channel } from \"./channel\";\nimport type Pusher from \"pusher-js\";\nimport type { Channel as BasePusherChannel } from \"pusher-js\";\nimport type { EchoOptionsWithDefaults } from \"../connector\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Pusher channel.\n */\nexport class PusherChannel<\n    TBroadcastDriver extends BroadcastDriver,\n> extends Channel {\n    /**\n     * The Pusher client instance.\n     */\n    pusher: Pusher;\n\n    /**\n     * The name of the channel.\n     */\n    name: string;\n\n    /**\n     * The event formatter.\n     */\n    eventFormatter: EventFormatter;\n\n    /**\n     * The subscription of the channel.\n     */\n    subscription: BasePusherChannel;\n\n    /**\n     * Create a new class instance.\n     */\n    constructor(\n        pusher: Pusher,\n        name: string,\n        options: EchoOptionsWithDefaults<TBroadcastDriver>,\n    ) {\n        super();\n\n        this.name = name;\n        this.pusher = pusher;\n        this.options = options;\n        this.eventFormatter = new EventFormatter(this.options.namespace);\n\n        this.subscribe();\n    }\n\n    /**\n     * Subscribe to a Pusher channel.\n     */\n    subscribe(): void {\n        this.subscription = this.pusher.subscribe(this.name);\n    }\n\n    /**\n     * Unsubscribe from a Pusher channel.\n     */\n    unsubscribe(): void {\n        this.pusher.unsubscribe(this.name);\n    }\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    listen(event: string, callback: CallableFunction): this {\n        this.on(this.eventFormatter.format(event), callback);\n\n        return this;\n    }\n\n    /**\n     * Listen for all events on the channel instance.\n     */\n    listenToAll(callback: CallableFunction): this {\n        this.subscription.bind_global((event: string, data: unknown) => {\n            if (event.startsWith(\"pusher:\")) {\n                return;\n            }\n\n            let namespace = String(this.options.namespace ?? \"\").replace(\n                /\\./g,\n                \"\\\\\",\n            );\n\n            let formattedEvent = event.startsWith(namespace)\n                ? event.substring(namespace.length + 1)\n                : \".\" + event;\n\n            callback(formattedEvent, data);\n        });\n\n        return this;\n    }\n\n    /**\n     * Stop listening for an event on the channel instance.\n     */\n    stopListening(event: string, callback?: CallableFunction): this {\n        if (callback) {\n            this.subscription.unbind(\n                this.eventFormatter.format(event),\n                callback,\n            );\n        } else {\n            this.subscription.unbind(this.eventFormatter.format(event));\n        }\n\n        return this;\n    }\n\n    /**\n     * Stop listening for all events on the channel instance.\n     */\n    stopListeningToAll(callback?: CallableFunction): this {\n        if (callback) {\n            this.subscription.unbind_global(callback);\n        } else {\n            this.subscription.unbind_global();\n        }\n\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription succeeds.\n     */\n    subscribed(callback: CallableFunction): this {\n        this.on(\"pusher:subscription_succeeded\", () => {\n            callback();\n        });\n\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription error occurs.\n     */\n    error(callback: CallableFunction): this {\n        this.on(\"pusher:subscription_error\", (status: Record<string, any>) => {\n            callback(status);\n        });\n\n        return this;\n    }\n\n    /**\n     * Bind a channel to an event.\n     */\n    on(event: string, callback: CallableFunction): this {\n        this.subscription.bind(event, callback);\n\n        return this;\n    }\n}\n", "import { PusherChannel } from \"./pusher-channel\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Pusher private channel.\n */\nexport class PusherPrivateChannel<\n    TBroadcastDriver extends BroadcastDriver,\n> extends PusherChannel<TBroadcastDriver> {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: Record<any, any>): this {\n        this.pusher.channels.channels[this.name].trigger(\n            `client-${eventName}`,\n            data,\n        );\n\n        return this;\n    }\n}\n", "import { PusherChannel } from \"./pusher-channel\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Pusher private channel.\n */\nexport class PusherEncryptedPrivateChannel<\n    TBroadcastDriver extends BroadcastDriver,\n> extends PusherChannel<TBroadcastDriver> {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: Record<any, any>): this {\n        this.pusher.channels.channels[this.name].trigger(\n            `client-${eventName}`,\n            data,\n        );\n\n        return this;\n    }\n}\n", "import type { PresenceChannel } from \"./presence-channel\";\nimport { PusherPrivateChannel } from \"./pusher-private-channel\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Pusher presence channel.\n */\nexport class PusherPresenceChannel<TBroadcastDriver extends BroadcastDriver>\n    extends PusherPrivateChannel<TBroadcastDriver>\n    implements PresenceChannel\n{\n    /**\n     * Register a callback to be called anytime the member list changes.\n     */\n    here(callback: CallableFunction): this {\n        this.on(\"pusher:subscription_succeeded\", (data: Record<any, any>) => {\n            callback(Object.keys(data.members).map((k) => data.members[k]));\n        });\n\n        return this;\n    }\n\n    /**\n     * Listen for someone joining the channel.\n     */\n    joining(callback: CallableFunction): this {\n        this.on(\"pusher:member_added\", (member: Record<any, any>) => {\n            callback(member.info);\n        });\n\n        return this;\n    }\n\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: Record<any, any>): this {\n        this.pusher.channels.channels[this.name].trigger(\n            `client-${eventName}`,\n            data,\n        );\n\n        return this;\n    }\n\n    /**\n     * Listen for someone leaving the channel.\n     */\n    leaving(callback: CallableFunction): this {\n        this.on(\"pusher:member_removed\", (member: Record<any, any>) => {\n            callback(member.info);\n        });\n\n        return this;\n    }\n}\n", "import { EventFormatter } from \"../util\";\nimport { Channel } from \"./channel\";\nimport type { Socket } from \"socket.io-client\";\nimport type { EchoOptionsWithDefaults } from \"../connector\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Socket.io channel.\n */\nexport class SocketIoChannel extends Channel {\n    /**\n     * The Socket.io client instance.\n     */\n    socket: Socket;\n\n    /**\n     * The name of the channel.\n     */\n    name: string;\n\n    /**\n     * The event formatter.\n     */\n    eventFormatter: EventFormatter;\n\n    /**\n     * The event callbacks applied to the socket.\n     */\n    events: Record<string, any> = {};\n\n    /**\n     * User supplied callbacks for events on this channel.\n     */\n    private listeners: Record<string, CallableFunction[]> = {};\n\n    /**\n     * Create a new class instance.\n     */\n    constructor(\n        socket: Socket,\n        name: string,\n        options: EchoOptionsWithDefaults<BroadcastDriver>,\n    ) {\n        super();\n\n        this.name = name;\n        this.socket = socket;\n        this.options = options;\n        this.eventFormatter = new EventFormatter(this.options.namespace);\n\n        this.subscribe();\n    }\n\n    /**\n     * Subscribe to a Socket.io channel.\n     */\n    subscribe(): void {\n        this.socket.emit(\"subscribe\", {\n            channel: this.name,\n            auth: this.options.auth || {},\n        });\n    }\n\n    /**\n     * Unsubscribe from channel and ubind event callbacks.\n     */\n    unsubscribe(): void {\n        this.unbind();\n\n        this.socket.emit(\"unsubscribe\", {\n            channel: this.name,\n            auth: this.options.auth || {},\n        });\n    }\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    listen(event: string, callback: CallableFunction): this {\n        this.on(this.eventFormatter.format(event), callback);\n\n        return this;\n    }\n\n    /**\n     * Stop listening for an event on the channel instance.\n     */\n    stopListening(event: string, callback?: CallableFunction): this {\n        this.unbindEvent(this.eventFormatter.format(event), callback);\n\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription succeeds.\n     */\n    subscribed(callback: CallableFunction): this {\n        this.on(\"connect\", (socket: Socket) => {\n            callback(socket);\n        });\n\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime an error occurs.\n     */\n    error(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Bind the channel's socket to an event and store the callback.\n     */\n    on(event: string, callback: CallableFunction): this {\n        this.listeners[event] = this.listeners[event] || [];\n\n        if (!this.events[event]) {\n            this.events[event] = (channel: string, data: unknown) => {\n                if (this.name === channel && this.listeners[event]) {\n                    this.listeners[event].forEach((cb) => cb(data));\n                }\n            };\n\n            this.socket.on(event, this.events[event]);\n        }\n\n        this.listeners[event].push(callback);\n\n        return this;\n    }\n\n    /**\n     * Unbind the channel's socket from all stored event callbacks.\n     */\n    unbind(): void {\n        Object.keys(this.events).forEach((event) => {\n            this.unbindEvent(event);\n        });\n    }\n\n    /**\n     * Unbind the listeners for the given event.\n     */\n    protected unbindEvent(event: string, callback?: CallableFunction): void {\n        this.listeners[event] = this.listeners[event] || [];\n\n        if (callback) {\n            this.listeners[event] = this.listeners[event].filter(\n                (cb) => cb !== callback,\n            );\n        }\n\n        if (!callback || this.listeners[event].length === 0) {\n            if (this.events[event]) {\n                this.socket.removeListener(event, this.events[event]);\n\n                delete this.events[event];\n            }\n\n            delete this.listeners[event];\n        }\n    }\n}\n", "import { SocketIoChannel } from \"./socketio-channel\";\n\n/**\n * This class represents a Socket.io private channel.\n */\nexport class SocketIoPrivateChannel extends SocketIoChannel {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: unknown): this {\n        this.socket.emit(\"client event\", {\n            channel: this.name,\n            event: `client-${eventName}`,\n            data: data,\n        });\n\n        return this;\n    }\n}\n", "import type { PresenceChannel } from \"./presence-channel\";\nimport { SocketIoPrivateChannel } from \"./socketio-private-channel\";\n\n/**\n * This class represents a Socket.io presence channel.\n */\nexport class SocketIoPresenceChannel\n    extends SocketIoPrivateChannel\n    implements PresenceChannel\n{\n    /**\n     * Register a callback to be called anytime the member list changes.\n     */\n    here(callback: CallableFunction): this {\n        this.on(\"presence:subscribed\", (members: Record<string, any>[]) => {\n            callback(members.map((m) => m.user_info));\n        });\n\n        return this;\n    }\n\n    /**\n     * Listen for someone joining the channel.\n     */\n    joining(callback: CallableFunction): this {\n        this.on(\"presence:joining\", (member: Record<string, any>) =>\n            callback(member.user_info),\n        );\n\n        return this;\n    }\n\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: unknown): this {\n        this.socket.emit(\"client event\", {\n            channel: this.name,\n            event: `client-${eventName}`,\n            data: data,\n        });\n\n        return this;\n    }\n\n    /**\n     * Listen for someone leaving the channel.\n     */\n    leaving(callback: CallableFunction): this {\n        this.on(\"presence:leaving\", (member: Record<string, any>) =>\n            callback(member.user_info),\n        );\n\n        return this;\n    }\n}\n", "import { Channel } from \"./channel\";\n\n/**\n * This class represents a null channel.\n */\nexport class NullChannel extends Channel {\n    /**\n     * Subscribe to a channel.\n     */\n    subscribe(): void {\n        //\n    }\n\n    /**\n     * Unsubscribe from a channel.\n     */\n    unsubscribe(): void {\n        //\n    }\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    listen(_event: string, _callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Listen for all events on the channel instance.\n     */\n    listenToAll(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Stop listening for an event on the channel instance.\n     */\n    stopListening(_event: string, _callback?: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription succeeds.\n     */\n    subscribed(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime an error occurs.\n     */\n    error(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Bind a channel to an event.\n     */\n    on(_event: string, _callback: CallableFunction): this {\n        return this;\n    }\n}\n", "import { NullChannel } from \"./null-channel\";\n\n/**\n * This class represents a null private channel.\n */\nexport class NullPrivateChannel extends NullChannel {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(_eventName: string, _data: Record<any, any>): this {\n        return this;\n    }\n}\n", "import { NullChannel } from \"./null-channel\";\n\n/**\n * This class represents a null private channel.\n */\nexport class NullEncryptedPrivateChannel extends NullChannel {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(_eventName: string, _data: Record<any, any>): this {\n        return this;\n    }\n}\n", "import { NullPrivateChannel } from \"./null-private-channel\";\nimport type { PresenceChannel } from \"./presence-channel\";\n\n/**\n * This class represents a null presence channel.\n */\nexport class NullPresenceChannel\n    extends NullPrivateChannel\n    implements PresenceChannel\n{\n    /**\n     * Register a callback to be called anytime the member list changes.\n     */\n    here(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Listen for someone joining the channel.\n     */\n    joining(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(_eventName: string, _data: Record<any, any>): this {\n        return this;\n    }\n\n    /**\n     * Listen for someone leaving the channel.\n     */\n    leaving(_callback: CallableFunction): this {\n        return this;\n    }\n}\n", "/// <reference types=\"window\" />\n\nimport type { Channel, PresenceChannel } from \"../channel\";\nimport type { BroadcastDriver, EchoOptions } from \"../echo\";\n\nexport type EchoOptionsWithDefaults<TBroadcaster extends BroadcastDriver> = {\n    broadcaster: TBroadcaster;\n    auth: {\n        headers: Record<string, string>;\n    };\n    authEndpoint: string;\n    userAuthentication: {\n        endpoint: string;\n        headers: Record<string, string>;\n    };\n    csrfToken: string | null;\n    bearerToken: string | null;\n    host: string | null;\n    key: string | null;\n    namespace: string | false;\n\n    [key: string]: any;\n};\n\nexport abstract class Connector<\n    TBroadcastDriver extends BroadcastDriver,\n    TPublic extends Channel,\n    TPrivate extends Channel,\n    TPresence extends PresenceChannel,\n> {\n    /**\n     * Default connector options.\n     */\n    public static readonly _defaultOptions = {\n        auth: {\n            headers: {},\n        },\n        authEndpoint: \"/broadcasting/auth\",\n        userAuthentication: {\n            endpoint: \"/broadcasting/user-auth\",\n            headers: {},\n        },\n        csrfToken: null,\n        bearerToken: null,\n        host: null,\n        key: null,\n        namespace: \"App.Events\",\n    } as const;\n\n    /**\n     * Connector options.\n     */\n    options: EchoOptionsWithDefaults<TBroadcastDriver>;\n\n    /**\n     * Create a new class instance.\n     */\n    constructor(options: EchoOptions<TBroadcastDriver>) {\n        this.setOptions(options);\n        this.connect();\n    }\n\n    /**\n     * Merge the custom options with the defaults.\n     */\n    protected setOptions(options: EchoOptions<TBroadcastDriver>): void {\n        this.options = {\n            ...Connector._defaultOptions,\n            ...options,\n            broadcaster: options.broadcaster as TBroadcastDriver,\n        };\n\n        let token = this.csrfToken();\n\n        if (token) {\n            this.options.auth.headers[\"X-CSRF-TOKEN\"] = token;\n            this.options.userAuthentication.headers[\"X-CSRF-TOKEN\"] = token;\n        }\n\n        token = this.options.bearerToken;\n\n        if (token) {\n            this.options.auth.headers[\"Authorization\"] = \"Bearer \" + token;\n            this.options.userAuthentication.headers[\"Authorization\"] =\n                \"Bearer \" + token;\n        }\n    }\n\n    /**\n     * Extract the CSRF token from the page.\n     */\n    protected csrfToken(): null | string {\n        if (typeof window !== \"undefined\" && window.Laravel?.csrfToken) {\n            return window.Laravel.csrfToken;\n        }\n\n        if (this.options.csrfToken) {\n            return this.options.csrfToken;\n        }\n\n        if (\n            typeof document !== \"undefined\" &&\n            typeof document.querySelector === \"function\"\n        ) {\n            return (\n                document\n                    .querySelector('meta[name=\"csrf-token\"]')\n                    ?.getAttribute(\"content\") ?? null\n            );\n        }\n\n        return null;\n    }\n\n    /**\n     * Create a fresh connection.\n     */\n    abstract connect(): void;\n\n    /**\n     * Get a channel instance by name.\n     */\n    abstract channel(channel: string): TPublic;\n\n    /**\n     * Get a private channel instance by name.\n     */\n    abstract privateChannel(channel: string): TPrivate;\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    abstract presenceChannel(channel: string): TPresence;\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    abstract leave(channel: string): void;\n\n    /**\n     * Leave the given channel.\n     */\n    abstract leaveChannel(channel: string): void;\n\n    /**\n     * Get the socket_id of the connection.\n     */\n    abstract socketId(): string | undefined;\n\n    /**\n     * Disconnect from the Echo server.\n     */\n    abstract disconnect(): void;\n}\n", "import type Pusher from \"pusher-js\";\nimport type { Options as PusherJsOptions } from \"pusher-js\";\nimport {\n    PusherChannel,\n    PusherEncryptedPrivateChannel,\n    PusherPresenceChannel,\n    PusherPrivateChannel,\n} from \"../channel\";\nimport type { BroadcastDriver } from \"../echo\";\nimport { Connector, type EchoOptionsWithDefaults } from \"./connector\";\n\ntype AnyPusherChannel =\n    | PusherChannel<BroadcastDriver>\n    | PusherPrivateChannel<BroadcastDriver>\n    | PusherEncryptedPrivateChannel<BroadcastDriver>\n    | PusherPresenceChannel<BroadcastDriver>;\n\nexport type PusherOptions<TBroadcastDriver extends BroadcastDriver> =\n    EchoOptionsWithDefaults<TBroadcastDriver> & {\n        key: string;\n        Pusher?: typeof Pusher;\n    } & PusherJsOptions;\n\n/**\n * This class creates a connector to Pusher.\n */\nexport class PusherConnector<\n    TBroadcastDriver extends BroadcastDriver,\n> extends Connector<\n    TBroadcastDriver,\n    PusherChannel<TBroadcastDriver>,\n    PusherPrivateChannel<TBroadcastDriver>,\n    PusherPresenceChannel<TBroadcastDriver>\n> {\n    /**\n     * The Pusher instance.\n     */\n    pusher: Pusher;\n\n    /**\n     * All of the subscribed channel names.\n     */\n    channels: Record<string, AnyPusherChannel> = {};\n\n    declare options: PusherOptions<TBroadcastDriver>;\n\n    /**\n     * Create a fresh Pusher connection.\n     */\n    connect(): void {\n        if (typeof this.options.client !== \"undefined\") {\n            this.pusher = this.options.client as Pusher;\n        } else if (this.options.Pusher) {\n            this.pusher = new this.options.Pusher(\n                this.options.key,\n                this.options,\n            );\n        } else if (\n            typeof window !== \"undefined\" &&\n            typeof window.Pusher !== \"undefined\"\n        ) {\n            this.pusher = new window.Pusher(this.options.key, this.options);\n        } else {\n            throw new Error(\n                \"Pusher client not found. Should be globally available or passed via options.client\",\n            );\n        }\n    }\n\n    /**\n     * Sign in the user via Pusher user authentication (https://pusher.com/docs/channels/using_channels/user-authentication/).\n     */\n    signin(): void {\n        this.pusher.signin();\n    }\n\n    /**\n     * Listen for an event on a channel instance.\n     */\n    listen(\n        name: string,\n        event: string,\n        callback: CallableFunction,\n    ): AnyPusherChannel {\n        return this.channel(name).listen(event, callback);\n    }\n\n    /**\n     * Get a channel instance by name.\n     */\n    channel(name: string): AnyPusherChannel {\n        if (!this.channels[name]) {\n            this.channels[name] = new PusherChannel(\n                this.pusher,\n                name,\n                this.options,\n            );\n        }\n\n        return this.channels[name];\n    }\n\n    /**\n     * Get a private channel instance by name.\n     */\n    privateChannel(name: string): PusherPrivateChannel<TBroadcastDriver> {\n        if (!this.channels[\"private-\" + name]) {\n            this.channels[\"private-\" + name] = new PusherPrivateChannel(\n                this.pusher,\n                \"private-\" + name,\n                this.options,\n            );\n        }\n\n        return this.channels[\n            \"private-\" + name\n        ] as PusherPrivateChannel<TBroadcastDriver>;\n    }\n\n    /**\n     * Get a private encrypted channel instance by name.\n     */\n    encryptedPrivateChannel(\n        name: string,\n    ): PusherEncryptedPrivateChannel<TBroadcastDriver> {\n        if (!this.channels[\"private-encrypted-\" + name]) {\n            this.channels[\"private-encrypted-\" + name] =\n                new PusherEncryptedPrivateChannel(\n                    this.pusher,\n                    \"private-encrypted-\" + name,\n                    this.options,\n                );\n        }\n\n        return this.channels[\n            \"private-encrypted-\" + name\n        ] as PusherEncryptedPrivateChannel<TBroadcastDriver>;\n    }\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    presenceChannel(name: string): PusherPresenceChannel<TBroadcastDriver> {\n        if (!this.channels[\"presence-\" + name]) {\n            this.channels[\"presence-\" + name] = new PusherPresenceChannel(\n                this.pusher,\n                \"presence-\" + name,\n                this.options,\n            );\n        }\n\n        return this.channels[\n            \"presence-\" + name\n        ] as PusherPresenceChannel<TBroadcastDriver>;\n    }\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    leave(name: string): void {\n        let channels = [\n            name,\n            \"private-\" + name,\n            \"private-encrypted-\" + name,\n            \"presence-\" + name,\n        ];\n\n        channels.forEach((name: string) => {\n            this.leaveChannel(name);\n        });\n    }\n\n    /**\n     * Leave the given channel.\n     */\n    leaveChannel(name: string): void {\n        if (this.channels[name]) {\n            this.channels[name].unsubscribe();\n\n            delete this.channels[name];\n        }\n    }\n\n    /**\n     * Get the socket ID for the connection.\n     */\n    socketId(): string {\n        return this.pusher.connection.socket_id;\n    }\n\n    /**\n     * Disconnect Pusher connection.\n     */\n    disconnect(): void {\n        this.pusher.disconnect();\n    }\n}\n", "import { Connector } from \"./connector\";\nimport {\n    SocketIoChannel,\n    SocketIoPrivateChannel,\n    SocketIoPresenceChannel,\n} from \"../channel\";\nimport type {\n    io,\n    ManagerOptions,\n    Socket,\n    SocketOptions,\n} from \"socket.io-client\";\n\ntype AnySocketIoChannel =\n    | SocketIoChannel\n    | SocketIoPrivateChannel\n    | SocketIoPresenceChannel;\n\n/**\n * This class creates a connector to a Socket.io server.\n */\nexport class SocketIoConnector extends Connector<\n    \"socket.io\",\n    SocketIoChannel,\n    SocketIoPrivateChannel,\n    SocketIoPresenceChannel\n> {\n    /**\n     * The Socket.io connection instance.\n     */\n    socket: Socket;\n\n    /**\n     * All of the subscribed channel names.\n     */\n    channels: { [name: string]: SocketIoChannel } = {};\n\n    /**\n     * Create a fresh Socket.io connection.\n     */\n    connect(): void {\n        let io = this.getSocketIO();\n\n        this.socket = io(\n            this.options.host ?? undefined,\n            this.options as Partial<ManagerOptions & SocketOptions>,\n        );\n\n        this.socket.io.on(\"reconnect\", () => {\n            Object.values(this.channels).forEach((channel) => {\n                channel.subscribe();\n            });\n        });\n    }\n\n    /**\n     * Get socket.io module from global scope or options.\n     */\n    getSocketIO(): typeof io {\n        if (typeof this.options.client !== \"undefined\") {\n            return this.options.client as typeof io;\n        }\n\n        if (typeof window !== \"undefined\" && typeof window.io !== \"undefined\") {\n            return window.io;\n        }\n\n        throw new Error(\n            \"Socket.io client not found. Should be globally available or passed via options.client\",\n        );\n    }\n\n    /**\n     * Listen for an event on a channel instance.\n     */\n    listen(\n        name: string,\n        event: string,\n        callback: CallableFunction,\n    ): AnySocketIoChannel {\n        return this.channel(name).listen(event, callback);\n    }\n\n    /**\n     * Get a channel instance by name.\n     */\n    channel(name: string): AnySocketIoChannel {\n        if (!this.channels[name]) {\n            this.channels[name] = new SocketIoChannel(\n                this.socket,\n                name,\n                this.options,\n            );\n        }\n\n        return this.channels[name];\n    }\n\n    /**\n     * Get a private channel instance by name.\n     */\n    privateChannel(name: string): SocketIoPrivateChannel {\n        if (!this.channels[\"private-\" + name]) {\n            this.channels[\"private-\" + name] = new SocketIoPrivateChannel(\n                this.socket,\n                \"private-\" + name,\n                this.options,\n            );\n        }\n\n        return this.channels[\"private-\" + name] as SocketIoPrivateChannel;\n    }\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    presenceChannel(name: string): SocketIoPresenceChannel {\n        if (!this.channels[\"presence-\" + name]) {\n            this.channels[\"presence-\" + name] = new SocketIoPresenceChannel(\n                this.socket,\n                \"presence-\" + name,\n                this.options,\n            );\n        }\n\n        return this.channels[\"presence-\" + name] as SocketIoPresenceChannel;\n    }\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    leave(name: string): void {\n        let channels = [name, \"private-\" + name, \"presence-\" + name];\n\n        channels.forEach((name) => {\n            this.leaveChannel(name);\n        });\n    }\n\n    /**\n     * Leave the given channel.\n     */\n    leaveChannel(name: string): void {\n        if (this.channels[name]) {\n            this.channels[name].unsubscribe();\n\n            delete this.channels[name];\n        }\n    }\n\n    /**\n     * Get the socket ID for the connection.\n     */\n    socketId(): string | undefined {\n        return this.socket.id;\n    }\n\n    /**\n     * Disconnect Socketio connection.\n     */\n    disconnect(): void {\n        this.socket.disconnect();\n    }\n}\n", "import { Connector } from \"./connector\";\nimport {\n    NullChannel,\n    NullPrivateChannel,\n    NullPresenceChannel,\n    NullEncryptedPrivateChannel,\n} from \"../channel\";\n\n/**\n * This class creates a null connector.\n */\nexport class NullConnector extends Connector<\n    \"null\",\n    NullChannel,\n    NullPrivateChannel,\n    NullPresenceChannel\n> {\n    /**\n     * All of the subscribed channel names.\n     */\n    channels: any = {};\n\n    /**\n     * Create a fresh connection.\n     */\n    connect(): void {\n        //\n    }\n\n    /**\n     * Listen for an event on a channel instance.\n     */\n    listen(\n        _name: string,\n        _event: string,\n        _callback: CallableFunction,\n    ): NullChannel {\n        return new NullChannel();\n    }\n\n    /**\n     * Get a channel instance by name.\n     */\n    channel(_name: string): NullChannel {\n        return new NullChannel();\n    }\n\n    /**\n     * Get a private channel instance by name.\n     */\n    privateChannel(_name: string): NullPrivateChannel {\n        return new NullPrivateChannel();\n    }\n\n    /**\n     * Get a private encrypted channel instance by name.\n     */\n    encryptedPrivateChannel(_name: string): NullEncryptedPrivateChannel {\n        return new NullEncryptedPrivateChannel();\n    }\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    presenceChannel(_name: string): NullPresenceChannel {\n        return new NullPresenceChannel();\n    }\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    leave(_name: string): void {\n        //\n    }\n\n    /**\n     * Leave the given channel.\n     */\n    leaveChannel(_name: string): void {\n        //\n    }\n\n    /**\n     * Get the socket ID for the connection.\n     */\n    socketId(): string {\n        return \"fake-socket-id\";\n    }\n\n    /**\n     * Disconnect the connection.\n     */\n    disconnect(): void {\n        //\n    }\n}\n", "import type { InternalAxiosRequestConfig } from \"axios\";\nimport {\n    Channel,\n    NullChannel,\n    NullEncryptedPrivateChannel,\n    NullPresenceChannel,\n    NullPrivateChannel,\n    PusherChannel,\n    PusherEncryptedPrivateChannel,\n    PusherPresenceChannel,\n    PusherPrivateChannel,\n    SocketIoChannel,\n    SocketIoPresenceChannel,\n    SocketIoPrivateChannel,\n    type PresenceChannel,\n} from \"./channel\";\nimport {\n    Connector,\n    NullConnector,\n    PusherConnector,\n    SocketIoConnector,\n    type PusherOptions,\n} from \"./connector\";\nimport { isConstructor } from \"./util\";\n\n/**\n * This class is the primary API for interacting with broadcasting.\n */\nexport default class Echo<T extends keyof Broadcaster> {\n    /**\n     * The broadcasting connector.\n     */\n    connector: Broadcaster[Exclude<T, \"function\">][\"connector\"];\n\n    /**\n     * The Echo options.\n     */\n    options: EchoOptions<T>;\n\n    /**\n     * Create a new class instance.\n     */\n    constructor(options: EchoOptions<T>) {\n        this.options = options;\n        this.connect();\n\n        if (!this.options.withoutInterceptors) {\n            this.registerInterceptors();\n        }\n    }\n\n    /**\n     * Get a channel instance by name.\n     */\n    channel(channel: string): Broadcaster[T][\"public\"] {\n        return this.connector.channel(channel);\n    }\n\n    /**\n     * Create a new connection.\n     */\n    connect(): void {\n        if (this.options.broadcaster === \"reverb\") {\n            this.connector = new PusherConnector<\"reverb\">({\n                ...this.options,\n                cluster: \"\",\n            });\n        } else if (this.options.broadcaster === \"pusher\") {\n            this.connector = new PusherConnector<\"pusher\">(this.options);\n        } else if (this.options.broadcaster === \"ably\") {\n            this.connector = new PusherConnector<\"pusher\">({\n                ...this.options,\n                cluster: \"\",\n                broadcaster: \"pusher\",\n            });\n        } else if (this.options.broadcaster === \"socket.io\") {\n            this.connector = new SocketIoConnector(this.options);\n        } else if (this.options.broadcaster === \"null\") {\n            this.connector = new NullConnector(this.options);\n        } else if (\n            typeof this.options.broadcaster === \"function\" &&\n            isConstructor(this.options.broadcaster)\n        ) {\n            this.connector = new this.options.broadcaster(this.options);\n        } else {\n            throw new Error(\n                `Broadcaster ${typeof this.options.broadcaster} ${String(this.options.broadcaster)} is not supported.`,\n            );\n        }\n    }\n\n    /**\n     * Disconnect from the Echo server.\n     */\n    disconnect(): void {\n        this.connector.disconnect();\n    }\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    join(channel: string): Broadcaster[T][\"presence\"] {\n        return this.connector.presenceChannel(channel);\n    }\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    leave(channel: string): void {\n        this.connector.leave(channel);\n    }\n\n    /**\n     * Leave the given channel.\n     */\n    leaveChannel(channel: string): void {\n        this.connector.leaveChannel(channel);\n    }\n\n    /**\n     * Leave all channels.\n     */\n    leaveAllChannels(): void {\n        for (const channel in this.connector.channels) {\n            this.leaveChannel(channel);\n        }\n    }\n\n    /**\n     * Listen for an event on a channel instance.\n     */\n    listen(\n        channel: string,\n        event: string,\n        callback: CallableFunction,\n    ): Broadcaster[T][\"public\"] {\n        return this.connector.listen(channel, event, callback);\n    }\n\n    /**\n     * Get a private channel instance by name.\n     */\n    private(channel: string): Broadcaster[T][\"private\"] {\n        return this.connector.privateChannel(channel);\n    }\n\n    /**\n     * Get a private encrypted channel instance by name.\n     */\n    encryptedPrivate(channel: string): Broadcaster[T][\"encrypted\"] {\n        if (this.connectorSupportsEncryptedPrivateChannels(this.connector)) {\n            return this.connector.encryptedPrivateChannel(channel);\n        }\n\n        throw new Error(\n            `Broadcaster ${typeof this.options.broadcaster} ${String(\n                this.options.broadcaster,\n            )} does not support encrypted private channels.`,\n        );\n    }\n\n    private connectorSupportsEncryptedPrivateChannels(\n        connector: unknown,\n    ): connector is PusherConnector<any> | NullConnector {\n        return (\n            connector instanceof PusherConnector ||\n            connector instanceof NullConnector\n        );\n    }\n\n    /**\n     * Get the Socket ID for the connection.\n     */\n    socketId(): string | undefined {\n        return this.connector.socketId();\n    }\n\n    /**\n     * Register 3rd party request interceptiors. These are used to automatically\n     * send a connections socket id to a Laravel app with a X-Socket-Id header.\n     */\n    registerInterceptors(): void {\n        // TODO: This package is deprecated and we should remove it in a future version.\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        if (typeof Vue !== \"undefined\" && Vue?.http) {\n            this.registerVueRequestInterceptor();\n        }\n\n        if (typeof axios === \"function\") {\n            this.registerAxiosRequestInterceptor();\n        }\n\n        if (typeof jQuery === \"function\") {\n            this.registerjQueryAjaxSetup();\n        }\n\n        if (typeof Turbo === \"object\") {\n            this.registerTurboRequestInterceptor();\n        }\n    }\n\n    /**\n     * Register a Vue HTTP interceptor to add the X-Socket-ID header.\n     */\n    registerVueRequestInterceptor(): void {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n        Vue.http.interceptors.push(\n            (request: Record<any, any>, next: CallableFunction) => {\n                if (this.socketId()) {\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n                    request.headers.set(\"X-Socket-ID\", this.socketId());\n                }\n\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n                next();\n            },\n        );\n    }\n\n    /**\n     * Register an Axios HTTP interceptor to add the X-Socket-ID header.\n     */\n    registerAxiosRequestInterceptor(): void {\n        axios!.interceptors.request.use(\n            (config: InternalAxiosRequestConfig<any>) => {\n                if (this.socketId()) {\n                    config.headers[\"X-Socket-Id\"] = this.socketId();\n                }\n\n                return config;\n            },\n        );\n    }\n\n    /**\n     * Register jQuery AjaxPrefilter to add the X-Socket-ID header.\n     */\n    registerjQueryAjaxSetup(): void {\n        if (typeof jQuery.ajax != \"undefined\") {\n            jQuery.ajaxPrefilter(\n                (\n                    _options: any,\n                    _originalOptions: any,\n                    xhr: Record<any, any>,\n                ) => {\n                    if (this.socketId()) {\n                        xhr.setRequestHeader(\"X-Socket-Id\", this.socketId());\n                    }\n                },\n            );\n        }\n    }\n\n    /**\n     * Register the Turbo Request interceptor to add the X-Socket-ID header.\n     */\n    registerTurboRequestInterceptor(): void {\n        document.addEventListener(\n            \"turbo:before-fetch-request\",\n            (event: Record<any, any>) => {\n                event.detail.fetchOptions.headers[\"X-Socket-Id\"] =\n                    this.socketId();\n            },\n        );\n    }\n}\n\n/**\n * Export channel classes for TypeScript.\n */\nexport { Channel, Connector, type PresenceChannel };\n\nexport { EventFormatter } from \"./util\";\n\ntype CustomOmit<T, K extends PropertyKey> = {\n    [P in keyof T as Exclude<P, K>]: T[P];\n};\n\n/**\n * Specifies the broadcaster\n */\nexport type Broadcaster = {\n    reverb: {\n        connector: PusherConnector<\"reverb\">;\n        public: PusherChannel<\"reverb\">;\n        private: PusherPrivateChannel<\"reverb\">;\n        encrypted: PusherEncryptedPrivateChannel<\"reverb\">;\n        presence: PusherPresenceChannel<\"reverb\">;\n        options: GenericOptions<\"reverb\"> &\n            Partial<CustomOmit<PusherOptions<\"reverb\">, \"cluster\">>;\n    };\n    pusher: {\n        connector: PusherConnector<\"pusher\">;\n        public: PusherChannel<\"pusher\">;\n        private: PusherPrivateChannel<\"pusher\">;\n        encrypted: PusherEncryptedPrivateChannel<\"pusher\">;\n        presence: PusherPresenceChannel<\"pusher\">;\n        options: GenericOptions<\"pusher\"> & Partial<PusherOptions<\"pusher\">>;\n    };\n    ably: {\n        connector: PusherConnector<\"pusher\">;\n        public: PusherChannel<\"pusher\">;\n        private: PusherPrivateChannel<\"pusher\">;\n        encrypted: PusherEncryptedPrivateChannel<\"pusher\">;\n        presence: PusherPresenceChannel<\"pusher\">;\n        options: GenericOptions<\"ably\"> & Partial<PusherOptions<\"ably\">>;\n    };\n    \"socket.io\": {\n        connector: SocketIoConnector;\n        public: SocketIoChannel;\n        private: SocketIoPrivateChannel;\n        encrypted: never;\n        presence: SocketIoPresenceChannel;\n        options: GenericOptions<\"socket.io\">;\n    };\n    null: {\n        connector: NullConnector;\n        public: NullChannel;\n        private: NullPrivateChannel;\n        encrypted: NullEncryptedPrivateChannel;\n        presence: NullPresenceChannel;\n        options: GenericOptions<\"null\">;\n    };\n    function: {\n        connector: any;\n        public: any;\n        private: any;\n        encrypted: any;\n        presence: any;\n        options: GenericOptions<\"function\">;\n    };\n};\n\ntype Constructor<T = {}> = new (...args: any[]) => T;\n\nexport type BroadcastDriver = Exclude<keyof Broadcaster, \"function\">;\n\ntype GenericOptions<TBroadcaster extends keyof Broadcaster> = {\n    /**\n     * The broadcast connector.\n     */\n    broadcaster: TBroadcaster extends \"function\"\n        ? Constructor<InstanceType<Broadcaster[TBroadcaster][\"connector\"]>>\n        : TBroadcaster;\n\n    auth?: {\n        headers: Record<string, string>;\n    };\n    authEndpoint?: string;\n    userAuthentication?: {\n        endpoint: string;\n        headers: Record<string, string>;\n    };\n    csrfToken?: string | null;\n    bearerToken?: string | null;\n    host?: string | null;\n    key?: string | null;\n    namespace?: string | false;\n    withoutInterceptors?: boolean;\n\n    [key: string]: any;\n};\n\nexport type EchoOptions<TBroadcaster extends keyof Broadcaster> =\n    Broadcaster[TBroadcaster][\"options\"];\n"], "names": ["Channel", "event", "callback", "EventFormatter", "namespace", "value", "isConstructor", "obj", "err", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pusher", "name", "options", "data", "formattedEvent", "status", "PusherPrivateChannel", "eventName", "PusherEncryptedPrivateChannel", "PusherPresenceChannel", "k", "member", "SocketIoChannel", "socket", "_callback", "channel", "cb", "SocketIoPrivateChannel", "SocketIoPresenceChannel", "members", "m", "NullChannel", "_event", "NullPrivateChannel", "_eventName", "_data", "NullEncryptedPrivateChannel", "NullPresenceChannel", "_Connector", "token", "_a", "_b", "Connector", "PusherConnector", "SocketIoConnector", "io", "NullConnector", "_name", "Echo", "connector", "request", "next", "config", "_options", "_originalOptions", "xhr"], "mappings": "kCAMO,MAAeA,CAAQ,CAc1B,iBAAiBC,EAAeC,EAAkC,CAC9D,OAAO,KAAK,OAAO,WAAaD,EAAOC,CAAQ,CAAA,CAMnD,aAAaA,EAAkC,CAC3C,OAAO,KAAK,OACR,mEACAA,CACJ,CAAA,CAWJ,wBAAwBD,EAAeC,EAAmC,CACtE,OAAO,KAAK,cAAc,WAAaD,EAAOC,CAAQ,CAAA,CAY9D,CCpDO,MAAMC,CAAe,CAIxB,YAAoBC,EAAyC,CAAzC,KAAA,UAAAA,CAAA,CAOpB,OAAOH,EAAuB,CACtB,MAAA,CAAC,IAAK,IAAI,EAAE,SAASA,EAAM,OAAO,CAAC,CAAC,EAC7BA,EAAM,UAAU,CAAC,GACjB,KAAK,YACJA,EAAA,KAAK,UAAY,IAAMA,GAG5BA,EAAM,QAAQ,MAAO,IAAI,EAAA,CAMpC,aAAaI,EAA+B,CACxC,KAAK,UAAYA,CAAA,CAEzB,CC9BA,SAASC,EAAcC,EAAkD,CACjE,GAAA,CACA,IAAKA,QACAC,EAAK,CACV,GACIA,aAAe,OACfA,EAAI,QAAQ,SAAS,sBAAsB,EAEpC,MAAA,EACX,CAGG,MAAA,EACX,CCHO,MAAMC,UAEHT,CAAQ,CAwBd,YACIU,EACAC,EACAC,EACF,CACQ,MAAA,EAEN,KAAK,KAAOD,EACZ,KAAK,OAASD,EACd,KAAK,QAAUE,EACf,KAAK,eAAiB,IAAIT,EAAe,KAAK,QAAQ,SAAS,EAE/D,KAAK,UAAU,CAAA,CAMnB,WAAkB,CACd,KAAK,aAAe,KAAK,OAAO,UAAU,KAAK,IAAI,CAAA,CAMvD,aAAoB,CACX,KAAA,OAAO,YAAY,KAAK,IAAI,CAAA,CAMrC,OAAOF,EAAeC,EAAkC,CACpD,YAAK,GAAG,KAAK,eAAe,OAAOD,CAAK,EAAGC,CAAQ,EAE5C,IAAA,CAMX,YAAYA,EAAkC,CAC1C,YAAK,aAAa,YAAY,CAACD,EAAeY,IAAkB,CACxD,GAAAZ,EAAM,WAAW,SAAS,EAC1B,OAGJ,IAAIG,EAAY,OAAO,KAAK,QAAQ,WAAa,EAAE,EAAE,QACjD,MACA,IACJ,EAEIU,EAAiBb,EAAM,WAAWG,CAAS,EACzCH,EAAM,UAAUG,EAAU,OAAS,CAAC,EACpC,IAAMH,EAEZC,EAASY,EAAgBD,CAAI,CAAA,CAChC,EAEM,IAAA,CAMX,cAAcZ,EAAeC,EAAmC,CAC5D,OAAIA,EACA,KAAK,aAAa,OACd,KAAK,eAAe,OAAOD,CAAK,EAChCC,CACJ,EAEA,KAAK,aAAa,OAAO,KAAK,eAAe,OAAOD,CAAK,CAAC,EAGvD,IAAA,CAMX,mBAAmBC,EAAmC,CAClD,OAAIA,EACK,KAAA,aAAa,cAAcA,CAAQ,EAExC,KAAK,aAAa,cAAc,EAG7B,IAAA,CAMX,WAAWA,EAAkC,CACpC,YAAA,GAAG,gCAAiC,IAAM,CAClCA,EAAA,CAAA,CACZ,EAEM,IAAA,CAMX,MAAMA,EAAkC,CAC/B,YAAA,GAAG,4BAA8Ba,GAAgC,CAClEb,EAASa,CAAM,CAAA,CAClB,EAEM,IAAA,CAMX,GAAGd,EAAeC,EAAkC,CAC3C,YAAA,aAAa,KAAKD,EAAOC,CAAQ,EAE/B,IAAA,CAEf,CCvJO,MAAMc,UAEHP,CAAgC,CAItC,QAAQQ,EAAmBJ,EAA8B,CACrD,YAAK,OAAO,SAAS,SAAS,KAAK,IAAI,EAAE,QACrC,UAAUI,CAAS,GACnBJ,CACJ,EAEO,IAAA,CAEf,CCdO,MAAMK,UAEHT,CAAgC,CAItC,QAAQQ,EAAmBJ,EAA8B,CACrD,YAAK,OAAO,SAAS,SAAS,KAAK,IAAI,EAAE,QACrC,UAAUI,CAAS,GACnBJ,CACJ,EAEO,IAAA,CAEf,CCbO,MAAMM,UACDH,CAEZ,CAII,KAAKd,EAAkC,CAC9B,YAAA,GAAG,gCAAkCW,GAA2B,CACjEX,EAAS,OAAO,KAAKW,EAAK,OAAO,EAAE,IAAKO,GAAMP,EAAK,QAAQO,CAAC,CAAC,CAAC,CAAA,CACjE,EAEM,IAAA,CAMX,QAAQlB,EAAkC,CACjC,YAAA,GAAG,sBAAwBmB,GAA6B,CACzDnB,EAASmB,EAAO,IAAI,CAAA,CACvB,EAEM,IAAA,CAMX,QAAQJ,EAAmBJ,EAA8B,CACrD,YAAK,OAAO,SAAS,SAAS,KAAK,IAAI,EAAE,QACrC,UAAUI,CAAS,GACnBJ,CACJ,EAEO,IAAA,CAMX,QAAQX,EAAkC,CACjC,YAAA,GAAG,wBAA0BmB,GAA6B,CAC3DnB,EAASmB,EAAO,IAAI,CAAA,CACvB,EAEM,IAAA,CAEf,CC9CO,MAAMC,UAAwBtB,CAAQ,CA6BzC,YACIuB,EACAZ,EACAC,EACF,CACQ,MAAA,EAfV,KAAA,OAA8B,CAAC,EAK/B,KAAQ,UAAgD,CAAC,EAYrD,KAAK,KAAOD,EACZ,KAAK,OAASY,EACd,KAAK,QAAUX,EACf,KAAK,eAAiB,IAAIT,EAAe,KAAK,QAAQ,SAAS,EAE/D,KAAK,UAAU,CAAA,CAMnB,WAAkB,CACT,KAAA,OAAO,KAAK,YAAa,CAC1B,QAAS,KAAK,KACd,KAAM,KAAK,QAAQ,MAAQ,CAAA,CAAC,CAC/B,CAAA,CAML,aAAoB,CAChB,KAAK,OAAO,EAEP,KAAA,OAAO,KAAK,cAAe,CAC5B,QAAS,KAAK,KACd,KAAM,KAAK,QAAQ,MAAQ,CAAA,CAAC,CAC/B,CAAA,CAML,OAAOF,EAAeC,EAAkC,CACpD,YAAK,GAAG,KAAK,eAAe,OAAOD,CAAK,EAAGC,CAAQ,EAE5C,IAAA,CAMX,cAAcD,EAAeC,EAAmC,CAC5D,YAAK,YAAY,KAAK,eAAe,OAAOD,CAAK,EAAGC,CAAQ,EAErD,IAAA,CAMX,WAAWA,EAAkC,CACpC,YAAA,GAAG,UAAYqB,GAAmB,CACnCrB,EAASqB,CAAM,CAAA,CAClB,EAEM,IAAA,CAMX,MAAMC,EAAmC,CAC9B,OAAA,IAAA,CAMX,GAAGvB,EAAeC,EAAkC,CAChD,YAAK,UAAUD,CAAK,EAAI,KAAK,UAAUA,CAAK,GAAK,CAAC,EAE7C,KAAK,OAAOA,CAAK,IAClB,KAAK,OAAOA,CAAK,EAAI,CAACwB,EAAiBZ,IAAkB,CACjD,KAAK,OAASY,GAAW,KAAK,UAAUxB,CAAK,GACxC,KAAA,UAAUA,CAAK,EAAE,QAASyB,GAAOA,EAAGb,CAAI,CAAC,CAEtD,EAEA,KAAK,OAAO,GAAGZ,EAAO,KAAK,OAAOA,CAAK,CAAC,GAG5C,KAAK,UAAUA,CAAK,EAAE,KAAKC,CAAQ,EAE5B,IAAA,CAMX,QAAe,CACX,OAAO,KAAK,KAAK,MAAM,EAAE,QAASD,GAAU,CACxC,KAAK,YAAYA,CAAK,CAAA,CACzB,CAAA,CAMK,YAAYA,EAAeC,EAAmC,CACpE,KAAK,UAAUD,CAAK,EAAI,KAAK,UAAUA,CAAK,GAAK,CAAC,EAE9CC,IACA,KAAK,UAAUD,CAAK,EAAI,KAAK,UAAUA,CAAK,EAAE,OACzCyB,GAAOA,IAAOxB,CACnB,IAGA,CAACA,GAAY,KAAK,UAAUD,CAAK,EAAE,SAAW,KAC1C,KAAK,OAAOA,CAAK,IACjB,KAAK,OAAO,eAAeA,EAAO,KAAK,OAAOA,CAAK,CAAC,EAE7C,OAAA,KAAK,OAAOA,CAAK,GAGrB,OAAA,KAAK,UAAUA,CAAK,EAC/B,CAER,CC9JO,MAAM0B,UAA+BL,CAAgB,CAIxD,QAAQL,EAAmBJ,EAAqB,CACvC,YAAA,OAAO,KAAK,eAAgB,CAC7B,QAAS,KAAK,KACd,MAAO,UAAUI,CAAS,GAC1B,KAAAJ,CAAA,CACH,EAEM,IAAA,CAEf,CCZO,MAAMe,UACDD,CAEZ,CAII,KAAKzB,EAAkC,CAC9B,YAAA,GAAG,sBAAwB2B,GAAmC,CAC/D3B,EAAS2B,EAAQ,IAAKC,GAAMA,EAAE,SAAS,CAAC,CAAA,CAC3C,EAEM,IAAA,CAMX,QAAQ5B,EAAkC,CACjC,YAAA,GAAG,mBAAqBmB,GACzBnB,EAASmB,EAAO,SAAS,CAC7B,EAEO,IAAA,CAMX,QAAQJ,EAAmBJ,EAAqB,CACvC,YAAA,OAAO,KAAK,eAAgB,CAC7B,QAAS,KAAK,KACd,MAAO,UAAUI,CAAS,GAC1B,KAAAJ,CAAA,CACH,EAEM,IAAA,CAMX,QAAQX,EAAkC,CACjC,YAAA,GAAG,mBAAqBmB,GACzBnB,EAASmB,EAAO,SAAS,CAC7B,EAEO,IAAA,CAEf,CClDO,MAAMU,UAAoB/B,CAAQ,CAIrC,WAAkB,CAAA,CAOlB,aAAoB,CAAA,CAOpB,OAAOgC,EAAgBR,EAAmC,CAC/C,OAAA,IAAA,CAMX,YAAYA,EAAmC,CACpC,OAAA,IAAA,CAMX,cAAcQ,EAAgBR,EAAoC,CACvD,OAAA,IAAA,CAMX,WAAWA,EAAmC,CACnC,OAAA,IAAA,CAMX,MAAMA,EAAmC,CAC9B,OAAA,IAAA,CAMX,GAAGQ,EAAgBR,EAAmC,CAC3C,OAAA,IAAA,CAEf,CCxDO,MAAMS,UAA2BF,CAAY,CAIhD,QAAQG,EAAoBC,EAA+B,CAChD,OAAA,IAAA,CAEf,CCPO,MAAMC,UAAoCL,CAAY,CAIzD,QAAQG,EAAoBC,EAA+B,CAChD,OAAA,IAAA,CAEf,CCNO,MAAME,UACDJ,CAEZ,CAII,KAAKT,EAAmC,CAC7B,OAAA,IAAA,CAMX,QAAQA,EAAmC,CAChC,OAAA,IAAA,CAMX,QAAQU,EAAoBC,EAA+B,CAChD,OAAA,IAAA,CAMX,QAAQX,EAAmC,CAChC,OAAA,IAAA,CAEf,CCbO,MAAec,EAAf,MAAeA,CAKpB,CA4BE,YAAY1B,EAAwC,CAChD,KAAK,WAAWA,CAAO,EACvB,KAAK,QAAQ,CAAA,CAMP,WAAWA,EAA8C,CAC/D,KAAK,QAAU,CACX,GAAG0B,EAAU,gBACb,GAAG1B,EACH,YAAaA,EAAQ,WACzB,EAEI,IAAA2B,EAAQ,KAAK,UAAU,EAEvBA,IACA,KAAK,QAAQ,KAAK,QAAQ,cAAc,EAAIA,EAC5C,KAAK,QAAQ,mBAAmB,QAAQ,cAAc,EAAIA,GAG9DA,EAAQ,KAAK,QAAQ,YAEjBA,IACA,KAAK,QAAQ,KAAK,QAAQ,cAAmB,UAAYA,EACzD,KAAK,QAAQ,mBAAmB,QAAQ,cACpC,UAAYA,EACpB,CAMM,WAA2B,SACjC,OAAI,OAAO,OAAW,OAAeC,EAAA,OAAO,UAAP,MAAAA,EAAgB,WAC1C,OAAO,QAAQ,UAGtB,KAAK,QAAQ,UACN,KAAK,QAAQ,UAIpB,OAAO,SAAa,KACpB,OAAO,SAAS,eAAkB,aAG9BC,EAAA,SACK,cAAc,yBAAyB,IAD5C,YAAAA,EAEM,aAAa,aAAc,KAIlC,IAAA,CA0Cf,EAxHIH,EAAuB,gBAAkB,CACrC,KAAM,CACF,QAAS,CAAA,CACb,EACA,aAAc,qBACd,mBAAoB,CAChB,SAAU,0BACV,QAAS,CAAA,CACb,EACA,UAAW,KACX,YAAa,KACb,KAAM,KACN,IAAK,KACL,UAAW,YACf,EAvBG,IAAeI,EAAfJ,ECEA,MAAMK,UAEHD,CAKR,CAPK,aAAA,CAAA,MAAA,GAAA,SAAA,EAgBH,KAAA,SAA6C,CAAC,CAAA,CAO9C,SAAgB,CACZ,GAAI,OAAO,KAAK,QAAQ,OAAW,IAC1B,KAAA,OAAS,KAAK,QAAQ,eACpB,KAAK,QAAQ,OACf,KAAA,OAAS,IAAI,KAAK,QAAQ,OAC3B,KAAK,QAAQ,IACb,KAAK,OACT,UAEA,OAAO,OAAW,KAClB,OAAO,OAAO,OAAW,IAEpB,KAAA,OAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAK,KAAK,OAAO,MAE9D,OAAM,IAAI,MACN,oFACJ,CACJ,CAMJ,QAAe,CACX,KAAK,OAAO,OAAO,CAAA,CAMvB,OACI/B,EACAV,EACAC,EACgB,CAChB,OAAO,KAAK,QAAQS,CAAI,EAAE,OAAOV,EAAOC,CAAQ,CAAA,CAMpD,QAAQS,EAAgC,CACpC,OAAK,KAAK,SAASA,CAAI,IACd,KAAA,SAASA,CAAI,EAAI,IAAIF,EACtB,KAAK,OACLE,EACA,KAAK,OACT,GAGG,KAAK,SAASA,CAAI,CAAA,CAM7B,eAAeA,EAAsD,CACjE,OAAK,KAAK,SAAS,WAAaA,CAAI,IAChC,KAAK,SAAS,WAAaA,CAAI,EAAI,IAAIK,EACnC,KAAK,OACL,WAAaL,EACb,KAAK,OACT,GAGG,KAAK,SACR,WAAaA,CACjB,CAAA,CAMJ,wBACIA,EAC+C,CAC/C,OAAK,KAAK,SAAS,qBAAuBA,CAAI,IAC1C,KAAK,SAAS,qBAAuBA,CAAI,EACrC,IAAIO,EACA,KAAK,OACL,qBAAuBP,EACvB,KAAK,OACT,GAGD,KAAK,SACR,qBAAuBA,CAC3B,CAAA,CAMJ,gBAAgBA,EAAuD,CACnE,OAAK,KAAK,SAAS,YAAcA,CAAI,IACjC,KAAK,SAAS,YAAcA,CAAI,EAAI,IAAIQ,EACpC,KAAK,OACL,YAAcR,EACd,KAAK,OACT,GAGG,KAAK,SACR,YAAcA,CAClB,CAAA,CAMJ,MAAMA,EAAoB,CACP,CACXA,EACA,WAAaA,EACb,qBAAuBA,EACvB,YAAcA,CAClB,EAES,QAASA,GAAiB,CAC/B,KAAK,aAAaA,CAAI,CAAA,CACzB,CAAA,CAML,aAAaA,EAAoB,CACzB,KAAK,SAASA,CAAI,IACb,KAAA,SAASA,CAAI,EAAE,YAAY,EAEzB,OAAA,KAAK,SAASA,CAAI,EAC7B,CAMJ,UAAmB,CACR,OAAA,KAAK,OAAO,WAAW,SAAA,CAMlC,YAAmB,CACf,KAAK,OAAO,WAAW,CAAA,CAE/B,CC/KO,MAAMiC,UAA0BF,CAKrC,CALK,aAAA,CAAA,MAAA,GAAA,SAAA,EAcH,KAAA,SAAgD,CAAC,CAAA,CAKjD,SAAgB,CACR,IAAAG,EAAK,KAAK,YAAY,EAE1B,KAAK,OAASA,EACV,KAAK,QAAQ,MAAQ,OACrB,KAAK,OACT,EAEA,KAAK,OAAO,GAAG,GAAG,YAAa,IAAM,CACjC,OAAO,OAAO,KAAK,QAAQ,EAAE,QAASpB,GAAY,CAC9CA,EAAQ,UAAU,CAAA,CACrB,CAAA,CACJ,CAAA,CAML,aAAyB,CACrB,GAAI,OAAO,KAAK,QAAQ,OAAW,IAC/B,OAAO,KAAK,QAAQ,OAGxB,GAAI,OAAO,OAAW,KAAe,OAAO,OAAO,GAAO,IACtD,OAAO,OAAO,GAGlB,MAAM,IAAI,MACN,uFACJ,CAAA,CAMJ,OACId,EACAV,EACAC,EACkB,CAClB,OAAO,KAAK,QAAQS,CAAI,EAAE,OAAOV,EAAOC,CAAQ,CAAA,CAMpD,QAAQS,EAAkC,CACtC,OAAK,KAAK,SAASA,CAAI,IACd,KAAA,SAASA,CAAI,EAAI,IAAIW,EACtB,KAAK,OACLX,EACA,KAAK,OACT,GAGG,KAAK,SAASA,CAAI,CAAA,CAM7B,eAAeA,EAAsC,CACjD,OAAK,KAAK,SAAS,WAAaA,CAAI,IAChC,KAAK,SAAS,WAAaA,CAAI,EAAI,IAAIgB,EACnC,KAAK,OACL,WAAahB,EACb,KAAK,OACT,GAGG,KAAK,SAAS,WAAaA,CAAI,CAAA,CAM1C,gBAAgBA,EAAuC,CACnD,OAAK,KAAK,SAAS,YAAcA,CAAI,IACjC,KAAK,SAAS,YAAcA,CAAI,EAAI,IAAIiB,EACpC,KAAK,OACL,YAAcjB,EACd,KAAK,OACT,GAGG,KAAK,SAAS,YAAcA,CAAI,CAAA,CAM3C,MAAMA,EAAoB,CACP,CAACA,EAAM,WAAaA,EAAM,YAAcA,CAAI,EAElD,QAASA,GAAS,CACvB,KAAK,aAAaA,CAAI,CAAA,CACzB,CAAA,CAML,aAAaA,EAAoB,CACzB,KAAK,SAASA,CAAI,IACb,KAAA,SAASA,CAAI,EAAE,YAAY,EAEzB,OAAA,KAAK,SAASA,CAAI,EAC7B,CAMJ,UAA+B,CAC3B,OAAO,KAAK,OAAO,EAAA,CAMvB,YAAmB,CACf,KAAK,OAAO,WAAW,CAAA,CAE/B,CCxJO,MAAMmC,UAAsBJ,CAKjC,CALK,aAAA,CAAA,MAAA,GAAA,SAAA,EASH,KAAA,SAAgB,CAAC,CAAA,CAKjB,SAAgB,CAAA,CAOhB,OACIK,EACAf,EACAR,EACW,CACX,OAAO,IAAIO,CAAY,CAM3B,QAAQgB,EAA4B,CAChC,OAAO,IAAIhB,CAAY,CAM3B,eAAegB,EAAmC,CAC9C,OAAO,IAAId,CAAmB,CAMlC,wBAAwBc,EAA4C,CAChE,OAAO,IAAIX,CAA4B,CAM3C,gBAAgBW,EAAoC,CAChD,OAAO,IAAIV,CAAoB,CAMnC,MAAMU,EAAqB,CAAA,CAO3B,aAAaA,EAAqB,CAAA,CAOlC,UAAmB,CACR,MAAA,gBAAA,CAMX,YAAmB,CAAA,CAGvB,CCnEA,MAAqBC,CAAkC,CAcnD,YAAYpC,EAAyB,CACjC,KAAK,QAAUA,EACf,KAAK,QAAQ,EAER,KAAK,QAAQ,qBACd,KAAK,qBAAqB,CAC9B,CAMJ,QAAQa,EAA2C,CACxC,OAAA,KAAK,UAAU,QAAQA,CAAO,CAAA,CAMzC,SAAgB,CACR,GAAA,KAAK,QAAQ,cAAgB,SACxB,KAAA,UAAY,IAAIkB,EAA0B,CAC3C,GAAG,KAAK,QACR,QAAS,EAAA,CACZ,UACM,KAAK,QAAQ,cAAgB,SACpC,KAAK,UAAY,IAAIA,EAA0B,KAAK,OAAO,UACpD,KAAK,QAAQ,cAAgB,OAC/B,KAAA,UAAY,IAAIA,EAA0B,CAC3C,GAAG,KAAK,QACR,QAAS,GACT,YAAa,QAAA,CAChB,UACM,KAAK,QAAQ,cAAgB,YACpC,KAAK,UAAY,IAAIC,EAAkB,KAAK,OAAO,UAC5C,KAAK,QAAQ,cAAgB,OACpC,KAAK,UAAY,IAAIE,EAAc,KAAK,OAAO,UAE/C,OAAO,KAAK,QAAQ,aAAgB,YACpCxC,EAAc,KAAK,QAAQ,WAAW,EAEtC,KAAK,UAAY,IAAI,KAAK,QAAQ,YAAY,KAAK,OAAO,MAE1D,OAAM,IAAI,MACN,eAAe,OAAO,KAAK,QAAQ,WAAW,IAAI,OAAO,KAAK,QAAQ,WAAW,CAAC,oBACtF,CACJ,CAMJ,YAAmB,CACf,KAAK,UAAU,WAAW,CAAA,CAM9B,KAAKmB,EAA6C,CACvC,OAAA,KAAK,UAAU,gBAAgBA,CAAO,CAAA,CAMjD,MAAMA,EAAuB,CACpB,KAAA,UAAU,MAAMA,CAAO,CAAA,CAMhC,aAAaA,EAAuB,CAC3B,KAAA,UAAU,aAAaA,CAAO,CAAA,CAMvC,kBAAyB,CACV,UAAAA,KAAW,KAAK,UAAU,SACjC,KAAK,aAAaA,CAAO,CAC7B,CAMJ,OACIA,EACAxB,EACAC,EACwB,CACxB,OAAO,KAAK,UAAU,OAAOuB,EAASxB,EAAOC,CAAQ,CAAA,CAMzD,QAAQuB,EAA4C,CACzC,OAAA,KAAK,UAAU,eAAeA,CAAO,CAAA,CAMhD,iBAAiBA,EAA8C,CAC3D,GAAI,KAAK,0CAA0C,KAAK,SAAS,EACtD,OAAA,KAAK,UAAU,wBAAwBA,CAAO,EAGzD,MAAM,IAAI,MACN,eAAe,OAAO,KAAK,QAAQ,WAAW,IAAI,OAC9C,KAAK,QAAQ,WAAA,CAChB,+CACL,CAAA,CAGI,0CACJwB,EACiD,CAE7C,OAAAA,aAAqBN,GACrBM,aAAqBH,CAAA,CAO7B,UAA+B,CACpB,OAAA,KAAK,UAAU,SAAS,CAAA,CAOnC,sBAA6B,CAGrB,OAAO,IAAQ,MAAe,eAAK,OACnC,KAAK,8BAA8B,EAGnC,OAAO,OAAU,YACjB,KAAK,gCAAgC,EAGrC,OAAO,QAAW,YAClB,KAAK,wBAAwB,EAG7B,OAAO,OAAU,UACjB,KAAK,gCAAgC,CACzC,CAMJ,+BAAsC,CAElC,IAAI,KAAK,aAAa,KAClB,CAACI,EAA2BC,IAA2B,CAC/C,KAAK,YAELD,EAAQ,QAAQ,IAAI,cAAe,KAAK,UAAU,EAIjDC,EAAA,CAAA,CAEb,CAAA,CAMJ,iCAAwC,CACpC,MAAO,aAAa,QAAQ,IACvBC,IACO,KAAK,aACLA,EAAO,QAAQ,aAAa,EAAI,KAAK,SAAS,GAG3CA,EAEf,CAAA,CAMJ,yBAAgC,CACxB,OAAO,OAAO,KAAQ,KACf,OAAA,cACH,CACIC,EACAC,EACAC,IACC,CACG,KAAK,YACLA,EAAI,iBAAiB,cAAe,KAAK,SAAA,CAAU,CACvD,CAER,CACJ,CAMJ,iCAAwC,CAC3B,SAAA,iBACL,6BACCtD,GAA4B,CACzBA,EAAM,OAAO,aAAa,QAAQ,aAAa,EAC3C,KAAK,SAAS,CAAA,CAE1B,CAAA,CAER"}