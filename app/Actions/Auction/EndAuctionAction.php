<?php

declare(strict_types=1);

namespace App\Actions\Auction;

use App\Domain\Auction\Models\Auction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\BidRepositoryInterface;
use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\User\ValueObjects\UserId;
use Illuminate\Support\Facades\DB;

class EndAuctionAction
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private BidRepositoryInterface $bidRepository,
        private EventDispatcher $eventDispatcher
    ) {}

    public function execute(int $auctionId, ?UserId $adminUserId = null): Auction
    {
        return DB::transaction(function () use ($auctionId, $adminUserId) {
            // Get auction
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $auctionId));

            // Check if auction can be ended
            if (!$auction->status()->isActive()) {
                throw new BusinessRuleException('Only active auctions can be ended');
            }

            // If manually ended by admin, check permissions
            if ($adminUserId && !$auction->duration()->hasEnded()) {
                // This would require checking if user is admin
                // For now, we'll assume the check is done at the controller level
            }

            // Get winning bid
            $winningBid = $this->bidRepository->findWinningBidForAuction($auction->id());
            
            $winnerId = null;
            $finalPrice = null;

            if ($winningBid) {
                $winnerId = $winningBid->bidderId();
                $finalPrice = new Money($winningBid->amount()->amount(), $winningBid->amount()->currency());

                // Check if reserve price is met
                if ($auction->reservePrice()->hasReserve() && 
                    !$auction->reservePrice()->isMetBy($winningBid->amount())) {
                    // Auction ends without winner if reserve not met
                    $winnerId = null;
                    $finalPrice = null;
                }
            }

            // End auction
            $auction->end($winnerId, $finalPrice);

            // Mark all other bids as not winning
            if ($winningBid) {
                $this->bidRepository->markOtherBidsAsNotWinning(
                    $auction->id(),
                    $winningBid->id()
                );
            }

            // Save auction
            $this->auctionRepository->save($auction);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($auction->releaseEvents());

            return $auction;
        });
    }

    public function endExpiredAuctions(): int
    {
        $expiredAuctions = $this->auctionRepository->findExpiredActive();
        $count = 0;

        foreach ($expiredAuctions as $auction) {
            try {
                $this->execute($auction->id);
                $count++;
            } catch (\Exception $e) {
                // Log error but continue with other auctions
                \Log::error('Failed to end expired auction', [
                    'auction_id' => $auction->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $count;
    }
}
