<?php

declare(strict_types=1);

namespace App\Actions\Auction;

use App\Domain\Auction\Models\Auction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Domain\Auction\ValueObjects\AuctionDuration;
use App\Domain\Auction\ValueObjects\ReservePrice;
use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\Shared\ValueObjects\Timestamp;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\UserId;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CreateAuctionAction
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private CategoryRepositoryInterface $categoryRepository,
        private UserRepositoryInterface $userRepository,
        private EventDispatcher $eventDispatcher
    ) {}

    public function execute(array $data): Auction
    {
        $this->validate($data);

        return DB::transaction(function () use ($data) {
            // Get and validate user
            $user = $this->userRepository->findByIdOrFail(
                UserId::fromString($data['user_id'])
            );

            if (!$user->canCreateAuctions()) {
                throw BusinessRuleException::insufficientPermissions('create auction');
            }

            // Check daily auction limit
            $this->checkDailyAuctionLimit($user->id());

            // Get and validate category
            $category = $this->categoryRepository->findByIdOrFail(
                Id::fromString($data['category_id'])
            );

            if (!$category->isActive()) {
                throw new BusinessRuleException('Category is not active');
            }

            // Create value objects
            $startingPrice = new Money($data['starting_price'], $data['currency'] ?? 'USD');
            $reservePrice = isset($data['reserve_price']) 
                ? ReservePrice::fromFloat($data['reserve_price'], $data['currency'] ?? 'USD')
                : ReservePrice::none();

            $startTime = isset($data['start_time']) 
                ? Timestamp::fromString($data['start_time'])
                : Timestamp::now();
            
            $endTime = Timestamp::fromString($data['end_time']);
            $duration = new AuctionDuration($startTime, $endTime);

            $buyoutPrice = isset($data['buyout_price']) 
                ? new Money($data['buyout_price'], $data['currency'] ?? 'USD')
                : null;

            // Create auction
            $auction = new Auction(
                Id::generate(),
                $user->id(),
                $category->id(),
                $data['title'],
                $data['description'],
                $startingPrice,
                $duration,
                $reservePrice,
                $buyoutPrice
            );

            // Set optional fields
            if (isset($data['condition'])) {
                $auction->setCondition($data['condition']);
            }

            if (isset($data['shipping_cost'])) {
                $auction->setShippingCost(new Money($data['shipping_cost'], $data['currency'] ?? 'USD'));
            }

            if (isset($data['auto_extend'])) {
                $auction->setAutoExtend($data['auto_extend'], $data['extend_minutes'] ?? 10);
            }

            // Save auction
            $this->auctionRepository->save($auction);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($auction->releaseEvents());

            return $auction;
        });
    }

    private function validate(array $data): void
    {
        $validator = Validator::make($data, [
            'user_id' => 'required|integer|exists:users,id',
            'category_id' => 'required|integer|exists:categories,id',
            'title' => 'required|string|max:500',
            'description' => 'required|string|max:10000',
            'condition' => 'nullable|string|in:new,like_new,good,fair,poor',
            'starting_price' => 'required|numeric|min:0.01|max:1000000',
            'reserve_price' => 'nullable|numeric|min:0.01|max:1000000',
            'buyout_price' => 'nullable|numeric|min:0.01|max:1000000',
            'currency' => 'nullable|string|in:USD,EUR,GBP,CAD',
            'start_time' => 'nullable|date|after_or_equal:now',
            'end_time' => 'required|date|after:start_time',
            'shipping_cost' => 'nullable|numeric|min:0|max:1000',
            'auto_extend' => 'nullable|boolean',
            'extend_minutes' => 'nullable|integer|min:1|max:60',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withErrors($validator->errors()->toArray());
        }

        // Additional business rule validations
        if (isset($data['reserve_price']) && $data['reserve_price'] < $data['starting_price']) {
            throw ValidationException::singleError(
                'reserve_price',
                'Reserve price must be greater than or equal to starting price'
            );
        }

        if (isset($data['buyout_price']) && $data['buyout_price'] <= $data['starting_price']) {
            throw ValidationException::singleError(
                'buyout_price',
                'Buyout price must be greater than starting price'
            );
        }

        // Validate auction duration
        $startTime = isset($data['start_time']) ? strtotime($data['start_time']) : time();
        $endTime = strtotime($data['end_time']);
        $durationMinutes = ($endTime - $startTime) / 60;

        $minDuration = config('auction.auction.min_duration_minutes', 30);
        $maxDuration = config('auction.auction.max_duration_days', 30) * 24 * 60;

        if ($durationMinutes < $minDuration) {
            throw ValidationException::singleError(
                'end_time',
                "Auction duration must be at least {$minDuration} minutes"
            );
        }

        if ($durationMinutes > $maxDuration) {
            $maxDays = config('auction.auction.max_duration_days', 30);
            throw ValidationException::singleError(
                'end_time',
                "Auction duration cannot exceed {$maxDays} days"
            );
        }
    }

    private function checkDailyAuctionLimit(UserId $userId): void
    {
        $user = $this->userRepository->findByIdOrFail($userId);
        $maxAuctions = $user->role()->getMaxAuctionsPerDay();

        if ($maxAuctions === 0) {
            throw BusinessRuleException::insufficientPermissions('create auctions');
        }

        $todayCount = $this->auctionRepository->countTodayAuctions($userId);

        if ($todayCount >= $maxAuctions) {
            throw BusinessRuleException::maxAuctionsPerDayExceeded($maxAuctions, $todayCount);
        }
    }
}
