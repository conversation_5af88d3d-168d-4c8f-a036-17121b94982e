<?php

declare(strict_types=1);

namespace App\Actions\Auction;

use App\Domain\Auction\Models\Auction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\User\ValueObjects\UserId;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class UpdateAuctionAction
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private EventDispatcher $eventDispatcher
    ) {}

    public function execute(int $auctionId, array $data, UserId $userId): Auction
    {
        $this->validate($data);

        return DB::transaction(function () use ($auctionId, $data, $userId) {
            // Get auction
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $auctionId));

            // Check permissions
            if (!$auction->sellerId()->equals($userId)) {
                throw BusinessRuleException::insufficientPermissions('update auction');
            }

            // Check if auction can be edited
            if (!$auction->status()->canEdit()) {
                throw BusinessRuleException::auctionCannotBeEdited($auction->status()->value());
            }

            // Update fields
            if (isset($data['title'])) {
                $auction->updateTitle($data['title']);
            }

            if (isset($data['description'])) {
                $auction->updateDescription($data['description']);
            }

            if (isset($data['condition'])) {
                $auction->updateCondition($data['condition']);
            }

            if (isset($data['starting_price'])) {
                $startingPrice = new Money($data['starting_price'], $data['currency'] ?? 'USD');
                $auction->updateStartingPrice($startingPrice);
            }

            if (isset($data['reserve_price'])) {
                $reservePrice = $data['reserve_price'] 
                    ? new Money($data['reserve_price'], $data['currency'] ?? 'USD')
                    : null;
                $auction->updateReservePrice($reservePrice);
            }

            if (isset($data['buyout_price'])) {
                $buyoutPrice = $data['buyout_price'] 
                    ? new Money($data['buyout_price'], $data['currency'] ?? 'USD')
                    : null;
                $auction->updateBuyoutPrice($buyoutPrice);
            }

            if (isset($data['shipping_cost'])) {
                $shippingCost = new Money($data['shipping_cost'], $data['currency'] ?? 'USD');
                $auction->updateShippingCost($shippingCost);
            }

            if (isset($data['auto_extend'])) {
                $auction->setAutoExtend($data['auto_extend'], $data['extend_minutes'] ?? 10);
            }

            // Save auction
            $this->auctionRepository->save($auction);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($auction->releaseEvents());

            return $auction;
        });
    }

    private function validate(array $data): void
    {
        $validator = Validator::make($data, [
            'title' => 'sometimes|required|string|max:500',
            'description' => 'sometimes|required|string|max:10000',
            'condition' => 'sometimes|nullable|string|in:new,like_new,good,fair,poor',
            'starting_price' => 'sometimes|required|numeric|min:0.01|max:1000000',
            'reserve_price' => 'sometimes|nullable|numeric|min:0.01|max:1000000',
            'buyout_price' => 'sometimes|nullable|numeric|min:0.01|max:1000000',
            'currency' => 'sometimes|nullable|string|in:USD,EUR,GBP,CAD',
            'shipping_cost' => 'sometimes|nullable|numeric|min:0|max:1000',
            'auto_extend' => 'sometimes|nullable|boolean',
            'extend_minutes' => 'sometimes|nullable|integer|min:1|max:60',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withErrors($validator->errors()->toArray());
        }

        // Additional business rule validations
        if (isset($data['reserve_price'], $data['starting_price']) && 
            $data['reserve_price'] < $data['starting_price']) {
            throw ValidationException::singleError(
                'reserve_price',
                'Reserve price must be greater than or equal to starting price'
            );
        }

        if (isset($data['buyout_price'], $data['starting_price']) && 
            $data['buyout_price'] <= $data['starting_price']) {
            throw ValidationException::singleError(
                'buyout_price',
                'Buyout price must be greater than starting price'
            );
        }
    }
}
