<?php

declare(strict_types=1);

namespace App\Actions\Auction;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Models\Watchlist;
use Illuminate\Support\Facades\DB;

class RemoveFromWatchlistAction
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository
    ) {}

    public function execute(int $auctionId, UserId $userId): void
    {
        DB::transaction(function () use ($auctionId, $userId) {
            // Find watchlist entry
            $watchlist = Watchlist::where('user_id', $userId->value())
                ->where('auction_id', $auctionId)
                ->first();

            if (!$watchlist) {
                throw new BusinessRuleException('Auction is not in your watchlist');
            }

            // Remove from watchlist
            $watchlist->delete();

            // Update auction watchers count
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $auctionId));
            $this->auctionRepository->decrementWatchersCount($auction->id());
        });
    }
}
