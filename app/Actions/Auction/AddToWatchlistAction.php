<?php

declare(strict_types=1);

namespace App\Actions\Auction;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Models\Watchlist;
use Illuminate\Support\Facades\DB;

class AddToWatchlistAction
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository
    ) {}

    public function execute(int $auctionId, UserId $userId): Watchlist
    {
        return DB::transaction(function () use ($auctionId, $userId) {
            // Get auction
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $auctionId));

            // Check if user can watch this auction
            if ($auction->sellerId()->equals($userId)) {
                throw new BusinessRuleException('Cannot watch your own auction');
            }

            // Check if auction is watchable
            if (!$auction->status()->isActive() && !$auction->status()->isScheduled()) {
                throw new BusinessRuleException('Can only watch active or scheduled auctions');
            }

            // Check if already watching
            $existingWatch = Watchlist::where('user_id', $userId->value())
                ->where('auction_id', $auctionId)
                ->first();

            if ($existingWatch) {
                throw new BusinessRuleException('Auction is already in your watchlist');
            }

            // Create watchlist entry
            $watchlist = Watchlist::create([
                'user_id' => $userId->value(),
                'auction_id' => $auctionId,
            ]);

            // Update auction watchers count
            $this->auctionRepository->incrementWatchersCount($auction->id());

            return $watchlist;
        });
    }
}
