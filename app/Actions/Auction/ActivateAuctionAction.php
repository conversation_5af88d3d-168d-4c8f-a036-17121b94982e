<?php

declare(strict_types=1);

namespace App\Actions\Auction;

use App\Domain\Auction\Models\Auction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use Illuminate\Support\Facades\DB;

class ActivateAuctionAction
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private EventDispatcher $eventDispatcher
    ) {}

    public function execute(int $auctionId, UserId $userId): Auction
    {
        return DB::transaction(function () use ($auctionId, $userId) {
            // Get auction
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $auctionId));

            // Check permissions
            if (!$auction->sellerId()->equals($userId)) {
                throw BusinessRuleException::insufficientPermissions('activate auction');
            }

            // Validate auction can be activated
            if (!$auction->status()->isDraft() && !$auction->status()->isScheduled()) {
                throw new BusinessRuleException('Only draft or scheduled auctions can be activated');
            }

            // Check if auction has ended
            if ($auction->duration()->hasEnded()) {
                throw BusinessRuleException::auctionHasEnded($auctionId);
            }

            // Validate auction has required data
            $this->validateAuctionReadiness($auction);

            // Activate auction
            $auction->activate();

            // Save auction
            $this->auctionRepository->save($auction);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($auction->releaseEvents());

            return $auction;
        });
    }

    private function validateAuctionReadiness(Auction $auction): void
    {
        // Check if auction has at least one image
        if ($auction->getImagesCount() === 0) {
            throw new BusinessRuleException('Auction must have at least one image before activation');
        }

        // Check if auction has proper description
        if (strlen($auction->description()) < 50) {
            throw new BusinessRuleException('Auction description must be at least 50 characters long');
        }

        // Check if starting price is set
        if ($auction->startingPrice()->isZero()) {
            throw new BusinessRuleException('Auction must have a starting price greater than zero');
        }

        // Additional validation rules can be added here
    }
}
