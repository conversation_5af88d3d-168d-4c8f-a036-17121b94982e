<?php

declare(strict_types=1);

namespace App\Actions\Auction;

use App\Domain\Auction\Models\AuctionImage;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\ImageUrl;
use App\Domain\User\ValueObjects\UserId;
use App\Infrastructure\Services\ImageUploadService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;

class UploadAuctionImagesAction
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private ImageUploadService $imageUploadService
    ) {}

    public function execute(int $auctionId, array $files, UserId $userId): array
    {
        $this->validate($files);

        return DB::transaction(function () use ($auctionId, $files, $userId) {
            // Get auction
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $auctionId));

            // Check permissions
            if (!$auction->sellerId()->equals($userId)) {
                throw BusinessRuleException::insufficientPermissions('upload images');
            }

            // Check if auction can be edited
            if (!$auction->status()->canEdit()) {
                throw BusinessRuleException::auctionCannotBeEdited($auction->status()->value());
            }

            // Check image limit
            $maxImages = config('auction.auction.max_images_per_auction', 10);
            $currentImageCount = $auction->getImagesCount();
            
            if ($currentImageCount + count($files) > $maxImages) {
                throw new BusinessRuleException("Cannot upload more than {$maxImages} images per auction");
            }

            $uploadedImages = [];
            $sortOrder = $currentImageCount;

            foreach ($files as $file) {
                // Upload and process image
                $imageData = $this->imageUploadService->uploadAuctionImage(
                    $file,
                    $auctionId,
                    $sortOrder,
                    $currentImageCount === 0 && $sortOrder === 0 // First image is primary
                );

                // Create auction image
                $auctionImage = AuctionImage::upload(
                    Id::generate(),
                    $auction->id(),
                    $imageData['filename'],
                    $imageData['path'],
                    new ImageUrl($imageData['url']),
                    $imageData['original_name'],
                    $sortOrder
                );

                // Set metadata
                $auctionImage->updateMetadata(
                    $imageData['original_name'],
                    $imageData['size_bytes'],
                    $imageData['mime_type'],
                    $imageData['width'],
                    $imageData['height']
                );

                if (isset($imageData['thumbnail_url'])) {
                    $auctionImage->setThumbnail(new ImageUrl($imageData['thumbnail_url']));
                }

                if ($imageData['is_primary']) {
                    $auctionImage->markAsPrimary();
                }

                // Add to auction
                $auction->addImage($auctionImage);
                
                $uploadedImages[] = $auctionImage;
                $sortOrder++;
            }

            // Save auction
            $this->auctionRepository->save($auction);

            return $uploadedImages;
        });
    }

    private function validate(array $files): void
    {
        if (empty($files)) {
            throw ValidationException::singleError('files', 'At least one file must be provided');
        }

        foreach ($files as $index => $file) {
            if (!$file instanceof UploadedFile) {
                throw ValidationException::singleError("files.{$index}", 'Invalid file upload');
            }

            if (!$file->isValid()) {
                throw ValidationException::singleError("files.{$index}", 'File upload failed');
            }

            // Additional validation is handled by ImageUploadService
        }
    }
}
