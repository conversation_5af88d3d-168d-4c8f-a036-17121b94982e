<?php

declare(strict_types=1);

namespace App\Actions\Bid;

use App\Domain\Auction\Models\Bid;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\BidRepositoryInterface;
use App\Domain\Auction\ValueObjects\BidAmount;
use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\UserId;
use App\Infrastructure\Services\WebSocketService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PlaceBidAction
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private BidRepositoryInterface $bidRepository,
        private UserRepositoryInterface $userRepository,
        private EventDispatcher $eventDispatcher,
        private WebSocketService $webSocketService
    ) {}

    public function execute(array $data): Bid
    {
        $this->validate($data);

        return DB::transaction(function () use ($data) {
            // Get and validate user
            $user = $this->userRepository->findByIdOrFail(
                UserId::fromString($data['user_id'])
            );

            if (!$user->isActive() || !$user->isEmailVerified()) {
                throw BusinessRuleException::userNotVerified();
            }

            // Get and validate auction
            $auction = $this->auctionRepository->findByIdOrFail(
                Id::fromString($data['auction_id'])
            );

            // Business rule validations
            $this->validateBidRules($auction, $user->id(), $data);

            // Create bid amount
            $bidAmount = BidAmount::fromFloat($data['amount'], $data['currency'] ?? 'USD');
            $maxBid = isset($data['max_bid']) 
                ? new Money($data['max_bid'], $data['currency'] ?? 'USD')
                : null;

            // Determine bid type
            $bidType = $data['bid_type'] ?? 'manual';
            if ($maxBid && $bidType === 'manual') {
                $bidType = 'proxy';
            }

            // Create bid
            $bid = Bid::place(
                Id::generate(),
                $auction->id(),
                $user->id(),
                $bidAmount,
                $bidType,
                $maxBid,
                $data['ip_address'] ?? null,
                $data['user_agent'] ?? null
            );

            // Handle proxy bidding logic
            if ($bidType === 'proxy') {
                $this->handleProxyBidding($auction, $bid);
            }

            // Update auction current bid and mark bid as winning
            $this->updateAuctionBid($auction, $bid);

            // Handle auto-extension if enabled
            if ($auction->autoExtend() && $this->shouldAutoExtend($auction)) {
                $auction->extendDuration($auction->extendMinutes());
            }

            // Save entities
            $this->bidRepository->save($bid);
            $this->auctionRepository->save($auction);

            // Mark other bids as outbid
            $this->markOtherBidsAsOutbid($auction, $bid);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($bid->releaseEvents());
            $this->eventDispatcher->dispatchAll($auction->releaseEvents());

            // Real-time updates
            $this->broadcastBidUpdate($auction, $bid, $user);

            return $bid;
        });
    }

    private function validate(array $data): void
    {
        $validator = Validator::make($data, [
            'user_id' => 'required|integer|exists:users,id',
            'auction_id' => 'required|integer|exists:auctions,id',
            'amount' => 'required|numeric|min:0.01|max:1000000',
            'max_bid' => 'nullable|numeric|min:0.01|max:1000000',
            'currency' => 'nullable|string|in:USD,EUR,GBP,CAD',
            'bid_type' => 'nullable|string|in:manual,proxy',
            'ip_address' => 'nullable|ip',
            'user_agent' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withErrors($validator->errors()->toArray());
        }

        // Validate max_bid is greater than amount for proxy bids
        if (isset($data['max_bid']) && $data['max_bid'] <= $data['amount']) {
            throw ValidationException::singleError(
                'max_bid',
                'Maximum bid must be greater than bid amount'
            );
        }
    }

    private function validateBidRules($auction, UserId $userId, array $data): void
    {
        // Check if user can bid on this auction
        if ($auction->sellerId()->equals($userId)) {
            throw BusinessRuleException::cannotBidOnOwnAuction();
        }

        // Check if auction is active
        if (!$auction->status()->canBid()) {
            throw BusinessRuleException::auctionNotActive($auction->id()->value());
        }

        // Check if auction has ended
        if ($auction->duration()->hasEnded()) {
            throw BusinessRuleException::auctionHasEnded($auction->id()->value());
        }

        // Check minimum bid amount
        $currentBid = $auction->currentBid();
        $minimumBid = $currentBid->amount() > 0 
            ? $currentBid->add($auction->bidIncrement())
            : $auction->startingPrice();

        if ($data['amount'] < $minimumBid->amount()) {
            throw BusinessRuleException::bidTooLow($data['amount'], $minimumBid->amount());
        }

        // Check rate limiting
        $this->checkBidRateLimit($userId, $auction->id());
    }

    private function handleProxyBidding($auction, $bid): void
    {
        // Get other proxy bids that could compete
        $competingProxyBids = $this->bidRepository->findProxyBidsForAuction(
            $auction->id(),
            $bid->amount()
        );

        foreach ($competingProxyBids as $competingBid) {
            if ($competingBid->canAutoBid($bid->amount())) {
                $autoBidAmount = $competingBid->getNextAutoBidAmount(
                    $bid->amount(),
                    $auction->bidIncrement()
                );

                if ($autoBidAmount) {
                    // Create auto bid
                    $autoBid = Bid::place(
                        Id::generate(),
                        $auction->id(),
                        $competingBid->bidderId(),
                        $autoBidAmount,
                        'auto',
                        null,
                        null,
                        'Auto-bid system'
                    );

                    $this->bidRepository->save($autoBid);
                }
            }
        }
    }

    private function updateAuctionBid($auction, $bid): void
    {
        // Update auction current bid
        $auction->updateCurrentBid($bid->amount());
        $auction->incrementBidsCount();

        // Mark bid as winning
        $bid->markAsWinning();
    }

    private function markOtherBidsAsOutbid($auction, $winningBid): void
    {
        $outbidBids = $this->bidRepository->findBidsToMarkAsOutbid(
            $auction->id(),
            $winningBid->id()
        );

        foreach ($outbidBids as $outbidBid) {
            $outbidBid->markAsOutbid();
            $this->bidRepository->save($outbidBid);
        }
    }

    private function shouldAutoExtend($auction): bool
    {
        $remainingMinutes = $auction->duration()->getRemainingMinutes();
        return $remainingMinutes <= $auction->extendMinutes();
    }

    private function checkBidRateLimit(UserId $userId, Id $auctionId): void
    {
        $recentBids = $this->bidRepository->findRecentBidsForUser($userId, 1); // Last 1 minute
        $maxBidsPerMinute = config('auction.security.rate_limiting.bid_attempts_per_minute', 10);

        if ($recentBids->count() >= $maxBidsPerMinute) {
            throw new BusinessRuleException('Too many bid attempts. Please wait before bidding again.');
        }
    }

    private function broadcastBidUpdate($auction, $bid, $user): void
    {
        $this->webSocketService->broadcastNewBid(
            $auction->id()->value(),
            $bid->bidderId()->value(),
            $bid->amount()->amount(),
            $auction->bidsCount(),
            $user->name()
        );
    }
}
