<?php

declare(strict_types=1);

namespace App\Actions\Bid;

use App\Domain\Auction\Models\Bid;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\BidRepositoryInterface;
use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use Illuminate\Support\Facades\DB;

class CancelBidAction
{
    public function __construct(
        private BidRepositoryInterface $bidRepository,
        private AuctionRepositoryInterface $auctionRepository,
        private EventDispatcher $eventDispatcher
    ) {}

    public function execute(int $bidId, UserId $userId, ?string $reason = null): Bid
    {
        return DB::transaction(function () use ($bidId, $userId, $reason) {
            // Get bid
            $bid = $this->bidRepository->findByIdOrFail(Id::fromString((string) $bidId));

            // Get auction
            $auction = $this->auctionRepository->findByIdOrFail($bid->auctionId());

            // Check permissions
            if (!$bid->bidderId()->equals($userId)) {
                throw BusinessRuleException::insufficientPermissions('cancel bid');
            }

            // Validate bid can be cancelled
            $this->validateCancellation($bid, $auction);

            // Cancel bid
            $bid->invalidate($reason ?? 'Cancelled by user');

            // If this was the winning bid, find new winner
            if ($bid->isWinning()) {
                $this->findNewWinningBid($auction, $bid);
            }

            // Save entities
            $this->bidRepository->save($bid);
            $this->auctionRepository->save($auction);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($bid->releaseEvents());
            $this->eventDispatcher->dispatchAll($auction->releaseEvents());

            return $bid;
        });
    }

    private function validateCancellation($bid, $auction): void
    {
        // Cannot cancel if auction has ended
        if ($auction->status()->isEnded()) {
            throw new BusinessRuleException('Cannot cancel bids on ended auctions');
        }

        // Cannot cancel winning bids in active auctions (business rule)
        if ($auction->status()->isActive() && $bid->isWinning()) {
            throw new BusinessRuleException('Cannot cancel winning bid in active auction');
        }

        // Cannot cancel already invalid bids
        if (!$bid->isValid()) {
            throw new BusinessRuleException('Bid is already cancelled');
        }
    }

    private function findNewWinningBid($auction, $cancelledBid): void
    {
        // Find the next highest valid bid
        $nextHighestBid = $this->bidRepository->findNextHighestBidForAuction(
            $auction->id(),
            $cancelledBid->id()
        );

        if ($nextHighestBid) {
            // Mark as winning
            $nextHighestBid->markAsWinning();
            $auction->updateCurrentBid($nextHighestBid->amount());
            $this->bidRepository->save($nextHighestBid);
        } else {
            // No other bids, reset to starting price
            $auction->resetCurrentBid();
        }
    }
}
