<?php

declare(strict_types=1);

namespace App\Actions\User;

use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\User\Models\User;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\PhoneNumber;
use App\Domain\User\ValueObjects\UserId;
use App\Infrastructure\Services\ImageUploadService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class UpdateUserProfileAction
{
    public function __construct(
        private UserRepositoryInterface $userRepository,
        private EventDispatcher $eventDispatcher,
        private ImageUploadService $imageUploadService
    ) {}

    public function execute(UserId $userId, array $data, ?UploadedFile $avatarFile = null): User
    {
        $this->validate($data);

        return DB::transaction(function () use ($userId, $data, $avatarFile) {
            // Get user
            $user = $this->userRepository->findByIdOrFail($userId);

            // Handle avatar upload
            $avatarUrl = null;
            if ($avatarFile) {
                $avatarData = $this->imageUploadService->uploadAvatar($avatarFile, $userId->value());
                $avatarUrl = $avatarData['url'];
            }

            // Update profile
            $phone = isset($data['phone']) ? new PhoneNumber($data['phone']) : null;
            
            $user->updateProfile(
                $data['name'] ?? $user->name(),
                $phone,
                $avatarUrl ?? $user->avatar()
            );

            // Save user
            $this->userRepository->save($user);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($user->releaseEvents());

            return $user;
        });
    }

    private function validate(array $data): void
    {
        $validator = Validator::make($data, [
            'name' => 'sometimes|required|string|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'bio' => 'sometimes|nullable|string|max:1000',
            'location' => 'sometimes|nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withErrors($validator->errors()->toArray());
        }
    }
}
