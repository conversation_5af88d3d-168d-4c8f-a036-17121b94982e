<?php

declare(strict_types=1);

namespace App\Actions\User;

use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\User\Models\User;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\Email;
use App\Domain\User\ValueObjects\UserId;
use App\Domain\User\ValueObjects\UserRole;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class RegisterUserAction
{
    public function __construct(
        private UserRepositoryInterface $userRepository,
        private EventDispatcher $eventDispatcher
    ) {}

    public function execute(array $data): User
    {
        $this->validate($data);

        return DB::transaction(function () use ($data) {
            // Check if email already exists
            $email = new Email($data['email']);
            if ($this->userRepository->emailExists($email)) {
                throw ValidationException::singleError('email', 'Email address is already registered');
            }

            // Create user
            $user = User::register(
                UserId::generate(),
                $data['name'],
                $email,
                Hash::make($data['password']),
                isset($data['role']) ? new UserRole($data['role']) : UserRole::user()
            );

            // Set additional fields if provided
            if (isset($data['phone'])) {
                $user->updateProfile($data['name'], new PhoneNumber($data['phone']));
            }

            // Save user
            $this->userRepository->save($user);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($user->releaseEvents());

            return $user;
        });
    }

    private function validate(array $data): void
    {
        $validator = Validator::make($data, [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'role' => 'nullable|string|in:user,seller,premium_seller',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withErrors($validator->errors()->toArray());
        }

        // Additional password validation
        if (isset($data['password'])) {
            $this->validatePassword($data['password']);
        }
    }

    private function validatePassword(string $password): void
    {
        $errors = [];

        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        }

        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }

        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }

        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }

        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }

        if (!empty($errors)) {
            throw ValidationException::withErrors(['password' => $errors]);
        }
    }
}
