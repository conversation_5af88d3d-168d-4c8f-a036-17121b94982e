<?php

declare(strict_types=1);

namespace App\Actions\Category;

use App\Domain\Auction\Models\Category;
use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Slug;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CreateCategoryAction
{
    public function __construct(
        private CategoryRepositoryInterface $categoryRepository
    ) {}

    public function execute(array $data): Category
    {
        $this->validate($data);

        return DB::transaction(function () use ($data) {
            // Create slug from name
            $slug = Slug::fromString($data['name']);

            // Check if slug already exists
            if ($this->categoryRepository->slugExists($slug)) {
                throw ValidationException::singleError('name', 'A category with this name already exists');
            }

            // Validate parent category if provided
            $parentId = null;
            if (isset($data['parent_id'])) {
                $parentCategory = $this->categoryRepository->findByIdOrFail(
                    Id::fromString($data['parent_id'])
                );
                
                if (!$parentCategory->isActive()) {
                    throw ValidationException::singleError('parent_id', 'Parent category must be active');
                }
                
                $parentId = $parentCategory->id();
            }

            // Create category
            $category = Category::create(
                Id::generate(),
                $data['name'],
                $data['description'] ?? null,
                $parentId,
                $data['sort_order'] ?? 0
            );

            // Set optional fields
            if (isset($data['icon'])) {
                $category->setIcon($data['icon']);
            }

            if (isset($data['image'])) {
                $category->setImage($data['image']);
            }

            if (isset($data['meta_title']) || isset($data['meta_description'])) {
                $category->updateSeo(
                    $data['meta_title'] ?? null,
                    $data['meta_description'] ?? null
                );
            }

            // Save category
            $this->categoryRepository->save($category);

            return $category;
        });
    }

    private function validate(array $data): void
    {
        $validator = Validator::make($data, [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|integer|exists:categories,id',
            'sort_order' => 'nullable|integer|min:0',
            'icon' => 'nullable|string|max:100',
            'image' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withErrors($validator->errors()->toArray());
        }
    }
}
