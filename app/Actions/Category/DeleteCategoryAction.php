<?php

declare(strict_types=1);

namespace App\Actions\Category;

use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\ValueObjects\Id;
use Illuminate\Support\Facades\DB;

class DeleteCategoryAction
{
    public function __construct(
        private CategoryRepositoryInterface $categoryRepository
    ) {}

    public function execute(int $categoryId): void
    {
        DB::transaction(function () use ($categoryId) {
            // Get category
            $category = $this->categoryRepository->findByIdOrFail(Id::fromString((string) $categoryId));

            // Check if category has children
            if ($this->categoryRepository->hasChildren($category->id())) {
                throw new BusinessRuleException('Cannot delete category that has subcategories');
            }

            // Check if category has active auctions
            $auctionCount = $this->categoryRepository->countActiveAuctions($category->id());
            if ($auctionCount > 0) {
                throw BusinessRuleException::categoryHasActiveAuctions($categoryId, $auctionCount);
            }

            // Delete category
            $this->categoryRepository->delete($category);
        });
    }
}
