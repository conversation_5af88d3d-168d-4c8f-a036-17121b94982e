<?php

declare(strict_types=1);

namespace App\Actions\Payment;

use App\Domain\Payment\Models\Payment;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\User\ValueObjects\UserId;
use App\Infrastructure\Services\PaymentService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class RefundPaymentAction
{
    public function __construct(
        private PaymentRepositoryInterface $paymentRepository,
        private PaymentService $paymentService,
        private EventDispatcher $eventDispatcher
    ) {}

    public function execute(int $paymentId, array $data, UserId $adminUserId): Payment
    {
        $this->validate($data);

        return DB::transaction(function () use ($paymentId, $data, $adminUserId) {
            // Get payment
            $payment = $this->paymentRepository->findByIdOrFail(Id::fromString((string) $paymentId));

            // Validate refund rules
            $this->validateRefundRules($payment, $data);

            // Calculate refund amount
            $refundAmount = new Money($data['amount'], $payment->amount()->currency());

            // Validate refund amount
            if (!$payment->canRefund($refundAmount)) {
                throw BusinessRuleException::refundAmountExceedsPayment(
                    $refundAmount->amount(),
                    $payment->getRemainingRefundableAmount()->amount()
                );
            }

            // Process refund with Stripe
            if ($payment->stripePaymentIntentId()) {
                $refundResult = $this->paymentService->createRefund(
                    $payment->stripePaymentIntentId(),
                    $refundAmount,
                    $data['reason'] ?? 'Refund requested'
                );

                if ($refundResult['status'] !== 'succeeded') {
                    throw new BusinessRuleException('Refund processing failed');
                }
            }

            // Update payment
            $payment->refund($refundAmount);
            $payment->updateMetadata([
                'refund_reason' => $data['reason'] ?? 'Refund requested',
                'refunded_by' => $adminUserId->value(),
                'refund_date' => now()->toISOString(),
            ]);

            // Save payment
            $this->paymentRepository->save($payment);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($payment->releaseEvents());

            return $payment;
        });
    }

    private function validate(array $data): void
    {
        $validator = Validator::make($data, [
            'amount' => 'required|numeric|min:0.01|max:1000000',
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withErrors($validator->errors()->toArray());
        }
    }

    private function validateRefundRules($payment, array $data): void
    {
        // Check if payment can be refunded
        if (!$payment->status()->canBeRefunded()) {
            throw new BusinessRuleException('This payment cannot be refunded');
        }

        // Check refund window
        $refundWindowDays = config('auction.payment.refund_window_days', 14);
        $refundDeadline = $payment->processedAt()->addDays($refundWindowDays);
        
        if (now() > $refundDeadline) {
            throw new BusinessRuleException('Refund window has expired');
        }

        // Check if refund amount is valid
        $remainingAmount = $payment->getRemainingRefundableAmount();
        if ($data['amount'] > $remainingAmount->amount()) {
            throw BusinessRuleException::refundAmountExceedsPayment(
                $data['amount'],
                $remainingAmount->amount()
            );
        }
    }
}
