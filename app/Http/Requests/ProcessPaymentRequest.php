<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProcessPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'auction_id' => 'required|integer|exists:auctions,id',
            'amount' => 'required|numeric|min:0.01|max:1000000',
            'currency' => 'nullable|string|in:USD,EUR,GBP,CAD',
            'payment_method' => 'required|array',
            'payment_method.type' => 'required|string|in:card,bank_transfer,paypal',
            'payment_method.brand' => 'required_if:payment_method.type,card|string',
            'payment_method.last_four' => 'required_if:payment_method.type,card|string|size:4',
            'payment_method.exp_month' => 'required_if:payment_method.type,card|integer|min:1|max:12',
            'payment_method.exp_year' => 'required_if:payment_method.type,card|integer|min:' . date('Y'),
            'payment_method.bank_name' => 'required_if:payment_method.type,bank_transfer|string',
            'payment_method.account_last4' => 'required_if:payment_method.type,bank_transfer|string|size:4',
            'payment_method.email' => 'required_if:payment_method.type,paypal|email',
            'payment_method_id' => 'sometimes|required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'auction_id.required' => 'Auction ID is required.',
            'auction_id.exists' => 'The selected auction does not exist.',
            'amount.required' => 'Payment amount is required.',
            'amount.min' => 'Payment amount must be at least $0.01.',
            'amount.max' => 'Payment amount cannot exceed $1,000,000.',
            'payment_method.required' => 'Payment method is required.',
            'payment_method.type.required' => 'Payment method type is required.',
            'payment_method.type.in' => 'Invalid payment method type.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if ($this->has('auction_id')) {
                $auction = \App\Models\Auction::find($this->auction_id);
                
                if ($auction) {
                    // Check if auction has ended
                    if ($auction->status !== 'ended') {
                        $validator->errors()->add('auction_id', 'Payment can only be made for ended auctions.');
                    }

                    // Check if user is the winner
                    if ($auction->winner_id !== auth()->id()) {
                        $validator->errors()->add('auction_id', 'Only the auction winner can make payment.');
                    }

                    // Check if payment amount matches final price
                    if ($this->amount != $auction->final_price) {
                        $validator->errors()->add('amount', 'Payment amount must match the auction final price.');
                    }

                    // Check payment deadline
                    $paymentDueDays = config('auction.payment.payment_due_days', 3);
                    $paymentDeadline = $auction->actual_end_time->addDays($paymentDueDays);
                    
                    if (now() > $paymentDeadline) {
                        $validator->errors()->add('auction_id', 'Payment deadline has passed.');
                    }

                    // Check if payment already exists
                    $existingPayment = \App\Models\Payment::where('auction_id', $auction->id)
                        ->where('user_id', auth()->id())
                        ->where('status', 'succeeded')
                        ->first();

                    if ($existingPayment) {
                        $validator->errors()->add('auction_id', 'Payment has already been processed for this auction.');
                    }
                }
            }
        });
    }
}
