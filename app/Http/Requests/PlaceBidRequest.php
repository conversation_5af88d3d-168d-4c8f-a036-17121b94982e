<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PlaceBidRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isActive() && auth()->user()->isEmailVerified();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'auction_id' => 'required|integer|exists:auctions,id',
            'amount' => 'required|numeric|min:0.01|max:1000000',
            'max_bid' => 'nullable|numeric|min:0.01|max:1000000|gt:amount',
            'currency' => 'nullable|string|in:USD,EUR,GBP,CAD',
            'bid_type' => 'nullable|string|in:manual,proxy',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'auction_id.required' => 'Auction ID is required.',
            'auction_id.exists' => 'The selected auction does not exist.',
            'amount.required' => 'Bid amount is required.',
            'amount.min' => 'Bid amount must be at least $0.01.',
            'amount.max' => 'Bid amount cannot exceed $1,000,000.',
            'max_bid.gt' => 'Maximum bid must be greater than the bid amount.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if ($this->has('auction_id')) {
                $auction = \App\Models\Auction::find($this->auction_id);
                
                if ($auction) {
                    // Check if user is trying to bid on their own auction
                    if ($auction->user_id === auth()->id()) {
                        $validator->errors()->add('auction_id', 'You cannot bid on your own auction.');
                    }

                    // Check if auction is active
                    if ($auction->status !== 'active') {
                        $validator->errors()->add('auction_id', 'This auction is not accepting bids.');
                    }

                    // Check if auction has ended
                    if ($auction->end_time <= now()) {
                        $validator->errors()->add('auction_id', 'This auction has ended.');
                    }

                    // Check minimum bid amount
                    $currentBid = $auction->current_bid ?? $auction->starting_price;
                    $minimumBid = $currentBid + ($auction->bid_increment ?? 1.00);

                    if ($this->amount < $minimumBid) {
                        $validator->errors()->add('amount', "Minimum bid amount is $" . number_format($minimumBid, 2));
                    }
                }
            }
        });
    }
}
