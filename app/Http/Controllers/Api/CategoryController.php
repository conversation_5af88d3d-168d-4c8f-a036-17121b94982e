<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Actions\Category\CreateCategoryAction;
use App\Actions\Category\DeleteCategoryAction;
use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreateCategoryRequest;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\AuctionCollection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CategoryController extends Controller
{
    public function __construct(
        private CategoryRepositoryInterface $categoryRepository
    ) {}

    /**
     * Display a listing of categories
     */
    public function index(Request $request): JsonResponse
    {
        $includeHierarchy = $request->boolean('hierarchy', false);
        $includeStats = $request->boolean('include_stats', false);

        if ($includeHierarchy) {
            $categories = $this->categoryRepository->findHierarchy();
        } elseif ($includeStats) {
            $categories = $this->categoryRepository->findWithAuctionCounts();
        } else {
            $categories = $this->categoryRepository->findActive();
        }

        return response()->json([
            'data' => CategoryResource::collection($categories),
        ]);
    }

    /**
     * Store a newly created category (admin only)
     */
    public function store(CreateCategoryRequest $request, CreateCategoryAction $action): JsonResponse
    {
        try {
            $category = $action->execute($request->validated());

            return response()->json([
                'message' => 'Category created successfully',
                'data' => new CategoryResource($category),
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create category',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Display the specified category
     */
    public function show(int $id): JsonResponse
    {
        try {
            $category = $this->categoryRepository->findByIdOrFail(Id::fromString((string) $id));

            return response()->json([
                'data' => new CategoryResource($category),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Category not found',
            ], Response::HTTP_NOT_FOUND);
        }
    }

    /**
     * Update the specified category (admin only)
     */
    public function update(int $id, CreateCategoryRequest $request): JsonResponse
    {
        try {
            $category = $this->categoryRepository->findByIdOrFail(Id::fromString((string) $id));
            
            // Update category logic would go here
            // For now, just return the category
            
            return response()->json([
                'message' => 'Category updated successfully',
                'data' => new CategoryResource($category),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update category',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Remove the specified category (admin only)
     */
    public function destroy(int $id, DeleteCategoryAction $action): JsonResponse
    {
        try {
            $action->execute($id);

            return response()->json([
                'message' => 'Category deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete category',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get auctions in a category
     */
    public function auctions(int $id, Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status', 'active');

        try {
            $auctions = $this->categoryRepository->findAuctionsByCategory(
                Id::fromString((string) $id),
                $status,
                $perPage
            );

            return response()->json(new AuctionCollection($auctions));
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get category auctions',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get popular categories
     */
    public function popular(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 10), 20);
        $categories = $this->categoryRepository->findPopular($limit);

        return response()->json([
            'data' => CategoryResource::collection($categories),
        ]);
    }

    /**
     * Search categories
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
        ]);

        $categories = $this->categoryRepository->searchByName($request->query);

        return response()->json([
            'data' => CategoryResource::collection($categories),
        ]);
    }

    /**
     * Reorder categories (admin only)
     */
    public function reorder(Request $request): JsonResponse
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|integer|exists:categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        try {
            $categoryOrders = collect($request->categories)
                ->pluck('sort_order', 'id')
                ->toArray();

            $this->categoryRepository->updateSortOrders($categoryOrders);

            return response()->json([
                'message' => 'Categories reordered successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to reorder categories',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }
}
