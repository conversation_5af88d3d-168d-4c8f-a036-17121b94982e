<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Actions\Payment\ProcessAuctionPaymentAction;
use App\Actions\Payment\RefundPaymentAction;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Requests\ProcessPaymentRequest;
use App\Http\Requests\RefundPaymentRequest;
use App\Http\Resources\PaymentResource;
use App\Http\Resources\PaymentCollection;
use App\Infrastructure\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PaymentController extends Controller
{
    public function __construct(
        private PaymentRepositoryInterface $paymentRepository,
        private PaymentService $paymentService
    ) {}

    /**
     * Display user's payments
     */
    public function index(Request $request): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $perPage = min($request->get('per_page', 15), 50);
        $payments = $this->paymentRepository->findByUser($userId, $perPage);

        return response()->json(new PaymentCollection($payments));
    }

    /**
     * Process auction payment
     */
    public function store(ProcessPaymentRequest $request, ProcessAuctionPaymentAction $action): JsonResponse
    {
        try {
            $data = $request->validated();
            $data['user_id'] = auth()->id();

            $payment = $action->execute($data);

            return response()->json([
                'message' => 'Payment processed successfully',
                'data' => new PaymentResource($payment),
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to process payment',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Display the specified payment
     */
    public function show(int $id): JsonResponse
    {
        try {
            $payment = $this->paymentRepository->findByIdOrFail(Id::fromString((string) $id));
            $userId = UserId::fromString((string) auth()->id());

            // Check if user can view this payment
            if (!$payment->userId()->equals($userId) && !auth()->user()->isAdmin()) {
                return response()->json([
                    'message' => 'Unauthorized',
                ], Response::HTTP_FORBIDDEN);
            }

            return response()->json([
                'data' => new PaymentResource($payment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Payment not found',
            ], Response::HTTP_NOT_FOUND);
        }
    }

    /**
     * Create payment intent for auction
     */
    public function createPaymentIntent(Request $request): JsonResponse
    {
        $request->validate([
            'auction_id' => 'required|integer|exists:auctions,id',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'nullable|string|in:USD,EUR,GBP,CAD',
        ]);

        try {
            $userId = UserId::fromString((string) auth()->id());
            
            $paymentIntent = $this->paymentService->createPaymentIntent(
                new \App\Domain\Payment\ValueObjects\PaymentAmount(
                    new \App\Domain\Shared\ValueObjects\Money($request->amount, $request->currency ?? 'USD'),
                    0.029 // Platform fee rate
                ),
                \App\Domain\Payment\ValueObjects\PaymentMethod::card('unknown', '****', 1, 2025),
                $userId,
                Id::fromString($request->auction_id),
                ['type' => 'auction_payment']
            );

            return response()->json([
                'data' => $paymentIntent,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create payment intent',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Confirm payment intent
     */
    public function confirmPaymentIntent(Request $request): JsonResponse
    {
        $request->validate([
            'payment_intent_id' => 'required|string',
            'payment_method_id' => 'required|string',
        ]);

        try {
            $result = $this->paymentService->confirmPaymentIntent(
                $request->payment_intent_id,
                $request->payment_method_id
            );

            return response()->json([
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to confirm payment',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Refund a payment (admin only)
     */
    public function refund(int $id, RefundPaymentRequest $request, RefundPaymentAction $action): JsonResponse
    {
        try {
            $adminUserId = UserId::fromString((string) auth()->id());
            $payment = $action->execute($id, $request->validated(), $adminUserId);

            return response()->json([
                'message' => 'Payment refunded successfully',
                'data' => new PaymentResource($payment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to refund payment',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get payment statistics
     */
    public function statistics(): JsonResponse
    {
        if (!auth()->user()->isAdmin()) {
            return response()->json([
                'message' => 'Unauthorized',
            ], Response::HTTP_FORBIDDEN);
        }

        $stats = $this->paymentRepository->getPaymentStatistics();

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Get user's payment statistics
     */
    public function userStatistics(): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $stats = $this->paymentRepository->getUserPaymentStatistics($userId);

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Handle Stripe webhook
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->getContent();
            $signature = $request->header('Stripe-Signature');

            // Verify webhook signature
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                config('services.stripe.webhook_secret')
            );

            // Handle the event
            $this->paymentService->handleWebhook($event->toArray());

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Webhook handling failed',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get payment methods for user
     */
    public function paymentMethods(): JsonResponse
    {
        $paymentMethods = auth()->user()->paymentMethods()
            ->where('is_active', true)
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'data' => $paymentMethods,
        ]);
    }

    /**
     * Add payment method
     */
    public function addPaymentMethod(Request $request): JsonResponse
    {
        $request->validate([
            'payment_method_id' => 'required|string',
            'is_default' => 'nullable|boolean',
        ]);

        try {
            // This would integrate with Stripe to attach payment method to customer
            // and save to database
            
            return response()->json([
                'message' => 'Payment method added successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to add payment method',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Remove payment method
     */
    public function removePaymentMethod(int $id): JsonResponse
    {
        try {
            $paymentMethod = auth()->user()->paymentMethods()->findOrFail($id);
            
            // Detach from Stripe and deactivate
            $paymentMethod->update(['is_active' => false]);

            return response()->json([
                'message' => 'Payment method removed successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to remove payment method',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }
}
