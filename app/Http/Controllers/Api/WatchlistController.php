<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Actions\Auction\AddToWatchlistAction;
use App\Actions\Auction\RemoveFromWatchlistAction;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionCollection;
use App\Models\Watchlist;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class WatchlistController extends Controller
{
    /**
     * Display user's watchlist
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        
        $watchedAuctions = auth()->user()
            ->watchedAuctions()
            ->with(['seller', 'category', 'images'])
            ->orderBy('watchlist.created_at', 'desc')
            ->paginate($perPage);

        return response()->json(new AuctionCollection($watchedAuctions));
    }

    /**
     * Add auction to watchlist
     */
    public function store(Request $request, AddToWatchlistAction $action): JsonResponse
    {
        $request->validate([
            'auction_id' => 'required|integer|exists:auctions,id',
        ]);

        try {
            $userId = UserId::fromString((string) auth()->id());
            $watchlist = $action->execute($request->auction_id, $userId);

            return response()->json([
                'message' => 'Auction added to watchlist',
                'data' => $watchlist,
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to add auction to watchlist',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Remove auction from watchlist
     */
    public function destroy(int $auctionId, RemoveFromWatchlistAction $action): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $action->execute($auctionId, $userId);

            return response()->json([
                'message' => 'Auction removed from watchlist',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to remove auction from watchlist',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Check if auction is in user's watchlist
     */
    public function check(int $auctionId): JsonResponse
    {
        $isWatched = Watchlist::where('user_id', auth()->id())
            ->where('auction_id', $auctionId)
            ->exists();

        return response()->json([
            'is_watched' => $isWatched,
        ]);
    }

    /**
     * Get watchlist statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_watched' => auth()->user()->watchedAuctions()->count(),
            'active_watched' => auth()->user()->watchedAuctions()
                ->where('status', 'active')->count(),
            'ending_soon' => auth()->user()->watchedAuctions()
                ->where('status', 'active')
                ->where('end_time', '<=', now()->addHours(24))
                ->count(),
        ];

        return response()->json([
            'data' => $stats,
        ]);
    }
}
