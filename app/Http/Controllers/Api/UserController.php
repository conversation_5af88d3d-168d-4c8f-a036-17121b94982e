<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Actions\User\RegisterUserAction;
use App\Actions\User\UpdateUserProfileAction;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterUserRequest;
use App\Http\Requests\UpdateUserProfileRequest;
use App\Http\Resources\UserResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    public function __construct(
        private UserRepositoryInterface $userRepository
    ) {}

    /**
     * Register a new user
     */
    public function register(RegisterUserRequest $request, RegisterUserAction $action): JsonResponse
    {
        try {
            $user = $action->execute($request->validated());

            return response()->json([
                'message' => 'User registered successfully',
                'data' => new UserResource($user),
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to register user',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get authenticated user profile
     */
    public function profile(): JsonResponse
    {
        $user = $this->userRepository->findByIdOrFail(
            UserId::fromString((string) auth()->id())
        );

        return response()->json([
            'data' => new UserResource($user),
        ]);
    }

    /**
     * Update user profile
     */
    public function updateProfile(
        UpdateUserProfileRequest $request,
        UpdateUserProfileAction $action
    ): JsonResponse {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $avatarFile = $request->hasFile('avatar') ? $request->file('avatar') : null;
            
            $user = $action->execute($userId, $request->validated(), $avatarFile);

            return response()->json([
                'message' => 'Profile updated successfully',
                'data' => new UserResource($user),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update profile',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Change user password
     */
    public function changePassword(Request $request): JsonResponse
    {
        $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        $user = auth()->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'message' => 'Current password is incorrect',
            ], Response::HTTP_BAD_REQUEST);
        }

        $user->update([
            'password' => Hash::make($request->new_password),
        ]);

        return response()->json([
            'message' => 'Password changed successfully',
        ]);
    }

    /**
     * Get user statistics
     */
    public function statistics(): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $stats = $this->userRepository->getUserStatistics($userId);

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Get user's notifications
     */
    public function notifications(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        $unreadOnly = $request->boolean('unread_only', false);

        $notifications = auth()->user()->notifications();

        if ($unreadOnly) {
            $notifications->whereNull('read_at');
        }

        $notifications = $notifications->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json($notifications);
    }

    /**
     * Mark notification as read
     */
    public function markNotificationRead(int $notificationId): JsonResponse
    {
        $notification = auth()->user()->notifications()->findOrFail($notificationId);
        $notification->markAsRead();

        return response()->json([
            'message' => 'Notification marked as read',
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllNotificationsRead(): JsonResponse
    {
        auth()->user()->notifications()
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return response()->json([
            'message' => 'All notifications marked as read',
        ]);
    }

    /**
     * Get unread notifications count
     */
    public function unreadNotificationsCount(): JsonResponse
    {
        $count = auth()->user()->notifications()
            ->whereNull('read_at')
            ->count();

        return response()->json([
            'count' => $count,
        ]);
    }

    /**
     * Delete user account
     */
    public function deleteAccount(Request $request): JsonResponse
    {
        $request->validate([
            'password' => 'required|string',
            'confirmation' => 'required|string|in:DELETE',
        ]);

        $user = auth()->user();

        if (!Hash::check($request->password, $user->password)) {
            return response()->json([
                'message' => 'Password is incorrect',
            ], Response::HTTP_BAD_REQUEST);
        }

        // Check if user has active auctions
        $activeAuctions = $user->auctions()->whereIn('status', ['active', 'scheduled'])->count();
        if ($activeAuctions > 0) {
            return response()->json([
                'message' => 'Cannot delete account with active auctions',
            ], Response::HTTP_BAD_REQUEST);
        }

        // Soft delete or anonymize user data
        $user->update([
            'email' => 'deleted_' . $user->id . '@deleted.com',
            'name' => 'Deleted User',
            'is_active' => false,
        ]);

        Auth::logout();

        return response()->json([
            'message' => 'Account deleted successfully',
        ]);
    }

    /**
     * Get user's dashboard data
     */
    public function dashboard(): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        
        $stats = $this->userRepository->getUserStatistics($userId);
        $recentAuctions = $this->userRepository->getRecentAuctions($userId, 5);
        $recentBids = $this->userRepository->getRecentBids($userId, 5);
        $watchedAuctions = $this->userRepository->getWatchedAuctions($userId, 5);

        return response()->json([
            'data' => [
                'statistics' => $stats,
                'recent_auctions' => $recentAuctions,
                'recent_bids' => $recentBids,
                'watched_auctions' => $watchedAuctions,
            ],
        ]);
    }
}
