<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BidResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'amount' => [
                'amount' => $this->amount,
                'currency' => $this->currency,
                'formatted' => '$' . number_format($this->amount, 2),
            ],
            'max_bid' => $this->when($this->max_bid, [
                'amount' => $this->max_bid,
                'currency' => $this->currency,
                'formatted' => '$' . number_format($this->max_bid ?? 0, 2),
            ]),
            'bid_type' => $this->bid_type,
            'status' => [
                'is_winning' => $this->is_winning,
                'is_valid' => $this->is_valid,
                'is_outbid' => !$this->is_winning && $this->is_valid,
            ],
            'bidder' => $this->when(
                $this->shouldShowBidder(),
                new UserResource($this->whenLoaded('bidder'))
            ),
            'bidder_name' => $this->when(
                !$this->shouldShowBidder(),
                $this->getAnonymizedBidderName()
            ),
            'auction' => new AuctionResource($this->whenLoaded('auction')),
            'timing' => [
                'timestamp' => $this->timestamp->toISOString(),
                'created_at' => $this->created_at->toISOString(),
                'time_ago' => $this->timestamp->diffForHumans(),
            ],
            'metadata' => [
                'ip_address' => $this->when(
                    $this->canViewSensitiveData(),
                    $this->ip_address
                ),
                'user_agent' => $this->when(
                    $this->canViewSensitiveData(),
                    $this->user_agent
                ),
                'invalidated_at' => $this->invalidated_at?->toISOString(),
                'invalidation_reason' => $this->invalidation_reason,
            ],
        ];
    }

    /**
     * Determine if bidder information should be shown
     */
    private function shouldShowBidder(): bool
    {
        // Show bidder if:
        // 1. Current user is the bidder
        // 2. Current user is the auction seller
        // 3. Current user is admin
        // 4. Auction has ended and bid history is public
        
        if (!auth()->check()) {
            return false;
        }

        if ($this->user_id === auth()->id()) {
            return true;
        }

        if (auth()->user()->isAdmin()) {
            return true;
        }

        if ($this->auction && $this->auction->user_id === auth()->id()) {
            return true;
        }

        if ($this->auction && 
            $this->auction->status === 'ended' && 
            config('auction.bidding.bid_history_public', true)) {
            return true;
        }

        return false;
    }

    /**
     * Get anonymized bidder name
     */
    private function getAnonymizedBidderName(): string
    {
        if (!$this->bidder) {
            return 'Anonymous';
        }

        // Show first letter + asterisks
        $name = $this->bidder->name;
        return substr($name, 0, 1) . str_repeat('*', min(strlen($name) - 1, 8));
    }

    /**
     * Determine if sensitive data can be viewed
     */
    private function canViewSensitiveData(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Only auction seller and admins can view sensitive data
        return auth()->user()->isAdmin() || 
               ($this->auction && $this->auction->user_id === auth()->id());
    }
}
