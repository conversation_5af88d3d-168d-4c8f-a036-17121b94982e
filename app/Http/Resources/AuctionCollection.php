<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class AuctionCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'last_page' => $this->lastPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'filters' => [
                'available_statuses' => [
                    'active' => 'Active',
                    'scheduled' => 'Scheduled',
                    'ended' => 'Ended',
                    'draft' => 'Draft',
                ],
                'available_conditions' => [
                    'new' => 'New',
                    'like_new' => 'Like New',
                    'good' => 'Good',
                    'fair' => 'Fair',
                    'poor' => 'Poor',
                ],
                'sort_options' => [
                    'ending_soon' => 'Ending Soon',
                    'newest' => 'Newest First',
                    'oldest' => 'Oldest First',
                    'price_low' => 'Price: Low to High',
                    'price_high' => 'Price: High to Low',
                    'most_bids' => 'Most Bids',
                    'most_watched' => 'Most Watched',
                ],
            ],
        ];
    }
}
