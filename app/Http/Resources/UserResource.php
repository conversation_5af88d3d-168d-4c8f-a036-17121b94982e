<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->when(
                $this->shouldShowEmail(),
                $this->email
            ),
            'role' => $this->role,
            'avatar' => [
                'url' => $this->avatar_url,
                'thumbnail' => $this->avatar_thumbnail_url,
            ],
            'profile' => $this->when($this->relationLoaded('profile'), [
                'bio' => $this->profile?->bio,
                'location' => $this->profile?->location,
                'phone' => $this->when(
                    $this->shouldShowPhone(),
                    $this->profile?->phone
                ),
                'website' => $this->profile?->website,
                'social_links' => $this->profile?->social_links,
            ]),
            'verification' => [
                'is_email_verified' => $this->email_verified_at !== null,
                'is_phone_verified' => $this->profile?->phone_verified_at !== null,
                'is_identity_verified' => $this->profile?->identity_verified_at !== null,
                'verification_level' => $this->getVerificationLevel(),
            ],
            'status' => [
                'is_active' => $this->is_active,
                'is_online' => $this->isOnline(),
                'last_login_at' => $this->last_login_at?->toISOString(),
                'last_activity_at' => $this->last_activity_at?->toISOString(),
            ],
            'statistics' => $this->when($this->shouldShowStatistics(), [
                'total_auctions' => $this->auctions_count ?? $this->auctions()->count(),
                'active_auctions' => $this->active_auctions_count ?? 
                    $this->auctions()->where('status', 'active')->count(),
                'total_bids' => $this->bids_count ?? $this->bids()->count(),
                'won_auctions' => $this->won_auctions_count ?? 
                    $this->wonAuctions()->count(),
                'success_rate' => $this->calculateBidSuccessRate(),
                'total_spent' => $this->calculateTotalSpent(),
                'total_earned' => $this->calculateTotalEarned(),
                'member_since' => $this->created_at->toISOString(),
            ]),
            'ratings' => $this->when($this->relationLoaded('ratings'), [
                'average_rating' => $this->average_rating,
                'total_ratings' => $this->ratings_count,
                'rating_breakdown' => $this->rating_breakdown,
            ]),
            'preferences' => $this->when($this->shouldShowPreferences(), [
                'timezone' => $this->timezone,
                'currency' => $this->preferred_currency,
                'language' => $this->preferred_language,
                'notifications' => $this->notification_preferences,
                'privacy' => $this->privacy_settings,
            ]),
            'metadata' => [
                'created_at' => $this->created_at->toISOString(),
                'updated_at' => $this->updated_at->toISOString(),
                'is_current_user' => $this->when(
                    auth()->check(),
                    fn() => $this->id === auth()->id()
                ),
                'can_contact' => $this->canBeContactedBy(auth()->user()),
                'can_view_profile' => $this->canViewProfile(auth()->user()),
            ],
        ];
    }

    /**
     * Determine if email should be shown
     */
    private function shouldShowEmail(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Show email if:
        // 1. Current user viewing their own profile
        // 2. Admin viewing any profile
        return $this->id === auth()->id() || auth()->user()->isAdmin();
    }

    /**
     * Determine if phone should be shown
     */
    private function shouldShowPhone(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Show phone if:
        // 1. Current user viewing their own profile
        // 2. Admin viewing any profile
        // 3. User has made phone public in privacy settings
        return $this->id === auth()->id() || 
               auth()->user()->isAdmin() || 
               ($this->privacy_settings['phone_public'] ?? false);
    }

    /**
     * Determine if statistics should be shown
     */
    private function shouldShowStatistics(): bool
    {
        // Show statistics if:
        // 1. Current user viewing their own profile
        // 2. Profile is public
        // 3. Admin viewing any profile
        if (!auth()->check()) {
            return $this->privacy_settings['profile_public'] ?? true;
        }

        return $this->id === auth()->id() || 
               auth()->user()->isAdmin() || 
               ($this->privacy_settings['statistics_public'] ?? true);
    }

    /**
     * Determine if preferences should be shown
     */
    private function shouldShowPreferences(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Only show preferences to the user themselves or admin
        return $this->id === auth()->id() || auth()->user()->isAdmin();
    }

    /**
     * Get verification level
     */
    private function getVerificationLevel(): string
    {
        $level = 'unverified';

        if ($this->email_verified_at) {
            $level = 'email_verified';
        }

        if ($this->profile?->phone_verified_at) {
            $level = 'phone_verified';
        }

        if ($this->profile?->identity_verified_at) {
            $level = 'fully_verified';
        }

        return $level;
    }

    /**
     * Check if user is online
     */
    private function isOnline(): bool
    {
        if (!$this->last_activity_at) {
            return false;
        }

        return $this->last_activity_at->diffInMinutes(now()) <= 5;
    }

    /**
     * Calculate bid success rate
     */
    private function calculateBidSuccessRate(): float
    {
        $totalBids = $this->bids()->count();
        if ($totalBids === 0) {
            return 0.0;
        }

        $winningBids = $this->bids()->where('is_winning', true)->count();
        return round(($winningBids / $totalBids) * 100, 2);
    }

    /**
     * Calculate total spent
     */
    private function calculateTotalSpent(): float
    {
        return $this->payments()
            ->where('status', 'succeeded')
            ->sum('amount') ?? 0.0;
    }

    /**
     * Calculate total earned
     */
    private function calculateTotalEarned(): float
    {
        return $this->auctions()
            ->where('status', 'ended')
            ->whereNotNull('final_price')
            ->sum('final_price') ?? 0.0;
    }

    /**
     * Check if user can be contacted
     */
    private function canBeContactedBy($user): bool
    {
        if (!$user) {
            return false;
        }

        if ($user->isAdmin()) {
            return true;
        }

        return $this->privacy_settings['allow_contact'] ?? true;
    }

    /**
     * Check if profile can be viewed
     */
    private function canViewProfile($user): bool
    {
        if ($this->privacy_settings['profile_public'] ?? true) {
            return true;
        }

        if (!$user) {
            return false;
        }

        return $user->id === $this->id || $user->isAdmin();
    }
}
