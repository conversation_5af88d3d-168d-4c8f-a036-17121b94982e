<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'amount' => [
                'gross_amount' => $this->amount,
                'fee_amount' => $this->fee_amount,
                'net_amount' => $this->amount - $this->fee_amount,
                'currency' => $this->currency,
                'formatted_gross' => '$' . number_format($this->amount, 2),
                'formatted_fee' => '$' . number_format($this->fee_amount, 2),
                'formatted_net' => '$' . number_format($this->amount - $this->fee_amount, 2),
            ],
            'refund' => $this->when($this->refund_amount > 0, [
                'amount' => $this->refund_amount,
                'currency' => $this->currency,
                'formatted' => '$' . number_format($this->refund_amount, 2),
                'refunded_at' => $this->refunded_at?->toISOString(),
                'remaining_refundable' => $this->amount - $this->refund_amount,
                'formatted_remaining' => '$' . number_format($this->amount - $this->refund_amount, 2),
            ]),
            'status' => $this->status,
            'description' => $this->description,
            'payment_method' => [
                'type' => $this->payment_method_type,
                'details' => $this->payment_method_details,
                'brand' => $this->payment_method_details['brand'] ?? null,
                'last_four' => $this->payment_method_details['last_four'] ?? null,
            ],
            'user' => new UserResource($this->whenLoaded('user')),
            'auction' => new AuctionResource($this->whenLoaded('auction')),
            'timing' => [
                'created_at' => $this->created_at->toISOString(),
                'processed_at' => $this->processed_at?->toISOString(),
                'failed_at' => $this->failed_at?->toISOString(),
                'refunded_at' => $this->refunded_at?->toISOString(),
                'updated_at' => $this->updated_at->toISOString(),
            ],
            'external_references' => $this->when($this->canViewSensitiveData(), [
                'payment_intent_id' => $this->payment_intent_id,
                'stripe_payment_intent_id' => $this->stripe_payment_intent_id,
                'transaction_id' => $this->transaction_id,
            ]),
            'metadata' => $this->when($this->canViewSensitiveData(), $this->metadata),
            'failure_reason' => $this->when($this->status === 'failed', $this->failure_reason),
        ];
    }

    /**
     * Determine if sensitive data can be viewed
     */
    private function canViewSensitiveData(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Show sensitive data if:
        // 1. Current user is the payment owner
        // 2. Current user is admin
        return $this->user_id === auth()->id() || auth()->user()->isAdmin();
    }
}
