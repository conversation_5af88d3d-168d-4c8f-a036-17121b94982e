<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class BidCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'last_page' => $this->lastPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
            'statistics' => $this->when($this->collection->isNotEmpty(), [
                'highest_bid' => $this->collection->max('amount'),
                'lowest_bid' => $this->collection->min('amount'),
                'average_bid' => $this->collection->avg('amount'),
                'total_bid_value' => $this->collection->sum('amount'),
                'unique_bidders' => $this->collection->unique('user_id')->count(),
                'winning_bids' => $this->collection->where('is_winning', true)->count(),
            ]),
        ];
    }
}
