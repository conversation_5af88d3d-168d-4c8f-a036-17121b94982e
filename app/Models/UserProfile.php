<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'bio',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'birth_date',
        'verification_status',
        'verification_documents',
        'social_links',
        'preferences',
    ];

    protected function casts(): array
    {
        return [
            'birth_date' => 'date',
            'verification_documents' => 'array',
            'social_links' => 'array',
            'preferences' => 'array',
        ];
    }

    /**
     * Get the user that owns the profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Check if the user is verified.
     */
    public function isVerified(): bool
    {
        return $this->verification_status === 'verified';
    }
}
