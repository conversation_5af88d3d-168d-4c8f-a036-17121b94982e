<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Bid extends Model
{
    use HasFactory;

    protected $fillable = [
        'auction_id',
        'user_id',
        'amount',
        'max_bid',
        'bid_type',
        'timestamp',
        'is_winning',
        'is_valid',
        'invalidated_at',
        'invalidation_reason',
        'ip_address',
        'user_agent',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'max_bid' => 'decimal:2',
            'timestamp' => 'datetime',
            'is_winning' => 'boolean',
            'is_valid' => 'boolean',
            'invalidated_at' => 'datetime',
        ];
    }

    /**
     * Get the auction that owns the bid.
     */
    public function auction(): BelongsTo
    {
        return $this->belongsTo(Auction::class);
    }

    /**
     * Get the user that placed the bid.
     */
    public function bidder(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Scope to get valid bids.
     */
    public function scopeValid($query)
    {
        return $query->where('is_valid', true);
    }

    /**
     * Scope to get winning bids.
     */
    public function scopeWinning($query)
    {
        return $query->where('is_winning', true);
    }

    /**
     * Scope to get proxy bids.
     */
    public function scopeProxy($query)
    {
        return $query->where('bid_type', 'proxy');
    }

    /**
     * Scope to get manual bids.
     */
    public function scopeManual($query)
    {
        return $query->where('bid_type', 'manual');
    }

    /**
     * Check if this is a proxy bid.
     */
    public function isProxy(): bool
    {
        return $this->bid_type === 'proxy';
    }

    /**
     * Check if this is a manual bid.
     */
    public function isManual(): bool
    {
        return $this->bid_type === 'manual';
    }

    /**
     * Check if this is an auto bid.
     */
    public function isAuto(): bool
    {
        return $this->bid_type === 'auto';
    }

    /**
     * Get the formatted bid amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }
}
