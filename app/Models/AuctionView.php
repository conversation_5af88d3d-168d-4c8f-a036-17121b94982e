<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuctionView extends Model
{
    use HasFactory;

    protected $fillable = [
        'auction_id',
        'user_id',
        'ip_address',
        'user_agent',
        'viewed_at',
        'session_id',
        'referrer',
    ];

    protected function casts(): array
    {
        return [
            'viewed_at' => 'datetime',
        ];
    }

    /**
     * Get the auction that was viewed.
     */
    public function auction(): BelongsTo
    {
        return $this->belongsTo(Auction::class);
    }

    /**
     * Get the user that viewed the auction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
