<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'stripe_payment_method_id',
        'type',
        'card_brand',
        'card_last_four',
        'card_exp_month',
        'card_exp_year',
        'is_default',
        'is_active',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'card_exp_month' => 'integer',
            'card_exp_year' => 'integer',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the user that owns the payment method.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get active payment methods.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get default payment methods.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get the display name for the payment method.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->type === 'card') {
            return ucfirst($this->card_brand) . ' ending in ' . $this->card_last_four;
        }

        return ucfirst($this->type);
    }

    /**
     * Check if the card is expired.
     */
    public function isExpired(): bool
    {
        if ($this->type !== 'card') {
            return false;
        }

        $currentYear = (int) date('Y');
        $currentMonth = (int) date('n');

        return $this->card_exp_year < $currentYear || 
               ($this->card_exp_year === $currentYear && $this->card_exp_month < $currentMonth);
    }
}
