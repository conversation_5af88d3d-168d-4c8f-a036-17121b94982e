<?php

declare(strict_types=1);

namespace App\Domain\Payment\Repositories;

use App\Domain\Payment\Models\Payment;
use App\Domain\Payment\ValueObjects\PaymentStatus;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface PaymentRepositoryInterface
{
    /**
     * Find payment by ID
     */
    public function findById(Id $id): ?Payment;

    /**
     * Find payment by ID or throw exception
     */
    public function findByIdOrFail(Id $id): Payment;

    /**
     * Find payment by payment intent ID
     */
    public function findByPaymentIntentId(string $paymentIntentId): ?Payment;

    /**
     * Find payment by Stripe payment intent ID
     */
    public function findByStripePaymentIntentId(string $stripePaymentIntentId): ?Payment;

    /**
     * Save payment
     */
    public function save(Payment $payment): void;

    /**
     * Delete payment
     */
    public function delete(Payment $payment): void;

    /**
     * Find payments by user
     */
    public function findByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find payments by auction
     */
    public function findByAuction(Id $auctionId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find payments by status
     */
    public function findByStatus(PaymentStatus $status, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find successful payments
     */
    public function findSuccessful(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find failed payments
     */
    public function findFailed(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find pending payments
     */
    public function findPending(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find refunded payments
     */
    public function findRefunded(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find payments for auction winner
     */
    public function findForAuctionWinner(Id $auctionId, UserId $winnerId): ?Payment;

    /**
     * Find user's payment history
     */
    public function findUserPaymentHistory(UserId $userId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find payments that need processing (stuck in processing state)
     */
    public function findStuckInProcessing(int $minutes = 30): Collection;

    /**
     * Find payments that can be retried
     */
    public function findRetryable(int $perPage = 15): LengthAwarePaginator;

    /**
     * Count payments by status
     */
    public function countByStatus(PaymentStatus $status): int;

    /**
     * Count user payments by status
     */
    public function countUserPaymentsByStatus(UserId $userId, PaymentStatus $status): int;

    /**
     * Get payment statistics
     */
    public function getPaymentStatistics(): array;

    /**
     * Get user payment statistics
     */
    public function getUserPaymentStatistics(UserId $userId): array;

    /**
     * Calculate total payment volume
     */
    public function calculateTotalVolume(): float;

    /**
     * Calculate total payment volume for user
     */
    public function calculateUserTotalVolume(UserId $userId): float;

    /**
     * Calculate total fees collected
     */
    public function calculateTotalFeesCollected(): float;

    /**
     * Calculate total refunds issued
     */
    public function calculateTotalRefundsIssued(): float;

    /**
     * Find payments within date range
     */
    public function findWithinDateRange(\DateTime $startDate, \DateTime $endDate, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find high-value payments (above threshold)
     */
    public function findHighValue(float $threshold = 1000.0, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find recent payments for monitoring
     */
    public function findRecent(int $hours = 24): Collection;

    /**
     * Search payments by description or metadata
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator;
}
