<?php

declare(strict_types=1);

namespace App\Domain\Payment\Models;

use App\Domain\Payment\Events\PaymentProcessed;
use App\Domain\Payment\Events\PaymentFailed;
use App\Domain\Payment\Events\RefundIssued;
use App\Domain\Payment\ValueObjects\PaymentAmount;
use App\Domain\Payment\ValueObjects\PaymentMethod;
use App\Domain\Payment\ValueObjects\PaymentStatus;
use App\Domain\Shared\Models\AggregateRoot;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\Shared\ValueObjects\Timestamp;
use App\Domain\User\ValueObjects\UserId;
use InvalidArgumentException;

class Payment extends AggregateRoot
{
    private Id $id;
    private UserId $userId;
    private ?Id $auctionId;
    private ?string $paymentIntentId;
    private ?string $stripePaymentIntentId;
    private PaymentAmount $amount;
    private PaymentStatus $status;
    private PaymentMethod $paymentMethod;
    private ?string $description;
    private ?string $failureReason;
    private Money $refundAmount;
    private ?Timestamp $refundedAt;
    private array $metadata;
    private ?Timestamp $processedAt;
    private Timestamp $createdAt;
    private Timestamp $updatedAt;

    public function __construct(
        Id $id,
        UserId $userId,
        PaymentAmount $amount,
        PaymentMethod $paymentMethod,
        ?Id $auctionId = null,
        ?string $description = null,
        array $metadata = []
    ) {
        $this->id = $id;
        $this->userId = $userId;
        $this->auctionId = $auctionId;
        $this->paymentIntentId = null;
        $this->stripePaymentIntentId = null;
        $this->amount = $amount;
        $this->status = PaymentStatus::pending();
        $this->paymentMethod = $paymentMethod;
        $this->description = $description;
        $this->failureReason = null;
        $this->refundAmount = Money::zero($amount->currency());
        $this->refundedAt = null;
        $this->metadata = $metadata;
        $this->processedAt = null;
        $this->createdAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();
    }

    public static function create(
        Id $id,
        UserId $userId,
        PaymentAmount $amount,
        PaymentMethod $paymentMethod,
        ?Id $auctionId = null,
        ?string $description = null,
        array $metadata = []
    ): self {
        return new self($id, $userId, $amount, $paymentMethod, $auctionId, $description, $metadata);
    }

    // Getters
    public function id(): Id
    {
        return $this->id;
    }

    public function userId(): UserId
    {
        return $this->userId;
    }

    public function auctionId(): ?Id
    {
        return $this->auctionId;
    }

    public function paymentIntentId(): ?string
    {
        return $this->paymentIntentId;
    }

    public function stripePaymentIntentId(): ?string
    {
        return $this->stripePaymentIntentId;
    }

    public function amount(): PaymentAmount
    {
        return $this->amount;
    }

    public function status(): PaymentStatus
    {
        return $this->status;
    }

    public function paymentMethod(): PaymentMethod
    {
        return $this->paymentMethod;
    }

    public function description(): ?string
    {
        return $this->description;
    }

    public function failureReason(): ?string
    {
        return $this->failureReason;
    }

    public function refundAmount(): Money
    {
        return $this->refundAmount;
    }

    public function refundedAt(): ?Timestamp
    {
        return $this->refundedAt;
    }

    public function metadata(): array
    {
        return $this->metadata;
    }

    public function processedAt(): ?Timestamp
    {
        return $this->processedAt;
    }

    public function createdAt(): Timestamp
    {
        return $this->createdAt;
    }

    public function updatedAt(): Timestamp
    {
        return $this->updatedAt;
    }

    // Business Logic Methods
    public function setPaymentIntentId(string $paymentIntentId): void
    {
        $this->paymentIntentId = $paymentIntentId;
        $this->updatedAt = Timestamp::now();
    }

    public function setStripePaymentIntentId(string $stripePaymentIntentId): void
    {
        $this->stripePaymentIntentId = $stripePaymentIntentId;
        $this->updatedAt = Timestamp::now();
    }

    public function markAsProcessing(): void
    {
        if (!$this->status->isPending()) {
            throw new InvalidArgumentException('Only pending payments can be marked as processing');
        }

        $this->status = PaymentStatus::processing();
        $this->updatedAt = Timestamp::now();
    }

    public function markAsSucceeded(): void
    {
        if (!$this->status->isProcessing() && !$this->status->isPending()) {
            throw new InvalidArgumentException('Only processing or pending payments can be marked as succeeded');
        }

        $this->status = PaymentStatus::succeeded();
        $this->processedAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();

        $this->recordEvent(new PaymentProcessed($this->userId, [
            'payment_id' => $this->id->value(),
            'auction_id' => $this->auctionId?->value(),
            'amount' => $this->amount->grossAmount(),
            'currency' => $this->amount->currency(),
            'payment_method' => $this->paymentMethod->type(),
        ]));
    }

    public function markAsFailed(string $reason): void
    {
        if ($this->status->isFinal() && !$this->status->isPending() && !$this->status->isProcessing()) {
            throw new InvalidArgumentException('Cannot mark completed payments as failed');
        }

        $this->status = PaymentStatus::failed();
        $this->failureReason = $reason;
        $this->processedAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();

        $this->recordEvent(new PaymentFailed($this->userId, [
            'payment_id' => $this->id->value(),
            'auction_id' => $this->auctionId?->value(),
            'amount' => $this->amount->grossAmount(),
            'currency' => $this->amount->currency(),
            'reason' => $reason,
        ]));
    }

    public function cancel(): void
    {
        if (!$this->status->canBeCancelled()) {
            throw new InvalidArgumentException('This payment cannot be cancelled');
        }

        $this->status = PaymentStatus::cancelled();
        $this->updatedAt = Timestamp::now();
    }

    public function refund(Money $refundAmount): void
    {
        if (!$this->status->canBeRefunded()) {
            throw new InvalidArgumentException('This payment cannot be refunded');
        }

        if ($refundAmount->isNegative() || $refundAmount->isZero()) {
            throw new InvalidArgumentException('Refund amount must be positive');
        }

        $totalRefundAmount = $this->refundAmount->add($refundAmount);
        
        if ($totalRefundAmount->isGreaterThan($this->amount->amount())) {
            throw new InvalidArgumentException('Total refund amount cannot exceed payment amount');
        }

        $this->refundAmount = $totalRefundAmount;
        $this->refundedAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();

        // Update status based on refund amount
        if ($this->refundAmount->equals($this->amount->amount())) {
            $this->status = PaymentStatus::refunded();
        } else {
            $this->status = PaymentStatus::partiallyRefunded();
        }

        $this->recordEvent(new RefundIssued($this->userId, [
            'payment_id' => $this->id->value(),
            'auction_id' => $this->auctionId?->value(),
            'refund_amount' => $refundAmount->amount(),
            'total_refund_amount' => $this->refundAmount->amount(),
            'currency' => $refundAmount->currency(),
            'is_full_refund' => $this->status->isRefunded(),
        ]));
    }

    public function updateMetadata(array $metadata): void
    {
        $this->metadata = array_merge($this->metadata, $metadata);
        $this->updatedAt = Timestamp::now();
    }

    public function isRefunded(): bool
    {
        return $this->status->isRefunded();
    }

    public function isPartiallyRefunded(): bool
    {
        return $this->status->value() === PaymentStatus::PARTIALLY_REFUNDED;
    }

    public function getRemainingRefundableAmount(): Money
    {
        return $this->amount->amount()->subtract($this->refundAmount);
    }

    public function canRefund(Money $amount): bool
    {
        if (!$this->status->canBeRefunded()) {
            return false;
        }

        $totalRefundAmount = $this->refundAmount->add($amount);
        return $totalRefundAmount->isLessThanOrEqual($this->amount->amount());
    }
}
