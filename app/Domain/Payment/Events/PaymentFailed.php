<?php

declare(strict_types=1);

namespace App\Domain\Payment\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\User\ValueObjects\UserId;

class PaymentFailed extends DomainEvent
{
    public function __construct(UserId $userId, array $payload = [])
    {
        parent::__construct($userId, $payload);
    }

    public function paymentId(): int
    {
        return $this->payload()['payment_id'];
    }

    public function auctionId(): ?int
    {
        return $this->payload()['auction_id'] ?? null;
    }

    public function amount(): float
    {
        return $this->payload()['amount'];
    }

    public function currency(): string
    {
        return $this->payload()['currency'];
    }

    public function reason(): string
    {
        return $this->payload()['reason'];
    }

    public function payerId(): int
    {
        return $this->userId()->value();
    }
}
