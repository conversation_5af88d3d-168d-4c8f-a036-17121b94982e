<?php

declare(strict_types=1);

namespace App\Domain\Auction\Models;

use App\Domain\Shared\Models\AggregateRoot;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\ImageUrl;
use App\Domain\Shared\ValueObjects\Timestamp;
use InvalidArgumentException;

class AuctionImage extends AggregateRoot
{
    private Id $id;
    private Id $auctionId;
    private string $filename;
    private ?string $originalName;
    private string $path;
    private ImageUrl $url;
    private ?ImageUrl $thumbnailUrl;
    private ?string $altText;
    private int $sortOrder;
    private ?int $sizeBytes;
    private ?string $mimeType;
    private ?int $width;
    private ?int $height;
    private bool $isPrimary;
    private Timestamp $createdAt;
    private Timestamp $updatedAt;

    public function __construct(
        Id $id,
        Id $auctionId,
        string $filename,
        string $path,
        ImageUrl $url,
        ?string $originalName = null,
        int $sortOrder = 0
    ) {
        $this->validateCreation($filename, $path);

        $this->id = $id;
        $this->auctionId = $auctionId;
        $this->filename = $filename;
        $this->originalName = $originalName;
        $this->path = $path;
        $this->url = $url;
        $this->thumbnailUrl = null;
        $this->altText = null;
        $this->sortOrder = $sortOrder;
        $this->sizeBytes = null;
        $this->mimeType = null;
        $this->width = null;
        $this->height = null;
        $this->isPrimary = false;
        $this->createdAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();
    }

    public static function upload(
        Id $id,
        Id $auctionId,
        string $filename,
        string $path,
        ImageUrl $url,
        ?string $originalName = null,
        int $sortOrder = 0
    ): self {
        return new self($id, $auctionId, $filename, $path, $url, $originalName, $sortOrder);
    }

    // Getters
    public function id(): Id
    {
        return $this->id;
    }

    public function auctionId(): Id
    {
        return $this->auctionId;
    }

    public function filename(): string
    {
        return $this->filename;
    }

    public function originalName(): ?string
    {
        return $this->originalName;
    }

    public function path(): string
    {
        return $this->path;
    }

    public function url(): ImageUrl
    {
        return $this->url;
    }

    public function thumbnailUrl(): ?ImageUrl
    {
        return $this->thumbnailUrl;
    }

    public function altText(): ?string
    {
        return $this->altText;
    }

    public function sortOrder(): int
    {
        return $this->sortOrder;
    }

    public function sizeBytes(): ?int
    {
        return $this->sizeBytes;
    }

    public function mimeType(): ?string
    {
        return $this->mimeType;
    }

    public function width(): ?int
    {
        return $this->width;
    }

    public function height(): ?int
    {
        return $this->height;
    }

    public function isPrimary(): bool
    {
        return $this->isPrimary;
    }

    public function createdAt(): Timestamp
    {
        return $this->createdAt;
    }

    public function updatedAt(): Timestamp
    {
        return $this->updatedAt;
    }

    // Business Logic Methods
    public function updateMetadata(
        ?string $altText = null,
        ?int $sizeBytes = null,
        ?string $mimeType = null,
        ?int $width = null,
        ?int $height = null
    ): void {
        $this->altText = $altText;
        $this->sizeBytes = $sizeBytes;
        $this->mimeType = $mimeType;
        $this->width = $width;
        $this->height = $height;
        $this->updatedAt = Timestamp::now();
    }

    public function setThumbnail(ImageUrl $thumbnailUrl): void
    {
        $this->thumbnailUrl = $thumbnailUrl;
        $this->updatedAt = Timestamp::now();
    }

    public function updateSortOrder(int $sortOrder): void
    {
        if ($sortOrder < 0) {
            throw new InvalidArgumentException('Sort order cannot be negative');
        }

        $this->sortOrder = $sortOrder;
        $this->updatedAt = Timestamp::now();
    }

    public function markAsPrimary(): void
    {
        $this->isPrimary = true;
        $this->updatedAt = Timestamp::now();
    }

    public function unmarkAsPrimary(): void
    {
        $this->isPrimary = false;
        $this->updatedAt = Timestamp::now();
    }

    public function updateAltText(string $altText): void
    {
        $this->altText = $altText;
        $this->updatedAt = Timestamp::now();
    }

    public function getDisplayName(): string
    {
        return $this->originalName ?? $this->filename;
    }

    public function getFileExtension(): string
    {
        return $this->url->getExtension();
    }

    public function isImage(): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
        return in_array(strtolower($this->getFileExtension()), $imageExtensions);
    }

    public function getFormattedSize(): string
    {
        if ($this->sizeBytes === null) {
            return 'Unknown size';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->sizeBytes;
        $unitIndex = 0;

        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }

        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }

    public function getDimensions(): ?string
    {
        if ($this->width && $this->height) {
            return $this->width . ' × ' . $this->height;
        }

        return null;
    }

    private function validateCreation(string $filename, string $path): void
    {
        if (empty(trim($filename))) {
            throw new InvalidArgumentException('Filename cannot be empty');
        }

        if (empty(trim($path))) {
            throw new InvalidArgumentException('Path cannot be empty');
        }

        if (strlen($filename) > 255) {
            throw new InvalidArgumentException('Filename cannot exceed 255 characters');
        }

        if (strlen($path) > 500) {
            throw new InvalidArgumentException('Path cannot exceed 500 characters');
        }
    }
}
