<?php

declare(strict_types=1);

namespace App\Domain\Auction\Policies;

use App\Domain\Auction\Models\Auction;
use App\Domain\User\Models\User;

class AuctionPolicy
{
    /**
     * Determine if the user can view any auctions
     */
    public function viewAny(User $user): bool
    {
        return $user->isActive();
    }

    /**
     * Determine if the user can view the auction
     */
    public function view(?User $user, Auction $auction): bool
    {
        // Public auctions can be viewed by anyone
        if ($auction->status()->isActive() || $auction->status()->isEnded()) {
            return true;
        }

        // Draft and scheduled auctions can only be viewed by owner or admin
        if ($user === null) {
            return false;
        }

        return $this->isOwnerOrAdmin($user, $auction);
    }

    /**
     * Determine if the user can create auctions
     */
    public function create(User $user): bool
    {
        return $user->isActive() && 
               $user->isEmailVerified() && 
               $user->canCreateAuctions();
    }

    /**
     * Determine if the user can update the auction
     */
    public function update(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can update any auction
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Owners can only update their own auctions if they're editable
        return $this->isOwner($user, $auction) && $auction->status()->canEdit();
    }

    /**
     * Determine if the user can delete the auction
     */
    public function delete(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can delete any auction
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Owners can only delete their own auctions if they're in draft status
        return $this->isOwner($user, $auction) && $auction->status()->isDraft();
    }

    /**
     * Determine if the user can activate the auction
     */
    public function activate(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Must be owner or admin
        if (!$this->isOwnerOrAdmin($user, $auction)) {
            return false;
        }

        // Auction must be in draft or scheduled status
        return $auction->status()->isDraft() || $auction->status()->isScheduled();
    }

    /**
     * Determine if the user can cancel the auction
     */
    public function cancel(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can cancel any auction
        if ($user->canModerateAuctions()) {
            return $auction->status()->canCancel();
        }

        // Owners can cancel their own auctions under certain conditions
        if ($this->isOwner($user, $auction)) {
            // Can cancel if no bids or only in draft/scheduled status
            return $auction->status()->canCancel() && 
                   ($auction->bidsCount() === 0 || !$auction->status()->isActive());
        }

        return false;
    }

    /**
     * Determine if the user can end the auction
     */
    public function end(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Only admins can manually end auctions
        return $user->canModerateAuctions() && $auction->status()->isActive();
    }

    /**
     * Determine if the user can bid on the auction
     */
    public function bid(User $user, Auction $auction): bool
    {
        if (!$user->isActive() || !$user->isEmailVerified()) {
            return false;
        }

        // Cannot bid on own auction
        if ($this->isOwner($user, $auction)) {
            return false;
        }

        // Auction must be active and accepting bids
        return $auction->status()->canBid();
    }

    /**
     * Determine if the user can watch the auction
     */
    public function watch(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Cannot watch own auction
        if ($this->isOwner($user, $auction)) {
            return false;
        }

        // Can watch active or scheduled auctions
        return $auction->status()->isActive() || 
               $auction->status()->isScheduled();
    }

    /**
     * Determine if the user can edit auction images
     */
    public function manageImages(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can manage any auction images
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Owners can manage images if auction is editable
        return $this->isOwner($user, $auction) && $auction->status()->canEdit();
    }

    /**
     * Determine if the user can feature the auction
     */
    public function feature(User $user, Auction $auction): bool
    {
        return $user->isActive() && $user->canModerateAuctions();
    }

    /**
     * Determine if the user can suspend the auction
     */
    public function suspend(User $user, Auction $auction): bool
    {
        return $user->isActive() && $user->canModerateAuctions();
    }

    /**
     * Determine if the user can view auction analytics
     */
    public function viewAnalytics(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can view any auction analytics
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Owners can view their own auction analytics
        return $this->isOwner($user, $auction);
    }

    /**
     * Determine if the user can export auction data
     */
    public function export(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can export any auction data
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Owners can export their own auction data
        return $this->isOwner($user, $auction);
    }

    /**
     * Check if user is the owner of the auction
     */
    private function isOwner(User $user, Auction $auction): bool
    {
        return $user->id()->equals($auction->sellerId());
    }

    /**
     * Check if user is the owner or an admin
     */
    private function isOwnerOrAdmin(User $user, Auction $auction): bool
    {
        return $this->isOwner($user, $auction) || $user->canModerateAuctions();
    }
}
