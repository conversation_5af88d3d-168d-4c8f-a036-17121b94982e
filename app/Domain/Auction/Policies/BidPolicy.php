<?php

declare(strict_types=1);

namespace App\Domain\Auction\Policies;

use App\Domain\Auction\Models\Auction;
use App\Domain\Auction\Models\Bid;
use App\Domain\User\Models\User;

class BidPolicy
{
    /**
     * Determine if the user can view any bids
     */
    public function viewAny(User $user): bool
    {
        return $user->isActive();
    }

    /**
     * Determine if the user can view the bid
     */
    public function view(User $user, Bid $bid): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can view any bid
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Users can view their own bids
        return $user->id()->equals($bid->bidderId());
    }

    /**
     * Determine if the user can view bid history for an auction
     */
    public function viewAuctionBidHistory(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Public bid history for active/ended auctions
        if ($auction->status()->isActive() || $auction->status()->isEnded()) {
            return true;
        }

        // Private bid history for draft/scheduled auctions - only owner or admin
        return $user->id()->equals($auction->sellerId()) || $user->canModerateAuctions();
    }

    /**
     * Determine if the user can place a bid
     */
    public function create(User $user, Auction $auction): bool
    {
        if (!$user->isActive() || !$user->isEmailVerified()) {
            return false;
        }

        // Cannot bid on own auction
        if ($user->id()->equals($auction->sellerId())) {
            return false;
        }

        // Auction must be active
        if (!$auction->status()->isActive()) {
            return false;
        }

        // Check if auction duration allows bidding
        if ($auction->duration()->hasEnded()) {
            return false;
        }

        return true;
    }

    /**
     * Determine if the user can place a proxy bid
     */
    public function createProxy(User $user, Auction $auction): bool
    {
        // Same rules as regular bidding
        return $this->create($user, $auction);
    }

    /**
     * Determine if the user can update their bid (only for proxy bids)
     */
    public function update(User $user, Bid $bid, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Must be the bidder
        if (!$user->id()->equals($bid->bidderId())) {
            return false;
        }

        // Can only update proxy bids
        if (!$bid->isProxy()) {
            return false;
        }

        // Auction must still be active
        if (!$auction->status()->isActive()) {
            return false;
        }

        // Bid must be valid
        return $bid->isValid();
    }

    /**
     * Determine if the user can cancel their bid
     */
    public function cancel(User $user, Bid $bid, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can cancel any bid
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Users can only cancel their own bids under specific conditions
        if (!$user->id()->equals($bid->bidderId())) {
            return false;
        }

        // Cannot cancel if auction has ended
        if ($auction->status()->isEnded()) {
            return false;
        }

        // Cannot cancel winning bids in active auctions
        if ($auction->status()->isActive() && $bid->isWinning()) {
            return false;
        }

        // Can cancel non-winning bids or bids in non-active auctions
        return true;
    }

    /**
     * Determine if the user can invalidate a bid (admin only)
     */
    public function invalidate(User $user, Bid $bid): bool
    {
        return $user->isActive() && $user->canModerateAuctions();
    }

    /**
     * Determine if the user can view bid details (amount, timestamp, etc.)
     */
    public function viewDetails(User $user, Bid $bid, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can view all bid details
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Auction owner can view bid details for their auction
        if ($user->id()->equals($auction->sellerId())) {
            return true;
        }

        // Bidders can view their own bid details
        if ($user->id()->equals($bid->bidderId())) {
            return true;
        }

        // For ended auctions, winning bid details are public
        if ($auction->status()->isEnded() && $bid->isWinning()) {
            return true;
        }

        return false;
    }

    /**
     * Determine if the user can view bidder information
     */
    public function viewBidderInfo(User $user, Bid $bid, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can view bidder info
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Auction owner can view bidder info for their auction
        if ($user->id()->equals($auction->sellerId())) {
            return true;
        }

        // Bidders can view their own info
        if ($user->id()->equals($bid->bidderId())) {
            return true;
        }

        return false;
    }

    /**
     * Determine if the user can export bid data
     */
    public function export(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can export any bid data
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Auction owners can export bid data for their auctions
        return $user->id()->equals($auction->sellerId());
    }

    /**
     * Determine if the user can view bid analytics
     */
    public function viewAnalytics(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Admins can view bid analytics
        if ($user->canModerateAuctions()) {
            return true;
        }

        // Auction owners can view analytics for their auctions
        return $user->id()->equals($auction->sellerId());
    }

    /**
     * Determine if the user can receive bid notifications
     */
    public function receiveNotifications(User $user, Auction $auction): bool
    {
        if (!$user->isActive()) {
            return false;
        }

        // Auction owners receive notifications about bids on their auctions
        if ($user->id()->equals($auction->sellerId())) {
            return true;
        }

        // Users receive notifications about being outbid
        // This would be checked in the context of a specific bid
        return true;
    }

    /**
     * Determine if the user can set up auto-bidding
     */
    public function setupAutoBidding(User $user, Auction $auction): bool
    {
        // Same rules as placing a bid
        return $this->create($user, $auction);
    }

    /**
     * Determine if the user can view their bid history
     */
    public function viewOwnHistory(User $user): bool
    {
        return $user->isActive();
    }

    /**
     * Determine if the user can view bid statistics
     */
    public function viewStatistics(User $user): bool
    {
        return $user->isActive();
    }
}
