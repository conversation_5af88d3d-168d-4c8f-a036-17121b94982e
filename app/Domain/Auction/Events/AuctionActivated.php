<?php

declare(strict_types=1);

namespace App\Domain\Auction\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\User\ValueObjects\UserId;

class AuctionActivated extends DomainEvent
{
    public function __construct(UserId $sellerId, array $payload = [])
    {
        parent::__construct($sellerId, $payload);
    }

    public function auctionId(): int
    {
        return $this->payload()['auction_id'];
    }

    public function title(): string
    {
        return $this->payload()['title'];
    }

    public function sellerId(): int
    {
        return $this->userId()->value();
    }
}
