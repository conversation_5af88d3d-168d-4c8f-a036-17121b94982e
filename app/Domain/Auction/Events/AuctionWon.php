<?php

declare(strict_types=1);

namespace App\Domain\Auction\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\User\ValueObjects\UserId;

class AuctionWon extends DomainEvent
{
    public function __construct(UserId $winnerId, array $payload = [])
    {
        parent::__construct($winnerId, $payload);
    }

    public function auctionId(): int
    {
        return $this->payload()['auction_id'];
    }

    public function title(): string
    {
        return $this->payload()['title'];
    }

    public function finalPrice(): float
    {
        return $this->payload()['final_price'];
    }

    public function currency(): string
    {
        return $this->payload()['currency'];
    }

    public function sellerId(): int
    {
        return $this->payload()['seller_id'];
    }

    public function winnerId(): int
    {
        return $this->userId()->value();
    }
}
