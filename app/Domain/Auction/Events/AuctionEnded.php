<?php

declare(strict_types=1);

namespace App\Domain\Auction\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\User\ValueObjects\UserId;

class AuctionEnded extends DomainEvent
{
    public function __construct(UserId $sellerId, array $payload = [])
    {
        parent::__construct($sellerId, $payload);
    }

    public function auctionId(): int
    {
        return $this->payload()['auction_id'];
    }

    public function title(): string
    {
        return $this->payload()['title'];
    }

    public function winnerId(): ?int
    {
        return $this->payload()['winner_id'] ?? null;
    }

    public function finalPrice(): ?float
    {
        return $this->payload()['final_price'] ?? null;
    }

    public function currency(): ?string
    {
        return $this->payload()['currency'] ?? null;
    }

    public function bidsCount(): int
    {
        return $this->payload()['bids_count'];
    }

    public function sellerId(): int
    {
        return $this->userId()->value();
    }

    public function hasWinner(): bool
    {
        return $this->winnerId() !== null;
    }
}
