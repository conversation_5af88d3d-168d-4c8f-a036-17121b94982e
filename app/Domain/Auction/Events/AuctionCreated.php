<?php

declare(strict_types=1);

namespace App\Domain\Auction\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\User\ValueObjects\UserId;

class AuctionCreated extends DomainEvent
{
    public function __construct(UserId $sellerId, array $payload = [])
    {
        parent::__construct($sellerId, $payload);
    }

    public function auctionId(): int
    {
        return $this->payload()['auction_id'];
    }

    public function title(): string
    {
        return $this->payload()['title'];
    }

    public function startingPrice(): float
    {
        return $this->payload()['starting_price'];
    }

    public function currency(): string
    {
        return $this->payload()['currency'];
    }

    public function startTime(): string
    {
        return $this->payload()['start_time'];
    }

    public function endTime(): string
    {
        return $this->payload()['end_time'];
    }

    public function sellerId(): int
    {
        return $this->userId()->value();
    }
}
