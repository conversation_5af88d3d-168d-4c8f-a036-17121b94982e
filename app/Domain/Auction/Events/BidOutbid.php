<?php

declare(strict_types=1);

namespace App\Domain\Auction\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\User\ValueObjects\UserId;

class BidOutbid extends DomainEvent
{
    public function __construct(UserId $bidderId, array $payload = [])
    {
        parent::__construct($bidderId, $payload);
    }

    public function bidId(): int
    {
        return $this->payload()['bid_id'];
    }

    public function auctionId(): int
    {
        return $this->payload()['auction_id'];
    }

    public function amount(): float
    {
        return $this->payload()['amount'];
    }

    public function currency(): string
    {
        return $this->payload()['currency'];
    }

    public function bidderId(): int
    {
        return $this->userId()->value();
    }
}
