<?php

declare(strict_types=1);

namespace App\Domain\Auction\Repositories;

use App\Domain\Auction\Models\Category;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Slug;
use Illuminate\Support\Collection;

interface CategoryRepositoryInterface
{
    /**
     * Find category by ID
     */
    public function findById(Id $id): ?Category;

    /**
     * Find category by ID or throw exception
     */
    public function findByIdOrFail(Id $id): Category;

    /**
     * Find category by slug
     */
    public function findBySlug(Slug $slug): ?Category;

    /**
     * Save category
     */
    public function save(Category $category): void;

    /**
     * Delete category
     */
    public function delete(Category $category): void;

    /**
     * Find all active categories
     */
    public function findActive(): Collection;

    /**
     * Find root categories (no parent)
     */
    public function findRootCategories(): Collection;

    /**
     * Find child categories of a parent
     */
    public function findChildCategories(Id $parentId): Collection;

    /**
     * Find category hierarchy (tree structure)
     */
    public function findHierarchy(): Collection;

    /**
     * Find categories ordered by sort order
     */
    public function findOrderedBySort(): Collection;

    /**
     * Find categories with auction counts
     */
    public function findWithAuctionCounts(): Collection;

    /**
     * Find popular categories (by auction count)
     */
    public function findPopular(int $limit = 10): Collection;

    /**
     * Search categories by name
     */
    public function searchByName(string $query): Collection;

    /**
     * Check if category has children
     */
    public function hasChildren(Id $categoryId): bool;

    /**
     * Check if category has auctions
     */
    public function hasAuctions(Id $categoryId): bool;

    /**
     * Count auctions in category
     */
    public function countAuctions(Id $categoryId): int;

    /**
     * Count active auctions in category
     */
    public function countActiveAuctions(Id $categoryId): int;

    /**
     * Find category path (breadcrumb)
     */
    public function findCategoryPath(Id $categoryId): Collection;

    /**
     * Check if slug exists (for uniqueness)
     */
    public function slugExists(Slug $slug, ?Id $excludeId = null): bool;

    /**
     * Update sort orders
     */
    public function updateSortOrders(array $categoryOrders): void;
}
