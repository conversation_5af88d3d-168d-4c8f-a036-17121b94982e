<?php

declare(strict_types=1);

namespace App\Domain\Auction\Repositories;

use App\Domain\Auction\Models\Bid;
use App\Domain\Auction\ValueObjects\BidAmount;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface BidRepositoryInterface
{
    /**
     * Find bid by ID
     */
    public function findById(Id $id): ?Bid;

    /**
     * Find bid by ID or throw exception
     */
    public function findByIdOrFail(Id $id): Bid;

    /**
     * Save bid
     */
    public function save(Bid $bid): void;

    /**
     * Delete bid
     */
    public function delete(Bid $bid): void;

    /**
     * Find bids for auction
     */
    public function findByAuction(Id $auctionId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find bids by bidder
     */
    public function findByBidder(UserId $bidderId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find highest bid for auction
     */
    public function findHighestBidForAuction(Id $auctionId): ?Bid;

    /**
     * Find current winning bid for auction
     */
    public function findWinningBidForAuction(Id $auctionId): ?Bid;

    /**
     * Find user's highest bid for auction
     */
    public function findUserHighestBidForAuction(Id $auctionId, UserId $bidderId): ?Bid;

    /**
     * Find all bids for auction ordered by amount desc
     */
    public function findAllBidsForAuction(Id $auctionId): Collection;

    /**
     * Find valid bids for auction
     */
    public function findValidBidsForAuction(Id $auctionId): Collection;

    /**
     * Find proxy bids that can auto-bid
     */
    public function findProxyBidsForAuction(Id $auctionId, BidAmount $currentBid): Collection;

    /**
     * Find bids that need to be marked as outbid
     */
    public function findBidsToMarkAsOutbid(Id $auctionId, Id $excludeBidId): Collection;

    /**
     * Count bids for auction
     */
    public function countBidsForAuction(Id $auctionId): int;

    /**
     * Count valid bids for auction
     */
    public function countValidBidsForAuction(Id $auctionId): int;

    /**
     * Find user's bid history
     */
    public function findUserBidHistory(UserId $bidderId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find user's winning bids
     */
    public function findUserWinningBids(UserId $bidderId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find user's outbid bids
     */
    public function findUserOutbidBids(UserId $bidderId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Check if user has bid on auction
     */
    public function hasUserBidOnAuction(Id $auctionId, UserId $bidderId): bool;

    /**
     * Get bid statistics for auction
     */
    public function getBidStatisticsForAuction(Id $auctionId): array;

    /**
     * Get user bid statistics
     */
    public function getUserBidStatistics(UserId $bidderId): array;

    /**
     * Mark all other bids as not winning for auction
     */
    public function markOtherBidsAsNotWinning(Id $auctionId, Id $winningBidId): void;

    /**
     * Invalidate bids with reason
     */
    public function invalidateBids(Collection $bidIds, string $reason): void;

    /**
     * Find recent bids for auction (for real-time updates)
     */
    public function findRecentBidsForAuction(Id $auctionId, int $minutes = 5): Collection;
}
