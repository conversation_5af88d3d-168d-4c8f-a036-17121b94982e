<?php

declare(strict_types=1);

namespace App\Domain\User\Repositories;

use App\Domain\User\Models\User;
use App\Domain\User\ValueObjects\Email;
use App\Domain\User\ValueObjects\UserId;
use App\Domain\User\ValueObjects\UserRole;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface UserRepositoryInterface
{
    /**
     * Find user by ID
     */
    public function findById(UserId $id): ?User;

    /**
     * Find user by ID or throw exception
     */
    public function findByIdOrFail(UserId $id): User;

    /**
     * Find user by email
     */
    public function findByEmail(Email $email): ?User;

    /**
     * Save user
     */
    public function save(User $user): void;

    /**
     * Delete user
     */
    public function delete(User $user): void;

    /**
     * Find users by role
     */
    public function findByRole(UserRole $role, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find active users
     */
    public function findActive(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find inactive users
     */
    public function findInactive(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find verified users
     */
    public function findVerified(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find unverified users
     */
    public function findUnverified(int $perPage = 15): LengthAwarePaginator;

    /**
     * Search users by name or email
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find recently registered users
     */
    public function findRecentlyRegistered(int $days = 7, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find users who haven't logged in for specified days
     */
    public function findInactiveForDays(int $days = 30, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find top sellers (by auction count or sales volume)
     */
    public function findTopSellers(int $limit = 10): Collection;

    /**
     * Find top bidders (by bid count or bid volume)
     */
    public function findTopBidders(int $limit = 10): Collection;

    /**
     * Check if email exists
     */
    public function emailExists(Email $email, ?UserId $excludeId = null): bool;

    /**
     * Count users by role
     */
    public function countByRole(UserRole $role): int;

    /**
     * Count active users
     */
    public function countActive(): int;

    /**
     * Count verified users
     */
    public function countVerified(): int;

    /**
     * Get user statistics
     */
    public function getUserStatistics(UserId $userId): array;

    /**
     * Get platform user statistics
     */
    public function getPlatformStatistics(): array;

    /**
     * Find users with most auctions
     */
    public function findUsersWithMostAuctions(int $limit = 10): Collection;

    /**
     * Find users with highest successful bid rate
     */
    public function findUsersWithHighestBidSuccessRate(int $limit = 10): Collection;

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(UserId $userId): void;

    /**
     * Bulk update user roles
     */
    public function bulkUpdateRoles(array $userRoles): void;

    /**
     * Find users for notification (active and verified)
     */
    public function findForNotification(): Collection;
}
