<?php

declare(strict_types=1);

namespace App\Domain\Shared\Exceptions;

class BusinessRuleException extends DomainException
{
    public function __construct(string $message, string $errorCode = 'BUSINESS_RULE_VIOLATION', array $context = [])
    {
        parent::__construct($message, $errorCode, $context);
    }

    public static function auctionNotActive(int $auctionId): self
    {
        return new self(
            'Auction is not active and cannot accept bids',
            'AUCTION_NOT_ACTIVE',
            ['auction_id' => $auctionId]
        );
    }

    public static function bidTooLow(float $bidAmount, float $minimumBid): self
    {
        return new self(
            sprintf('Bid amount %.2f is below minimum required bid of %.2f', $bidAmount, $minimumBid),
            'BID_TOO_LOW',
            ['bid_amount' => $bidAmount, 'minimum_bid' => $minimumBid]
        );
    }

    public static function cannotBidOnOwnAuction(): self
    {
        return new self(
            'You cannot bid on your own auction',
            'CANNOT_BID_ON_OWN_AUCTION'
        );
    }

    public static function auctionHasEnded(int $auctionId): self
    {
        return new self(
            'Auction has ended and no longer accepts bids',
            'AUCTION_HAS_ENDED',
            ['auction_id' => $auctionId]
        );
    }

    public static function insufficientPermissions(string $action): self
    {
        return new self(
            sprintf('Insufficient permissions to perform action: %s', $action),
            'INSUFFICIENT_PERMISSIONS',
            ['action' => $action]
        );
    }

    public static function userNotVerified(): self
    {
        return new self(
            'User email must be verified to perform this action',
            'USER_NOT_VERIFIED'
        );
    }

    public static function paymentAlreadyProcessed(int $paymentId): self
    {
        return new self(
            'Payment has already been processed',
            'PAYMENT_ALREADY_PROCESSED',
            ['payment_id' => $paymentId]
        );
    }

    public static function refundAmountExceedsPayment(float $refundAmount, float $paymentAmount): self
    {
        return new self(
            sprintf('Refund amount %.2f exceeds payment amount %.2f', $refundAmount, $paymentAmount),
            'REFUND_AMOUNT_EXCEEDS_PAYMENT',
            ['refund_amount' => $refundAmount, 'payment_amount' => $paymentAmount]
        );
    }

    public static function auctionCannotBeEdited(string $status): self
    {
        return new self(
            sprintf('Auction with status "%s" cannot be edited', $status),
            'AUCTION_CANNOT_BE_EDITED',
            ['status' => $status]
        );
    }

    public static function categoryHasActiveAuctions(int $categoryId, int $auctionCount): self
    {
        return new self(
            sprintf('Category cannot be deleted as it has %d active auctions', $auctionCount),
            'CATEGORY_HAS_ACTIVE_AUCTIONS',
            ['category_id' => $categoryId, 'auction_count' => $auctionCount]
        );
    }

    public static function maxAuctionsPerDayExceeded(int $maxAllowed, int $currentCount): self
    {
        return new self(
            sprintf('Maximum auctions per day (%d) exceeded. Current count: %d', $maxAllowed, $currentCount),
            'MAX_AUCTIONS_PER_DAY_EXCEEDED',
            ['max_allowed' => $maxAllowed, 'current_count' => $currentCount]
        );
    }

    public static function reservePriceNotMet(float $currentBid, float $reservePrice): self
    {
        return new self(
            sprintf('Current bid %.2f does not meet reserve price of %.2f', $currentBid, $reservePrice),
            'RESERVE_PRICE_NOT_MET',
            ['current_bid' => $currentBid, 'reserve_price' => $reservePrice]
        );
    }
}
