<?php

declare(strict_types=1);

namespace App\Domain\Shared\Exceptions;

class ValidationException extends DomainException
{
    private array $errors;

    public function __construct(string $message, array $errors = [], array $context = [])
    {
        parent::__construct($message, 'VALIDATION_ERROR', $context);
        $this->errors = $errors;
    }

    public static function withErrors(array $errors, string $message = 'Validation failed'): self
    {
        return new self($message, $errors);
    }

    public static function singleError(string $field, string $error, string $message = 'Validation failed'): self
    {
        return new self($message, [$field => [$error]]);
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function getFirstError(): ?string
    {
        if (empty($this->errors)) {
            return null;
        }

        $firstField = array_key_first($this->errors);
        $firstFieldErrors = $this->errors[$firstField];
        
        return is_array($firstFieldErrors) ? $firstFieldErrors[0] : $firstFieldErrors;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'errors' => $this->errors,
        ]);
    }
}
