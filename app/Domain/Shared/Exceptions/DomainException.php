<?php

declare(strict_types=1);

namespace App\Domain\Shared\Exceptions;

use Exception;

abstract class DomainException extends Exception
{
    protected string $errorCode;
    protected array $context;

    public function __construct(string $message, string $errorCode = '', array $context = [], int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errorCode = $errorCode ?: static::class;
        $this->context = $context;
    }

    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function toArray(): array
    {
        return [
            'error_code' => $this->errorCode,
            'message' => $this->getMessage(),
            'context' => $this->context,
        ];
    }
}
