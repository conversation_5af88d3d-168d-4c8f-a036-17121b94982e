<?php

declare(strict_types=1);

namespace App\Infrastructure\Providers;

use App\Domain\Shared\Events\EventDispatcher;
use App\Infrastructure\Services\LaravelEventDispatcher;
use App\Infrastructure\Services\ImageUploadService;
use App\Infrastructure\Services\NotificationService;
use App\Infrastructure\Services\PaymentService;
use App\Infrastructure\Services\WebSocketService;
use Illuminate\Support\ServiceProvider;

class DomainServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Event dispatcher
        $this->app->bind(
            EventDispatcher::class,
            LaravelEventDispatcher::class
        );

        // Infrastructure services
        $this->app->singleton(PaymentService::class);
        $this->app->singleton(NotificationService::class);
        $this->app->singleton(WebSocketService::class);
        $this->app->singleton(ImageUploadService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
