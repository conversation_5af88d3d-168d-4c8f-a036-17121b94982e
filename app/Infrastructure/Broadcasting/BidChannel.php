<?php

declare(strict_types=1);

namespace App\Infrastructure\Broadcasting;

use App\Models\User;
use App\Models\Auction;

class BidChannel
{
    /**
     * Authenticate the user's access to the channel.
     */
    public function join(User $user, int $auctionId): array|bool
    {
        // Check if auction exists and is active
        $auction = Auction::find($auctionId);
        
        if (!$auction || $auction->status !== 'active') {
            return false;
        }

        // All authenticated users can join bid channels for active auctions
        return [
            'id' => $user->id,
            'name' => $user->name,
        ];
    }
}
