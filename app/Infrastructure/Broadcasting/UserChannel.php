<?php

declare(strict_types=1);

namespace App\Infrastructure\Broadcasting;

use App\Models\User;

class UserChannel
{
    /**
     * Authenticate the user's access to the channel.
     */
    public function join(User $user, int $userId): array|bool
    {
        // Users can only join their own private channels
        return $user->id === $userId ? [
            'id' => $user->id,
            'name' => $user->name,
        ] : false;
    }
}
