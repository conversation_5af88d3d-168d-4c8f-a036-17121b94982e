<?php

declare(strict_types=1);

namespace App\Infrastructure\Broadcasting;

use App\Models\User;
use App\Models\Auction;

class AuctionChannel
{
    /**
     * Authenticate the user's access to the channel.
     */
    public function join(User $user, int $auctionId): array|bool
    {
        // All authenticated users can join auction channels
        // for public auctions
        $auction = Auction::find($auctionId);
        
        if (!$auction) {
            return false;
        }

        // Allow access to active and ended auctions
        if (in_array($auction->status, ['active', 'ended'])) {
            return [
                'id' => $user->id,
                'name' => $user->name,
            ];
        }

        // For draft/scheduled auctions, only owner and admins
        if ($auction->user_id === $user->id || $user->role === 'admin') {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'role' => $user->role,
            ];
        }

        return false;
    }
}
