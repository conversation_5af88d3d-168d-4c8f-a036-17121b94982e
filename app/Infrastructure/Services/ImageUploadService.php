<?php

declare(strict_types=1);

namespace App\Infrastructure\Services;

use App\Domain\Shared\ValueObjects\ImageUrl;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageUploadService
{
    private ImageManager $imageManager;
    private string $disk;
    private array $allowedMimeTypes;
    private int $maxFileSize;
    private array $thumbnailSizes;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
        $this->disk = config('filesystems.default');
        $this->allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $this->maxFileSize = config('auction.max_image_size', 5120) * 1024; // Convert KB to bytes
        $this->thumbnailSizes = [
            'thumb' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 400, 'height' => 400],
            'large' => ['width' => 800, 'height' => 800],
        ];
    }

    /**
     * Upload and process auction image
     */
    public function uploadAuctionImage(
        UploadedFile $file,
        int $auctionId,
        int $sortOrder = 0,
        bool $isPrimary = false
    ): array {
        $this->validateFile($file);

        $filename = $this->generateFilename($file);
        $path = "auctions/{$auctionId}";
        
        // Store original image
        $originalPath = $file->storeAs($path, $filename, $this->disk);
        $originalUrl = Storage::disk($this->disk)->url($originalPath);

        // Process and create thumbnails
        $thumbnails = $this->createThumbnails($file, $path, $filename);

        // Get image metadata
        $metadata = $this->getImageMetadata($file);

        return [
            'filename' => $filename,
            'original_name' => $file->getClientOriginalName(),
            'path' => $originalPath,
            'url' => $originalUrl,
            'thumbnail_url' => $thumbnails['thumb'] ?? null,
            'thumbnails' => $thumbnails,
            'size_bytes' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'width' => $metadata['width'],
            'height' => $metadata['height'],
            'sort_order' => $sortOrder,
            'is_primary' => $isPrimary,
        ];
    }

    /**
     * Upload user avatar
     */
    public function uploadAvatar(UploadedFile $file, int $userId): array
    {
        $this->validateFile($file);

        $filename = $this->generateFilename($file);
        $path = "avatars/{$userId}";
        
        // Resize to standard avatar size
        $image = $this->imageManager->read($file->getPathname());
        $image->resize(200, 200);
        
        // Store processed avatar
        $avatarPath = "{$path}/{$filename}";
        Storage::disk($this->disk)->put($avatarPath, $image->encode());
        
        $avatarUrl = Storage::disk($this->disk)->url($avatarPath);

        return [
            'filename' => $filename,
            'path' => $avatarPath,
            'url' => $avatarUrl,
            'size_bytes' => Storage::disk($this->disk)->size($avatarPath),
        ];
    }

    /**
     * Create thumbnails for an image
     */
    public function createThumbnails(UploadedFile $file, string $basePath, string $filename): array
    {
        $thumbnails = [];
        $image = $this->imageManager->read($file->getPathname());
        
        foreach ($this->thumbnailSizes as $size => $dimensions) {
            $thumbnailImage = clone $image;
            $thumbnailImage->resize($dimensions['width'], $dimensions['height']);
            
            $thumbnailFilename = $this->getThumbnailFilename($filename, $size);
            $thumbnailPath = "{$basePath}/thumbnails/{$thumbnailFilename}";
            
            Storage::disk($this->disk)->put($thumbnailPath, $thumbnailImage->encode());
            $thumbnails[$size] = Storage::disk($this->disk)->url($thumbnailPath);
        }

        return $thumbnails;
    }

    /**
     * Delete image and its thumbnails
     */
    public function deleteImage(string $path): bool
    {
        $deleted = Storage::disk($this->disk)->delete($path);
        
        // Delete thumbnails
        $pathInfo = pathinfo($path);
        $thumbnailBasePath = $pathInfo['dirname'] . '/thumbnails/';
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];
        
        foreach (array_keys($this->thumbnailSizes) as $size) {
            $thumbnailPath = $thumbnailBasePath . $filename . "_{$size}.{$extension}";
            Storage::disk($this->disk)->delete($thumbnailPath);
        }

        return $deleted;
    }

    /**
     * Optimize image for web
     */
    public function optimizeImage(string $path, int $quality = 85): void
    {
        $image = $this->imageManager->read(Storage::disk($this->disk)->path($path));
        
        // Optimize based on file type
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                $optimized = $image->toJpeg($quality);
                break;
            case 'png':
                $optimized = $image->toPng();
                break;
            case 'webp':
                $optimized = $image->toWebp($quality);
                break;
            default:
                return; // Skip optimization for unsupported formats
        }
        
        Storage::disk($this->disk)->put($path, $optimized);
    }

    /**
     * Convert image to WebP format
     */
    public function convertToWebP(string $path): string
    {
        $image = $this->imageManager->read(Storage::disk($this->disk)->path($path));
        
        $pathInfo = pathinfo($path);
        $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
        
        $webpImage = $image->toWebp(85);
        Storage::disk($this->disk)->put($webpPath, $webpImage);
        
        return $webpPath;
    }

    /**
     * Get image metadata
     */
    public function getImageMetadata(UploadedFile $file): array
    {
        $image = $this->imageManager->read($file->getPathname());
        
        return [
            'width' => $image->width(),
            'height' => $image->height(),
            'size_bytes' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'extension' => $file->getClientOriginalExtension(),
        ];
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file): void
    {
        if (!$file->isValid()) {
            throw new \InvalidArgumentException('Invalid file upload');
        }

        if (!in_array($file->getMimeType(), $this->allowedMimeTypes)) {
            throw new \InvalidArgumentException('File type not allowed. Allowed types: ' . implode(', ', $this->allowedMimeTypes));
        }

        if ($file->getSize() > $this->maxFileSize) {
            $maxSizeMB = $this->maxFileSize / 1024 / 1024;
            throw new \InvalidArgumentException("File size exceeds maximum allowed size of {$maxSizeMB}MB");
        }

        // Check if it's actually an image
        $imageInfo = getimagesize($file->getPathname());
        if ($imageInfo === false) {
            throw new \InvalidArgumentException('File is not a valid image');
        }
    }

    /**
     * Generate unique filename
     */
    private function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $hash = hash('sha256', $file->getPathname() . time());
        return substr($hash, 0, 32) . '.' . $extension;
    }

    /**
     * Get thumbnail filename
     */
    private function getThumbnailFilename(string $originalFilename, string $size): string
    {
        $pathInfo = pathinfo($originalFilename);
        return $pathInfo['filename'] . "_{$size}." . $pathInfo['extension'];
    }

    /**
     * Get storage URL for path
     */
    public function getUrl(string $path): string
    {
        return Storage::disk($this->disk)->url($path);
    }

    /**
     * Check if file exists
     */
    public function exists(string $path): bool
    {
        return Storage::disk($this->disk)->exists($path);
    }

    /**
     * Get file size
     */
    public function getSize(string $path): int
    {
        return Storage::disk($this->disk)->size($path);
    }

    /**
     * Move image to different location
     */
    public function moveImage(string $fromPath, string $toPath): bool
    {
        return Storage::disk($this->disk)->move($fromPath, $toPath);
    }

    /**
     * Copy image to different location
     */
    public function copyImage(string $fromPath, string $toPath): bool
    {
        return Storage::disk($this->disk)->copy($fromPath, $toPath);
    }
}
