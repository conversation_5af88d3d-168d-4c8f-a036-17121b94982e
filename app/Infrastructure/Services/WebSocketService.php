<?php

declare(strict_types=1);

namespace App\Infrastructure\Services;

use App\Domain\User\ValueObjects\UserId;
use Illuminate\Support\Facades\Log;
use Pusher\Pusher;

class WebSocketService
{
    private Pusher $pusher;

    public function __construct()
    {
        $this->pusher = new Pusher(
            config('broadcasting.connections.pusher.key'),
            config('broadcasting.connections.pusher.secret'),
            config('broadcasting.connections.pusher.app_id'),
            [
                'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                'useTLS' => config('broadcasting.connections.pusher.options.useTLS', true),
            ]
        );
    }

    /**
     * Broadcast new bid to auction channel
     */
    public function broadcastNewBid(
        int $auctionId,
        int $bidderId,
        float $bidAmount,
        int $bidsCount,
        string $bidderName = 'Anonymous'
    ): void {
        $data = [
            'auction_id' => $auctionId,
            'bidder_id' => $bidderId,
            'bidder_name' => $bidderName,
            'bid_amount' => $bidAmount,
            'formatted_amount' => '$' . number_format($bidAmount, 2),
            'bids_count' => $bidsCount,
            'timestamp' => now()->toISOString(),
        ];

        $this->broadcast("auction.{$auctionId}", 'bid.placed', $data);
        $this->broadcast('auctions.global', 'bid.placed', $data);
    }

    /**
     * Broadcast auction status change
     */
    public function broadcastAuctionStatusChange(
        int $auctionId,
        string $oldStatus,
        string $newStatus,
        array $additionalData = []
    ): void {
        $data = array_merge([
            'auction_id' => $auctionId,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'timestamp' => now()->toISOString(),
        ], $additionalData);

        $this->broadcast("auction.{$auctionId}", 'status.changed', $data);
        $this->broadcast('auctions.global', 'auction.status.changed', $data);
    }

    /**
     * Broadcast auction ending soon
     */
    public function broadcastAuctionEndingSoon(
        int $auctionId,
        int $minutesRemaining,
        float $currentBid
    ): void {
        $data = [
            'auction_id' => $auctionId,
            'minutes_remaining' => $minutesRemaining,
            'current_bid' => $currentBid,
            'formatted_current_bid' => '$' . number_format($currentBid, 2),
            'timestamp' => now()->toISOString(),
        ];

        $this->broadcast("auction.{$auctionId}", 'ending.soon', $data);
    }

    /**
     * Broadcast auction ended
     */
    public function broadcastAuctionEnded(
        int $auctionId,
        ?int $winnerId = null,
        ?float $finalPrice = null,
        int $totalBids = 0
    ): void {
        $data = [
            'auction_id' => $auctionId,
            'winner_id' => $winnerId,
            'final_price' => $finalPrice,
            'formatted_final_price' => $finalPrice ? '$' . number_format($finalPrice, 2) : null,
            'total_bids' => $totalBids,
            'timestamp' => now()->toISOString(),
        ];

        $this->broadcast("auction.{$auctionId}", 'ended', $data);
        $this->broadcast('auctions.global', 'auction.ended', $data);
    }

    /**
     * Broadcast user notification
     */
    public function broadcastUserNotification(
        UserId $userId,
        string $type,
        string $title,
        string $message,
        array $data = []
    ): void {
        $notificationData = [
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString(),
        ];

        $this->broadcast("user.{$userId->value()}", 'notification', $notificationData);
    }

    /**
     * Broadcast outbid notification
     */
    public function broadcastOutbidNotification(
        UserId $outbidUserId,
        int $auctionId,
        float $newBidAmount,
        string $auctionTitle
    ): void {
        $this->broadcastUserNotification(
            $outbidUserId,
            'outbid',
            'You\'ve Been Outbid!',
            "You've been outbid on \"{$auctionTitle}\". New bid: $" . number_format($newBidAmount, 2),
            [
                'auction_id' => $auctionId,
                'new_bid_amount' => $newBidAmount,
                'auction_title' => $auctionTitle,
            ]
        );
    }

    /**
     * Broadcast auction view count update
     */
    public function broadcastViewCountUpdate(int $auctionId, int $viewsCount): void
    {
        $data = [
            'auction_id' => $auctionId,
            'views_count' => $viewsCount,
            'timestamp' => now()->toISOString(),
        ];

        $this->broadcast("auction.{$auctionId}", 'views.updated', $data);
    }

    /**
     * Broadcast watchers count update
     */
    public function broadcastWatchersCountUpdate(int $auctionId, int $watchersCount): void
    {
        $data = [
            'auction_id' => $auctionId,
            'watchers_count' => $watchersCount,
            'timestamp' => now()->toISOString(),
        ];

        $this->broadcast("auction.{$auctionId}", 'watchers.updated', $data);
    }

    /**
     * Broadcast system announcement
     */
    public function broadcastSystemAnnouncement(
        string $title,
        string $message,
        string $type = 'info',
        array $targetChannels = ['auctions.global']
    ): void {
        $data = [
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'timestamp' => now()->toISOString(),
        ];

        foreach ($targetChannels as $channel) {
            $this->broadcast($channel, 'system.announcement', $data);
        }
    }

    /**
     * Send private message to user
     */
    public function sendPrivateMessage(
        UserId $userId,
        string $event,
        array $data
    ): void {
        $this->broadcast("private-user.{$userId->value()}", $event, $data);
    }

    /**
     * Broadcast to multiple channels
     */
    public function broadcastToMultiple(
        array $channels,
        string $event,
        array $data
    ): void {
        foreach ($channels as $channel) {
            $this->broadcast($channel, $event, $data);
        }
    }

    /**
     * Get channel info
     */
    public function getChannelInfo(string $channel): array
    {
        try {
            return $this->pusher->getChannelInfo($channel);
        } catch (\Exception $e) {
            Log::error("Failed to get channel info for {$channel}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get channels list
     */
    public function getChannels(string $prefix = null): array
    {
        try {
            $options = [];
            if ($prefix) {
                $options['filter_by_prefix'] = $prefix;
            }
            
            return $this->pusher->getChannels($options);
        } catch (\Exception $e) {
            Log::error("Failed to get channels: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Authenticate user for private channels
     */
    public function authenticateUser(string $socketId, string $channel, UserId $userId): array
    {
        try {
            // Verify user has access to this channel
            if (!$this->canAccessChannel($channel, $userId)) {
                throw new \Exception('User does not have access to this channel');
            }

            return $this->pusher->socketAuth($channel, $socketId);
        } catch (\Exception $e) {
            Log::error("Failed to authenticate user for channel {$channel}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Core broadcast method
     */
    private function broadcast(string $channel, string $event, array $data): void
    {
        try {
            $this->pusher->trigger($channel, $event, $data);
            
            Log::debug("WebSocket broadcast sent", [
                'channel' => $channel,
                'event' => $event,
                'data_keys' => array_keys($data),
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to broadcast to {$channel}: " . $e->getMessage(), [
                'channel' => $channel,
                'event' => $event,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if user can access a channel
     */
    private function canAccessChannel(string $channel, UserId $userId): bool
    {
        // Private user channels
        if (str_starts_with($channel, 'private-user.')) {
            $channelUserId = str_replace('private-user.', '', $channel);
            return $channelUserId === (string) $userId->value();
        }

        // Auction channels are generally public
        if (str_starts_with($channel, 'auction.')) {
            return true;
        }

        // Global channels are public
        if ($channel === 'auctions.global') {
            return true;
        }

        // Default to deny access
        return false;
    }
}
