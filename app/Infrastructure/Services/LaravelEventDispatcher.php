<?php

declare(strict_types=1);

namespace App\Infrastructure\Services;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Shared\Events\EventDispatcher;
use Illuminate\Contracts\Events\Dispatcher as LaravelDispatcher;

class LaravelEventDispatcher implements EventDispatcher
{
    private LaravelDispatcher $dispatcher;

    public function __construct(LaravelDispatcher $dispatcher)
    {
        $this->dispatcher = $dispatcher;
    }

    public function dispatch(DomainEvent $event): void
    {
        $this->dispatcher->dispatch($event);
    }

    public function dispatchAll(array $events): void
    {
        foreach ($events as $event) {
            $this->dispatch($event);
        }
    }
}
