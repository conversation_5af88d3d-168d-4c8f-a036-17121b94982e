<?php

declare(strict_types=1);

namespace App\Infrastructure\Services;

use App\Domain\Payment\Models\Payment;
use App\Domain\Payment\ValueObjects\PaymentAmount;
use App\Domain\Payment\ValueObjects\PaymentMethod;
use App\Domain\Payment\ValueObjects\PaymentStatus;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\User\ValueObjects\UserId;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;

class PaymentService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a payment intent with Stripe
     */
    public function createPaymentIntent(
        PaymentAmount $amount,
        PaymentMethod $paymentMethod,
        UserId $userId,
        ?Id $auctionId = null,
        array $metadata = []
    ): array {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $amount->toCents(),
                'currency' => strtolower($amount->currency()),
                'payment_method_types' => [$paymentMethod->type()],
                'metadata' => array_merge($metadata, [
                    'user_id' => $userId->value(),
                    'auction_id' => $auctionId?->value(),
                ]),
                'description' => $this->generateDescription($auctionId),
            ]);

            return [
                'payment_intent_id' => $paymentIntent->id,
                'client_secret' => $paymentIntent->client_secret,
                'status' => $paymentIntent->status,
            ];
        } catch (ApiErrorException $e) {
            throw new \Exception('Failed to create payment intent: ' . $e->getMessage());
        }
    }

    /**
     * Confirm a payment intent
     */
    public function confirmPaymentIntent(string $paymentIntentId, string $paymentMethodId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            
            $paymentIntent->confirm([
                'payment_method' => $paymentMethodId,
            ]);

            return [
                'status' => $paymentIntent->status,
                'charges' => $paymentIntent->charges->data,
            ];
        } catch (ApiErrorException $e) {
            throw new \Exception('Failed to confirm payment: ' . $e->getMessage());
        }
    }

    /**
     * Capture a payment intent (for manual capture)
     */
    public function capturePaymentIntent(string $paymentIntentId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            $paymentIntent->capture();

            return [
                'status' => $paymentIntent->status,
                'amount_received' => $paymentIntent->amount_received,
            ];
        } catch (ApiErrorException $e) {
            throw new \Exception('Failed to capture payment: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a payment intent
     */
    public function cancelPaymentIntent(string $paymentIntentId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            $paymentIntent->cancel();

            return [
                'status' => $paymentIntent->status,
            ];
        } catch (ApiErrorException $e) {
            throw new \Exception('Failed to cancel payment: ' . $e->getMessage());
        }
    }

    /**
     * Create a refund
     */
    public function createRefund(string $paymentIntentId, Money $refundAmount, string $reason = null): array
    {
        try {
            $refund = \Stripe\Refund::create([
                'payment_intent' => $paymentIntentId,
                'amount' => $refundAmount->toCents(),
                'reason' => $reason,
            ]);

            return [
                'refund_id' => $refund->id,
                'status' => $refund->status,
                'amount' => $refund->amount,
            ];
        } catch (ApiErrorException $e) {
            throw new \Exception('Failed to create refund: ' . $e->getMessage());
        }
    }

    /**
     * Get payment intent details
     */
    public function getPaymentIntent(string $paymentIntentId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            return [
                'id' => $paymentIntent->id,
                'status' => $paymentIntent->status,
                'amount' => $paymentIntent->amount,
                'currency' => $paymentIntent->currency,
                'payment_method' => $paymentIntent->payment_method,
                'charges' => $paymentIntent->charges->data,
                'metadata' => $paymentIntent->metadata->toArray(),
            ];
        } catch (ApiErrorException $e) {
            throw new \Exception('Failed to retrieve payment intent: ' . $e->getMessage());
        }
    }

    /**
     * Calculate platform fee
     */
    public function calculatePlatformFee(Money $amount): Money
    {
        $feeRate = config('auction.platform_fee_rate', 0.029); // 2.9%
        $fixedFee = new Money(0.30, $amount->currency()); // $0.30 fixed fee
        
        $percentageFee = $amount->multiply($feeRate);
        return $percentageFee->add($fixedFee);
    }

    /**
     * Process auction payment
     */
    public function processAuctionPayment(
        UserId $winnerId,
        Id $auctionId,
        Money $finalPrice,
        string $paymentMethodId
    ): Payment {
        $platformFee = $this->calculatePlatformFee($finalPrice);
        $paymentAmount = PaymentAmount::fromGrossAmount($finalPrice, $platformFee->amount() / $finalPrice->amount());
        
        $paymentMethod = PaymentMethod::card('unknown', '****', 1, 2025); // This should be retrieved from Stripe
        
        // Create payment intent
        $paymentIntentData = $this->createPaymentIntent(
            $paymentAmount,
            $paymentMethod,
            $winnerId,
            $auctionId,
            ['type' => 'auction_payment']
        );

        // Create domain payment
        $payment = Payment::create(
            Id::generate(),
            $winnerId,
            $paymentAmount,
            $paymentMethod,
            $auctionId,
            'Payment for auction win'
        );

        $payment->setPaymentIntentId($paymentIntentData['payment_intent_id']);
        
        return $payment;
    }

    /**
     * Handle webhook events from Stripe
     */
    public function handleWebhook(array $event): void
    {
        switch ($event['type']) {
            case 'payment_intent.succeeded':
                $this->handlePaymentSucceeded($event['data']['object']);
                break;
            case 'payment_intent.payment_failed':
                $this->handlePaymentFailed($event['data']['object']);
                break;
            case 'charge.dispute.created':
                $this->handleChargeDispute($event['data']['object']);
                break;
        }
    }

    private function handlePaymentSucceeded(array $paymentIntent): void
    {
        // Find payment by payment intent ID and mark as succeeded
        // This would integrate with your payment repository
    }

    private function handlePaymentFailed(array $paymentIntent): void
    {
        // Find payment by payment intent ID and mark as failed
        // This would integrate with your payment repository
    }

    private function handleChargeDispute(array $dispute): void
    {
        // Handle charge disputes
        // This would integrate with your notification system
    }

    private function generateDescription(?Id $auctionId): string
    {
        if ($auctionId) {
            return "Payment for auction #{$auctionId->value()}";
        }
        
        return "Platform payment";
    }
}
