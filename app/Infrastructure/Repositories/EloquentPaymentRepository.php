<?php

declare(strict_types=1);

namespace App\Infrastructure\Repositories;

use App\Domain\Payment\Models\Payment as DomainPayment;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\Payment\ValueObjects\PaymentStatus;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Models\Payment;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class EloquentPaymentRepository implements PaymentRepositoryInterface
{
    public function findById(Id $id): ?DomainPayment
    {
        $payment = Payment::find($id->value());
        return $payment ? $this->toDomainModel($payment) : null;
    }

    public function findByIdOrFail(Id $id): DomainPayment
    {
        $payment = Payment::findOrFail($id->value());
        return $this->toDomainModel($payment);
    }

    public function findByPaymentIntentId(string $paymentIntentId): ?DomainPayment
    {
        $payment = Payment::where('payment_intent_id', $paymentIntentId)->first();
        return $payment ? $this->toDomainModel($payment) : null;
    }

    public function findByStripePaymentIntentId(string $stripePaymentIntentId): ?DomainPayment
    {
        $payment = Payment::where('stripe_payment_intent_id', $stripePaymentIntentId)->first();
        return $payment ? $this->toDomainModel($payment) : null;
    }

    public function save(DomainPayment $payment): void
    {
        $eloquentPayment = $this->toEloquentModel($payment);
        $eloquentPayment->save();
    }

    public function delete(DomainPayment $payment): void
    {
        Payment::destroy($payment->id()->value());
    }

    public function findByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Payment::where('user_id', $userId->value())
            ->with(['auction'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findByAuction(Id $auctionId, int $perPage = 15): LengthAwarePaginator
    {
        return Payment::where('auction_id', $auctionId->value())
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findByStatus(PaymentStatus $status, int $perPage = 15): LengthAwarePaginator
    {
        return Payment::where('status', $status->value())
            ->with(['user', 'auction'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findSuccessful(int $perPage = 15): LengthAwarePaginator
    {
        return Payment::successful()
            ->with(['user', 'auction'])
            ->orderBy('processed_at', 'desc')
            ->paginate($perPage);
    }

    public function findFailed(int $perPage = 15): LengthAwarePaginator
    {
        return Payment::failed()
            ->with(['user', 'auction'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findPending(int $perPage = 15): LengthAwarePaginator
    {
        return Payment::pending()
            ->with(['user', 'auction'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findRefunded(int $perPage = 15): LengthAwarePaginator
    {
        return Payment::refunded()
            ->with(['user', 'auction'])
            ->orderBy('refunded_at', 'desc')
            ->paginate($perPage);
    }

    public function findForAuctionWinner(Id $auctionId, UserId $winnerId): ?DomainPayment
    {
        $payment = Payment::where('auction_id', $auctionId->value())
            ->where('user_id', $winnerId->value())
            ->first();

        return $payment ? $this->toDomainModel($payment) : null;
    }

    public function findUserPaymentHistory(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Payment::where('user_id', $userId->value())
            ->with(['auction'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findStuckInProcessing(int $minutes = 30): Collection
    {
        return Payment::where('status', 'processing')
            ->where('created_at', '<=', now()->subMinutes($minutes))
            ->get();
    }

    public function findRetryable(int $perPage = 15): LengthAwarePaginator
    {
        return Payment::whereIn('status', ['failed', 'cancelled'])
            ->with(['user', 'auction'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function countByStatus(PaymentStatus $status): int
    {
        return Payment::where('status', $status->value())->count();
    }

    public function countUserPaymentsByStatus(UserId $userId, PaymentStatus $status): int
    {
        return Payment::where('user_id', $userId->value())
            ->where('status', $status->value())
            ->count();
    }

    public function getPaymentStatistics(): array
    {
        return [
            'total_payments' => Payment::count(),
            'successful_payments' => Payment::where('status', 'succeeded')->count(),
            'failed_payments' => Payment::where('status', 'failed')->count(),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'total_volume' => Payment::where('status', 'succeeded')->sum('amount'),
            'total_fees' => Payment::where('status', 'succeeded')->sum('fee_amount'),
            'total_refunds' => Payment::where('status', 'refunded')->sum('refund_amount'),
            'average_payment' => Payment::where('status', 'succeeded')->avg('amount'),
        ];
    }

    public function getUserPaymentStatistics(UserId $userId): array
    {
        $userPayments = Payment::where('user_id', $userId->value());

        return [
            'total_payments' => $userPayments->count(),
            'successful_payments' => $userPayments->where('status', 'succeeded')->count(),
            'failed_payments' => $userPayments->where('status', 'failed')->count(),
            'total_spent' => $userPayments->where('status', 'succeeded')->sum('amount'),
            'total_refunded' => $userPayments->sum('refund_amount'),
            'average_payment' => $userPayments->where('status', 'succeeded')->avg('amount'),
        ];
    }

    public function calculateTotalVolume(): float
    {
        return (float) Payment::where('status', 'succeeded')->sum('amount');
    }

    public function calculateUserTotalVolume(UserId $userId): float
    {
        return (float) Payment::where('user_id', $userId->value())
            ->where('status', 'succeeded')
            ->sum('amount');
    }

    public function calculateTotalFeesCollected(): float
    {
        return (float) Payment::where('status', 'succeeded')->sum('fee_amount');
    }

    public function calculateTotalRefundsIssued(): float
    {
        return (float) Payment::sum('refund_amount');
    }

    public function findWithinDateRange(\DateTime $startDate, \DateTime $endDate, int $perPage = 15): LengthAwarePaginator
    {
        return Payment::whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'auction'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findHighValue(float $threshold = 1000.0, int $perPage = 15): LengthAwarePaginator
    {
        return Payment::where('amount', '>=', $threshold)
            ->with(['user', 'auction'])
            ->orderBy('amount', 'desc')
            ->paginate($perPage);
    }

    public function findRecent(int $hours = 24): Collection
    {
        return Payment::where('created_at', '>=', now()->subHours($hours))
            ->with(['user', 'auction'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function search(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return Payment::where(function ($q) use ($query) {
                $q->where('description', 'ILIKE', "%{$query}%")
                  ->orWhere('payment_intent_id', 'ILIKE', "%{$query}%")
                  ->orWhereHas('user', function ($userQuery) use ($query) {
                      $userQuery->where('name', 'ILIKE', "%{$query}%")
                               ->orWhere('email', 'ILIKE', "%{$query}%");
                  });
            })
            ->with(['user', 'auction'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    private function toDomainModel(Payment $payment): DomainPayment
    {
        // Convert Eloquent model to Domain model
        // This is a placeholder - implement based on your domain model constructor
        throw new \Exception('Domain model conversion not implemented yet');
    }

    private function toEloquentModel(DomainPayment $payment): Payment
    {
        // Convert Domain model to Eloquent model
        // This is a placeholder - implement based on your domain model structure
        throw new \Exception('Eloquent model conversion not implemented yet');
    }
}
