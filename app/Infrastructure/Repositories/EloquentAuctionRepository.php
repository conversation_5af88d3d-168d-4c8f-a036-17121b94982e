<?php

declare(strict_types=1);

namespace App\Infrastructure\Repositories;

use App\Domain\Auction\Models\Auction as DomainAuction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\ValueObjects\AuctionStatus;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Models\Auction;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class EloquentAuctionRepository implements AuctionRepositoryInterface
{
    public function findById(Id $id): ?DomainAuction
    {
        $auction = Auction::find($id->value());
        return $auction ? $this->toDomainModel($auction) : null;
    }

    public function findByIdOrFail(Id $id): DomainAuction
    {
        $auction = Auction::findOrFail($id->value());
        return $this->toDomainModel($auction);
    }

    public function save(DomainAuction $auction): void
    {
        $eloquentAuction = $this->toEloquentModel($auction);
        $eloquentAuction->save();
    }

    public function delete(DomainAuction $auction): void
    {
        Auction::destroy($auction->id()->value());
    }

    public function findBySeller(UserId $sellerId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::where('user_id', $sellerId->value())
            ->with(['category', 'images', 'bids'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findByCategory(Id $categoryId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::where('category_id', $categoryId->value())
            ->where('status', 'active')
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function findByStatus(AuctionStatus $status, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::where('status', $status->value())
            ->with(['seller', 'category', 'images'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findActive(int $perPage = 15): LengthAwarePaginator
    {
        return Auction::active()
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function findEndingSoon(int $hours = 24, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::endingSoon($hours)
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function findFeatured(int $perPage = 15): LengthAwarePaginator
    {
        return Auction::featured()
            ->active()
            ->with(['seller', 'category', 'images'])
            ->orderBy('featured_until', 'desc')
            ->paginate($perPage);
    }

    public function findExpiredActive(): Collection
    {
        return Auction::where('status', 'active')
            ->where('end_time', '<=', now())
            ->get();
    }

    public function findScheduledToActivate(): Collection
    {
        return Auction::where('status', 'scheduled')
            ->where('start_time', '<=', now())
            ->get();
    }

    public function search(string $query, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $queryBuilder = Auction::query()
            ->where(function ($q) use ($query) {
                $q->where('title', 'ILIKE', "%{$query}%")
                  ->orWhere('description', 'ILIKE', "%{$query}%");
            });

        // Apply filters
        if (isset($filters['category_id'])) {
            $queryBuilder->where('category_id', $filters['category_id']);
        }

        if (isset($filters['status'])) {
            $queryBuilder->where('status', $filters['status']);
        }

        if (isset($filters['min_price'])) {
            $queryBuilder->where('current_bid', '>=', $filters['min_price']);
        }

        if (isset($filters['max_price'])) {
            $queryBuilder->where('current_bid', '<=', $filters['max_price']);
        }

        if (isset($filters['condition'])) {
            $queryBuilder->where('condition', $filters['condition']);
        }

        return $queryBuilder
            ->with(['seller', 'category', 'images'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findWatchedByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::whereHas('watchers', function ($query) use ($userId) {
                $query->where('user_id', $userId->value());
            })
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function findWonByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::where('winner_id', $userId->value())
            ->where('status', 'ended')
            ->with(['seller', 'category', 'images'])
            ->orderBy('actual_end_time', 'desc')
            ->paginate($perPage);
    }

    public function findBidOnByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::whereHas('bids', function ($query) use ($userId) {
                $query->where('user_id', $userId->value());
            })
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function getStatistics(): array
    {
        return [
            'total_auctions' => Auction::count(),
            'active_auctions' => Auction::where('status', 'active')->count(),
            'ended_auctions' => Auction::where('status', 'ended')->count(),
            'total_bids' => Auction::sum('bids_count'),
            'total_value' => Auction::where('status', 'ended')->sum('final_price'),
            'average_final_price' => Auction::where('status', 'ended')->avg('final_price'),
        ];
    }

    public function countByStatus(AuctionStatus $status): int
    {
        return Auction::where('status', $status->value())->count();
    }

    public function findSimilar(DomainAuction $auction, int $limit = 10): Collection
    {
        return Auction::where('category_id', $auction->categoryId()->value())
            ->where('id', '!=', $auction->id()->value())
            ->where('status', 'active')
            ->whereBetween('current_bid', [
                $auction->currentBid()->amount() * 0.5,
                $auction->currentBid()->amount() * 2
            ])
            ->limit($limit)
            ->get();
    }

    public function findRecentlyViewedByUser(UserId $userId, int $limit = 10): Collection
    {
        return Auction::whereHas('views', function ($query) use ($userId) {
                $query->where('user_id', $userId->value());
            })
            ->with(['seller', 'category', 'images'])
            ->orderBy('updated_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function incrementViewCount(Id $auctionId): void
    {
        Auction::where('id', $auctionId->value())->increment('views_count');
    }

    public function updateWatchersCount(Id $auctionId, int $count): void
    {
        Auction::where('id', $auctionId->value())->update(['watchers_count' => $count]);
    }

    public function updateBidsCount(Id $auctionId, int $count): void
    {
        Auction::where('id', $auctionId->value())->update(['bids_count' => $count]);
    }

    private function toDomainModel(Auction $auction): DomainAuction
    {
        // This would convert Eloquent model to Domain model
        // Implementation depends on your domain model constructor
        // For now, returning a placeholder
        throw new \Exception('Domain model conversion not implemented yet');
    }

    private function toEloquentModel(DomainAuction $auction): Auction
    {
        // This would convert Domain model to Eloquent model
        // Implementation depends on your domain model structure
        // For now, returning a placeholder
        throw new \Exception('Eloquent model conversion not implemented yet');
    }
}
