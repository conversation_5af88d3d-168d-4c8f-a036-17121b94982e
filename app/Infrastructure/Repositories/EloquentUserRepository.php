<?php

declare(strict_types=1);

namespace App\Infrastructure\Repositories;

use App\Domain\User\Models\User as DomainUser;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\Email;
use App\Domain\User\ValueObjects\UserId;
use App\Domain\User\ValueObjects\UserRole;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class EloquentUserRepository implements UserRepositoryInterface
{
    public function findById(UserId $id): ?DomainUser
    {
        $user = User::find($id->value());
        return $user ? $this->toDomainModel($user) : null;
    }

    public function findByIdOrFail(UserId $id): DomainUser
    {
        $user = User::findOrFail($id->value());
        return $this->toDomainModel($user);
    }

    public function findByEmail(Email $email): ?DomainUser
    {
        $user = User::where('email', $email->value())->first();
        return $user ? $this->toDomainModel($user) : null;
    }

    public function save(DomainUser $user): void
    {
        $eloquentUser = $this->toEloquentModel($user);
        $eloquentUser->save();
    }

    public function delete(DomainUser $user): void
    {
        User::destroy($user->id()->value());
    }

    public function findByRole(UserRole $role, int $perPage = 15): LengthAwarePaginator
    {
        return User::where('role', $role->value())
            ->with(['profile'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findActive(int $perPage = 15): LengthAwarePaginator
    {
        return User::where('is_active', true)
            ->with(['profile'])
            ->orderBy('last_login_at', 'desc')
            ->paginate($perPage);
    }

    public function findInactive(int $perPage = 15): LengthAwarePaginator
    {
        return User::where('is_active', false)
            ->with(['profile'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findVerified(int $perPage = 15): LengthAwarePaginator
    {
        return User::whereNotNull('email_verified_at')
            ->with(['profile'])
            ->orderBy('email_verified_at', 'desc')
            ->paginate($perPage);
    }

    public function findUnverified(int $perPage = 15): LengthAwarePaginator
    {
        return User::whereNull('email_verified_at')
            ->with(['profile'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function search(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return User::where(function ($q) use ($query) {
                $q->where('name', 'ILIKE', "%{$query}%")
                  ->orWhere('email', 'ILIKE', "%{$query}%");
            })
            ->with(['profile'])
            ->orderBy('name')
            ->paginate($perPage);
    }

    public function findRecentlyRegistered(int $days = 7, int $perPage = 15): LengthAwarePaginator
    {
        return User::where('created_at', '>=', now()->subDays($days))
            ->with(['profile'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findInactiveForDays(int $days = 30, int $perPage = 15): LengthAwarePaginator
    {
        return User::where('last_login_at', '<=', now()->subDays($days))
            ->orWhereNull('last_login_at')
            ->where('created_at', '<=', now()->subDays($days))
            ->with(['profile'])
            ->orderBy('last_login_at', 'asc')
            ->paginate($perPage);
    }

    public function findTopSellers(int $limit = 10): Collection
    {
        return User::withCount(['auctions', 'wonAuctions'])
            ->having('auctions_count', '>', 0)
            ->orderBy('auctions_count', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findTopBidders(int $limit = 10): Collection
    {
        return User::withCount('bids')
            ->having('bids_count', '>', 0)
            ->orderBy('bids_count', 'desc')
            ->limit($limit)
            ->get();
    }

    public function emailExists(Email $email, ?UserId $excludeId = null): bool
    {
        $query = User::where('email', $email->value());
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId->value());
        }

        return $query->exists();
    }

    public function countByRole(UserRole $role): int
    {
        return User::where('role', $role->value())->count();
    }

    public function countActive(): int
    {
        return User::where('is_active', true)->count();
    }

    public function countVerified(): int
    {
        return User::whereNotNull('email_verified_at')->count();
    }

    public function getUserStatistics(UserId $userId): array
    {
        $user = User::withCount([
            'auctions',
            'bids',
            'wonAuctions',
            'payments'
        ])->find($userId->value());

        if (!$user) {
            return [];
        }

        return [
            'total_auctions' => $user->auctions_count,
            'total_bids' => $user->bids_count,
            'auctions_won' => $user->won_auctions_count,
            'total_payments' => $user->payments_count,
            'member_since' => $user->created_at,
            'last_login' => $user->last_login_at,
            'is_verified' => $user->email_verified_at !== null,
        ];
    }

    public function getPlatformStatistics(): array
    {
        return [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'users_with_auctions' => User::has('auctions')->count(),
            'users_with_bids' => User::has('bids')->count(),
            'recent_registrations' => User::where('created_at', '>=', now()->subDays(30))->count(),
        ];
    }

    public function findUsersWithMostAuctions(int $limit = 10): Collection
    {
        return User::withCount('auctions')
            ->having('auctions_count', '>', 0)
            ->orderBy('auctions_count', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findUsersWithHighestBidSuccessRate(int $limit = 10): Collection
    {
        return User::selectRaw('
                users.*,
                COUNT(bids.id) as total_bids,
                COUNT(CASE WHEN bids.is_winning = true THEN 1 END) as winning_bids,
                CASE 
                    WHEN COUNT(bids.id) > 0 
                    THEN (COUNT(CASE WHEN bids.is_winning = true THEN 1 END)::float / COUNT(bids.id)) * 100
                    ELSE 0 
                END as success_rate
            ')
            ->leftJoin('bids', 'users.id', '=', 'bids.user_id')
            ->groupBy('users.id')
            ->having('total_bids', '>=', 5) // Minimum 5 bids to qualify
            ->orderBy('success_rate', 'desc')
            ->limit($limit)
            ->get();
    }

    public function updateLastLogin(UserId $userId): void
    {
        User::where('id', $userId->value())
            ->update(['last_login_at' => now()]);
    }

    public function bulkUpdateRoles(array $userRoles): void
    {
        foreach ($userRoles as $userId => $role) {
            User::where('id', $userId)
                ->update(['role' => $role]);
        }
    }

    public function findForNotification(): Collection
    {
        return User::where('is_active', true)
            ->whereNotNull('email_verified_at')
            ->get();
    }

    private function toDomainModel(User $user): DomainUser
    {
        // Convert Eloquent model to Domain model
        // This is a placeholder - implement based on your domain model constructor
        throw new \Exception('Domain model conversion not implemented yet');
    }

    private function toEloquentModel(DomainUser $user): User
    {
        // Convert Domain model to Eloquent model
        // This is a placeholder - implement based on your domain model structure
        throw new \Exception('Eloquent model conversion not implemented yet');
    }
}
