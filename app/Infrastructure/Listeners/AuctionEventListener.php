<?php

declare(strict_types=1);

namespace App\Infrastructure\Listeners;

use App\Domain\Auction\Events\AuctionCreated;
use App\Domain\Auction\Events\AuctionActivated;
use App\Domain\Auction\Events\AuctionEnded;
use App\Domain\Auction\Events\AuctionWon;
use App\Domain\Auction\Events\BidPlaced;
use App\Domain\Auction\Events\BidOutbid;
use App\Infrastructure\Services\NotificationService;
use App\Infrastructure\Services\WebSocketService;
use Illuminate\Events\Dispatcher;

class AuctionEventListener
{
    private NotificationService $notificationService;
    private WebSocketService $webSocketService;

    public function __construct(
        NotificationService $notificationService,
        WebSocketService $webSocketService
    ) {
        $this->notificationService = $notificationService;
        $this->webSocketService = $webSocketService;
    }

    /**
     * Register the listeners for the subscriber.
     */
    public function subscribe(Dispatcher $events): void
    {
        $events->listen(
            AuctionCreated::class,
            [AuctionEventListener::class, 'handleAuctionCreated']
        );

        $events->listen(
            AuctionActivated::class,
            [AuctionEventListener::class, 'handleAuctionActivated']
        );

        $events->listen(
            BidPlaced::class,
            [AuctionEventListener::class, 'handleBidPlaced']
        );

        $events->listen(
            BidOutbid::class,
            [AuctionEventListener::class, 'handleBidOutbid']
        );

        $events->listen(
            AuctionEnded::class,
            [AuctionEventListener::class, 'handleAuctionEnded']
        );

        $events->listen(
            AuctionWon::class,
            [AuctionEventListener::class, 'handleAuctionWon']
        );
    }

    /**
     * Handle auction created event
     */
    public function handleAuctionCreated(AuctionCreated $event): void
    {
        // Log auction creation
        \Log::info('Auction created', [
            'auction_id' => $event->auctionId(),
            'seller_id' => $event->sellerId(),
            'title' => $event->title(),
        ]);

        // Send notification to seller
        $this->notificationService->send(
            \App\Domain\User\ValueObjects\UserId::fromString((string) $event->sellerId()),
            'auction_created',
            'Auction Created Successfully',
            "Your auction \"{$event->title()}\" has been created and is ready to be activated.",
            [
                'auction_id' => $event->auctionId(),
                'title' => $event->title(),
            ],
            ['database'],
            'normal',
            null,
            route('auctions.show', $event->auctionId()),
            'View Auction'
        );
    }

    /**
     * Handle auction activated event
     */
    public function handleAuctionActivated(AuctionActivated $event): void
    {
        // Broadcast auction activation
        $this->webSocketService->broadcastAuctionStatusChange(
            $event->auctionId(),
            'scheduled',
            'active',
            [
                'title' => $event->title(),
                'seller_id' => $event->sellerId(),
            ]
        );

        // Notify watchers that auction is now active
        // This would require getting watchers from the repository
        \Log::info('Auction activated', [
            'auction_id' => $event->auctionId(),
            'seller_id' => $event->sellerId(),
        ]);
    }

    /**
     * Handle bid placed event
     */
    public function handleBidPlaced(BidPlaced $event): void
    {
        // Broadcast new bid in real-time
        $this->webSocketService->broadcastNewBid(
            $event->auctionId(),
            $event->bidderId(),
            $event->amount(),
            0, // This should be fetched from repository
            'Anonymous' // This should be fetched from user repository
        );

        // Send notification to auction seller
        // This would require getting auction details from repository
        \Log::info('Bid placed', [
            'bid_id' => $event->bidId(),
            'auction_id' => $event->auctionId(),
            'bidder_id' => $event->bidderId(),
            'amount' => $event->amount(),
        ]);
    }

    /**
     * Handle bid outbid event
     */
    public function handleBidOutbid(BidOutbid $event): void
    {
        // Send outbid notification
        $this->webSocketService->broadcastOutbidNotification(
            \App\Domain\User\ValueObjects\UserId::fromString((string) $event->bidderId()),
            $event->auctionId(),
            $event->amount(),
            'Auction Title' // This should be fetched from repository
        );

        // Send email notification
        $this->notificationService->sendOutbidNotification(
            \App\Domain\User\ValueObjects\UserId::fromString((string) $event->bidderId()),
            $event->auctionId(),
            'Auction Title', // This should be fetched from repository
            $event->amount()
        );

        \Log::info('User outbid', [
            'bid_id' => $event->bidId(),
            'auction_id' => $event->auctionId(),
            'bidder_id' => $event->bidderId(),
            'amount' => $event->amount(),
        ]);
    }

    /**
     * Handle auction ended event
     */
    public function handleAuctionEnded(AuctionEnded $event): void
    {
        // Broadcast auction ended
        $this->webSocketService->broadcastAuctionEnded(
            $event->auctionId(),
            $event->winnerId(),
            $event->finalPrice(),
            $event->bidsCount()
        );

        // Send notification to seller
        $this->notificationService->sendAuctionEndedNotification(
            \App\Domain\User\ValueObjects\UserId::fromString((string) $event->sellerId()),
            $event->auctionId(),
            $event->title(),
            $event->winnerId() ? \App\Domain\User\ValueObjects\UserId::fromString((string) $event->winnerId()) : null,
            $event->finalPrice()
        );

        \Log::info('Auction ended', [
            'auction_id' => $event->auctionId(),
            'seller_id' => $event->sellerId(),
            'winner_id' => $event->winnerId(),
            'final_price' => $event->finalPrice(),
            'bids_count' => $event->bidsCount(),
        ]);
    }

    /**
     * Handle auction won event
     */
    public function handleAuctionWon(AuctionWon $event): void
    {
        // Send congratulations notification to winner
        $this->notificationService->sendAuctionWonNotification(
            \App\Domain\User\ValueObjects\UserId::fromString((string) $event->winnerId()),
            $event->auctionId(),
            $event->title(),
            $event->finalPrice()
        );

        // Create payment record for the winner
        // This would integrate with the payment service

        \Log::info('Auction won', [
            'auction_id' => $event->auctionId(),
            'winner_id' => $event->winnerId(),
            'seller_id' => $event->sellerId(),
            'final_price' => $event->finalPrice(),
        ]);
    }
}
