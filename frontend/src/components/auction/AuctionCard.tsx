import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Heart, Eye, Clock, Gavel } from 'lucide-react';
import { Auction } from '../../types';
import { Card, CardContent } from '../ui/Card';
import Badge from '../ui/Badge';
import Button from '../ui/Button';
import { formatCurrency, formatTimeRemaining, getAuctionStatusColor, cn } from '../../utils';
import { useAuth } from '../../hooks/useAuth';

interface AuctionCardProps {
  auction: Auction;
  onWatchToggle?: (auctionId: number, isWatched: boolean) => void;
  className?: string;
}

const AuctionCard: React.FC<AuctionCardProps> = ({ 
  auction, 
  onWatchToggle,
  className 
}) => {
  const { isAuthenticated } = useAuth();
  
  const primaryImage = auction.images.find(img => img.is_primary) || auction.images[0];
  const isEndingSoon = auction.timing.is_ending_soon;
  const timeRemaining = auction.timing.remaining_minutes;
  
  const handleWatchToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onWatchToggle) {
      onWatchToggle(auction.id, !auction.metadata.is_watched);
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'ended':
        return 'secondary';
      case 'scheduled':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  return (
    <Card className={cn('group hover:shadow-lg transition-all duration-200', className)}>
      <Link to={`/auctions/${auction.id}`} className="block">
        {/* Image */}
        <div className="relative aspect-[4/3] overflow-hidden rounded-t-lg">
          {primaryImage ? (
            <img
              src={primaryImage.thumbnail_url || primaryImage.url}
              alt={primaryImage.alt_text || auction.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full bg-secondary-100 flex items-center justify-center">
              <Gavel className="h-12 w-12 text-secondary-400" />
            </div>
          )}
          
          {/* Status Badge */}
          <div className="absolute top-3 left-3">
            <Badge variant={getStatusVariant(auction.status)} size="sm">
              {auction.status.charAt(0).toUpperCase() + auction.status.slice(1)}
            </Badge>
          </div>

          {/* Featured Badge */}
          {auction.features.is_featured && (
            <div className="absolute top-3 right-3">
              <Badge variant="primary" size="sm">
                Featured
              </Badge>
            </div>
          )}

          {/* Watch Button */}
          {isAuthenticated && (
            <button
              onClick={handleWatchToggle}
              className="absolute bottom-3 right-3 p-2 bg-white/90 hover:bg-white rounded-full shadow-sm transition-colors"
            >
              <Heart 
                className={cn(
                  'h-4 w-4',
                  auction.metadata.is_watched 
                    ? 'fill-danger-500 text-danger-500' 
                    : 'text-secondary-600'
                )}
              />
            </button>
          )}

          {/* Ending Soon Indicator */}
          {isEndingSoon && auction.status === 'active' && (
            <div className="absolute bottom-3 left-3">
              <Badge variant="danger" size="sm">
                <Clock className="h-3 w-3 mr-1" />
                Ending Soon
              </Badge>
            </div>
          )}
        </div>

        <CardContent className="p-4">
          {/* Title */}
          <h3 className="font-semibold text-lg text-secondary-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
            {auction.title}
          </h3>

          {/* Price Information */}
          <div className="space-y-2 mb-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-secondary-600">Current Bid</span>
              <span className="font-bold text-lg text-primary-600">
                {auction.current_bid.formatted}
              </span>
            </div>
            
            {auction.reserve_price && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-secondary-600">Reserve</span>
                <span className={cn(
                  'font-medium',
                  auction.reserve_price.is_met ? 'text-success-600' : 'text-warning-600'
                )}>
                  {auction.reserve_price.is_met ? 'Met' : 'Not Met'}
                </span>
              </div>
            )}

            {auction.buyout_price && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-secondary-600">Buy Now</span>
                <span className="font-medium text-secondary-900">
                  {auction.buyout_price.formatted}
                </span>
              </div>
            )}
          </div>

          {/* Time Remaining */}
          {auction.status === 'active' && timeRemaining !== null && (
            <div className="mb-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-secondary-600">Time Left</span>
                <span className={cn(
                  'font-medium',
                  isEndingSoon ? 'text-danger-600' : 'text-secondary-900'
                )}>
                  {timeRemaining > 0 ? formatTimeRemaining(auction.timing.end_time) : 'Ended'}
                </span>
              </div>
            </div>
          )}

          {/* Statistics */}
          <div className="flex items-center justify-between text-sm text-secondary-600 pt-3 border-t border-secondary-100">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <Gavel className="h-4 w-4 mr-1" />
                <span>{auction.statistics.bids_count}</span>
              </div>
              <div className="flex items-center">
                <Eye className="h-4 w-4 mr-1" />
                <span>{auction.statistics.views_count}</span>
              </div>
              <div className="flex items-center">
                <Heart className="h-4 w-4 mr-1" />
                <span>{auction.statistics.watchers_count}</span>
              </div>
            </div>
            
            {auction.seller && (
              <div className="text-right">
                <div className="font-medium text-secondary-900">
                  {auction.seller.name}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Link>
    </Card>
  );
};

export default AuctionCard;
