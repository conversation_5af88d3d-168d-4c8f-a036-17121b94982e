import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';
import { cn } from '../../utils';

interface CountdownTimerProps {
  endTime: string;
  onExpire?: () => void;
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  total: number;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({
  endTime,
  onExpire,
  className,
  showIcon = true,
  size = 'md'
}) => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 });
  const [isExpired, setIsExpired] = useState(false);

  const calculateTimeLeft = (): TimeLeft => {
    const difference = new Date(endTime).getTime() - new Date().getTime();
    
    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 };
    }

    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60),
      total: difference,
    };
  };

  useEffect(() => {
    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft();
      setTimeLeft(newTimeLeft);

      if (newTimeLeft.total <= 0 && !isExpired) {
        setIsExpired(true);
        if (onExpire) {
          onExpire();
        }
      }
    }, 1000);

    // Calculate initial time
    const initialTimeLeft = calculateTimeLeft();
    setTimeLeft(initialTimeLeft);
    
    if (initialTimeLeft.total <= 0) {
      setIsExpired(true);
    }

    return () => clearInterval(timer);
  }, [endTime, onExpire, isExpired]);

  const formatNumber = (num: number): string => {
    return num.toString().padStart(2, '0');
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'text-sm',
          icon: 'h-4 w-4',
          number: 'text-lg font-bold',
          label: 'text-xs',
        };
      case 'lg':
        return {
          container: 'text-lg',
          icon: 'h-6 w-6',
          number: 'text-3xl font-bold',
          label: 'text-sm',
        };
      default:
        return {
          container: 'text-base',
          icon: 'h-5 w-5',
          number: 'text-2xl font-bold',
          label: 'text-sm',
        };
    }
  };

  const sizeClasses = getSizeClasses();

  if (isExpired || timeLeft.total <= 0) {
    return (
      <div className={cn('flex items-center text-danger-600', sizeClasses.container, className)}>
        {showIcon && <Clock className={cn('mr-2', sizeClasses.icon)} />}
        <span className="font-semibold">Auction Ended</span>
      </div>
    );
  }

  const isEndingSoon = timeLeft.total <= 24 * 60 * 60 * 1000; // Less than 24 hours
  const isCritical = timeLeft.total <= 60 * 60 * 1000; // Less than 1 hour

  return (
    <div className={cn('flex items-center', sizeClasses.container, className)}>
      {showIcon && (
        <Clock 
          className={cn(
            'mr-2',
            sizeClasses.icon,
            isCritical ? 'text-danger-600' : isEndingSoon ? 'text-warning-600' : 'text-secondary-600'
          )} 
        />
      )}
      
      <div className="flex items-center space-x-1 font-mono">
        {timeLeft.days > 0 && (
          <>
            <div className="text-center">
              <div className={cn(
                sizeClasses.number,
                isCritical ? 'text-danger-600' : isEndingSoon ? 'text-warning-600' : 'text-secondary-900'
              )}>
                {formatNumber(timeLeft.days)}
              </div>
              <div className={cn('text-secondary-600', sizeClasses.label)}>
                {timeLeft.days === 1 ? 'day' : 'days'}
              </div>
            </div>
            <span className={cn(
              'font-bold',
              isCritical ? 'text-danger-600' : isEndingSoon ? 'text-warning-600' : 'text-secondary-900'
            )}>:</span>
          </>
        )}
        
        <div className="text-center">
          <div className={cn(
            sizeClasses.number,
            isCritical ? 'text-danger-600' : isEndingSoon ? 'text-warning-600' : 'text-secondary-900'
          )}>
            {formatNumber(timeLeft.hours)}
          </div>
          <div className={cn('text-secondary-600', sizeClasses.label)}>
            {timeLeft.hours === 1 ? 'hr' : 'hrs'}
          </div>
        </div>
        
        <span className={cn(
          'font-bold',
          isCritical ? 'text-danger-600' : isEndingSoon ? 'text-warning-600' : 'text-secondary-900'
        )}>:</span>
        
        <div className="text-center">
          <div className={cn(
            sizeClasses.number,
            isCritical ? 'text-danger-600' : isEndingSoon ? 'text-warning-600' : 'text-secondary-900'
          )}>
            {formatNumber(timeLeft.minutes)}
          </div>
          <div className={cn('text-secondary-600', sizeClasses.label)}>
            {timeLeft.minutes === 1 ? 'min' : 'mins'}
          </div>
        </div>
        
        <span className={cn(
          'font-bold',
          isCritical ? 'text-danger-600' : isEndingSoon ? 'text-warning-600' : 'text-secondary-900'
        )}>:</span>
        
        <div className="text-center">
          <div className={cn(
            sizeClasses.number,
            isCritical ? 'text-danger-600' : isEndingSoon ? 'text-warning-600' : 'text-secondary-900'
          )}>
            {formatNumber(timeLeft.seconds)}
          </div>
          <div className={cn('text-secondary-600', sizeClasses.label)}>
            {timeLeft.seconds === 1 ? 'sec' : 'secs'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountdownTimer;
