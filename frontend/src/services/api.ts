import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  Auction, 
  Bid, 
  User, 
  Category, 
  Payment,
  ApiResponse, 
  PaginatedResponse,
  CreateAuctionForm,
  PlaceBidForm,
  RegisterForm,
  LoginForm,
  AuctionFilters,
  BidFilters
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth methods
  async register(data: RegisterForm): Promise<ApiResponse<User>> {
    const response = await this.api.post('/register', data);
    return response.data;
  }

  async login(data: LoginForm): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await this.api.post('/login', data);
    if (response.data.data.token) {
      localStorage.setItem('auth_token', response.data.data.token);
    }
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/logout');
    } finally {
      localStorage.removeItem('auth_token');
    }
  }

  async getProfile(): Promise<ApiResponse<User>> {
    const response = await this.api.get('/user/profile');
    return response.data;
  }

  async updateProfile(data: FormData): Promise<ApiResponse<User>> {
    const response = await this.api.put('/user/profile', data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  }

  // Auction methods
  async getAuctions(filters?: AuctionFilters): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get('/auctions', { params: filters });
    return response.data;
  }

  async getAuction(id: number): Promise<ApiResponse<Auction>> {
    const response = await this.api.get(`/auctions/${id}`);
    return response.data;
  }

  async createAuction(data: CreateAuctionForm): Promise<ApiResponse<Auction>> {
    const response = await this.api.post('/auctions', data);
    return response.data;
  }

  async updateAuction(id: number, data: Partial<CreateAuctionForm>): Promise<ApiResponse<Auction>> {
    const response = await this.api.put(`/auctions/${id}`, data);
    return response.data;
  }

  async deleteAuction(id: number): Promise<ApiResponse<void>> {
    const response = await this.api.delete(`/auctions/${id}`);
    return response.data;
  }

  async activateAuction(id: number): Promise<ApiResponse<Auction>> {
    const response = await this.api.post(`/auctions/${id}/activate`);
    return response.data;
  }

  async endAuction(id: number): Promise<ApiResponse<Auction>> {
    const response = await this.api.post(`/auctions/${id}/end`);
    return response.data;
  }

  async uploadAuctionImages(id: number, files: FileList): Promise<ApiResponse<any>> {
    const formData = new FormData();
    Array.from(files).forEach((file) => {
      formData.append('images[]', file);
    });
    
    const response = await this.api.post(`/auctions/${id}/images`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  }

  async getFeaturedAuctions(): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get('/auctions/featured');
    return response.data;
  }

  async getEndingSoonAuctions(): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get('/auctions/ending-soon');
    return response.data;
  }

  async getUserAuctions(): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get('/user/auctions');
    return response.data;
  }

  async getWatchedAuctions(): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get('/user/watched-auctions');
    return response.data;
  }

  async getWonAuctions(): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get('/user/won-auctions');
    return response.data;
  }

  // Bid methods
  async getAuctionBids(auctionId: number, filters?: BidFilters): Promise<PaginatedResponse<Bid>> {
    const response = await this.api.get(`/auctions/${auctionId}/bids`, { params: filters });
    return response.data;
  }

  async placeBid(data: PlaceBidForm): Promise<ApiResponse<Bid>> {
    const response = await this.api.post('/bids', data);
    return response.data;
  }

  async cancelBid(id: number): Promise<ApiResponse<Bid>> {
    const response = await this.api.delete(`/bids/${id}`);
    return response.data;
  }

  async getUserBids(): Promise<PaginatedResponse<Bid>> {
    const response = await this.api.get('/user/bids');
    return response.data;
  }

  async getUserWinningBids(): Promise<PaginatedResponse<Bid>> {
    const response = await this.api.get('/user/winning-bids');
    return response.data;
  }

  async getHighestBid(auctionId: number): Promise<ApiResponse<Bid>> {
    const response = await this.api.get(`/auctions/${auctionId}/highest-bid`);
    return response.data;
  }

  async getWinningBid(auctionId: number): Promise<ApiResponse<Bid>> {
    const response = await this.api.get(`/auctions/${auctionId}/winning-bid`);
    return response.data;
  }

  async getRecentBids(auctionId: number, minutes = 5): Promise<ApiResponse<Bid[]>> {
    const response = await this.api.get(`/auctions/${auctionId}/recent-bids`, {
      params: { minutes },
    });
    return response.data;
  }

  // Category methods
  async getCategories(includeHierarchy = false): Promise<ApiResponse<Category[]>> {
    const response = await this.api.get('/categories', {
      params: { hierarchy: includeHierarchy },
    });
    return response.data;
  }

  async getCategory(id: number): Promise<ApiResponse<Category>> {
    const response = await this.api.get(`/categories/${id}`);
    return response.data;
  }

  async getCategoryAuctions(id: number, filters?: AuctionFilters): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get(`/categories/${id}/auctions`, { params: filters });
    return response.data;
  }

  // Watchlist methods
  async getWatchlist(): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get('/watchlist');
    return response.data;
  }

  async addToWatchlist(auctionId: number): Promise<ApiResponse<any>> {
    const response = await this.api.post('/watchlist', { auction_id: auctionId });
    return response.data;
  }

  async removeFromWatchlist(auctionId: number): Promise<ApiResponse<void>> {
    const response = await this.api.delete(`/watchlist/${auctionId}`);
    return response.data;
  }

  async checkWatchlistStatus(auctionId: number): Promise<ApiResponse<{ is_watched: boolean }>> {
    const response = await this.api.get(`/watchlist/check/${auctionId}`);
    return response.data;
  }

  // Payment methods
  async getPayments(): Promise<PaginatedResponse<Payment>> {
    const response = await this.api.get('/payments');
    return response.data;
  }

  async createPaymentIntent(data: {
    auction_id: number;
    amount: number;
    currency?: string;
  }): Promise<ApiResponse<any>> {
    const response = await this.api.post('/payments/create-intent', data);
    return response.data;
  }

  async confirmPaymentIntent(data: {
    payment_intent_id: string;
    payment_method_id: string;
  }): Promise<ApiResponse<any>> {
    const response = await this.api.post('/payments/confirm-intent', data);
    return response.data;
  }

  async processPayment(data: {
    auction_id: number;
    amount: number;
    currency?: string;
    payment_method: any;
    payment_method_id?: string;
  }): Promise<ApiResponse<Payment>> {
    const response = await this.api.post('/payments', data);
    return response.data;
  }

  // Notification methods
  async getNotifications(unreadOnly = false): Promise<any> {
    const response = await this.api.get('/user/notifications', {
      params: { unread_only: unreadOnly },
    });
    return response.data;
  }

  async markNotificationRead(id: number): Promise<ApiResponse<void>> {
    const response = await this.api.put(`/user/notifications/${id}/read`);
    return response.data;
  }

  async markAllNotificationsRead(): Promise<ApiResponse<void>> {
    const response = await this.api.put('/user/notifications/read-all');
    return response.data;
  }

  async getUnreadNotificationsCount(): Promise<ApiResponse<{ count: number }>> {
    const response = await this.api.get('/user/notifications/unread-count');
    return response.data;
  }

  // Statistics methods
  async getUserStatistics(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/user/statistics');
    return response.data;
  }

  async getUserDashboard(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/user/dashboard');
    return response.data;
  }

  async getPlatformStatistics(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/statistics');
    return response.data;
  }

  // Search methods
  async search(query: string, filters?: any): Promise<PaginatedResponse<Auction>> {
    const response = await this.api.get('/search', {
      params: { query, ...filters },
    });
    return response.data;
  }

  async getSearchSuggestions(query: string): Promise<ApiResponse<string[]>> {
    const response = await this.api.get('/search/suggestions', {
      params: { query },
    });
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
