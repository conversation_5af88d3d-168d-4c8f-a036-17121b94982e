import { Auction, User, Category, AuctionImage } from '../types';

// Mock users
export const mockUsers: User[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'seller',
    avatar: {
      url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      thumbnail: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
    },
    verification: {
      is_email_verified: true,
      is_phone_verified: true,
      is_identity_verified: true,
      verification_level: 'fully_verified',
    },
    status: {
      is_active: true,
      is_online: true,
      last_login_at: new Date().toISOString(),
      last_activity_at: new Date().toISOString(),
    },
    metadata: {
      created_at: '2023-01-15T10:00:00Z',
      updated_at: new Date().toISOString(),
      can_contact: true,
      can_view_profile: true,
    },
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'premium_seller',
    avatar: {
      url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      thumbnail: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face',
    },
    verification: {
      is_email_verified: true,
      is_phone_verified: true,
      is_identity_verified: true,
      verification_level: 'fully_verified',
    },
    status: {
      is_active: true,
      is_online: false,
      last_login_at: '2024-01-07T14:30:00Z',
      last_activity_at: '2024-01-07T16:45:00Z',
    },
    metadata: {
      created_at: '2023-03-22T08:15:00Z',
      updated_at: '2024-01-07T16:45:00Z',
      can_contact: true,
      can_view_profile: true,
    },
  },
];

// Mock categories
export const mockCategories: Category[] = [
  {
    id: 1,
    name: 'Electronics',
    description: 'Computers, phones, gadgets and more',
    slug: 'electronics',
    sort_order: 1,
    is_active: true,
    metadata: {
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  },
  {
    id: 2,
    name: 'Art & Collectibles',
    description: 'Paintings, sculptures, rare collectibles',
    slug: 'art-collectibles',
    sort_order: 2,
    is_active: true,
    metadata: {
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  },
  {
    id: 3,
    name: 'Jewelry & Watches',
    description: 'Fine jewelry, luxury watches, accessories',
    slug: 'jewelry-watches',
    sort_order: 3,
    is_active: true,
    metadata: {
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  },
];

// Mock auction images
const createMockImages = (baseUrl: string, count: number = 3): AuctionImage[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    filename: `image-${index + 1}.jpg`,
    url: `${baseUrl}?w=800&h=600&fit=crop`,
    thumbnail_url: `${baseUrl}?w=300&h=225&fit=crop`,
    alt_text: `Product image ${index + 1}`,
    sort_order: index,
    is_primary: index === 0,
    metadata: {
      original_name: `product-${index + 1}.jpg`,
      size_bytes: 245760,
      mime_type: 'image/jpeg',
      width: 800,
      height: 600,
    },
  }));
};

// Mock auctions
export const mockAuctions: Auction[] = [
  {
    id: 1,
    title: 'Vintage Rolex Submariner 1960s',
    description: 'Rare vintage Rolex Submariner from the 1960s in excellent condition. This iconic timepiece features the original dial, hands, and bezel. A true collector\'s piece with documented provenance.',
    condition: 'good',
    status: 'active',
    starting_price: {
      amount: 5000,
      currency: 'USD',
      formatted: '$5,000.00',
    },
    current_bid: {
      amount: 12500,
      currency: 'USD',
      formatted: '$12,500.00',
    },
    reserve_price: {
      amount: 15000,
      currency: 'USD',
      formatted: '$15,000.00',
      is_met: false,
    },
    buyout_price: {
      amount: 25000,
      currency: 'USD',
      formatted: '$25,000.00',
    },
    timing: {
      start_time: '2024-01-01T10:00:00Z',
      end_time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days from now
      duration_minutes: 7200,
      remaining_minutes: 2880, // 2 days
      is_ending_soon: false,
    },
    statistics: {
      bids_count: 23,
      views_count: 456,
      watchers_count: 34,
    },
    features: {
      auto_extend: true,
      extend_minutes: 10,
      is_featured: true,
    },
    seller: mockUsers[0],
    category: mockCategories[2],
    images: createMockImages('https://images.unsplash.com/photo-1523170335258-f5c6c6bd6eaf'),
    metadata: {
      created_at: '2024-01-01T10:00:00Z',
      updated_at: new Date().toISOString(),
      slug: 'vintage-rolex-submariner-1960s',
      is_watched: false,
      user_has_bid: false,
      can_bid: true,
      can_edit: false,
    },
  },
  {
    id: 2,
    title: 'MacBook Pro 16" M3 Max - Like New',
    description: 'Nearly new MacBook Pro 16" with M3 Max chip, 32GB RAM, 1TB SSD. Used for only 2 months, comes with original box, charger, and documentation. Perfect for professionals.',
    condition: 'like_new',
    status: 'active',
    starting_price: {
      amount: 2000,
      currency: 'USD',
      formatted: '$2,000.00',
    },
    current_bid: {
      amount: 2850,
      currency: 'USD',
      formatted: '$2,850.00',
    },
    timing: {
      start_time: '2024-01-05T14:00:00Z',
      end_time: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(), // 6 hours from now
      duration_minutes: 4320,
      remaining_minutes: 360, // 6 hours
      is_ending_soon: true,
    },
    statistics: {
      bids_count: 15,
      views_count: 234,
      watchers_count: 18,
    },
    features: {
      auto_extend: true,
      extend_minutes: 5,
      is_featured: false,
    },
    seller: mockUsers[1],
    category: mockCategories[0],
    images: createMockImages('https://images.unsplash.com/photo-*************-489689fd1ca8'),
    metadata: {
      created_at: '2024-01-05T14:00:00Z',
      updated_at: new Date().toISOString(),
      slug: 'macbook-pro-16-m3-max-like-new',
      is_watched: true,
      user_has_bid: false,
      can_bid: true,
      can_edit: false,
    },
  },
  {
    id: 3,
    title: 'Original Banksy Print "Girl with Balloon"',
    description: 'Authentic Banksy print "Girl with Balloon" from 2006. Certificate of authenticity included. Framed and in pristine condition. A rare opportunity to own this iconic piece.',
    condition: 'new',
    status: 'active',
    starting_price: {
      amount: 8000,
      currency: 'USD',
      formatted: '$8,000.00',
    },
    current_bid: {
      amount: 18500,
      currency: 'USD',
      formatted: '$18,500.00',
    },
    reserve_price: {
      amount: 20000,
      currency: 'USD',
      formatted: '$20,000.00',
      is_met: false,
    },
    timing: {
      start_time: '2024-01-03T09:00:00Z',
      end_time: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // 18 hours from now
      duration_minutes: 7200,
      remaining_minutes: 1080, // 18 hours
      is_ending_soon: true,
    },
    statistics: {
      bids_count: 31,
      views_count: 789,
      watchers_count: 67,
    },
    features: {
      auto_extend: true,
      extend_minutes: 15,
      is_featured: true,
    },
    seller: mockUsers[0],
    category: mockCategories[1],
    images: createMockImages('https://images.unsplash.com/photo-*************-48f60103fc96'),
    metadata: {
      created_at: '2024-01-03T09:00:00Z',
      updated_at: new Date().toISOString(),
      slug: 'original-banksy-print-girl-with-balloon',
      is_watched: false,
      user_has_bid: true,
      can_bid: true,
      can_edit: false,
    },
  },
  {
    id: 4,
    title: 'Diamond Tennis Bracelet 5ct',
    description: 'Stunning 5-carat diamond tennis bracelet featuring round brilliant cut diamonds in 18k white gold setting. Each diamond is VS1 clarity, F color. Comes with GIA certification.',
    condition: 'new',
    status: 'active',
    starting_price: {
      amount: 15000,
      currency: 'USD',
      formatted: '$15,000.00',
    },
    current_bid: {
      amount: 22750,
      currency: 'USD',
      formatted: '$22,750.00',
    },
    timing: {
      start_time: '2024-01-04T16:00:00Z',
      end_time: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
      duration_minutes: 5760,
      remaining_minutes: 4320, // 3 days
      is_ending_soon: false,
    },
    statistics: {
      bids_count: 19,
      views_count: 345,
      watchers_count: 28,
    },
    features: {
      auto_extend: true,
      extend_minutes: 10,
      is_featured: true,
    },
    seller: mockUsers[1],
    category: mockCategories[2],
    images: createMockImages('https://images.unsplash.com/photo-1515562141207-7a88fb7ce338'),
    metadata: {
      created_at: '2024-01-04T16:00:00Z',
      updated_at: new Date().toISOString(),
      slug: 'diamond-tennis-bracelet-5ct',
      is_watched: false,
      user_has_bid: false,
      can_bid: true,
      can_edit: false,
    },
  },
  {
    id: 5,
    title: 'Vintage Gibson Les Paul 1959',
    description: 'Extremely rare 1959 Gibson Les Paul Standard in original sunburst finish. This is a true holy grail guitar with incredible tone and playability. All original hardware and electronics.',
    condition: 'good',
    status: 'active',
    starting_price: {
      amount: 100000,
      currency: 'USD',
      formatted: '$100,000.00',
    },
    current_bid: {
      amount: 185000,
      currency: 'USD',
      formatted: '$185,000.00',
    },
    reserve_price: {
      amount: 200000,
      currency: 'USD',
      formatted: '$200,000.00',
      is_met: false,
    },
    timing: {
      start_time: '2024-01-02T12:00:00Z',
      end_time: new Date(Date.now() + 45 * 60 * 1000).toISOString(), // 45 minutes from now
      duration_minutes: 10080,
      remaining_minutes: 45,
      is_ending_soon: true,
    },
    statistics: {
      bids_count: 42,
      views_count: 1234,
      watchers_count: 89,
    },
    features: {
      auto_extend: true,
      extend_minutes: 15,
      is_featured: true,
    },
    seller: mockUsers[0],
    category: mockCategories[1],
    images: createMockImages('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f'),
    metadata: {
      created_at: '2024-01-02T12:00:00Z',
      updated_at: new Date().toISOString(),
      slug: 'vintage-gibson-les-paul-1959',
      is_watched: true,
      user_has_bid: false,
      can_bid: true,
      can_edit: false,
    },
  },
  {
    id: 6,
    title: 'iPhone 15 Pro Max 1TB - Sealed',
    description: 'Brand new, sealed iPhone 15 Pro Max with 1TB storage in Natural Titanium. Never opened, comes with full Apple warranty. Perfect for those who want the latest technology.',
    condition: 'new',
    status: 'active',
    starting_price: {
      amount: 1000,
      currency: 'USD',
      formatted: '$1,000.00',
    },
    current_bid: {
      amount: 1350,
      currency: 'USD',
      formatted: '$1,350.00',
    },
    buyout_price: {
      amount: 1599,
      currency: 'USD',
      formatted: '$1,599.00',
    },
    timing: {
      start_time: '2024-01-06T11:00:00Z',
      end_time: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
      duration_minutes: 7200,
      remaining_minutes: 7200, // 5 days
      is_ending_soon: false,
    },
    statistics: {
      bids_count: 8,
      views_count: 156,
      watchers_count: 12,
    },
    features: {
      auto_extend: false,
      extend_minutes: 0,
      is_featured: false,
    },
    seller: mockUsers[1],
    category: mockCategories[0],
    images: createMockImages('https://images.unsplash.com/photo-1592750475338-74b7b21085ab'),
    metadata: {
      created_at: '2024-01-06T11:00:00Z',
      updated_at: new Date().toISOString(),
      slug: 'iphone-15-pro-max-1tb-sealed',
      is_watched: false,
      user_has_bid: false,
      can_bid: true,
      can_edit: false,
    },
  },
];

// Mock API responses
export const mockApiResponses = {
  featuredAuctions: {
    data: mockAuctions.filter(auction => auction.features.is_featured),
    meta: {
      total: 4,
      count: 4,
      per_page: 15,
      current_page: 1,
      last_page: 1,
      from: 1,
      to: 4,
    },
    links: {
      first: '/api/v1/auctions/featured?page=1',
      last: '/api/v1/auctions/featured?page=1',
      prev: null,
      next: null,
    },
  },
  endingSoonAuctions: {
    data: mockAuctions.filter(auction => auction.timing.is_ending_soon),
    meta: {
      total: 3,
      count: 3,
      per_page: 15,
      current_page: 1,
      last_page: 1,
      from: 1,
      to: 3,
    },
    links: {
      first: '/api/v1/auctions/ending-soon?page=1',
      last: '/api/v1/auctions/ending-soon?page=1',
      prev: null,
      next: null,
    },
  },
  allAuctions: {
    data: mockAuctions,
    meta: {
      total: 6,
      count: 6,
      per_page: 15,
      current_page: 1,
      last_page: 1,
      from: 1,
      to: 6,
    },
    links: {
      first: '/api/v1/auctions?page=1',
      last: '/api/v1/auctions?page=1',
      prev: null,
      next: null,
    },
  },
};
