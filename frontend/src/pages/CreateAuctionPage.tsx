import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Upload, Calendar, DollarSign, Package } from 'lucide-react';
import { useCreateAuction } from '../hooks/useAuctions';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { CreateAuctionForm } from '../types';

const CreateAuctionPage: React.FC = () => {
  const navigate = useNavigate();
  const createAuction = useCreateAuction();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<CreateAuctionForm>();

  const onSubmit = async (data: CreateAuctionForm) => {
    try {
      const result = await createAuction.mutateAsync(data);
      navigate(`/auctions/${result.data.id}`);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Create New Auction</h1>
        <p className="text-gray-600 mt-2">
          List your item for auction and reach thousands of potential buyers.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Input
                  label="Auction Title"
                  placeholder="Enter a descriptive title for your item"
                  {...register('title', {
                    required: 'Title is required',
                    maxLength: {
                      value: 500,
                      message: 'Title cannot exceed 500 characters',
                    },
                  })}
                  error={errors.title?.message}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  {...register('category_id', {
                    required: 'Category is required',
                  })}
                  className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select a category</option>
                  <option value="1">Electronics</option>
                  <option value="2">Art & Collectibles</option>
                  <option value="3">Jewelry & Watches</option>
                </select>
                {errors.category_id && (
                  <p className="text-sm text-red-600 mt-1">{errors.category_id.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Condition
                </label>
                <select
                  {...register('condition')}
                  className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select condition</option>
                  <option value="new">New</option>
                  <option value="like_new">Like New</option>
                  <option value="good">Good</option>
                  <option value="fair">Fair</option>
                  <option value="poor">Poor</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  {...register('description', {
                    required: 'Description is required',
                    maxLength: {
                      value: 10000,
                      message: 'Description cannot exceed 10,000 characters',
                    },
                  })}
                  rows={6}
                  placeholder="Provide a detailed description of your item..."
                  className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
                {errors.description && (
                  <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pricing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Pricing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Input
                label="Starting Price"
                type="number"
                step="0.01"
                min="0.01"
                placeholder="0.00"
                {...register('starting_price', {
                  required: 'Starting price is required',
                  min: {
                    value: 0.01,
                    message: 'Starting price must be at least $0.01',
                  },
                })}
                error={errors.starting_price?.message}
              />

              <Input
                label="Reserve Price (Optional)"
                type="number"
                step="0.01"
                min="0.01"
                placeholder="0.00"
                {...register('reserve_price')}
                error={errors.reserve_price?.message}
                helperText="Minimum price you'll accept"
              />

              <Input
                label="Buy Now Price (Optional)"
                type="number"
                step="0.01"
                min="0.01"
                placeholder="0.00"
                {...register('buyout_price')}
                error={errors.buyout_price?.message}
                helperText="Price for immediate purchase"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Shipping Cost (Optional)"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                {...register('shipping_cost')}
                error={errors.shipping_cost?.message}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <select
                  {...register('currency')}
                  className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="GBP">GBP (£)</option>
                  <option value="CAD">CAD (C$)</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Timing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Auction Timing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Start Time (Optional)"
                type="datetime-local"
                {...register('start_time')}
                error={errors.start_time?.message}
                helperText="Leave empty to start immediately"
              />

              <Input
                label="End Time"
                type="datetime-local"
                {...register('end_time', {
                  required: 'End time is required',
                })}
                error={errors.end_time?.message}
              />
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <input
                  id="auto_extend"
                  type="checkbox"
                  {...register('auto_extend')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="auto_extend" className="ml-2 block text-sm text-gray-900">
                  Auto-extend auction
                </label>
              </div>

              <Input
                label="Extend by (minutes)"
                type="number"
                min="1"
                max="60"
                defaultValue="10"
                {...register('extend_minutes')}
                className="w-32"
              />
            </div>
          </CardContent>
        </Card>

        {/* Images */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="h-5 w-5 mr-2" />
              Images
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <div className="mt-4">
                <label htmlFor="images" className="cursor-pointer">
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    Upload images
                  </span>
                  <span className="mt-1 block text-sm text-gray-600">
                    PNG, JPG, GIF up to 10MB each
                  </span>
                </label>
                <input
                  id="images"
                  type="file"
                  multiple
                  accept="image/*"
                  className="sr-only"
                />
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              You can upload images after creating the auction.
            </p>
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/dashboard')}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={isSubmitting}
          >
            Create Auction
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CreateAuctionPage;
