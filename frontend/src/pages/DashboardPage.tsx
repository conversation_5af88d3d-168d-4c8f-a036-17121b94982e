import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, Plus, Eye, Heart, TrendingUp, Clock, DollarSign } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useUserAuctions, useWatchedAuctions, useWonAuctions } from '../hooks/useAuctions';
import Button from '../components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import AuctionCard from '../components/auction/AuctionCard';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const { data: userAuctions, isLoading: userAuctionsLoading } = useUserAuctions();
  const { data: watchedAuctions, isLoading: watchedLoading } = useWatchedAuctions();
  const { data: wonAuctions, isLoading: wonLoading } = useWonAuctions();

  const stats = [
    {
      name: 'Active Auctions',
      value: '3',
      icon: G<PERSON><PERSON>,
      color: 'text-primary-600',
      bgColor: 'bg-primary-100',
    },
    {
      name: 'Total Views',
      value: '1,234',
      icon: Eye,
      color: 'text-success-600',
      bgColor: 'bg-success-100',
    },
    {
      name: 'Watching',
      value: '12',
      icon: Heart,
      color: 'text-danger-600',
      bgColor: 'bg-danger-100',
    },
    {
      name: 'Won Auctions',
      value: '5',
      icon: TrendingUp,
      color: 'text-warning-600',
      bgColor: 'bg-warning-100',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600 mt-2">
          Manage your auctions, track your bids, and discover new opportunities.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row gap-4">
          <Link to="/create-auction">
            <Button className="w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Create New Auction
            </Button>
          </Link>
          <Link to="/auctions">
            <Button variant="outline" className="w-full sm:w-auto">
              <Gavel className="h-4 w-4 mr-2" />
              Browse Auctions
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => (
          <Card key={stat.name}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* My Auctions */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">My Auctions</h2>
            <Link to="/my-auctions">
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </div>

          {userAuctionsLoading ? (
            <div className="space-y-4">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 h-32 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : userAuctions?.data && userAuctions.data.length > 0 ? (
            <div className="space-y-4">
              {userAuctions.data.slice(0, 2).map((auction) => (
                <AuctionCard key={auction.id} auction={auction} className="h-auto" />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Gavel className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No auctions yet</h3>
                <p className="text-gray-600 mb-4">
                  Start selling by creating your first auction.
                </p>
                <Link to="/create-auction">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Auction
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Watched Auctions */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Watched Auctions</h2>
            <Link to="/watchlist">
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </div>

          {watchedLoading ? (
            <div className="space-y-4">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 h-32 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : watchedAuctions?.data && watchedAuctions.data.length > 0 ? (
            <div className="space-y-4">
              {watchedAuctions.data.slice(0, 2).map((auction) => (
                <AuctionCard key={auction.id} auction={auction} className="h-auto" />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No watched auctions</h3>
                <p className="text-gray-600 mb-4">
                  Start watching auctions to keep track of items you're interested in.
                </p>
                <Link to="/auctions">
                  <Button variant="outline">
                    Browse Auctions
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mt-8">
        <h2 className="text-xl font-bold text-gray-900 mb-6">Recent Activity</h2>
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-success-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-success-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    You won the auction for "Vintage Camera"
                  </p>
                  <p className="text-sm text-gray-600">2 hours ago</p>
                </div>
                <div className="text-sm font-medium text-gray-900">$450.00</div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <Gavel className="h-5 w-5 text-primary-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    New bid placed on your "MacBook Pro" auction
                  </p>
                  <p className="text-sm text-gray-600">5 hours ago</p>
                </div>
                <div className="text-sm font-medium text-gray-900">$2,850.00</div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="p-2 bg-warning-100 rounded-lg">
                  <Clock className="h-5 w-5 text-warning-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    Auction "Vintage Watch" is ending in 2 hours
                  </p>
                  <p className="text-sm text-gray-600">1 day ago</p>
                </div>
                <div className="text-sm font-medium text-gray-900">$1,200.00</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DashboardPage;
