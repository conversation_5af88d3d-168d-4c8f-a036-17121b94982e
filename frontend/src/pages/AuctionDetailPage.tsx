import React from 'react';
import { useParams } from 'react-router-dom';
import { Gavel } from 'lucide-react';

const AuctionDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center">
        <Gavel className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Auction Detail</h1>
        <p className="text-gray-600">Auction ID: {id}</p>
        <p className="text-gray-600">This page will show detailed auction information with bidding interface.</p>
      </div>
    </div>
  );
};

export default AuctionDetailPage;
