import React from 'react';
import { Gavel } from 'lucide-react';

const AuctionListPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center">
        <Gavel className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Auction List</h1>
        <p className="text-gray-600">This page will show all auctions with filtering and search capabilities.</p>
      </div>
    </div>
  );
};

export default AuctionListPage;
