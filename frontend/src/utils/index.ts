import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { formatDistanceToNow, format, isAfter, isBefore, addMinutes } from 'date-fns';

// Utility function to merge Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format currency
export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

// Format large numbers
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// Format time remaining
export function formatTimeRemaining(endTime: string): string {
  const end = new Date(endTime);
  const now = new Date();
  
  if (isAfter(now, end)) {
    return 'Ended';
  }
  
  const distance = formatDistanceToNow(end, { addSuffix: false });
  return distance;
}

// Get time remaining in minutes
export function getTimeRemainingMinutes(endTime: string): number {
  const end = new Date(endTime);
  const now = new Date();
  
  if (isAfter(now, end)) {
    return 0;
  }
  
  return Math.floor((end.getTime() - now.getTime()) / (1000 * 60));
}

// Check if auction is ending soon (within 24 hours)
export function isEndingSoon(endTime: string): boolean {
  const end = new Date(endTime);
  const now = new Date();
  const twentyFourHoursFromNow = addMinutes(now, 24 * 60);
  
  return isAfter(end, now) && isBefore(end, twentyFourHoursFromNow);
}

// Format date
export function formatDate(date: string, formatStr = 'MMM dd, yyyy'): string {
  return format(new Date(date), formatStr);
}

// Format date with time
export function formatDateTime(date: string): string {
  return format(new Date(date), 'MMM dd, yyyy HH:mm');
}

// Get relative time (e.g., "2 hours ago")
export function getRelativeTime(date: string): string {
  return formatDistanceToNow(new Date(date), { addSuffix: true });
}

// Truncate text
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

// Generate initials from name
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
}

// Validate email
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Generate random ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Debounce function
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Get auction status color
export function getAuctionStatusColor(status: string): string {
  switch (status) {
    case 'active':
      return 'text-success-600 bg-success-100';
    case 'ended':
      return 'text-secondary-600 bg-secondary-100';
    case 'scheduled':
      return 'text-warning-600 bg-warning-100';
    case 'draft':
      return 'text-secondary-600 bg-secondary-100';
    default:
      return 'text-secondary-600 bg-secondary-100';
  }
}

// Get bid status color
export function getBidStatusColor(isWinning: boolean, isValid: boolean): string {
  if (!isValid) {
    return 'text-danger-600 bg-danger-100';
  }
  if (isWinning) {
    return 'text-success-600 bg-success-100';
  }
  return 'text-warning-600 bg-warning-100';
}

// Get payment status color
export function getPaymentStatusColor(status: string): string {
  switch (status) {
    case 'succeeded':
      return 'text-success-600 bg-success-100';
    case 'failed':
      return 'text-danger-600 bg-danger-100';
    case 'pending':
    case 'processing':
      return 'text-warning-600 bg-warning-100';
    case 'cancelled':
      return 'text-secondary-600 bg-secondary-100';
    case 'refunded':
      return 'text-primary-600 bg-primary-100';
    default:
      return 'text-secondary-600 bg-secondary-100';
  }
}

// Calculate minimum bid amount
export function calculateMinimumBid(currentBid: number, startingPrice: number, increment = 1): number {
  const baseAmount = currentBid > 0 ? currentBid : startingPrice;
  return baseAmount + increment;
}

// Validate bid amount
export function validateBidAmount(
  amount: number,
  currentBid: number,
  startingPrice: number,
  increment = 1
): { isValid: boolean; message?: string } {
  const minimumBid = calculateMinimumBid(currentBid, startingPrice, increment);
  
  if (amount < minimumBid) {
    return {
      isValid: false,
      message: `Minimum bid amount is ${formatCurrency(minimumBid)}`,
    };
  }
  
  return { isValid: true };
}

// Format countdown timer
export function formatCountdown(minutes: number): string {
  if (minutes <= 0) return '00:00:00';
  
  const hours = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  const secs = Math.floor((minutes % 1) * 60);
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// Copy to clipboard
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error('Failed to copy text: ', err);
    return false;
  }
}

// Download file
export function downloadFile(url: string, filename: string): void {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Scroll to element
export function scrollToElement(elementId: string, offset = 0): void {
  const element = document.getElementById(elementId);
  if (element) {
    const top = element.offsetTop - offset;
    window.scrollTo({ top, behavior: 'smooth' });
  }
}

// Local storage helpers
export const storage = {
  get: <T>(key: string, defaultValue?: T): T | null => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue || null;
    } catch {
      return defaultValue || null;
    }
  },
  
  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (err) {
      console.error('Failed to save to localStorage:', err);
    }
  },
  
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch (err) {
      console.error('Failed to remove from localStorage:', err);
    }
  },
  
  clear: (): void => {
    try {
      localStorage.clear();
    } catch (err) {
      console.error('Failed to clear localStorage:', err);
    }
  },
};
