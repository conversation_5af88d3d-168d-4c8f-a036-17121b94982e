import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Auction, AuctionFilters, CreateAuctionForm } from '../types';
import apiService from '../services/api';
import toast from 'react-hot-toast';

export function useAuctions(filters?: AuctionFilters) {
  return useQuery({
    queryKey: ['auctions', filters],
    queryFn: () => apiService.getAuctions(filters),
    staleTime: 30000, // 30 seconds
  });
}

export function useAuction(id: number) {
  return useQuery({
    queryKey: ['auction', id],
    queryFn: () => apiService.getAuction(id),
    enabled: !!id,
  });
}

export function useFeaturedAuctions() {
  return useQuery({
    queryKey: ['auctions', 'featured'],
    queryFn: apiService.getFeaturedAuctions,
    staleTime: 60000, // 1 minute
  });
}

export function useEndingSoonAuctions() {
  return useQuery({
    queryKey: ['auctions', 'ending-soon'],
    queryFn: apiService.getEndingSoonAuctions,
    staleTime: 30000, // 30 seconds
  });
}

export function useUserAuctions() {
  return useQuery({
    queryKey: ['user-auctions'],
    queryFn: apiService.getUserAuctions,
  });
}

export function useWatchedAuctions() {
  return useQuery({
    queryKey: ['watched-auctions'],
    queryFn: apiService.getWatchedAuctions,
  });
}

export function useWonAuctions() {
  return useQuery({
    queryKey: ['won-auctions'],
    queryFn: apiService.getWonAuctions,
  });
}

export function useCreateAuction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAuctionForm) => apiService.createAuction(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auctions'] });
      queryClient.invalidateQueries({ queryKey: ['user-auctions'] });
      toast.success('Auction created successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create auction');
    },
  });
}

export function useUpdateAuction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<CreateAuctionForm> }) =>
      apiService.updateAuction(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['auction', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['auctions'] });
      queryClient.invalidateQueries({ queryKey: ['user-auctions'] });
      toast.success('Auction updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update auction');
    },
  });
}

export function useDeleteAuction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiService.deleteAuction(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auctions'] });
      queryClient.invalidateQueries({ queryKey: ['user-auctions'] });
      toast.success('Auction deleted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete auction');
    },
  });
}

export function useActivateAuction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiService.activateAuction(id),
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: ['auction', id] });
      queryClient.invalidateQueries({ queryKey: ['auctions'] });
      queryClient.invalidateQueries({ queryKey: ['user-auctions'] });
      toast.success('Auction activated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to activate auction');
    },
  });
}

export function useEndAuction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiService.endAuction(id),
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: ['auction', id] });
      queryClient.invalidateQueries({ queryKey: ['auctions'] });
      queryClient.invalidateQueries({ queryKey: ['user-auctions'] });
      toast.success('Auction ended successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to end auction');
    },
  });
}

export function useUploadAuctionImages() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, files }: { id: number; files: FileList }) =>
      apiService.uploadAuctionImages(id, files),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['auction', variables.id] });
      toast.success('Images uploaded successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to upload images');
    },
  });
}

// Real-time auction updates hook
export function useAuctionUpdates(auctionId: number) {
  const queryClient = useQueryClient();

  // This would integrate with WebSocket service
  // For now, we'll use polling for real-time updates
  return useQuery({
    queryKey: ['auction-updates', auctionId],
    queryFn: () => apiService.getRecentBids(auctionId, 1),
    refetchInterval: 5000, // Poll every 5 seconds
    enabled: !!auctionId,
  });
}
