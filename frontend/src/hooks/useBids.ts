import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Bid, BidFilters, PlaceBidForm } from '../types';
import apiService from '../services/api';
import toast from 'react-hot-toast';

export function useAuctionBids(auctionId: number, filters?: BidFilters) {
  return useQuery({
    queryKey: ['auction-bids', auctionId, filters],
    queryFn: () => apiService.getAuctionBids(auctionId, filters),
    enabled: !!auctionId,
    refetchInterval: 10000, // Refetch every 10 seconds for real-time updates
  });
}

export function useUserBids() {
  return useQuery({
    queryKey: ['user-bids'],
    queryFn: apiService.getUserBids,
  });
}

export function useUserWinningBids() {
  return useQuery({
    queryKey: ['user-winning-bids'],
    queryFn: apiService.getUserWinningBids,
  });
}

export function useHighestBid(auctionId: number) {
  return useQuery({
    queryKey: ['highest-bid', auctionId],
    queryFn: () => apiService.getHighestBid(auctionId),
    enabled: !!auctionId,
    refetchInterval: 5000, // Refetch every 5 seconds
  });
}

export function useWinningBid(auctionId: number) {
  return useQuery({
    queryKey: ['winning-bid', auctionId],
    queryFn: () => apiService.getWinningBid(auctionId),
    enabled: !!auctionId,
    refetchInterval: 5000, // Refetch every 5 seconds
  });
}

export function useRecentBids(auctionId: number, minutes = 5) {
  return useQuery({
    queryKey: ['recent-bids', auctionId, minutes],
    queryFn: () => apiService.getRecentBids(auctionId, minutes),
    enabled: !!auctionId,
    refetchInterval: 3000, // Refetch every 3 seconds for real-time updates
  });
}

export function usePlaceBid() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: PlaceBidForm) => apiService.placeBid(data),
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['auction-bids', variables.auction_id] });
      queryClient.invalidateQueries({ queryKey: ['auction', variables.auction_id] });
      queryClient.invalidateQueries({ queryKey: ['highest-bid', variables.auction_id] });
      queryClient.invalidateQueries({ queryKey: ['winning-bid', variables.auction_id] });
      queryClient.invalidateQueries({ queryKey: ['recent-bids', variables.auction_id] });
      queryClient.invalidateQueries({ queryKey: ['user-bids'] });
      queryClient.invalidateQueries({ queryKey: ['user-winning-bids'] });
      
      toast.success('Bid placed successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to place bid');
    },
  });
}

export function useCancelBid() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiService.cancelBid(id),
    onSuccess: (data) => {
      // Invalidate related queries
      if (data.data.auction) {
        const auctionId = data.data.auction.id;
        queryClient.invalidateQueries({ queryKey: ['auction-bids', auctionId] });
        queryClient.invalidateQueries({ queryKey: ['auction', auctionId] });
        queryClient.invalidateQueries({ queryKey: ['highest-bid', auctionId] });
        queryClient.invalidateQueries({ queryKey: ['winning-bid', auctionId] });
        queryClient.invalidateQueries({ queryKey: ['recent-bids', auctionId] });
      }
      queryClient.invalidateQueries({ queryKey: ['user-bids'] });
      queryClient.invalidateQueries({ queryKey: ['user-winning-bids'] });
      
      toast.success('Bid cancelled successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to cancel bid');
    },
  });
}

// Hook for bid validation
export function useBidValidation(auctionId: number) {
  const { data: auction } = useQuery({
    queryKey: ['auction', auctionId],
    queryFn: () => apiService.getAuction(auctionId),
    enabled: !!auctionId,
  });

  const validateBid = (amount: number) => {
    if (!auction?.data) {
      return { isValid: false, message: 'Auction data not available' };
    }

    const currentBid = auction.data.current_bid.amount;
    const startingPrice = auction.data.starting_price.amount;
    const minimumBid = currentBid > 0 ? currentBid + 1 : startingPrice;

    if (amount < minimumBid) {
      return {
        isValid: false,
        message: `Minimum bid amount is ${auction.data.current_bid.currency} ${minimumBid.toFixed(2)}`,
      };
    }

    if (auction.data.status !== 'active') {
      return {
        isValid: false,
        message: 'This auction is not accepting bids',
      };
    }

    const remainingMinutes = auction.data.timing.remaining_minutes;
    if (remainingMinutes !== null && remainingMinutes <= 0) {
      return {
        isValid: false,
        message: 'This auction has ended',
      };
    }

    return { isValid: true };
  };

  return {
    validateBid,
    auction: auction?.data,
    isLoading: !auction,
  };
}
