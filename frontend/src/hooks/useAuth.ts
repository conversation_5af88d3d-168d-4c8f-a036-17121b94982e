import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { User, LoginForm, RegisterForm } from '../types';
import apiService from '../services/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (data: LoginForm) => Promise<void>;
  register: (data: RegisterForm) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (data: FormData) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const queryClient = useQueryClient();

  // Get user profile
  const { data: profileData } = useQuery({
    queryKey: ['profile'],
    queryFn: apiService.getProfile,
    enabled: !!localStorage.getItem('auth_token'),
    retry: false,
  });

  useEffect(() => {
    if (profileData?.data) {
      setUser(profileData.data);
    }
    setIsLoading(false);
  }, [profileData]);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: apiService.login,
    onSuccess: (data) => {
      setUser(data.data.user);
      queryClient.setQueryData(['profile'], { data: data.data.user });
      toast.success('Welcome back!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Login failed');
    },
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: apiService.register,
    onSuccess: (data) => {
      setUser(data.data);
      queryClient.setQueryData(['profile'], data);
      toast.success('Account created successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Registration failed');
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: apiService.updateProfile,
    onSuccess: (data) => {
      setUser(data.data);
      queryClient.setQueryData(['profile'], data);
      toast.success('Profile updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Profile update failed');
    },
  });

  const login = async (data: LoginForm) => {
    await loginMutation.mutateAsync(data);
  };

  const register = async (data: RegisterForm) => {
    await registerMutation.mutateAsync(data);
  };

  const updateProfile = async (data: FormData) => {
    await updateProfileMutation.mutateAsync(data);
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } finally {
      setUser(null);
      queryClient.clear();
      toast.success('Logged out successfully');
    }
  };

  const value: AuthContextType = {
    user,
    isLoading: isLoading || loginMutation.isPending || registerMutation.isPending,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
